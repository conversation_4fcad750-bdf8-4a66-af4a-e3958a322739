#!/bin/sh

set -e

cleanup () {
  nvm cache clear
  nvm deactivate
  rm -rf ${NVM_DIR}/v*
  nvm unalias default
}

die () { >&2 echo "$@" ; cleanup ; exit 1; }

\. ../../../nvm.sh

nvm_has_colors() { return 1 ; }

cleanup

OUTPUT="$(2>&1 nvm install --no-progress v0.12.18)"
EXPECTED_OUTPUT="Downloading and installing node v0.12.18...
Downloading https://nodejs.org/dist/v0.12.18/node-v0.12.18-linux-x64.tar.xz...
Computing checksum with sha256sum
Checksums matched!
Now using node v0.12.18 (npm v2.15.11)
Creating default alias: default -> v0.12.18 *"

[ "${OUTPUT}" = "${EXPECTED_OUTPUT}" ] || die "expected >${EXPECTED_OUTPUT}<, got >${OUTPUT}<"

cleanup

OUTPUT="$(2>&1 nvm install v0.12.18)"
EXPECTED_OUTPUT="Downloading and installing node v0.12.18...
Downloading https://nodejs.org/dist/v0.12.18/node-v0.12.18-linux-x64.tar.xz...
######################################################################## 100.0%
Computing checksum with sha256sum
Checksums matched!
Now using node v0.12.18 (npm v2.15.11)
Creating default alias: default -> v0.12.18 *"

[ "${OUTPUT}" = "${EXPECTED_OUTPUT}" ] || die "expected >${EXPECTED_OUTPUT}<, got >${OUTPUT}<"

cleanup
