language: generic
dist: xenial
addons:
  apt:
    packages:
      - zsh
    #  - ksh
    #  - gcc-4.8
    #  - g++-4.8

cache:
  ccache: true
  directories:
    - $HOME/.npm
    - $TRAVIS_BUILD_DIR/.cache
    - $TRAVIS_BUILD_DIR/node_modules
before_install:
  - $SHELL --version 2> /dev/null || dpkg -s $SHELL 2> /dev/null || which $SHELL
  - curl --version
  - wget --version
install:
  - if [ -z "${SHELLCHECK-}" ]; then nvm install node && npm install && npm prune && npm ls urchin doctoc eclint dockerfile_lint; fi
  - '[ -z "$WITHOUT_CURL" ] || sudo apt-get remove curl -y'
script:
  - if [ -n "${SHELL-}" ] && [ -n "${TEST_SUITE}" ]; then if [ "${TEST_SUITE}" = 'installation_iojs' ]; then travis_retry make TEST_SUITE=$TEST_SUITE URCHIN="$(npm bin)/urchin" test-$SHELL ; else make TEST_SUITE=$TEST_SUITE URCHIN="$(npm bin)/urchin" test-$SHELL; fi; fi
before_cache:
  - if [ -n "$WITHOUT_CURL" ]; then sudo apt-get install curl -y ; fi
env:
  global:
    - CXX=g++
    - CC=gcc
    - PATH="$(echo $PATH | sed 's/::/:/')"
    - PATH="/usr/lib/ccache/:$PATH"
    - NVM_DIR="${TRAVIS_BUILD_DIR}"
  matrix:
    - SHELL=bash TEST_SUITE=install_script
    - SHELL=sh TEST_SUITE=fast
    - SHELL=dash TEST_SUITE=fast
    - SHELL=bash TEST_SUITE=fast
    - SHELL=zsh TEST_SUITE=fast
    #  - SHELL=ksh TEST_SUITE=fast
    - SHELL=sh TEST_SUITE=sourcing
    - SHELL=dash TEST_SUITE=sourcing
    - SHELL=bash TEST_SUITE=sourcing
    - SHELL=zsh TEST_SUITE=sourcing
    #  - SHELL=ksh TEST_SUITE=sourcing
    - SHELL=sh TEST_SUITE=slow
    - SHELL=dash TEST_SUITE=slow
    - SHELL=bash TEST_SUITE=slow
    - SHELL=zsh TEST_SUITE=slow
    #  - SHELL=ksh TEST_SUITE=slow
    - SHELL=sh TEST_SUITE=installation_node
    - SHELL=sh TEST_SUITE=installation_node WITHOUT_CURL=1
    - SHELL=dash TEST_SUITE=installation_node
    - SHELL=dash TEST_SUITE=installation_node WITHOUT_CURL=1
    - SHELL=bash TEST_SUITE=installation_node
    - SHELL=bash TEST_SUITE=installation_node WITHOUT_CURL=1
    - SHELL=zsh TEST_SUITE=installation_node
    - SHELL=zsh TEST_SUITE=installation_node WITHOUT_CURL=1
    #  - SHELL=ksh TEST_SUITE=installation_node
    #  - SHELL=ksh TEST_SUITE=installation_node WITHOUT_CURL=1
    - SHELL=sh TEST_SUITE=installation_iojs
    - SHELL=sh TEST_SUITE=installation_iojs WITHOUT_CURL=1
    - SHELL=dash TEST_SUITE=installation_iojs
    - SHELL=dash TEST_SUITE=installation_iojs WITHOUT_CURL=1
    - SHELL=bash TEST_SUITE=installation_iojs
    - SHELL=bash TEST_SUITE=installation_iojs WITHOUT_CURL=1
    - SHELL=zsh TEST_SUITE=installation_iojs
    - SHELL=zsh TEST_SUITE=installation_iojs WITHOUT_CURL=1
    #  - SHELL=ksh TEST_SUITE=installation_iojs
    #  - SHELL=ksh TEST_SUITE=installation_iojs WITHOUT_CURL=1
