#% impex.setLocale(Locale.ENGLISH);

##### HYBRIS DATA (copied from platform) #####

INSERT_UPDATE Language;isocode[unique=true];active;;;;;;
;de;true;;;;;;
;en;true;;;;;;

UPDATE Language;isocode[unique=true];name[lang=de];name[lang=en];;;;;
;de;Deutsch;German;;;;;
;en;Englisch;English;;;;;

INSERT_UPDATE Currency;isocode[unique=true];conversion;digits;symbol;base;active
;EUR;1;2;€;true;true
;USD;1.4;2;$;false;true
;JPY;120;0;¥;false;false
;GBP;1;2;£;false;false

INSERT_UPDATE Unit;unitType[unique=true];code[unique=true];name[lang=de];name[lang=en];conversion;;;
;weight;kg;Kilogramm;kilogram;1
;weight;g;Gramm;gram;0.001
;weight;t;Tonnen;ton;1000
;weight;mg;Milligramm;milligram;0.000001
;volume;l;Liter;liter;1
;volume;m³;Kubikmeter;cubic meter;0.001
;volume;dm³;Kubikdezimeter;cubic decimeter;1
;volume;cm³;Kubikzentimeter;cubic centimeter;1000
;volume;ml;Milliliter;milliliter;1000

INSERT_UPDATE Title;code[unique=true];name[lang=de];name[lang=en];;
;dr;Dr.;dr.;;
;prof;Prof.;prof.;;

INSERT_UPDATE Country;isocode[unique=true];name[lang=de];name[lang=en];active[default=false];enabledForDevelopers[default=false];enabledForIntegrators[default=false];currency(isocode);supportedPaymentProviders(code)[mode=append];supportedPaymentMethods(code)[mode=append];
;AT;Österreich;Austria;
;GB;Großbritannien;United Kingdom;
;DE;Deutschland;Germany;true;true;true;EUR;BOSCH_TRANSFER,DPG,PGW,ADYEN;CREDIT_CARD,SEPA_CREDIT,SEPA_DIRECTDEBIT;
;CH;Schweiz;Switzerland;
;US;Vereinigte Staaten von Amerika;United States of America;true;true;true;USD;DPG;CREDIT_CARD,ACH_INTERNATIONAL;
;AD;Andorra;Andorra;
;AL;Albania;Albanien;
;BA;Bosnia and Herzegovina;Bosnien und Herzegowina;
;BE;Belgium;Belgien;
;BG;Bulgaria;Bulgarien;
;BY;Belarus;Weißrussland;
;CN;China;China;
;CY;Cyprus;Zypern;
;CZ;Czech Republic;Tschechien;
;DK;Denmark;Dänemark;
;EE;Estonia;Estland;
;ES;Spain;Spanien;
;FI;Finland;Finnland;
;FO;Faroe Islands;Färöer Inseln;
;FR;France;Frankreich;
;GG;Guernsey;Guernsey;
;GI;Gibraltar;Gibraltar;
;GL;Greenland;Grönland;
;GR;Greece;Griechenland;
;HK;Hong Kong;Hongkong;
;HR;Croatia;Kroatien;
;HU;Hungary;Ungarn;
;IE;Ireland;Irland;
;IM;Isle of Man;Isle of Man;
;IS;Iceland;Island;
;IT;Italy;Italien;
;JE;Jersey;Jersey;
;JP;Japan;Japan;
;KP;Korea, Democratic People's Republic of;Demokratische Volksrepublik Korea;
;KR;Republic of Korea;Republik Korea;
;LI;Liechtenstein;Liechtenstein;
;LT;Lithuania;Litauen;
;LU;Luxembourg;Luxemburg;
;LV;Latvia;Lettland;
;MC;Monaco;Monaco;
;MD;Moldova;Moldawien;
;ME;Montenegro;Montenegro;
;MK;Macedonia;Mazedonien;
;MT;Malta;Malta;
;NL;Netherlands;Niederlande;
;NO;Norway;Norwegen;
;PL;Poland;Polen;
;PT;Portugal;Portugal;
;RO;Romania;Rumänien;
;RS;Serbia;Serbien;
;RU;Russian Federation;Russische Föderation;
;SE;Sweden;Schweden;
;SI;Slovenia;Slowenien;
;SK;Slovakia;Slowakei;
;SM;San Marino;San Marino;
;TR;Turkey;Türkei;
;UA;Ukraine;Ukraine;
;VA;Holy See (Vatican City State);Heiliger Stuhl (Staat Vatikanstadt);
;VN;Viet Nam;Vietnam;


INSERT_UPDATE CreditCardType;code[unique=true];name[lang=de];name[lang=en];;
;amex;American Express;American Express;;
;visa;Visa;Visa;;
;master;MasterCard;MasterCard;;
;diners;Diners;Diners;;


INSERT_UPDATE Region;isocode[unique=true];name[lang=de];name[lang=en];active;Country(isocode)
;DE-BW;Baden-Württemberg;Baden-Wuerttemberg;true;DE
;DE-BY;Bayern;Bavaria;true;DE
;DE-BE;Berlin;Berlin;true;DE
;DE-BR;Brandenburg;Brandenburg;true;DE
;DE-HB;Bremen;Bremen;true;DE
;DE-HH;Hamburg;Hamburg;true;DE
;DE-HE;Hessen;Hesse;true;DE
;DE-MV;Mecklenburg-Vorpommern;Mecklenburg-Western Pomerania;true;DE
;DE-NI;Niedersachsen;Lower Saxony;true;DE
;DE-NW;Nordrhein-Westfalen;North Rhine-Westphalia;true;DE
;DE-RP;Rheinland-Pfalz;Rhineland-Palatinate;true;DE
;DE-SL;Saarland;Saarland;true;DE
;DE-ST;Sachsen-Anhalt;Saxony-Anhalt;true;DE
;DE-SN;Sachsen;Saxony;true;DE
;DE-SH;Schleswig-Holstein;Schleswig-Holstein;true;DE
;DE-TH;Thüringen;Thuringia;true;DE
;AL;Alabama;Alabama;true;US
;AK;Alaska;Alaska;true;US
;AZ;Arizona;Arizona;true;US
;AR;Arkansas;Arkansas;true;US
;CO;Colorado;Colorado;true;US
;CT;Connecticut;Connecticut;true;US
;DE;Delaware;Delaware;true;US
;DC;Washington, D.C.;Washington, D.C.;true;US
;FL;Florida;Florida;true;US
;GA;Georgia;Georgia;true;US
;HI;Hawaii;Hawaii;true;US
;ID;Idaho;Idaho;true;US
;IL;Illinois;Illinois;true;US
;IN;Indiana;Indiana;true;US
;IA;Iowa;Iowa;true;US
;CA;Kalifornien;California;true;US
;KS;Kansas;Kansas;true;US
;KY;Kentucky;Kentucky;true;US
;LA;Louisiana;Louisiana;true;US
;ME;Maine;Maine;true;US
;MD;Maryland;Maryland;true;US
;MA;Massachusetts;Massachusetts;true;US
;MI;Michigan;Michigan;true;US
;MN;Minnesota;Minnesota;true;US
;MS;Mississippi;Mississippi;true;US
;MO;Missouri;Missouri;true;US
;MT;Montana;Montana;true;US
;NE;Nebraska;Nebraska;true;US
;NV;Nevada;Nevada;true;US
;NH;New Hampshire;New Hampshire;true;US
;NJ;New Jersey;New Jersey;true;US
;NM;New Mexico;New Mexico;true;US
;NY;New York;New York;true;US
;NC;North Carolina;North Carolina;true;US
;ND;North Dakota;North Dakota;true;US
;OH;Ohio;Ohio;true;US
;OK;Oklahoma;Oklahoma;true;US
;OR;Oregon;Oregon;true;US
;PA;Pennsylvania;Pennsylvania;true;US
;RI;Rhode Island;Rhode Island;true;US
;SC;South Carolina;South Carolina;true;US
;SD;South Dakota;South Dakota;true;US
;TN;Tennessee;Tennessee;true;US
;TX;Texas;Texas;true;US
;UT;Utah;Utah;true;US
;VT;Vermont;Vermont;true;US
;VA;Virginia;Virginia;true;US
;WA;Washington;Washington;true;US
;WV;West Virginia;West Virginia;true;US
;WI;Wisconsin;Wisconsin;true;US
;WY;Wyoming;Wyoming;true;US;;;;;;;;;;;;

INSERT_UPDATE Company;uid[unique=true];buyer;manufacturer;supplier;carrier;country(isocode);locname[lang=de];locname[lang=en];;;;;;;;;
;hybris;true;false;true;true;DE;hybris GmbH;hybris GmbH;;;;;;;;;

INSERT_UPDATE Address;owner(Company.uid)[unique=true];streetname[unique=true];streetnumber[unique=true];postalcode[unique=true];town;country(isocode);billingAddress;contactAddress;shippingAddress;unloadingAddress;email;phone1;url;company;appartment;building;fax
;hybris;Nymphenburger Strasse;86;D-80636;München;DE;true;true;true;true;<EMAIL>;+49(0)89 / 890 65 0;http://www.hybris.de;hybris GmbH;3. Etage;Treppenhaus 3;+49(0)89 / 890 65 555

UPDATE Company;uid[unique=true];addresses(owner(Company.uid),streetname,streetnumber,postalcode);billingAddress(owner(Company.uid),streetname,streetnumber,postalcode);contactAddress(owner(Company.uid),streetname,streetnumber,postalcode);shippingAddress(owner(Company.uid),streetname,streetnumber,postalcode);vatID;unloadingAddress(owner(Company.uid),streetname,streetnumber,postalcode);;;;;;;;;;
;hybris;hybris:Nymphenburger Strasse:86:D-80636;hybris:Nymphenburger Strasse:86:D-80636;hybris:Nymphenburger Strasse:86:D-80636;hybris:Nymphenburger Strasse:86:D-80636;DE *********;hybris:Nymphenburger Strasse:86:D-80636;;;;;;;;;;


##### CUSTOM DATA #####

INSERT_UPDATE Unit;unitType[unique=true];code[unique=true];name[lang=de];name[lang=en];conversion;;;
;pieces;pieces;pieces;pieces;1

INSERT_UPDATE MediaFolder;qualifier[unique=true];path[unique=true]
;apks;apks
;images;images
;invoices;invoices
;self-billing-invoices;self-billing-invoices
;catalog-unaware-images;catalog-unaware-images

INSERT_UPDATE Usergroup;uid[unique=true];groups(uid)[mode=append]
;customergroup;
;developergroup;customergroup
;integratorgroup;customergroup
;syncusers
;webserviceclients;
;stripeusers;

INSERT_UPDATE User;uid[unique=true];groups(uid)[mode=append];password
;remoteportal;webserviceclients;12341234
;syncuser;syncusers
;payoutuser
;stripe;stripeusers;epirts;

INSERT_UPDATE Customer;uid[unique=true];groups(uid);
;anonymous;integratorgroup

INSERT_UPDATE ContentCatalog;id[unique=true]
;integratorContentCatalog

INSERT_UPDATE CatalogVersion;catalog(id)[unique=true];version[unique=true];active;languages(isoCode)
;integratorContentCatalog;Staged;false;en,de
;integratorContentCatalog;Online;true;en,de

INSERT_UPDATE Catalog;id[unique=true]
;cisProductCatalog
;secondProductCatalog

INSERT_UPDATE ClassificationSystem;id[unique=true]
;cisClassificationCatalog

INSERT_UPDATE CatalogVersion;catalog(id)[unique=true];version[unique=true];active;languages(isoCode);readPrincipals(uid)
;cisProductCatalog;Staged;false;en,de;employeegroup
;secondProductCatalog;Staged;false;en,de;employeegroup
;cisProductCatalog;Online;true;en,de;employeegroup
;secondProductCatalog;Online;true;en,de;employeegroup

UPDATE CatalogVersion;catalog(id)[unique=true];version[unique=true];writePrincipals(uid)[mode=append];readPrincipals(uid)[mode=append];
;cisProductCatalog;Staged;syncusers;syncusers
;secondProductCatalog;Staged;syncusers;syncusers
;cisProductCatalog;Online;syncusers;syncusers
;secondProductCatalog;Online;syncusers;syncusers

INSERT_UPDATE CatalogVersionSyncJob;code[unique=true];sourceVersion(catalog(id[default=cisProductCatalog]),version[default='Staged'])[unique=true,default='cisProductCatalog:Staged'];targetVersion(catalog(id[default=cisProductCatalog]),version[default='Online'])[unique=true       ,default='cisProductCatalog:Online'];createNewItems;removeMissingItems;rootTypes(code)[mode=append];syncPrincipals(uid)[mode=append]; syncPrincipalsOnly[default=false];sessionUser(uid)
;sync cisProductCatalog:Staged->Online;;;true;false;;syncusers;true;syncuser
;sync secondProductCatalog:Staged->Online;;;true;false;;syncusers;true;syncuser

INSERT_UPDATE ClassificationSystemVersion;catalog(id)[unique=true];version[unique=true];active;inclPacking[virtual=true,default=true];inclDuty[virtual=true,default=true];inclFreight[virtual=true,default=true];inclAssurance[virtual=true,default=true]
;cisClassificationCatalog;1.0;true

$unitedKingdom=GB,GG,IM,JE
$europeNotUK=AD,AL,AT,BA,BE,BG,BY,CH,CY,CZ,DE,DK,EE,ES,FI,FO,FR,GI,GL,GR,HR,HU,IE,IS,IT,LI,LT,LU,LV,MC,MD,ME,MK,MT,NL,NO,PL,PT,RO,RS,RU,SE,SI,SK,SM,TR,UA,VA
$asianCountries=CN,JP,VN,HK,KP,KR
$deliveryCountries=$unitedKingdom,$europeNotUK,$asianCountries,US

INSERT_UPDATE BaseStore;uid[unique=true];catalogs(id);currencies(isocode);net;storelocatorDistanceUnit(code);defaultCurrency(isocode);languages(isocode);defaultLanguage(isocode);deliveryCountries(isocode);customerAllowedToIgnoreSuggestions;
;iotstore;cisProductCatalog,cisClassificationCatalog;USD,JPY,EUR;true;km;USD;en,de;en;$deliveryCountries;true;

INSERT_UPDATE SiteTheme;code[unique=true]
;alpha

INSERT_UPDATE CMSSite;uid[unique=true];theme(code);channel(code);stores(uid);contentCatalogs(id);defaultCatalog(id);defaultLanguage(isoCode);urlPatterns;active;
;developerconsole;alpha;B2C;iotstore;<ignore>;cisProductCatalog;en;.*devcon.*;true;
;iotstore;alpha;B2C;iotstore;integratorContentCatalog;cisProductCatalog;en;.*shop.*;true;

UPDATE GenericItem[processor=de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor];pk[unique=true]
$demoCompany=$config-demoCompanyId
$testaccountdev_demoUserID=$config-testaccountdev_demoUserID

INSERT_UPDATE UserPriceGroup ; code[unique=true]
; integrationtestCompanyA
; integrationtestCompanyB
; integrationtestCompanyC
; $demoCompany

INSERT_UPDATE IoTCompany;uid[unique=true];name;country(isocode);stripeCustomerId;&compRef;userPriceGroup(code);manualAppApprovalEnabled[default=true];store(uid)[default=iotstore]
;integrationtestCompanyA;CompanyA;DE;stripeCustomerIdA;integrationtestCompanyA;integrationtestCompanyA
;integrationtestCompanyB;CompanyB;US;;integrationtestCompanyB;integrationtestCompanyB
;integrationtestCompanyC;CompanyC;DE;;integrationtestCompanyC;integrationtestCompanyC
;$demoCompany;Demo Apps;DE;;sastDemo;$demoCompany

INSERT_UPDATE Address;owner(&compRef)[unique=true];duplicate[unique=true];town;country(isocode);billingAddress;contactAddress;lastname;email;&addId
;integrationtestCompanyA;false;Testlingen;DE;true;true;A Company;<EMAIL>;AAddress
;integrationtestCompanyB;false;Testtown;US;true;true;B Company;<EMAIL>;BAddress
;integrationtestCompanyC;false;Testlingen;DE;true;true;C Company;<EMAIL>;CAddress
;sastDemo;false;Aachen;DE;false;true;Demo Apps;<EMAIL>;sastDemoAddress

UPDATE IoTCompany;uid[unique=true];billingAddress(&addId);contactAddress(&addId);approvalStatus(code);bpmdId;operationalStage(code)
;integrationtestCompanyA;AAddress;AAddress;APPROVED_COMMERCIAL;intTestCoA_bpmdid;OPERATIONAL
;integrationtestCompanyB;BAddress;BAddress;APPROVED_COMMERCIAL;intTestCoB_bpmdid;OPERATIONAL
;integrationtestCompanyC;CAddress;CAddress;APPROVED_COMMERCIAL;intTestCoC_bpmdid;OPERATIONAL
;$demoCompany;sastDemoAddress;sastDemoAddress;APPROVED_COMMERCIAL;demoCo_bpmdid;OPERATIONAL

# Create sample users (devcon)
INSERT_UPDATE Developer;originalUid[unique=true];uid[unique=true];title(code);name;description;sessionLanguage(isocode);sessionCurrency(isocode);password;groups(uid[default=$integratorGroup]);company(uid)
;4c87662c-f212-11e8-8eb2-f2801f1b9fd1@devcon;4c87662c-f212-11e8-8eb2-f2801f1b9fd1@devcon;;CBS Developer Account;;de;USD;1234;developergroup,integrationtestCompanyA;integrationtestCompanyA
;279aaa80-f213-11e8-8eb2-f2801f1b9fd1@devcon;279aaa80-f213-11e8-8eb2-f2801f1b9fd1@devcon;;Test Dev1;;en;USD;1234;developergroup,integrationtestCompanyA;integrationtestCompanyA
;760937fe-f213-11e8-8eb2-f2801f1b9fd1@devcon;760937fe-f213-11e8-8eb2-f2801f1b9fd1@devcon;;Test Dev2;;en;USD;1234;developergroup,integrationtestCompanyA;integrationtestCompanyA
;8f7247a8-f213-11e8-8eb2-f2801f1b9fd1@devcon;8f7247a8-f213-11e8-8eb2-f2801f1b9fd1@devcon;;Test Dev3;;en;USD;1234;developergroup,integrationtestCompanyB;integrationtestCompanyB
;fa3eff4a-f213-11e8-8eb2-f2801f1b9fd1@devcon;fa3eff4a-f213-11e8-8eb2-f2801f1b9fd1@devcon;;Test Dev4;;de;EUR;1234;developergroup,integrationtestCompanyC;integrationtestCompanyC

INSERT_UPDATE Developer;originalUid[unique=true];uid[unique=true];title(code);name;description;sessionLanguage(isocode);sessionCurrency(isocode);password;groups(uid[default=$developerGroup]);company(uid)
;$testaccountdev_demoUserID@devcon;$testaccountdev_demoUserID@devcon;;Tina Test;Test user Tina Test;en;USD;1234;developergroup,$demoCompany;$demoCompany

# Create sample users (shop)
INSERT_UPDATE Integrator;originalUid[unique=true];uid[unique=true];developer(uid);title(code);name;description;sessionLanguage(isocode);sessionCurrency(isocode);company(uid);password;groups(uid[default=$integratorGroup]);emailAddress
;4c87662c-f212-11e8-8eb2-f2801f1b9fd1@shop;4c87662c-f212-11e8-8eb2-f2801f1b9fd1@shop;4c87662c-f212-11e8-8eb2-f2801f1b9fd1@devcon;;CBS;CBS Integrator Account;de;USD;integrationtestCompanyA;1234;integratorgroup,integrationtestCompanyA;<EMAIL>
;279aaa80-f213-11e8-8eb2-f2801f1b9fd1@shop;279aaa80-f213-11e8-8eb2-f2801f1b9fd1@shop;279aaa80-f213-11e8-8eb2-f2801f1b9fd1@devcon;;TestIntegrator1;;de;USD;integrationtestCompanyA;1234;integratorgroup,integrationtestCompanyA;<EMAIL>
;760937fe-f213-11e8-8eb2-f2801f1b9fd1@shop;760937fe-f213-11e8-8eb2-f2801f1b9fd1@shop;760937fe-f213-11e8-8eb2-f2801f1b9fd1@devcon;;TestIntegrator2;;de;USD;integrationtestCompanyA;1234;integratorgroup,integrationtestCompanyA;<EMAIL>
;8f7247a8-f213-11e8-8eb2-f2801f1b9fd1@shop;8f7247a8-f213-11e8-8eb2-f2801f1b9fd1@shop;8f7247a8-f213-11e8-8eb2-f2801f1b9fd1@devcon;;TestIntegrator3;;de;USD;integrationtestCompanyB;1234;integratorgroup,integrationtestCompanyA;<EMAIL>
;fa3eff4a-f213-11e8-8eb2-f2801f1b9fd1@shop;fa3eff4a-f213-11e8-8eb2-f2801f1b9fd1@shop;fa3eff4a-f213-11e8-8eb2-f2801f1b9fd1@devcon;;TestIntegrator4;;de;EUR;integrationtestCompanyC;1234;integratorgroup,integrationtestCompanyA;<EMAIL>

INSERT_UPDATE Integrator;originalUid[unique=true];uid[unique=true];developer(uid);name;description;sessionLanguage(isocode);sessionCurrency(isocode);company(uid);password;groups(uid[default=$integratorGroup])
;$testaccountdev_demoUserID@shop;$testaccountdev_demoUserID@shop;$testaccountdev_demoUserID@devcon;Tina Test;Test user Tina Test;en;USD;$demoCompany;1234;integratorgroup,$demoCompany


INSERT_UPDATE BoschSepaCollectionAccount; code[ unique = true]                    ; bankName                       ; bic           ; iban                     ;
                                        ; BoschSepaCollectionAccount1             ; "First Ferengi Interplanetary" ; "BYLADEM1001" ; "**********************" ;

INSERT_UPDATE BoschSellerAccount; accountId[unique = true]        ; company(uid)             ; user(uid)                                   ; paymentProvider(code) ; billingSystemStatus(code) ; status(code) ; sepaCollectionAccount(code)             ;
                                ; "completedAaBoschSellerAccount" ; integrationtestCompanyA  ; 4c87662c-f212-11e8-8eb2-f2801f1b9fd1@devcon ; BOSCH_TRANSFER        ; IN_SYNC                   ; ACTIVE       ; BoschSepaCollectionAccount1             ;


INSERT_UPDATE AdyenSellerAccount; accountId[unique = true]; company(uid)             ; user(uid)                                   ; paymentProvider(code) ; billingSystemStatus(code) ; status(code) ;
                                ; "MA_DE_BOSS_EIL"        ; integrationtestCompanyA  ; 4c87662c-f212-11e8-8eb2-f2801f1b9fd1@devcon ; ADYEN                 ; IN_SYNC                   ; ACTIVE       ;




INSERT_UPDATE WorkflowTemplate;code[unique=true];name[lang=de];name[lang=en];owner(uid);description[lang=de];description[lang=en];visibleForPrincipals(uid)
;appApprovalWorkflow;App Überprüfungsworkflow;App approval workflow;admin;Workflow zur Überprüfung neuer Apps;Workflow for the approval of new apps;admingroup

INSERT_UPDATE WorkflowActionTemplate;code[unique=true];name[lang=de];name[lang=en];description[lang=de];description[lang=en];principalAssigned(uid);workflow(code)[unique=true];actionType(code);sendEmail[default=false]
;endAppApprovalAction;Ende;End;Beendet die Überprüfung einer App;Ends the approval process for a review;admingroup;appApprovalWorkflow;end;
;manualApproval;Manuelle Überprüfung;Manual Approval;Manuelle Überprüfung einer App;Manual approval of an app;admingroup;appApprovalWorkflow;normal

INSERT_UPDATE AutomatedWorkflowActionTemplate;code[unique=true];name[lang=de];name[lang=en];description[lang=de];description[lang=en];principalAssigned(uid);workflow(code)[unique=true];actionType(code);jobHandler;sendEmail[default=false]
;checkAppAutomaticallyAction;Automatische Überprüfung;Automatic approval;Überprüft eine App automatisch;Validates an app automatically;admingroup;appApprovalWorkflow;start;automaticAppApprovalJob
;createAppReleaseAction;App-Erstellung;App creation;Generiert die App aus dem draft;Creates the App from the draft;admingroup;appApprovalWorkflow;normal;releaseAppJob
;setAppUnapprovedAction;App Ablehnung;Declining app;Setzt den Status einer App auf 'Unapproved';Sets the status of an app to 'Unapproved';admingroup;appApprovalWorkflow;normal;declineAppJob
;syncReleasedAppAction;App-Synchronisierung;Synch App;App-Synchronisierung;Synch App;admingroup;appApprovalWorkflow;normal;syncReleasedAppJob

#Be careful refactoring the code field! It's referenced in AutomaticAppApprovalJob
INSERT_UPDATE WorkflowDecisionTemplate;code[unique=true];name[lang=de];name[lang=en];actionTemplate(code)
;approveAppDecision;App zulassen;Approve app;checkAppAutomaticallyAction;
;declineAppDecision;App ablehnen;Decline app;checkAppAutomaticallyAction;
;approveAppManually;Genehmigen;Approve manually;manualApproval
;declineAppManually;Ablehnen;Decline manually;manualApproval
;endApproved;Erstellt;Created;createAppReleaseAction;
;endDisapproved;enden;end;setAppUnapprovedAction;
;endReleased;Synchronisiert;Synched;syncReleasedAppAction;

INSERT_UPDATE WorkflowActionTemplateLinkTemplateRelation;source(code)[unique=true];target(code)[unique=true];andConnectionTemplate[default=false]
;approveAppDecision;manualApproval
;declineAppDecision;setAppUnapprovedAction
;approveAppManually;createAppReleaseAction
;declineAppManually;setAppUnapprovedAction
;endApproved;syncReleasedAppAction
;endDisapproved;endAppApprovalAction
;endReleased;endAppApprovalAction


INSERT_UPDATE WorkflowTemplate;code[unique=true];name[lang=de];name[lang=en];owner(uid);description[lang=de];description[lang=en];visibleForPrincipals(uid)
;appVersionApprovalWorkflow;App-Version Überprüfungsworkflow;App version approval workflow;admin;Workflow zur Überprüfung neuer Apps-Versionen;Workflow for the approval of new app versions;admingroup

INSERT_UPDATE WorkflowActionTemplate;code[unique=true];name[lang=de];name[lang=en];description[lang=de];description[lang=en];principalAssigned(uid);workflow(code)[unique=true];actionType(code);sendEmail[default=false]
;endAppVersionApprovalAction;Ende;End;Beendet die Überprüfung einer App-Version;Ends the approval process for a app version;admingroup;appVersionApprovalWorkflow;end;
;manualAppVersionApproval;Manuelle Überprüfung;Manual Approval;Manuelle Überprüfung einer App-Version;Manual approval of an app version;admingroup;appVersionApprovalWorkflow;normal

INSERT_UPDATE AutomatedWorkflowActionTemplate;code[unique=true];name[lang=de];name[lang=en];description[lang=de];description[lang=en];principalAssigned(uid);workflow(code)[unique=true];actionType(code);jobHandler;sendEmail[default=false]
;checkAppVersionAutomaticallyAction;Automatische Überprüfung;Automatic approval;Überprüft eine App-Version automatisch;Validates an app version automatically;admingroup;appVersionApprovalWorkflow;start;automaticAppVersionApprovalJob
;postAppVersionReviewCheckAction;Validierung nach manueller Prüfung;Validation after manual approval;Prüft die Version auf Basis der beim Review angegebenen Daten;Checks the version based on data provided during review;admingroup;appVersionApprovalWorkflow;normal;postAppVersionReviewCheckJob
;setAppVersionApprovedAction;App-Versions Zulassung;Approving app version;Setzt den Status einer App-Version auf 'Approved';Sets the status of an app version to 'Approved';admingroup;appVersionApprovalWorkflow;normal;approveAppVersionJob
;setAppVersionUnapprovedAction;App-Versions Ablehnung;Declining app version;Setzt den Status einer App-Version auf 'Unapproved';Sets the status of an app version to 'Unapproved';admingroup;appVersionApprovalWorkflow;normal;declineAppVersionJob

#Be careful refactoring the code field! It's referenced in AutomaticAppVersionApprovalJob
INSERT_UPDATE WorkflowDecisionTemplate;code[unique=true];name[lang=de];name[lang=en];actionTemplate(code)
;approveAppVersionDecision;App-Version zulassen;Approve app version;checkAppVersionAutomaticallyAction;
;declineAppVersionDecision;App-Version ablehnen;Decline app version;checkAppVersionAutomaticallyAction;
;approveAppVersionManually;Manuell genehmigen;Approve manually;manualAppVersionApproval
;declineAppVersionManually;Manuell ablehnen;Decline manually;manualAppVersionApproval
;endAppVersionApproved;enden;end;setAppVersionApprovedAction;
;endAppVersionDisapproved;enden;end;setAppVersionUnapprovedAction;
;approveAppVersionAfterReview;Version nach Prüfung genehmigen;Approve version after review;postAppVersionReviewCheckAction
;declineAppVersionAfterReview;Version nach Prüfung ablehnen;Deline version after review;postAppVersionReviewCheckAction

INSERT_UPDATE WorkflowActionTemplateLinkTemplateRelation;source(code)[unique=true];target(code)[unique=true];andConnectionTemplate[default=false]
;approveAppVersionDecision;manualAppVersionApproval
;declineAppVersionDecision;setAppVersionUnapprovedAction
;approveAppVersionManually;postAppVersionReviewCheckAction
;declineAppVersionManually;setAppVersionUnapprovedAction
;approveAppVersionAfterReview;setAppVersionApprovedAction
;declineAppVersionAfterReview;setAppVersionUnapprovedAction
;endAppVersionApproved;endAppVersionApprovalAction
;endAppVersionDisapproved;endAppVersionApprovalAction


INSERT_UPDATE WorkflowTemplate;code[unique=true];name[lang=de];name[lang=en];owner(uid);description[lang=de];description[lang=en];visibleForPrincipals(uid)
;reviewApprovalWorkflow;Bewertungs-Zulassungsworkflow;Review approval workflow;admin;Workflow zur Überprüfung neuer Reviews;Workflow for the approval of new reviews;admingroup

INSERT_UPDATE WorkflowActionTemplate;code[unique=true];name[lang=de];name[lang=en];description[lang=de];description[lang=en];principalAssigned(uid);workflow(code)[unique=true];actionType(code);sendEmail[default=false]
;endReviewApprovalAction;Ende;End;Beendet die Überprüfung einer Bewertung;Ends the approval process for a review;admingroup;reviewApprovalWorkflow;end;

INSERT_UPDATE AutomatedWorkflowActionTemplate;code[unique=true];name[lang=de];name[lang=en];description[lang=de];description[lang=en];principalAssigned(uid);workflow(code)[unique=true];actionType(code);jobHandler;sendEmail[default=false]
;checkReviewAutomaticallyAction;Automatische Überprüfung einer Bewertung;Automatic review approval;Überprüft eine Bewertung automatisch;Validates a review automatically;admingroup;reviewApprovalWorkflow;start;automaticReviewApprovalJob
;setReviewApprovedAction;Zulassung der Bewertung;Approving review;Setzt den Status einer Bewertung auf 'Approved';Sets the status of a review to 'Approved';admingroup;reviewApprovalWorkflow;normal;approveReviewJob

INSERT_UPDATE WorkflowDecisionTemplate;code[unique=true];name[lang=de];name[lang=en];actionTemplate(code)
;approveReviewDecision;Review zulassen;Approve review;checkReviewAutomaticallyAction;
;endReviewApprovalDecision;enden;end;setReviewApprovedAction;

INSERT_UPDATE WorkflowActionTemplateLinkTemplateRelation;source(code)[unique=true];target(code)[unique=true];andConnectionTemplate[default=false]
;approveReviewDecision;setReviewApprovedAction
;endReviewApprovalDecision;endReviewApprovalAction

INSERT_UPDATE SearchRestriction;code[unique=true];principal(uid);generate;query;restrictedType(code)
;AppVisibility_DeveloperCompany;developergroup;true;"EXISTS (
{{
SELECT * FROM {IoTCompany as company}
WHERE {company.pk} IN (?session.user.company) AND {item.company}={company.pk}
}}
)";App
;AppVersionVisibility_DeveloperCompany;developergroup;true;"EXISTS (
{{
SELECT * FROM {AppLicense AS license
JOIN App AS app ON {license.baseproduct}={app.pk}
JOIN IoTCompany AS company ON {app.company}={company.pk}}
WHERE {company.pk} IN (?session.user.company) AND {item.baseproduct}={app.pk}
}}
)";AppLicense
;Sync_ProductCatalog;syncusers;true;"{item.approvalstatus} = ({{
SELECT {status.pk} FROM {ArticleApprovalStatus AS status} WHERE {status.code} = 'approved'
}})";Product
;ProductContainerVisibility_Company;developergroup;true;"{item:company} = (?session.user.company)";ProductContainer
;SyncMedia_ProductCatalog;syncusers;true;"( EXISTS (
{{
SELECT 1 FROM { App as app
JOIN MediaContainer AS mc on {app.galleryimages} LIKE CONCAT( '%', CONCAT( {mc.PK} , '%' ) ) OR {app.icon} = {mc.PK}
JOIN ArticleApprovalStatus AS appstat on {appstat.pk} = {app.approvalstatus}}
WHERE {appstat.code} = 'approved' and {item.mediaContainer} = {mc.pk}
}})
AND {item.original} is null
AND {item.originalDataPk} is null
OR EXISTS (
{{
SELECT 1 FROM {
 Type AS ct } WHERE {ct.code} NOT LIKE 'Media' and {item.itemtype}={ct.pk}
}}
)
)";Media
;SyncMediaContainer_ProductCatalog;syncusers;true;"EXISTS (
{{
SELECT 1 FROM { App as app
JOIN ArticleApprovalStatus AS appstat on {appstat.pk} = {app.approvalstatus} }
WHERE {appstat.code} = 'approved'
and ({app.galleryimages} LIKE CONCAT( '%', CONCAT( {item.PK} , '%' ) ) OR {app.icon} = {item.PK} )
}}
)";MediaContainer

INSERT_UPDATE SearchRestriction;code[unique=true];principal(uid);generate;query;restrictedType(code);
;App_IsEnabledInStore;integratorgroup;true;"{item.masterEnabled} && {item.storeAvailabilityMode} != ({{
SELECT {saMode.pk} FROM {StoreAvailabilityMode as saMode} WHERE {saMode.code} = 'UNAVAILABLE'
}})";App

INSERT_UPDATE Category;code[unique=true];allowedPrincipals(uid)[default='customergroup'];catalogversion(catalog(id[default=cisProductCatalog]),version[default='Staged'])[unique=true,default=cisProductCatalog:Staged]
;main
;cat_00001

INSERT_UPDATE CategoryCategoryRelation;target(code, catalogversion(catalog(id[default=cisProductCatalog]),version[default='Staged'])[unique=true,default=cisProductCatalog:Staged])[unique=true];source(code, catalogversion(catalog(id[default=cisProductCatalog]),version[default='Staged'])[unique=true,default=cisProductCatalog:Staged])[unique=true]
;cat_00001;main

INSERT_UPDATE ServicelayerJob;code[unique=true];springId
;createInvoiceJob;createInvoiceJobPerformable;
;getAcquisitionsPerAppLicenseJob;getAcquisitionsPerAppLicenseJobPerformable;
;unusedMediaRemovalJob;unusedMediaRemovalJobPerformable;
;purchasedAppsAssignmentJob;purchasedAppsAssignmentJobPerformable;
;unusedPaymentRemovalJob;unusedPaymentRemovalJobPerformable;
;priceRecalculationJob;priceRecalculationJobPerformable
;orderStatusOverdueJob;orderStatusOverdueJobPerformable;
;oldCartRemovalJob;oldCartRemovalJob;
;invoiceStatusOverdueJob;invoiceStatusOverdueJobPerformable;

INSERT_UPDATE CronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);filesCount;filesDaysOld;filesOperator(code);logToFile
;getAcquisitionsPerAppLicenseCronJob;getAcquisitionsPerAppLicenseJob;admin;en;60;60;AND;TRUE

INSERT_UPDATE CronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);filesCount;filesDaysOld;filesOperator(code);logToFile
;orderStatusOverdueCronJob;orderStatusOverdueJob;anonymous;en;60;60;AND;TRUE

INSERT_UPDATE UnusedMediaRemovalCronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);
;unusedMediaRemovalCronJob;unusedMediaRemovalJob;anonymous;en;

INSERT_UPDATE PurchasedAppsAssignmentCronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);filesCount;filesDaysOld;filesOperator(code);logToFile
;purchasedAppsAssignmentCronJob;purchasedAppsAssignmentJob;syncuser;en;60;60;AND;TRUE

INSERT_UPDATE UnusedPaymentRemovalCronJob;code[unique=true];age;job(code);sessionLanguage(isoCode)[default=en]
;unusedPaymentRemovalCronJob;86400;unusedPaymentRemovalJob;;

INSERT_UPDATE CronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);filesCount;filesDaysOld;filesOperator(code);logToFile
;invoiceStatusOverdueCronJob;invoiceStatusOverdueJob;anonymous;en;60;60;AND;TRUE

REMOVE CronJob;code[unique=true];
;priceRecalulationCronJob

INSERT_UPDATE PriceRecalculationCronjob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);filesCount;filesDaysOld;filesOperator(code);logToFile;nodeGroup
                     ;priceRecalulationCronJob;priceRecalculationJob;syncuser;en;60;60;AND;TRUE;ncf

INSERT_UPDATE ServicelayerJob;code[unique=true];springId
                             ;videoValidationJob;videoValidationJobPerformable

INSERT_UPDATE CronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);filesCount;filesDaysOld;filesOperator(code);logToFile;nodeGroup
                     ;videoValidationCronJob;videoValidationJob;syncuser;en;60;60;AND;TRUE;ncf

INSERT_UPDATE OldCartRemovalCronJob;code[unique=true];cartRemovalAge;anonymousCartRemovalAge;job(code);sites(uid);nodeGroup
;oldCartRemovalCronJob;7776000;604800;oldCartRemovalJob;iotstore;ncf

INSERT_UPDATE CatalogUnawareMedia; code[unique=true];@media[translator=de.hybris.platform.impex.jalo.media.MediaDataTranslator];mime[default='image/png'];folder(qualifier)
;defaultPermissionIcon;jar:com.sast.cis.initialdata.setup.InitialDataSystemSetup&/cisinitialdata/import/common/permission/icon/defaultPermission.png;;;;

INSERT_UPDATE MediaFormat;qualifier[unique=true]
;1200Wx1200H
;515Wx515H
;365Wx246H
;300Wx300H
;96Wx96H
;65Wx65H
;30Wx30H

INSERT_UPDATE ConversionMediaFormat;qualifier[unique=true];conversion;mimeType[default=image/png];conversionStrategy[default=imageMagickMediaConversionStrategy]
;44Wx44H;-resize 44x44
;74Wx74H;-resize 74x74
;118Wx118H;-resize 118x118
;210Wx118H;-resize 210x118
;1280Wx720H;-resize 1280x720

INSERT_UPDATE ConversionGroup;code[unique=true];supportedFormats(qualifier)
;Icon-Conversion;44Wx44H,90Wx90H,118Wx118H
;Screenshot-Conversion;210Wx118H,1280Wx720H

INSERT_UPDATE PriceLimit;code[unique=true];upperLimit;currency(isocode)
;fallback;200000.0;

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]; active; content
                                      ; export-psp-account ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='exportPSPAccountAction'
    name='export-psp-account' processClass='com.sast.cis.onboarding.model.ExportPSPAccountProcessModel' onError='error'>

    <action id='exportPSPAccountAction' bean='exportPSPAccountAction'>
        <transition name='OK' to='waitForConfirmation'/>
        <transition name='NOK' to='failed'/>
    </action>

    <wait id='waitForConfirmation' then='error'>
        <case  event='company-export-response-event'>
                <choice id='success' then='confirmPSPAccountExported'/>
                <choice id='fail' then='error'/>
        </case>
    </wait>

   <action id='confirmPSPAccountExported' bean='confirmPSPAccountAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
    </action>


    <end id='error' state='ERROR'>An error occurred</end>
    <end id='failed' state='FAILED'>Failed to complete</end>
    <end id='success' state='SUCCEEDED'>Successfully completed</end>
</process>"


INSERT_UPDATE ReservedPackageName;packageNamePrefix[unique=true];company(uid);
;prefix.reserved.for.company.a.;integrationtestCompanyA
;another.prefix.reserved.for.company.a.;integrationtestCompanyA
;prefix.reserved.for.company.b.;integrationtestCompanyB
;prefix.blocked.for.all.companies.;


INSERT_UPDATE SupportedLicense; licenseType(code)[unique = true]; enabled[default = true]
                              ; EVALUATION                      ;
                              ; FULL                            ;
                              ; SUBSCRIPTION                    ;
                              ; TOOL                            ;

### Azena store ###
### HEADER ###
# GLOBAL
INSERT_UPDATE NavigationItem; uid[unique = true]; itemCode; index; url; target; icon; text[lang=en]; text[lang=de]; enabled[default=true]; group(code)[default=HEADER]; type(code)[default=GLOBAL]; store(uid)[default = 'iotstore'];
; globalMyProfile; globalMyProfile; 1; $config-usermanagementportal.myprofile.url      ; "_blank"; "user"   ; "My Profile"; "Mein Profil"
; globalMyCompany; globalMyCompany; 2; $config-usermanagementportal.mycompany.url      ; "_blank"; "company"; "My Company"; "Meine Firma"
; globalSupport  ; globalSupport  ; 5; "https://support.securityandsafetythings.com/hc"; "_blank"; "support"; "Support"   ; "Unterstützung"

#STORE
INSERT_UPDATE NavigationItem; uid[unique = true]; itemCode; index; url; target; icon; text[lang=en]; text[lang=de]; enabled[default=true]; group(code)[default=HEADER]; type(code)[default=STORE]; store(uid)[default = 'iotstore'];
; storeOrderHistory  ; storeOrderHistory  ; 3; "/shop/my-account/orders"         ; ""; "user"  ; "Order History"  ; "Bestellverlauf"
; storePaymentDetails; storePaymentDetails; 4; "/shop/my-account/payment-details"; ""; "user"  ; "Payment Details"; "Zahlungsdetails"
; storeSignOut       ; storeSignOut       ; 6; "/shop/logout"                    ; ""; "logout"; "Sign Out"       ; "Ausloggen"

#DEVCON
INSERT_UPDATE NavigationItem; uid[unique = true]; itemCode; index; url; target; icon; text[lang=en]; text[lang=de]; enabled[default=true]; group(code)[default=HEADER]; type(code)[default=DEVCON]; store(uid)[default = 'iotstore'];
; devconPayment; devconPayment; 1; "/my-account/payment-services"; ""; "payment"; "Payment"; "Zahlung"
; devconSignOut; devconSignOut; 6; "/logout"; ""; "logout"; "Sign Out"; "Ausloggen"


### FOOTER ###
# GLOBAL
INSERT_UPDATE NavigationItem; uid[unique = true]; itemCode; index; url; target; icon; text[lang=en]; text[lang=de]; enabled[default=true]; group(code)[default=FOOTER]; type(code)[default=GLOBAL]; store(uid)[default = 'iotstore'];
; globalLegal  ; globalLegal  ; 3; "https://www.securityandsafetythings.com/legal-notice"; "_blank" ; ""; "Legal"  ; "Rechtliche Grundlagen"
; globalImprint; globalImprint; 5; "https://www.securityandsafetythings.com/imprint"     ; "_blank" ; ""; "Imprint"; "Impressum"

# STORE
INSERT_UPDATE NavigationItem; uid[unique = true]; itemCode; index; url; target; icon; text[lang=en]; text[lang=de]; enabled[default=true]; group(code)[default=FOOTER]; type(code)[default=STORE]; store(uid)[default = 'iotstore'];
; storeTermsAndConditions; storeTermsAndConditions; 1; $config-usermanagementportal.legal.url/terms.html; "_blank" ; ""; "Terms and Conditions"; "AGB"
; storePrivacyPolicy     ; storePrivacyPolicy;      2; "https://www.securityandsafetythings.com/privacy-policy"     ; "_blank" ; ""; "Privacy Policy"      ; "Datenschutz"

# DEVCON
INSERT_UPDATE NavigationItem; uid[unique = true]; itemCode; index; url; target; icon; text[lang=en]; text[lang=de]; enabled[default=true]; group(code)[default=FOOTER]; type(code)[default=DEVCON]; store(uid)[default = 'iotstore'];
; devconStore             ; devconStore             ; 1; $config-website.iotstore.https                                 ; ""       ; ""; "Application Store"   ; "Application-Store"
; devconPrivacyPolicy     ; devconPrivacyPolicy     ; 2; "https://www.securityandsafetythings.com/privacy-policy"       ; "_blank" ; ""; "Privacy Policy"      ; "Datenschutz"
; devconTermsAndConditions; devconTermsAndConditions; 4; "https://accounts.securityandsafetythings.com/legal/terms.html"; "_blank" ; ""; "Terms and Conditions"; "AGB"

INSERT_UPDATE Country;isocode[unique=true];name[lang=en]
;KR;Republic of Korea
;VI;The Virgin Islands of the United States
;VG;The Virgin Islands
;KP;The Democratic People's Republic of Korea
;FM;The Federated States of Micronesia
;CD;The Democratic Republic of the Congo

INSERT_UPDATE Industry;name[lang=en, unique=true];index[default=0]
;Airports
;Borders
;Commercial Building
;Congress/Exhibition
;Critical Infrastructure
;Data centers
;Harbors
;Healthcare
;Hospitals
;Intelligent traffic systems
;Manufacturing plants
;Marine transportation
;Metro
;Mining
;Museums
;Museums
;Railway
;Retail
;Smart city
;Stadiums
;Telecom networks
;Traffic monitoring
;Tunnels
;Warehouses
;Other;1

INSERT_UPDATE Usecase;name[lang=en, unique=true];index[default=0]
;Anomaly detection
;Camera utilities
;Cloud connector
;Container identification
;Crossline detection
;Crowd management
;Demographic estimation
;Drone threat
;Dynamic masking
;Emotion estimation
;Face recognition
;Fall detection
;Gun / active shooter detection
;Health & safety
;Heat mapping
;Indoor detection
;Intrusion detection
;License plate recognition
;Logistics
;Loitering detection
;Motion detection
;Object recognition
;On camera VMS
;Parking management
;People counting
;Perimeter protection
;Person re-identification
;Physical advertising optimization
;Queue management
;Retail store management
;Sabotage detection
;Smoke detection
;Traffic analysis
;Traffic incident detection
;Vehicle identification
;Zone masking
;Other;1

INSERT_UPDATE FeatureToggle;code[unique=true];enabled
;FEATURE_ALLOW_SELF_PURCHASE;true;
;FEATURE_STORE_MAINTENANCE_BANNER;false;
;FEATURE_PAYMENT_UPDATES;false;
;FEATURE_ENFORCE_EULA_ACCEPTANCE;true;

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]; active; content
                                      ; billing-order-process; true; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='setOrderStatusCreated' name='billing-order-process'
        processClass='de.hybris.platform.orderprocessing.model.OrderProcessModel' onError='error'>

    <action id='setOrderStatusCreated' bean='orderStatusCreatedAction'>
        <transition name='OK' to='orderRouterByLicenseCategory'/>
    </action>

    <action id='orderRouterByLicenseCategory' bean='improvedOrderRouterAction'>
        <transition name='PAID' to='exportOrder'/>
        <transition name='PAID_FREE' to='publishFreeOrderToSqs'/>
        <transition name='TRIAL' to='publishTrialOrderToSqs'/>
        <transition name='MULTI' to='error'/>
        <transition name='NO_LICENSE' to='error'/>
    </action>

    <action id='exportOrder' bean='brimExportOrderAction'>
        <transition name='OK' to='publishOrderToSqs'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='publishOrderToSqs' bean='sqsMessageAction'>
        <transition name='OK' to='sendOrderCompletedNotification'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendOrderCompletedNotification' bean='orderSuccessMessageAction'>
        <transition name='OK' to='waitForOrderCompleteEvent'/>
        <transition name='NOK' to='error'/>
	</action>

    <wait id='waitForOrderCompleteEvent' then='error'>
        <case event='order-response-event'>
                <choice id='success' then='setOrderStatusCompleted'/>
                <choice id='fail' then='error'/>
        </case>
    </wait>

    <action id='setOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='waitForInvoiceEvent'/>
    </action>

	<wait id='waitForInvoiceEvent' then='error'>
	    <case event='invoice-received-event'>
	        <choice id='success' then='success'/>
	        <choice id='fail' then='error'/>
        </case>
	</wait>

	 <action id='publishTrialOrderToSqs' bean='sqsMessageAction'>
        <transition name='OK' to='setTrialOrderStatusCompleted'/>
        <transition name='NOK' to='error'/>
    </action>

     <action id='setTrialOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='sendTrialOrderCompletedNotification'/>
    </action>

    <action id='sendTrialOrderCompletedNotification' bean='orderSuccessMessageAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
	</action>

    <action id='publishFreeOrderToSqs' bean='sqsMessageAction'>
        <transition name='OK' to='setFreeOrderStatusCompleted'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setFreeOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='sendFreeOrderCompletedNotification'/>
    </action>

    <action id='sendFreeOrderCompletedNotification' bean='orderSuccessMessageAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
    </action>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Order not placed.</end>
    <end id='success' state='SUCCEEDED'>Order Fulfilled.</end>
</process>"

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]          ; active; content
                                      ; billing-app-to-store-process ; true  ; "

<process xmlns='http://www.hybris.de/xsd/processdefinition' start='releaseNonCommercialLicense'
    name='billing-app-to-store-process' processClass='com.sast.cis.approval.model.AppProcessModel' onError='error'>

    <action id='releaseNonCommercialLicense' bean='releaseNonCommercialLicenseAction'>
        <transition name='OK' to='exportLicenseToBillingBackend'/>
        <transition name='NOK' to='failed'/>
    </action>

    <action id='exportLicenseToBillingBackend' bean='exportBillingBackendAction'>
        <transition name='NOK' to='failed'/>
        <transition name='OK' to='prepareSync'/>
    </action>

    <action id='prepareSync' bean='prepareSyncAction'>
        <transition name='OK' to='performSync'/>
    </action>

    <action id='performSync' bean='performSyncAction'>
        <transition name='OK' to='sendEmail'/>
    </action>

    <action id='sendEmail' bean='sendAppReleaseEmailAction'>
        <transition name='OK' to='success'/>
    </action>

    <end id='error' state='ERROR'>An error occurred</end>
    <end id='failed' state='FAILED'>Failed to complete</end>
    <end id='success' state='SUCCEEDED'>Successfully completed</end>
</process>"

INSERT_UPDATE DynamicProcessDefinition;code[unique=true];active[unique=true];content
                                               ;billing-applicense-to-store-process;true;"
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='exportProductToBillingBackend'
    name='billing-applicense-to-store-process' processClass='com.sast.cis.approval.model.AppLicenseProcessModel' onError='error'>
    <action id='exportProductToBillingBackend' bean='exportBillingProductAction'>
        <transition name='OK' to='prepareSync'/>
        <transition name='NOK' to='failed'/>
    </action>

    <action id='prepareSync' bean='prepareAppAndAppLicenseSyncAction'>
        <transition name='OK' to='performSync'/>
        <transition name='NOK' to='failed'/>
    </action>

    <action id='performSync' bean='performAppAndAppLicenseSyncAction'>
        <transition name='OK' to='success' />
    </action>

    <end id='error' state='ERROR'>An error occurred</end>
    <end id='failed' state='FAILED'>Failed to complete</end>
    <end id='success' state='SUCCEEDED'>Successfully completed</end>
</process>"

INSERT_UPDATE StandardAppIntegration;code[unique=true];name[unique=true];description;displayName;integrationType(code);externalDescription;order
;MESSAGE_BROKER;Azena MessageBroker and/or DataTrolley (enables Azena IoT Gateway); Select this option if your app uses one of these APIs to send/receive metadata and/or events.;Azena IoT Gateway;GATEWAY;This app uses our IoT gateway and can share data via MQTT and REST.;1;
;ONVIF;ONVIF Metadata;Select this option if your app explicitly translates metadata to an ONVIF XML schema.;ONVIF metadata;STANDARD;This app sends metadata in an ONVIF XML schema.;2;

INSERT_UPDATE ConversionMediaFormat;qualifier[unique=true];conversion;mimeType[default=image/jpeg];conversionStrategy[default=imageMagickMediaConversionStrategy]
;90Wx90H;-resize 90x90
;120Wx120H;-resize 120x120
;800Wx800H;-resize 800x800

INSERT_UPDATE ConversionGroup;code[unique=true];supportedFormats(qualifier)
;CompanyProfile-Logo-Conversion;90Wx90H,120Wx120H,800Wx800H

INSERT_UPDATE CompanySize;code[unique=true];value[lang=en];value[lang=de];order
;NANO;1 employee;1 mitarbeiter;1
;MICRO;2-10 employees;2-10 mitarbeiter;2
;MINI;11-50 employees;11-50 mitarbeiter;3
;SMALL;51-200 employees;51-200 mitarbeiter;4
;MEDIUM;201-500 employees;201-500 mitarbeiter;5
;BIG;501-1000 employees;501-1000 mitarbeiter;6
;LARGE;1000+ employees;1000+ mitarbeiter;7

INSERT_UPDATE CronjobConfiguration;code[unique=true];baseStore(uid);productCatalogVersion(catalog(id),version)
;cronConfigForIotStore;iotstore;cisProductCatalog:Staged

INSERT_UPDATE ServicelayerJob;code[unique = true];springId
;cleanUpLogsJobPerformable;cleanUpLogsJobPerformable
INSERT_UPDATE CronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);active;nodeGroup
;jobLogsLogFilesCleanupCronJob;cleanUpLogsJobPerformable;anonymous;en;true;ncf

##### AA CUSTOM DATA #####

$languages=en,de
$contentCatalog=aaContentCatalog
$productCatalog=aaProductCatalog
$storeUid=aastore
$siteUid=aastore
$classificationCatalog=aaClassificationCatalog
$syncContentCatalogJob=sync $contentCatalog:Staged->Online
$syncProductCatalogJob=sync $productCatalog:Staged->Online
$sourceContentCV=sourceVersion(catalog(id[default=$contentCatalog]),version[default='Staged'])[unique=true,default='$contentCatalog:Staged']
$targetContentCV=targetVersion(catalog(id[default=$contentCatalog]),version[default='Online'])[unique=true,default='$contentCatalog:Online']
$sourceProductCV=sourceVersion(catalog(id[default=$productCatalog]),version[default='Staged'])[unique=true,default='$productCatalog:Staged']
$targetProductCV=targetVersion(catalog(id[default=$productCatalog]),version[default='Online'])[unique=true,default='$productCatalog:Online']
$productCatalogStagedVersion=catalogversion(catalog(id[default=$productCatalog]),version[default='Staged'])[unique=true,default=$productCatalog:Staged]
$contentCatalogStagedVersion=catalogVersion(catalog(id[default=$contentCatalog]),version[default='Staged'])[default=$contentCatalog:Staged]
$supercategories=source(code, $productCatalogStagedVersion)[unique=true]
$categories=target(code, $productCatalogStagedVersion)[unique=true]
$siteResource=jar:com.sast.cis.initialdata.setup.InitialDataSystemSetup&/cisinitialdata/import/sampledata/productCatalogs/$productCatalog

INSERT_UPDATE ContentCatalog;id[unique=true];name[lang=en];name[lang=de]
;$contentCatalog;Automotive Aftermarket Content Catalog;Automotive Aftermarket Content Catalog

INSERT_UPDATE CatalogVersion;catalog(id)[unique=true];version[unique=true];active;languages(isoCode)
;$contentCatalog;Staged;false;$languages
;$contentCatalog;Online;true;$languages

INSERT_UPDATE Catalog;id[unique=true];name[lang=en];name[lang=de]
;$productCatalog;Automotive Aftermarket Product catalog;Automotive Aftermarket Product catalog;

INSERT_UPDATE ClassificationSystem;id[unique=true]
;$classificationCatalog

INSERT_UPDATE ClassificationSystemVersion;catalog(id)[unique=true];version[unique=true];active;inclPacking[virtual=true,default=true];inclDuty[virtual=true,default=true];inclFreight[virtual=true,default=true];inclAssurance[virtual=true,default=true]
;$classificationCatalog;1.0;true

INSERT_UPDATE CatalogVersion;catalog(id)[unique=true];version[unique=true];active;languages(isoCode);readPrincipals(uid);writePrincipals(uid)
;$productCatalog;Staged;false;$languages;employeegroup,syncusers;syncusers
;$productCatalog;Online;true;$languages;employeegroup,syncusers;syncusers

INSERT_UPDATE CatalogVersionSyncJob;code[unique=true];$sourceContentCV;$targetContentCV;createNewItems;removeMissingItems;rootTypes(code)[mode=append];syncPrincipals(uid)[mode=append]; syncPrincipalsOnly[default=false];
;$syncContentCatalogJob;;;true;false;CMSItem,CMSRelation;syncusers;true;

INSERT_UPDATE SyncAttributeDescriptorConfig;syncJob(code)[default=synjobName][unique=true][path-delimiter=!];attributeDescriptor(enclosingType(code),qualifier)[unique=true];includedInSync;copyByValue[default=false];presetValue;translateValue[default=false];untranslatable[default=false]
;$syncContentCatalogJob;AbstractPage:originalPage;true
;$syncContentCatalogJob;AbstractPage:localizedPages;true
;$syncContentCatalogJob;AbstractCMSComponent:slots;true
;$syncContentCatalogJob;AbstractRestriction:pages;true

INSERT_UPDATE CatalogVersionSyncJob;code[unique=true];$sourceProductCV;$targetProductCV;createNewItems;removeMissingItems;rootTypes(code)[mode=append];syncPrincipals(uid)[mode=append]; syncPrincipalsOnly[default=false];sessionUser(uid)
;$syncProductCatalogJob;;;true;false;;syncusers;true;syncuser

INSERT_UPDATE SyncAttributeDescriptorConfig;syncJob(code)[unique=true][path-delimiter=!];attributeDescriptor[unique=true](enclosingType(code),qualifier);includedInSync;copyByValue
;$syncProductCatalogJob;Category:products;false;false
;$syncProductCatalogJob;Product:productReviews;false;false
;$syncProductCatalogJob;Product:variants;false;false
;$syncProductCatalogJob;App:versions;false;false
;$syncProductCatalogJob;App:latestVersion;false;false
;$syncProductCatalogJob;App:owningCompanies;false;false
;$syncProductCatalogJob;PdfMedia:app;false;false

INSERT_UPDATE Category;code[unique=true];allowedPrincipals(uid)[default='customergroup'];$productCatalogStagedVersion
;main
;cat_00001
;cat_00002

INSERT_UPDATE CategoryCategoryRelation;$categories;$supercategories
;cat_00001;main
;cat_00002;main

INSERT_UPDATE Media;code[unique=true];realfilename;@media[translator=de.hybris.platform.impex.jalo.media.MediaDataTranslator];mime[default='image/jpeg'];$catalogVersion

UPDATE Category;code[unique=true];thumbnail(code, $productCatalogStagedVersion);picture(code, $productCatalogStagedVersion);allowedPrincipals(uid)[default='customergroup'];$catalogVersion

UPDATE Category;code[unique=true];$productCatalogStagedVersion;name[lang=en];name[lang=de]
;main;;catalog;Katalog;
;cat_00001;;Developer Apps;Entwickler Apps;
;cat_00002;;Developer Apps;Entwickler Apps;

INSERT_UPDATE BaseStore;uid[unique=true];catalogs(id);currencies(isocode);net;storelocatorDistanceUnit(code);defaultCurrency(isocode);languages(isocode);defaultLanguage(isocode);deliveryCountries(isocode);customerAllowedToIgnoreSuggestions;
;$storeUid;$productCatalog,$classificationCatalog;EUR;true;km;EUR;$languages;en;$deliveryCountries;true;

INSERT_UPDATE Zone;code[unique=true];countries(isocode)
;asianCountries;$asianCountries

UPDATE GenericItem[processor=de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor];pk[unique=true]
$jarResource=$config-jarResource
$storefrontContextRoot=$config-storefrontContextRoot

INSERT_UPDATE CMSSite;uid[unique=true];theme(code);channel(code);stores(uid);contentCatalogs(id);defaultCatalog(id);defaultLanguage(isoCode);urlPatterns;active;previewURL;startingPage(uid,$contentCatalogStagedVersion);urlEncodingAttributes;
;$siteUid;alpha;B2C;$storeUid;$contentCatalog;$productCatalog;en;(?i)^https?://(aa.store.)[^/]+(|/shop.*);true;$storefrontContextRoot/?site=$siteUid;;

UPDATE CMSSite;uid[unique=true];name[lang=en];locale[lang=en]
;$siteUid;Automotive Aftermarket Store;

UPDATE CMSSite;uid[unique=true];name[lang=de];locale[lang=de]
;$siteUid;Automotive Aftermarket Store;

INSERT_UPDATE CronjobConfiguration;code[unique=true];baseStore(uid);productCatalogVersion(catalog(id),version)
;cronConfigForAaStore;aastore;$productCatalog:Staged

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]; active[unique=true]; version[unique=true];content
; aa-order-process; true; 0  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='checkNotMigrationOrder' name='aa-order-process'
        processClass='de.hybris.platform.orderprocessing.model.OrderProcessModel' onError='error'>

    <action id='checkNotMigrationOrder' bean='checkNotMigrationOrderAction'>
        <transition name='OK' to='setOrderStatusCreated'/>
        <transition name='NOK' to='failed'/>
    </action>

    <action id='setOrderStatusCreated' bean='orderStatusCreatedAction'>
        <transition name='OK' to='checkCompanyStatus'/>
    </action>

    <action id='checkCompanyStatus' bean='companyStatusAction'>
        <transition name='OK' to='orderRouterByLicenseType'/>
        <transition name='NOK' to='waitForCompanyApprovalEvent'/>
    </action>

    <wait id='waitForCompanyApprovalEvent' then='error'>
        <case event='company-approved-event'>
            <choice id='success' then='orderRouterByLicenseType'/>
            <choice id='fail' then='setOrderStatusCancelled'/>
        </case>
    </wait>

    <action id='orderRouterByLicenseType' bean='orderRouterAction'>
        <transition name='PAID' to='checkOrderActivationMode'/>
        <transition name='TRIAL' to='error'/>
        <transition name='MULTI' to='error'/>
        <transition name='NO_LICENSE' to='error'/>
    </action>

    <action id='checkOrderActivationMode' bean='checkOrderActivationModeAction'>
        <transition name='OK' to='sendOrderConfirmationMail'/>
        <transition name='NOK' to='delayOrderStatus'/>
    </action>

    <action id='sendOrderConfirmationMail' bean='immediateOrderConfirmationMailAction'>
        <transition name='OK' to='exportOrder'/>
        <transition name='NOK' to='error'/>
	</action>

    <action id='delayOrderStatus' bean='delayedOrderStatusAction'>
        <transition name='OK' to='publishOrderToDmp'/>
    </action>

    <action id='publishOrderToDmp' bean='aaSqsMessageAction'>
        <transition name='OK' to='sendOrderConfirmationMailForDelayedOrder'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendOrderConfirmationMailForDelayedOrder' bean='delayedOrderConfirmationMailAction'>
        <transition name='OK' to='waitForActivationEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForActivationEvent' then='error'>
        <case event='activate-license-event'>
            <choice id='success' then='setStartDateOfContracts'/>
            <choice id='cancel' then='setOrderStatusCancelled'/>
            <choice id='fail' then='error'/>
        </case>
    </wait>

    <action id='setOrderStatusCancelled' bean='orderStatusCancelledAction'>
        <transition name='OK' to='cancel'/>
    </action>

    <action id='setStartDateOfContracts' bean='startContractAction'>
        <transition name='OK' to='exportOrder'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='exportOrder' bean='brimExportOrderAction'>
        <transition name='OK' to='waitForOrderCompleteEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForOrderCompleteEvent' then='error'>
        <case event='order-response-event'>
                <choice id='success' then='setOrderStatusCompleted'/>
                <choice id='fail' then='error'/>
        </case>
    </wait>

    <action id='setOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='publishOrderToSqs'/>
    </action>

    <action id='publishOrderToSqs' bean='aaSqsMessageAction'>
        <transition name='OK' to='sendInternalOrderNotifications'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendInternalOrderNotifications' bean='aaInternalOrderNotificationAction'>
        <transition name='OK' to='waitForInvoiceEvent'/>
        <transition name='NOK' to='error'/>
	</action>

	<wait id='waitForInvoiceEvent' then='error'>
	    <case event='invoice-received-event'>
	        <choice id='success' then='success'/>
	        <choice id='fail' then='error'/>
        </case>
	</wait>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Order not placed.</end>
    <end id='cancel' state='FAILED'>Order Canceled.</end>
    <end id='success' state='SUCCEEDED'>Order Fulfilled.</end>
</process>"

UPDATE BaseStore;uid[unique=true];submitOrderProcessCode;
;$storeUid;aa-order-process

INSERT_UPDATE Country;isocode[unique=true];name[lang=de];name[lang=en];active[default=false];enabledForDevelopers[default=false];enabledForIntegrators[default=false];currency(isocode);supportedPaymentProviders(code)[mode=append];supportedPaymentMethods(code)[mode=append];
;AT;Österreich;Austria;true;true;true;EUR;DPG,BOSCH_TRANSFER,PGW;CREDIT_CARD,SEPA_CREDIT,SEPA_DIRECTDEBIT;


UPDATE GenericItem[processor=de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor];pk[unique=true]
$aaAustriaBuyer1Integrator=$config-aaAustriaBuyer1IntegratorCompanyId
$aaAustriaSeller1Developer=$config-aaAustriaSeller1DeveloperCompanyId
$aaAustriaMigratedBuyer1Integrator=$config-austriaMigratedBuyer1


INSERT_UPDATE UserPriceGroup; code[unique = true]
                            ; aastore_IDW000 ;
                            ; aastore_WD0000 ;
                            ; aastore_WD0001 ;
                            ; aastore_WD0002 ;
                            ; aastore_WD0006 ;
                            ; aastore_BCS000 ;
                            ; aastore_BDS000 ;
                            ; aastore_BDC000 ;
                            ; aastore_BM0000 ;
                            ; aastore_AC0000 ;
                            ; aastore_AT0000 ;
                            ; aastore_KA0001 ;
                            ; aastore_KA0002 ;
                            ; aastore_KA0003 ;
                            ; aastore_KA0008 ;
                            ; aastore_KA0009 ;
                            ; aastore_KA0010 ;
                            ; aastore_AUT000 ;
                            ; aastore_ISP000 ;

INSERT_UPDATE UserGroup; uid[unique = true]; userPriceGroup(code)
                       ; IDW000            ; aastore_IDW000
                       ; WD0000            ; aastore_WD0000
                       ; WD0001            ; aastore_WD0001
                       ; WD0002            ; aastore_WD0002
                       ; WD0006            ; aastore_WD0006
                       ; BCS000            ; aastore_BCS000
                       ; BDS000            ; aastore_BDS000
                       ; BDC000            ; aastore_BDC000
                       ; BM0000            ; aastore_BM0000
                       ; AC0000            ; aastore_AC0000
                       ; AT0000            ; aastore_AT0000
                       ; KA0001            ; aastore_KA0001
                       ; KA0002            ; aastore_KA0002
                       ; KA0003            ; aastore_KA0003
                       ; KA0008            ; aastore_KA0008
                       ; KA0009            ; aastore_KA0009
                       ; KA0010            ; aastore_KA0010
                       ; AUT000            ; aastore_AUT000
                       ; ISP000            ; aastore_ISP000


INSERT_UPDATE IoTCompany;uid[unique=true];name;country(isocode);&compRef;manualAppApprovalEnabled[default=true];store(uid)[default=iotstore];bpmdId;operationalStage(code);aaExternalCustomerId;
;$aaAustriaSeller1Developer;Austria Seller 1;AT;aaSeller1;;aastore;atseller1_bpmdid;OPERATIONAL;25011110;
;$aaAustriaBuyer1Integrator;Austria Buyer 1;AT;aaBuyer1;;aastore;atbuyer1_bpmdid;OPERATIONAL;35011117;
;$aaAustriaMigratedBuyer1Integrator;Austria Migrated Buyer 1;AT;aaMigratedBuyer1;;aastore;atmigradedbuyer1_bpmdid;OPERATIONAL;35021519;


UPDATE IotCompany;uid[unique=true];aaCustomerGroup(uid);groups(uid)[mode=merge];
;$aaAustriaBuyer1Integrator;IDW000;IDW000;
;$aaAustriaMigratedBuyer1Integrator;IDW000;IDW000;

UPDATE IotCompany;uid[unique=true]  ;aaImported[default=true]
;$aaAustriaMigratedBuyer1Integrator ;

INSERT_UPDATE Developer;originalUid[unique=true];uid[unique=true];title(code);name;description;sessionLanguage(isocode);sessionCurrency(isocode);password;groups(uid[default=$integratorGroup]);company(uid)
;********-5a60-481b-bef6-c8ee90e8fe7f@devcon;********-5a60-481b-bef6-c8ee90e8fe7f@devcon;;AustriaSeller 1 Dev;;de;EUR;1234;developergroup,$aaAustriaSeller1Developer;$aaAustriaSeller1Developer
;197575f5-ec56-40af-84dc-c07d95899c52@devcon;197575f5-ec56-40af-84dc-c07d95899c52@devcon;;AustriaBuyer 1 Dev;;de;EUR;1234;developergroup,$aaAustriaBuyer1Integrator;$aaAustriaBuyer1Integrator
;80b76b91-d8e2-49b6-a283-4945c2624f2b@devcon;80b76b91-d8e2-49b6-a283-4945c2624f2b@devcon;;AustriaMigratedBuyer 1 Dev;;de;EUR;1234;developergroup,$aaAustriaMigratedBuyer1Integrator;$aaAustriaMigratedBuyer1Integrator

INSERT_UPDATE Integrator;originalUid[unique=true];uid[unique=true];developer(uid);title(code);name;description;sessionLanguage(isocode);sessionCurrency(isocode);company(uid);password;groups(uid[default=$integratorGroup]);emailAddress
;197575f5-ec56-40af-84dc-c07d95899c52@shop;197575f5-ec56-40af-84dc-c07d95899c52@shop;197575f5-ec56-40af-84dc-c07d95899c52@devcon;;AustriaBuyer1;;de;EUR;$aaAustriaBuyer1Integrator;1234;integratorgroup,$aaAustriaBuyer1Integrator;<EMAIL>
;80b76b91-d8e2-49b6-a283-4945c2624f2b@shop;80b76b91-d8e2-49b6-a283-4945c2624f2b@shop;197575f5-ec56-40af-84dc-c07d95899c52@devcon;;AustriaMigratedBuyer1;;de;EUR;$aaAustriaMigratedBuyer1Integrator;1234;integratorgroup,$aaAustriaMigratedBuyer1Integrator;<EMAIL>

INSERT_UPDATE PgwSellerAccount; accountId[unique = true]           ; company(uid)                ; user(uid)                                   ; paymentProvider(code) ; billingSystemStatus(code) ; status(code) ;
                              ; "aaAustriaSeller1PgwSellerAccount" ; $aaAustriaSeller1Developer  ; ********-5a60-481b-bef6-c8ee90e8fe7f@devcon ; PGW                   ; IN_SYNC                   ; ACTIVE       ;


INSERT_UPDATE BoschSepaCollectionAccount; code[ unique = true]                          ; bankName                     ; bic           ; iban                     ;
                                        ; aaAustriaSeller1BoschSepaCollectionAccount1   ; "DEUTSCHE KREDITBANK BERLIN" ; BYLADEM1001   ; **********************   ;

INSERT_UPDATE BoschSellerAccount; accountId[unique = true]                      ; company(uid)                ; user(uid)                                   ; paymentProvider(code) ; billingSystemStatus(code) ; status(code) ; sepaCollectionAccount(code)                 ;
                                ; "completedAaAustriaSeller1BoschSellerAccount" ; $aaAustriaSeller1Developer  ; ********-5a60-481b-bef6-c8ee90e8fe7f@devcon ; BOSCH_TRANSFER        ; IN_SYNC                   ; ACTIVE       ; aaAustriaSeller1BoschSepaCollectionAccount1 ;


INSERT BlockedApkSignerCertSubjectCName; cnValue[unique = true];
                                       ; "Android Debug"       ;

INSERT_UPDATE AaDistributorCompany;umpId[unique=true];companyName;countries(isocode);aaExternalId;
;12f25634-4bdf-42c9-8cbd-e97c9b26cce7;Test Distributor;AT;********;

$austriaDistributor1_umpId = 69aaafc9-04a7-4f66-83c9-016a5e2a8994
$austriaDistributor2_umpId = 12f25634-4bdf-42c9-8cbd-e97c9b26cce7
$germanyDistributor1_umpId = 258c49b7-67d0-4d67-a4cf-985bf3623f07

INSERT_UPDATE AaDistributorCompany; umpId[unique = true]       ; companyName           ; countries(isocode); aaExternalId;
                                  ; $austriaDistributor1_umpId ; Austria Distributor 1 ; AT                ; ********    ;
                                  ; $austriaDistributor2_umpId ; Austria Distributor 2 ; AT                ; ********    ;
                                  ; $germanyDistributor1_umpId ; Germany Distributor 1 ; DE                ; ********    ;

INSERT_UPDATE TerminationRulePeriod;code[unique=true];value;unit(code);
;1-year;1;YEAR;
;30-days;30;DAY;
;8-weeks;56;DAY;
;0-days;0;DAY;
;3-years;3;YEAR;
;1-day;1;DAY;

INSERT_UPDATE ContractTerminationRule;code[unique=true];noticePeriod(code);initialPeriod(code);followUpPeriod(code);gracePeriod(code);fixedPeriod(code);
;yearly-with-30d-notice;30-days;1-year;1-year;30-days;;
;yearly-with-56d-notice;8-weeks;1-year;1-year;30-days;;
;fixed-term-3y;;;;;3-years;
;fixed-term-1d;;;;;1-day;

# Default termination rules for base stores
UPDATE BaseStore;uid[unique=true];defaultTerminationRule(code);
;aastore;yearly-with-56d-notice;
;iotstore;yearly-with-30d-notice;

INSERT_UPDATE Runtime;code[unique=true];name[lang=en];name[lang=de];defaultRuntimeTerminationRule(code);description;
;runtime_subs_unlimited;Subscription;Abonnement;yearly-with-56d-notice;Unlimited runtime. Annually renewable.;
;runtime_full_3y;3-year contract;Einmalkauf (3 Jahre);fixed-term-3y;One time purchase with validity of 3 years.;
;runtime_full_unlimited;One time purchase;Einmalkauf;fixed-term-1d;One time purchase with unlimited validity.;

INSERT_UPDATE Runtime;code[unique=true];brimVariantKey;brimInvoicingCycle;
;runtime_full_3y;VARIANT_3Y;3YEAR;

INSERT_UPDATE ContentModule;code[unique=true];name;containerType;
;CM_040A;A;ESI;
;CM_040CoRe;CoRe;CORE;

INSERT_UPDATE RuntimeContentModule;contentModule(ContentModule.code)[unique=true];contentModuleIds;runtime(Runtime.code)[unique=true];
;CM_040A;1987P12410999;runtime_subs_unlimited
;CM_040A;1987P12418999;runtime_full_3y
;CM_040CoRe;1687P15076999,1687P15083999;runtime_subs_unlimited
;CM_040CoRe;1687P15078999,1687P15085999;runtime_full_3y


INSERT_UPDATE ServicelayerJob;code[unique=true];springId
                             ;aaSubscriptionEmailJob;aaSubscriptionReportJobPerformable

INSERT_UPDATE CronJob;code[unique=true];job(code);sessionUser(uid);sessionLanguage(isocode);filesCount;filesDaysOld;filesOperator(code);logToFile;nodeGroup
                     ;aaSubscriptionEmailCronJob;aaSubscriptionEmailJob;admin;en;60;60;AND;TRUE;ncf

INSERT_UPDATE BundleInfo; code[unique = true]; name; size
;BI_S_1;S;1
;BI_M_3;M;3


INSERT_UPDATE BrimProductExportParameterValue;baseStore(uid)[unique=true];country(isocode)[unique=true];parameter(code)[default=CATEGORY_ID];parameterValue
;aastore;AT;;SAST_AA_ESI_AT
;aastore;PT;;SAST_AA_ESI_PT
;aastore;HR;;SAST_AA_ESI_HR
;aastore;SI;;SAST_AA_ESI_HR


INSERT_UPDATE DynamicProcessDefinition; code[unique = true]                 ; active; content
                                      ; billing-group-prices-export-process ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='exportGroupPricesToBillingBackend'
    name='billing-group-prices-export-process' processClass='com.sast.cis.aa.core.model.GroupPriceUpdateProcessModel' onError='error'>
    <action id='exportGroupPricesToBillingBackend' bean='exportGroupPricesAction'>
        <transition name='NOK' to='failed'/>
        <transition name='OK' to='success'/>
    </action>

    <end id='error' state='ERROR'>An error occurred</end>
    <end id='failed' state='FAILED'>Failed to complete</end>
    <end id='success' state='SUCCEEDED'>Successfully completed</end>
</process>"

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]                ; active; content
                                      ; billing-list-prices-export-process ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='exportListPricesToBillingBackend'
    name='billing-list-prices-export-process' processClass='com.sast.cis.aa.core.model.ListPriceUpdateProcessModel' onError='error'>
    <action id='exportListPricesToBillingBackend' bean='exportListPricesAction'>
        <transition name='NOK' to='failed'/>
        <transition name='OK' to='success'/>
    </action>

    <end id='error' state='ERROR'>An error occurred</end>
    <end id='failed' state='FAILED'>Failed to complete</end>
    <end id='success' state='SUCCEEDED'>Successfully completed</end>
</process>"

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]        ; active; content
                                      ; aa-order-migration-process ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='validateInactivityStatus' name='aa-order-migration-process'
        processClass='com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel' onError='error'>

    <action id='validateInactivityStatus' bean='validateInactivityStatusAction'>
        <transition name='OK' to='validateOrderDraftStatus'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='validateOrderDraftStatus' bean='validateOrderDraftStatusAction'>
        <transition name='OK' to='setOrderDraftStatusToInProgress'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderDraftStatusToInProgress' bean='setOrderDraftStatusToInProgressAction'>
        <transition name='OK' to='sendValidationRequestToLmp'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendValidationRequestToLmp' bean='sendValidationRequestToLmpAction'>
        <transition name='OK' to='waitForLmpValidationResponse'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForLmpValidationResponse' then='error'>
        <case event='lmp-validation-response-event'>
                <choice id='success' then='createOrderFromMigrationOrderDraft'/>
                <choice id='fail' then='notifyCmtAboutLmpValidationFailure'/>
        </case>
    </wait>

    <action id='notifyCmtAboutLmpValidationFailure' bean='notifyCmtAboutLmpValidationFailureAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='createOrderFromMigrationOrderDraft' bean='createOrderFromMigrationOrderDraftAction'>
        <transition name='OK' to='routeByBillingTypeAfterOrderCreation'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='routeByBillingTypeAfterOrderCreation' bean='migrationOrderBillingRoutingAction'>
        <transition name='BILLABLE' to='assignPaymentDataToOrder'/>
        <transition name='NON_BILLABLE' to='exportOrderToLmp'/>
        <transition name='ERROR' to='error'/>
    </action>

    <action id='assignPaymentDataToOrder' bean='assignPaymentDataToOrderAction'>
        <transition name='OK' to='exportOrderToBrim'/>
        <transition name='NOK' to='notifyCmtAboutUnavailablePaymentData'/>
    </action>

    <action id='notifyCmtAboutUnavailablePaymentData' bean='notifyCmtAboutUnavailablePaymentDataAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='exportOrderToBrim' bean='brimExportMigrationOrderAction'>
        <transition name='OK' to='waitForBrimOrderExportResponseEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForBrimOrderExportResponseEvent' then='error'>
        <case event='order-response-event'>
                <choice id='success' then='exportOrderToLmp'/>
                <choice id='fail' then='notifyCmtAboutBrimExportFailure'/>
        </case>
    </wait>

    <action id='notifyCmtAboutBrimExportFailure' bean='notifyCmtAboutBrimExportFailureAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='exportOrderToLmp' bean='exportOrderToLmpAction'>
        <transition name='OK' to='waitForLmpOrderExportResponseEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForLmpOrderExportResponseEvent' then='error'>
        <case event='lmp-order-response-event'>
                <choice id='success' then='setOrderDraftStatusCompleted'/>
                <choice id='fail' then='routeByBillingTypeAfterLmpExportFailure'/>
        </case>
    </wait>

    <action id='routeByBillingTypeAfterLmpExportFailure' bean='migrationOrderBillingRoutingAction'>
        <transition name='BILLABLE' to='cancelOrderInBrim'/>
        <transition name='NON_BILLABLE' to='notifyCmtAboutLmpExportFailure'/>
        <transition name='ERROR' to='error'/>
    </action>

    <action id='cancelOrderInBrim' bean='cancelOrderInBrimAction'>
        <transition name='OK' to='notifyCmtAboutLmpExportFailure'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='notifyCmtAboutLmpExportFailure' bean='notifyCmtAboutLmpExportFailureAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderDraftStatusCompleted' bean='migrationOrderDraftCompletedAction'>
        <transition name='OK' to='routeByBillingTypeAfterDraftCompletion'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='routeByBillingTypeAfterDraftCompletion' bean='migrationOrderBillingRoutingAction'>
        <transition name='BILLABLE' to='notifyCmtOnSuccessfulMigration'/>
        <transition name='NON_BILLABLE' to='setOrderStatusToCompletedForFull'/>
        <transition name='ERROR' to='error'/>
    </action>

    <action id='setOrderStatusToCompletedForFull' bean='migrationOrderCompletedAction'>
        <transition name='OK' to='notifyCmtOnSuccessfulMigration'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='notifyCmtOnSuccessfulMigration' bean='notifyCmtOnSuccessfulMigrationAction'>
        <transition name='OK' to='routeByOrderStatusAfterSuccessNotification'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='routeByOrderStatusAfterSuccessNotification' bean='migrationOrderStatusRoutingAction'>
        <transition name='OPEN' to='waitForBrimOpenOrderFinalizationEvent'/>
        <transition name='COMPLETED' to='success'/>
        <transition name='REJECTED' to='failed'/>
        <transition name='OTHER' to='error'/>
    </action>

    <wait id='waitForBrimOpenOrderFinalizationEvent' then='error'>
        <case event='open-order-finalized-event'>
                <choice id='success' then='success'/>
                <choice id='error' then='error'/>
        </case>
    </wait>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Order migration failed.</end>
    <end id='success' state='SUCCEEDED'>Order Migrated.</end>
</process>"

INSERT_UPDATE CountryMigrationConfiguration; country(isocode)[unique = true]; contractStartDate[dateformat = dd.MM.yyyy HH:mm]; sepaMandateCreationDueDate[dateformat = dd.MM.yyyy HH:mm]; firstMigrationNoticeDate[dateformat = dd.MM.yyyy HH:mm]; purchaseAllowedFromDate[dateformat = dd.MM.yyyy]; lmpEndDateForFutureDatedContracts[dateformat = dd.MM.yyyy HH:mm:ss]
                                           ; AT                             ; 01.01.2025 00:00                                ; 01.12.2024 00:00                                         ; 02.09.2024 00:00                                       ; 01.01.2025                                      ; 31.12.24 23:59:59



INSERT_UPDATE ServicelayerJob; code[unique = true]                ; springId
                             ; paymentMethodAvailabilityReportJob ; paymentMethodAvailabilityReportJobPerformable

INSERT_UPDATE CronJob; code[unique = true]                    ; job(code)                          ; sessionUser(uid); sessionLanguage(isocode); filesCount; filesDaysOld; filesOperator(code); logToFile; nodeGroup
                     ; paymentMethodAvailabilityReportCronJob ; paymentMethodAvailabilityReportJob ; anonymous       ; en                      ; 60        ; 60          ; AND                ; TRUE     ; ncf

INSERT_UPDATE ServicelayerJob; code[unique = true]                    ; springId
                             ; sepaDDPaymentMethodCreationReminderJob ; sepaDDPaymentMethodCreationReminderJobPerformable

INSERT_UPDATE CronJob; code[unique = true]                        ; job(code)                              ; sessionUser(uid); sessionLanguage(isocode); filesCount; filesDaysOld; filesOperator(code); logToFile; nodeGroup
                     ; sepaDDPaymentMethodCreationReminderCronJob ; sepaDDPaymentMethodCreationReminderJob ; anonymous       ; en                      ; 60        ; 60          ; AND                ; TRUE     ; ncf


INSERT_UPDATE DynamicProcessDefinition; code[unique = true]                  ; active; content
                                      ; future-contract-cancellation-process ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='prepareContractContext' name='future-contract-cancellation-process'
        processClass='com.sast.subscription.model.FutureContractCancellationBusinessProcessModel' onError='error'>

    <action id='prepareContractContext' bean='prepareContractContextAction'>
        <transition name='OK' to='validateFutureContractForCancellation'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='validateFutureContractForCancellation' bean='validateFutureContractForCancellationAction'>
        <transition name='OK' to='initiateOrderRejectionInBrim'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='initiateOrderRejectionInBrim' bean='initiateOrderRejectionInBrimAction'>
        <transition name='OK' to='waitForBrimOrderRejectionResponse'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForBrimOrderRejectionResponse' then='error'>
        <case event='brim-order-rejection-response-event'>
                <choice id='success' then='markFutureContractAsCancelled'/>
                <choice id='fail' then='failed'/>
        </case>
    </wait>

    <action id='markFutureContractAsCancelled' bean='markFutureContractAsCancelledAction'>
        <transition name='OK' to='adaptOrderAfterFutureContractCancellation'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='adaptOrderAfterFutureContractCancellation' bean='adaptOrderAfterFutureContractCancellationAction'>
        <transition name='OK' to='evaluateBrimExportRequirement'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='evaluateBrimExportRequirement' bean='evaluateBrimExportRequirementAction'>
        <transition name='EXPORT_REQUIRED' to='exportOrderToBrimAfterFutureContractCancellation'/>
        <transition name='NO_EXPORT_REQUIRED' to='sendFutureContractCancellationConfirmationToLmp'/>
        <transition name='OTHER' to='error'/>
    </action>

    <action id='exportOrderToBrimAfterFutureContractCancellation' bean='exportOrderToBrimAfterFutureContractCancellationAction'>
        <transition name='OK' to='waitForBrimOrderExportResponse'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForBrimOrderExportResponse' then='error'>
        <case event='brim-order-reexport-response-event'>
                <choice id='success' then='sendFutureContractCancellationConfirmationToLmp'/>
                <choice id='fail' then='failed'/>
        </case>
    </wait>

    <action id='sendFutureContractCancellationConfirmationToLmp' bean='sendFutureContractCancellationConfirmationToLmpAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
    </action>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>´Contract cancellation failed.</end>
    <end id='success' state='SUCCEEDED'>Contract cancelled.</end>
</process>"

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]                   ; active; content
                                      ; aa-order-migration-simulation-process ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='validateInactivityStatus' name='aa-order-migration-simulation-process'
        processClass='com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel' onError='error'>
    <action id='validateInactivityStatus' bean='validateInactivityStatusAction'>
        <transition name='OK' to='validateOrderDraftStatus'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='validateOrderDraftStatus' bean='validateOrderDraftStatusAction'>
        <transition name='OK' to='setOrderDraftStatusToInProgress'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderDraftStatusToInProgress' bean='setOrderDraftStatusToInProgressAction'>
        <transition name='OK' to='sendValidationRequestToLmp'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendValidationRequestToLmp' bean='sendValidationRequestToLmpAction'>
        <transition name='OK' to='waitForLmpValidationResponse'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForLmpValidationResponse' then='error'>
        <case event='lmp-validation-response-event'>
                <choice id='success' then='notifyCmtOnSuccessfulMigrationSimulation'/>
                <choice id='fail' then='notifyCmtAboutLmpValidationFailure'/>
        </case>
    </wait>

    <action id='notifyCmtAboutLmpValidationFailure' bean='notifyCmtAboutLmpValidationFailureAction'>
        <transition name='OK' to='cleanupAfterSimulationFailure'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='notifyCmtOnSuccessfulMigrationSimulation' bean='notifyCmtOnSuccessfulMigrationAction'>
        <transition name='OK' to='cleanupAfterSimulationSuccess'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='cleanupAfterSimulationSuccess' bean='cleanupMigrationSimulationResourcesAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='cleanupAfterSimulationFailure' bean='cleanupMigrationSimulationResourcesAction'>
        <transition name='OK' to='failed'/>
        <transition name='NOK' to='error'/>
    </action>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Migration simulation failed.</end>
    <end id='success' state='SUCCEEDED'>Migration simulation succeeded.</end>
</process>"

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]                  ; active; content
                                      ; open-order-cancel-process ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='validateOpenOrderForCancellation' name='open-order-cancel-process'
        processClass='com.sast.cis.core.model.OpenOrderCancelBusinessProcessModel' onError='error'>

    <action id='validateOpenOrderForCancellation' bean='validateOrderForCancellationAction'>
        <transition name='OK' to='initiateOrderCancellationInBrim'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='initiateOrderCancellationInBrim' bean='initiateOrderCancellationInBrimAction'>
        <transition name='OK' to='waitForBrimOrderCancellationResponse'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForBrimOrderCancellationResponse' then='error'>
        <case event='brim-order-cancellation-response-event'>
                <choice id='success' then='markOpenOrderAsCancelled'/>
                <choice id='fail' then='failed'/>
        </case>
    </wait>

    <action id='markOpenOrderAsCancelled' bean='markOpenOrderAsCancelledAction'>
        <transition name='OK' to='sendOpenOrderCancellationConfirmationToLmp'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendOpenOrderCancellationConfirmationToLmp' bean='sendOpenOrderCancelConfirmationToLmpAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
    </action>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>´Order cancellation failed.</end>
    <end id='success' state='SUCCEEDED'>Order cancelled.</end>
</process>"

INSERT_UPDATE DynamicProcessDefinition; code[unique = true]                  ; active; content
                                      ; esimaster-thl-migration-process      ; true  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='cancelLegacyMasterContract' name='esimaster-thl-migration-process'
        processClass='com.sast.cis.thl.model.EsiMasterTHLMigrationProcessModel' onError='error'>

  <action id='cancelLegacyMasterContract' bean='cancelThlOrderAction'>
       <transition name='OK' to='waitForBrimOrderCancellationEvent'/>
       <transition name='NOK' to='error'/>
  </action>

  <wait id='waitForBrimOrderCancellationEvent' then='error'>
      <case event='order-cancellation-event'>
          <choice id='success' then='exportOrder'/>
          <choice id='fail' then='error'/>
      </case>
  </wait>

  <action id='exportOrder' bean='brimExportMasterThlOrderAction'>
      <transition name='OK' to='waitForBrimOrderExportResponseEvent'/>
      <transition name='NOK' to='error'/>
   </action>

   <wait id='waitForBrimOrderExportResponseEvent' then='error'>
       <case event='thl-order-response-event'>
               <choice id='success' then='notifyLmpAboutTHLMigration'/>
               <choice id='fail' then='error'/>
       </case>
   </wait>

   <action id='notifyLMPAboutTHLMigration' bean='notifyLMPAboutTHLMigrationAction'>
         <transition name='OK' to='success'/>
         <transition name='NOK' to='error'/>
    </action>

   <end id='error' state='ERROR'>All went wrong.</end>
   <end id='failed' state='FAILED'>Master/THL Contract migration failed.</end>
   <end id='success' state='SUCCEEDED'>Contract migrated.</end>
</process>"
