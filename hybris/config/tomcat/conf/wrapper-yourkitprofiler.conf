
# Properties for YourKitProfiler

#include ../conf/hybris-wrapper-license.conf
#********************************************************************
# Wrapper Properties
#********************************************************************
# see http://wrapper.tanukisoftware.org/doc/english/props-jvm.html

wrapper.java.command=${java.home}/bin/java

set.CATALINA_HOME=..
set.CATALINA_BASE=..

${tomcat.wrapper.yourkitprofilerjavaoptions}
wrapper.java.additional_file=${wrapper.java.options.file}

wrapper.java.classpath.1=../lib/wrapper.jar
wrapper.java.classpath.2=../bin/bootstrap.jar
wrapper.java.classpath.3=../bin/tomcat-juli.jar

wrapper.java.library.path.1=../lib
wrapper.java.mainclass=org.tanukisoftware.wrapper.WrapperSimpleApp
wrapper.app.parameter.1=org.apache.catalina.startup.Bootstrap
wrapper.app.parameter.2=start

wrapper.port.min=${tomcat.wrapper.startport}
wrapper.port.max=${tomcat.wrapper.endport}



#********************************************************************
# Wrapper Logging Properties
#********************************************************************
# Format of output for the console.  (See docs for formats)
# (http://wrapper.tanukisoftware.org/doc/english/props-logging.html)

wrapper.debug=false
wrapper.adviser=false
wrapper.java.command.loglevel=DEBUG

wrapper.console.flush=true
wrapper.console.format=M
wrapper.console.loglevel=INFO

wrapper.logfile=${tomcat.wrapper.logfile}
wrapper.logfile.format=LPDZM
wrapper.logfile.loglevel=INFO
wrapper.logfile.maxfiles=365
wrapper.logfile.rollmode=DATE

wrapper.syslog.loglevel=NONE


#********************************************************************
# Advanced Properties
#********************************************************************

wrapper.java.statusfile=${HYBRIS_DATA_DIR}/hybristomcat.java.status
wrapper.java.idfile=${HYBRIS_DATA_DIR}/hybristomcat.java.id
wrapper.java.pidfile=${HYBRIS_DATA_DIR}/hybristomcat.java.pid
wrapper.lockfile=${HYBRIS_DATA_DIR}/hybristomcat.lock

wrapper.restart.reload_configuration=TRUE
wrapper.ping.interval=20
wrapper.startup.timeout=0
wrapper.ping.timeout=0
wrapper.shutdown.timeout=0
wrapper.cpu.timeout=0
wrapper.jvm_exit.timeout=0


