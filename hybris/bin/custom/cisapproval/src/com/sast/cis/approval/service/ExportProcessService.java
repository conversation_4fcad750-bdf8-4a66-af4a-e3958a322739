package com.sast.cis.approval.service;

import com.sast.cis.approval.model.AppLicenseProcessModel;
import com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.service.CisBusinessProcessService;
import de.hybris.platform.processengine.BusinessProcessService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.UUID;

@Service
public class ExportProcessService implements CisBusinessProcessService<AppLicenseModel> {
    private static final Logger LOG = LoggerFactory.getLogger(ExportProcessService.class);

    @Resource
    private ModelService modelService;

    @Resource
    private UserService userService;

    @Resource
    private BusinessProcessService businessProcessService;

    @Override
    public void invokeBusinessProcess(AppLicenseModel appLicense, String appProcessCode, DynamicBusinessProcessesDefinitions processDefinition) {
        AppLicenseProcessModel appLicenseProcess = businessProcessService
            .createProcess(generateProcessId(appLicense.getCode()), processDefinition.getValue());
        appLicenseProcess.setAppLicense(appLicense);
        appLicenseProcess.setUser(userService.getAdminUser());
        appLicenseProcess.setAppVersionProcessCode(appProcessCode);
        modelService.save(appLicenseProcess);

        LOG.debug("Starting export process for license with code={}", appLicense.getCode());
        businessProcessService.startProcess(appLicenseProcess);
    }

    private String generateProcessId(String variantProductCode) {
        return "applicense-" + variantProductCode + "-" + UUID.randomUUID();
    }
}
