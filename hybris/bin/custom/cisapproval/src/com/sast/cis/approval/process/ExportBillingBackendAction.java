package com.sast.cis.approval.process;

import com.sast.cis.approval.model.AppProcessModel;
import com.sast.cis.core.billingintegration.exception.BillingIntegrationException;
import com.sast.cis.core.billingintegration.request.ProductExport;
import com.sast.cis.core.enums.BillingSystemStatus;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppLicenseService;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.task.RetryLaterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ExportBillingBackendAction extends AbstractSimpleDecisionAction<AppProcessModel> {
    private final ProductExport productExport;
    private final AppLicenseService appLicenseService;

    public ExportBillingBackendAction(ProductExport productExport, ModelService modelService, AppLicenseService appLicenseService) {
        setModelService(modelService);
        this.productExport = productExport;
        this.appLicenseService = appLicenseService;
    }

    @Override
    public Transition executeAction(AppProcessModel appProcess) {
        if (appProcess == null) {
            LOG.error("Could not find app process");
            return Transition.NOK;
        }
        AppModel app = appProcess.getApp();

        for (AppLicenseModel appLicense : appLicenseService.getExportableLicenses(app)) {
            try {
                productExport.createProduct(appLicense);
            } catch (BillingIntegrationException e) {
                final String msg = String.format("Failed to export product for process=%s", appProcess.getCode());
                LOG.error(msg, e);
                throw new RetryLaterException(msg, e);
            }

            appLicense.setBillingSystemStatus(BillingSystemStatus.CREATE_PENDING);
            save(appLicense);
            refresh(appLicense);
        }

        return Transition.OK;
    }
}
