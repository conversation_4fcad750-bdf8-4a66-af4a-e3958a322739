package com.sast.cis.approval.process;

import com.google.common.collect.ImmutableList;
import com.sast.cis.approval.model.AppProcessModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppSyncService;
import de.hybris.platform.core.model.ItemModel;
import de.hybris.platform.processengine.action.AbstractProceduralAction;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;

import static de.hybris.platform.servicelayer.util.ServicesUtil.validateParameterNotNull;

@Component
@Slf4j
public class PrepareSyncAction extends AbstractProceduralAction<AppProcessModel> {

    @Resource
    private AppSyncService appSyncService;

    @Override
    @Resource
    public void setModelService(final ModelService modelService) {
        super.setModelService(modelService);
    }

    @Override
    public void executeAction(AppProcessModel appProcess) {
        AppModel app = appProcess.getApp();
        validateParameterNotNull(app, "App is required for app release process.");

        LOG.info("Preparing sync cron job for app with code={}", app.getCode());

        List<ItemModel> itemsToSync = ImmutableList.<ItemModel>builder()
            .addAll(app.getVersions())
            .addAll(appSyncService.getItemsForSync(app))
            .build();

        appProcess.setSyncCronJob(appSyncService.prepareItemSyncCronJob(itemsToSync, app.getCatalogVersion().getCatalog().getId()));
        modelService.save(appProcess);
        modelService.refresh(appProcess);
        LOG.info("Prepared sync cron job for app with code={}", app.getCode());
    }

}
