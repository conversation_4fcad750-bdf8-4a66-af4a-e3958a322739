package com.sast.cis.approval.workflows;

import de.hybris.platform.workflow.jobs.AutomatedWorkflowTemplateJob;
import de.hybris.platform.workflow.model.WorkflowActionModel;
import de.hybris.platform.workflow.model.WorkflowDecisionModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
public class AutomaticReviewApprovalJob implements AutomatedWorkflowTemplateJob {
    private static final Logger LOG = LoggerFactory.getLogger(AutomaticReviewApprovalJob.class);

    private static final String ERROR_MESSAGE = "Misconfigured decision name. Check in essentialdataWorkflow.impex or in backoffice for actual decision name.";
    private static final String APPROVE = "approveReviewDecision";

    @Override
    public WorkflowDecisionModel perform(WorkflowActionModel workflowAction) {
        LOG.debug("Automatically approving review.");
        return deriveDecision(workflowAction.getDecisions(), APPROVE);
    }

    private WorkflowDecisionModel deriveDecision(Collection<WorkflowDecisionModel> availableDecisions, String decisionCode) {
        return availableDecisions.stream()
            .filter(d -> d.getCode().equals(decisionCode))
            .findFirst()
            .orElseThrow(() -> new IllegalStateException(ERROR_MESSAGE));
    }
}
