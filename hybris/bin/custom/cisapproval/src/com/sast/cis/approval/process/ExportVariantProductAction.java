package com.sast.cis.approval.process;

import com.sast.cis.approval.model.AppLicenseProcessModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import org.springframework.stereotype.Component;

@Component
@Deprecated(since = "release_59")
public class ExportVariantProductAction extends AbstractSimpleDecisionAction<AppLicenseProcessModel> {
    public Transition executeAction(AppLicenseProcessModel appLicenseProcess) {
        throw new UnsupportedOperationException("Not used since release_59");
    }
}
