$productCatalog = aaProductCatalog
$productCatalogName = Automotive Aftermarket Product catalog

$catalogVersion = catalogversion(catalog(id[default=$productCatalog]), version[default='Staged'])[unique=true, default=$productCatalog:Staged]
$supercategories = source(code, $catalogVersion)[unique=true]
$categories = target(code, $catalogVersion)[unique=true]

# Insert Categories
INSERT_UPDATE Category; code[unique = true]; order;allowedPrincipals(uid)[default = 'customergroup']; $catalogVersion
; main; 1
; cat_00001;1
; cat_00002;2
; cat_1; 1
; cat_2;2
; cat_3;3
; cat_4;4
; cat_101;1
; cat_201;2
; cat_301;3
; cat_401;4
; cat_10101;1
; cat_10102;2
; cat_1010101;1
; cat_1010102;2
; cat_1010201;1
; cat_1010202;2
;cat_40101;1

# Insert Category Structure
INSERT_UPDATE CategoryCategoryRelation; $categories; $supercategories
; cat_00001  ; main
; cat_00002  ; main
; cat_1  ; main
 ; cat_2  ; main
 ; cat_3  ; main
 ; cat_4  ; main
 ; cat_101  ; cat_1
 ; cat_201  ; cat_2
 ; cat_301  ; cat_3
 ; cat_401  ; cat_4
 ; cat_10101  ; cat_101
 ; cat_10102  ; cat_101
; cat_1010101;cat_10101
; cat_1010102; cat_10101
; cat_1010201;cat_10102
; cat_1010202;cat_10102
;cat_40101;cat_401