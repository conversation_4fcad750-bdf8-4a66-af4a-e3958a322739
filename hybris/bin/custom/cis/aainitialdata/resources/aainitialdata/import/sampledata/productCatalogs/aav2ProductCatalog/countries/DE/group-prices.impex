#% impex.setLocale( Locale.ENGLISH );

# numerical code for DE, used as prefix for product code
$deCc = 049

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$baseProduct = baseProduct(code, $catalogVersion)


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; billingPriceCode               ; billingRowId; ug(code)[unique = true, default = aastore_WD0001]; currency(isocode)[unique = true, default = EUR]; unit(code)[unique = true, default = pieces]; minqtd[default = 1]; unitFactor[default = 1]; net[default = true]
                      ; AA2_$deCc1687P15152                          ; 385  ; WD0001_AA2_$deCc1687P15152_EUR ; $deCc01_WD0001_EUR
                      ; AA2_$deCc1687P15150                          ; 265  ; WD0001_AA2_$deCc1687P15150_EUR ; $deCc02_WD0001_EUR
                      ; AA2_$deCc1687P15142                          ; 385  ; WD0001_AA2_$deCc1687P15142_EUR ; $deCc03_WD0001_EUR
                      ; AA2_$deCc1687P15140                          ; 265  ; WD0001_AA2_$deCc1687P15140_EUR ; $deCc04_WD0001_EUR
                      ; AA2_$deCc1687P15060                          ; 1    ; WD0001_AA2_$deCc1687P15060_EUR ; $deCc05_WD0001_EUR
                      ; AA2_$deCc1687P15045                          ; 310  ; WD0001_AA2_$deCc1687P15045_EUR ; $deCc06_WD0001_EUR
                      ; AA2_$deCc1687P15090                          ; 300  ; WD0001_AA2_$deCc1687P15090_EUR ; $deCc07_WD0001_EUR
                      ; AA2_$deCc1687P15102                          ; 400  ; WD0001_AA2_$deCc1687P15102_EUR ; $deCc08_WD0001_EUR
                      ; AA2_$deCc1687P15100                          ; 300  ; WD0001_AA2_$deCc1687P15100_EUR ; $deCc09_WD0001_EUR
                      ; AA2_$deCc1687P15107                          ; 400  ; WD0001_AA2_$deCc1687P15107_EUR ; $deCc10_WD0001_EUR
                      ; AA2_$deCc1987P12840                          ; 1380 ; WD0001_AA2_$deCc1987P12840_EUR ; $deCc11_WD0001_EUR
                      ; AA2_$deCc1987P12847                          ; 1480 ; WD0001_AA2_$deCc1987P12847_EUR ; $deCc12_WD0001_EUR
                      ; AA2_$deCc1987P12998                          ; 360  ; WD0001_AA2_$deCc1987P12998_EUR ; $deCc13_WD0001_EUR
                      ; AA2_$deCc1987P12784                          ; 460  ; WD0001_AA2_$deCc1987P12784_EUR ; $deCc14_WD0001_EUR
                      ; AA2_$deCc1987P12970                          ; 664  ; WD0001_AA2_$deCc1987P12970_EUR ; $deCc15_WD0001_EUR
                      ; AA2_$deCc1987P12297                          ; 764  ; WD0001_AA2_$deCc1987P12297_EUR ; $deCc16_WD0001_EUR
                      ; AA2_$deCc1987P12990                          ; 220  ; WD0001_AA2_$deCc1987P12990_EUR ; $deCc17_WD0001_EUR
                      ; AA2_$deCc1987P12295                          ; 320  ; WD0001_AA2_$deCc1987P12295_EUR ; $deCc18_WD0001_EUR
                      ; AA2_$deCc1987P12820                          ; 775  ; WD0001_AA2_$deCc1987P12820_EUR ; $deCc19_WD0001_EUR
                      ; AA2_$deCc1987P12824                          ; 875  ; WD0001_AA2_$deCc1987P12824_EUR ; $deCc20_WD0001_EUR
                      ; AA2_$deCc1987P12051                          ; 1360 ; WD0001_AA2_$deCc1987P12051_EUR ; $deCc21_WD0001_EUR
                      ; AA2_$deCc1987P12910                          ; 1985 ; WD0001_AA2_$deCc1987P12910_EUR ; $deCc22_WD0001_EUR
                      ; AA2_$deCc1987P12917                          ; 2085 ; WD0001_AA2_$deCc1987P12917_EUR ; $deCc23_WD0001_EUR
                      ; AA2_$deCc1987P12260                          ; 600  ; WD0001_AA2_$deCc1987P12260_EUR ; $deCc24_WD0001_EUR
                      ; AA2_$deCc1987P12262                          ; 700  ; WD0001_AA2_$deCc1987P12262_EUR ; $deCc25_WD0001_EUR
                      ; AA2_$deCc1987P12278                          ; 1180 ; WD0001_AA2_$deCc1987P12278_EUR ; $deCc26_WD0001_EUR
                      ; AA2_$deCc1987P12275                          ; 1280 ; WD0001_AA2_$deCc1987P12275_EUR ; $deCc27_WD0001_EUR
                      ; AA2_$deCc1987P12400                          ; 1280 ; WD0001_AA2_$deCc1987P12400_EUR ; $deCc28_WD0001_EUR
                      ; AA2_$deCc1987P12936                          ; 1380 ; WD0001_AA2_$deCc1987P12936_EUR ; $deCc29_WD0001_EUR
                      ; AA2_$deCc1987P12412                          ; 2890 ; WD0001_AA2_$deCc1987P12412_EUR ; $deCc30_WD0001_EUR
                      ; AA2_$deCc1987P12500                          ; 680  ; WD0001_AA2_$deCc1987P12500_EUR ; $deCc31_WD0001_EUR
                      ; AA2_$deCc1987729274                          ; 450  ; WD0001_AA2_$deCc1987729274_EUR ; $deCc32_WD0001_EUR
                      ; AA2_$deCc1987P13515                          ; 675  ; WD0001_AA2_$deCc1987P13515_EUR ; $deCc33_WD0001_EUR
                      ; AA2_$deCc1987P13516                          ; 915  ; WD0001_AA2_$deCc1987P13516_EUR ; $deCc34_WD0001_EUR
                      ; AA2_$deCc1687P15015                          ; 304  ; WD0001_AA2_$deCc1687P15015_EUR ; $deCc35_WD0001_EUR
                      ; AA2_$deCc1987P12404                          ; 1070 ; WD0001_AA2_$deCc1987P12404_EUR ; $deCc36_WD0001_EUR
                      ; AA2_$deCc1987P12359                          ; 1170 ; WD0001_AA2_$deCc1987P12359_EUR ; $deCc37_WD0001_EUR
                      ; AA2_$deCc1987P12389                          ; 399  ; WD0001_AA2_$deCc1987P12389_EUR ; $deCc38_WD0001_EUR
                      ; AA2_$deCc1987P12385                          ; 645  ; WD0001_AA2_$deCc1987P12385_EUR ; $deCc39_WD0001_EUR

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; billingPriceCode               ; billingRowId; ug(code)[unique = true, default = aastore_AT0000]; currency(isocode)[unique = true, default = EUR]; unit(code)[unique = true, default = pieces]; minqtd[default = 1]; unitFactor[default = 1]; net[default = true]
                      ; AA2_$deCc1987P12760                          ; 2150 ; AT0000_AA2_$deCc1987P12760_EUR ; $deCc01_AT0000_EUR
                      ; AA2_$deCc1987P12949                          ; 2190 ; AT0000_AA2_$deCc1987P12949_EUR ; $deCc02_AT0000_EUR

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; billingPriceCode               ; billingRowId; ug(code)[unique = true, default = aastore_BM0000]; currency(isocode)[unique = true, default = EUR]; unit(code)[unique = true, default = pieces]; minqtd[default = 1]; unitFactor[default = 1]; net[default = true]
                      ; AA2_$deCc1987P12097                          ; 1    ; BM0000_AA2_$deCc1987P12097_EUR ; $deCc01_BM0000_EUR
INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; billingPriceCode               ; billingRowId; ug(code)[unique = true, default = aastore_IDW000]; currency(isocode)[unique = true, default = EUR]; unit(code)[unique = true, default = pieces]; minqtd[default = 1]; unitFactor[default = 1]; net[default = true]
                      ; AA2_$deCc1987P12097                          ; 1    ; IDW000_AA2_$deCc1987P12097_EUR ; $deCc01_IDW000_EUR

UPDATE AppLicense; code[unique = true]; $catalogVersion[unique = true]; groupPriceUpdateStatus(code)[default = IN_SYNC];
                 ; AA2_$deCc1987P12760
                 ; AA2_$deCc1987P12949
                 ; AA2_$deCc1687P15152
                 ; AA2_$deCc1687P15150
                 ; AA2_$deCc1687P15142
                 ; AA2_$deCc1687P15140
                 ; AA2_$deCc1687P15060
                 ; AA2_$deCc1687P15045
                 ; AA2_$deCc1687P15090
                 ; AA2_$deCc1687P15102
                 ; AA2_$deCc1687P15100
                 ; AA2_$deCc1687P15107
                 ; AA2_$deCc1987P12840
                 ; AA2_$deCc1987P12847
                 ; AA2_$deCc1987P12998
                 ; AA2_$deCc1987P12784
                 ; AA2_$deCc1987P12970
                 ; AA2_$deCc1987P12297
                 ; AA2_$deCc1987P12990
                 ; AA2_$deCc1987P12295
                 ; AA2_$deCc1987P12820
                 ; AA2_$deCc1987P12824
                 ; AA2_$deCc1987P12051
                 ; AA2_$deCc1987P12910
                 ; AA2_$deCc1987P12917
                 ; AA2_$deCc1987P12260
                 ; AA2_$deCc1987P12262
                 ; AA2_$deCc1987P12278
                 ; AA2_$deCc1987P12275
                 ; AA2_$deCc1987P12400
                 ; AA2_$deCc1987P12936
                 ; AA2_$deCc1987P12412
                 ; AA2_$deCc1987P12500
                 ; AA2_$deCc1987729274
                 ; AA2_$deCc1987P13515
                 ; AA2_$deCc1987P13516
                 ; AA2_$deCc1687P15015
                 ; AA2_$deCc1987P12404
                 ; AA2_$deCc1987P12359
                 ; AA2_$deCc1987P12389
                 ; AA2_$deCc1987P12385
                 ; AA2_$deCc1987P12097
