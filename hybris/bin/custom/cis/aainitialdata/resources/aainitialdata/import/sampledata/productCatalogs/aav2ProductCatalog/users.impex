$developerGroup = developergroup
$integratorGroup = integratorgroup
$defaultPassword = ********

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$austriaSeller1Company_id = $config-austriaSeller1Id
$portugalSeller1Company_id = $config-portugalSeller1Id
$portugalSeller1Company_user_id = $config-testAccount.portugalSeller1.user.id
$portugalBuyerCompany_id = $config-portugalBuyer1Id
$portugalBuyer1Company_user_id = $config-testAccount.portugalBuyer1.user.id
$croatiaSeller1Company_id = $config-croatiaSeller1Id
$croatiaSeller1Company_user_id = $config-testAccount.croatiaSeller1.user.id
$croatiaBuyer1Company_id = $config-croatiaBuyer1Id
$croatiaBuyer1Company_user_id = $config-testAccount.croatiaBuyer1.user.id.user.id
$sloveniaBuyer1Company_id = $config-sloveniaBuyer1Id
$sloveniaBuyer1Company_user_id = $config-testAccount.sloveniaBuyer1.user.id
$finlandSeller1Company_id = $config-finlandSeller1Id
$finlandSeller1Company_user_id = $config-testAccount.finlandSeller1.user.id
$finlandBuyer1Company_id = $config-finlandBuyer1Id
$finlandBuyer1Company_user_id = $config-testAccount.finlandBuyer1.user.id.user.id
$estoniaBuyer1Company_id = $config-estoniaBuyer1Id
$estoniaBuyer1Company_user_id = $config-testAccount.estoniaBuyer1.user.id
$lithuaniaBuyer1Company_id = $config-lithuaniaBuyer1Id
$lithuaniaBuyer1Company_user_id = $config-testAccount.lithuaniaBuyer1.user.id
$latviaBuyer1Company_id = $config-latviaBuyer1Id
$latviaBuyer1Company_user_id = $config-testAccount.latviaBuyer1.user.id
$norwaySeller1Company_id = $config-norwaySeller1Id
$norwaySeller1Company_user_id = $config-testAccount.norwaySeller1.user.id
$norwayBuyer1Company_id = $config-norwayBuyer1Id
$norwayBuyer1Company_user_id = $config-testAccount.norwayBuyer1.user.id.user.id
$swedenSeller1Company_id = $config-swedenSeller1Id
$swedenSeller1Company_user_id = $config-testAccount.swedenSeller1.user.id
$swedenBuyer1Company_id = $config-swedenBuyer1Id
$swedenBuyer1Company_user_id = $config-testAccount.swedenBuyer1.user.id.user.id
$denmarkSeller1Company_id = $config-denmarkSeller1Id
$denmarkSeller1Company_user_id = $config-testAccount.denmarkSeller1.user.id
$denmarkBuyer1Company_id = $config-denmarkBuyer1Id
$denmarkBuyer1Company_user_id = $config-testAccount.denmarkBuyer1.user.id.user.id
$icelandBuyer1Company_id = $config-icelandBuyer1Id
$icelandBuyer1Company_user_id = $config-testAccount.icelandBuyer1.user.id.user.id
$franceBuyer1Company_id = $config-franceBuyer1Id
$franceBuyer1Company_user_id = $config-testAccount.franceBuyer1.user.id.user.id
$franceSeller1Company_id = $config-franceSeller1Id
$franceSeller1Company_user_id = $config-testAccount.franceSeller1.user.id.user.id
$belgiumBuyer1Company_id = $config-belgiumBuyer1Id
$belgiumBuyer1Company_user_id = $config-testAccount.belgiumBuyer1.user.id.user.id
$belgiumSeller1Company_id = $config-belgiumSeller1Id
$belgiumSeller1Company_user_id = $config-testAccount.belgiumSeller1.user.id.user.id
$netherlandsBuyer1Company_id = $config-netherlandsBuyer1Id
$netherlandsBuyer1Company_user_id = $config-testAccount.netherlandsBuyer1.user.id.user.id
$netherlandsSeller1Company_id = $config-netherlandsSeller1Id
$netherlandsSeller1Company_user_id = $config-testAccount.netherlandsSeller1.user.id.user.id
$spainBuyer1Company_id = $config-spainBuyer1Id
$spainBuyer1Company_user_id = $config-testAccount.spainBuyer1.user.id.user.id
$spainSeller1Company_id = $config-spainSeller1Id
$spainSeller1Company_user_id = $config-testAccount.spainSeller1.user.id.user.id
$germanyBuyer1Company_id = $config-germanyBuyer1Id
$germanyBuyer1Company_user_id = $config-testAccount.germanyBuyer1.user.id.user.id
$germanySeller1Company_id = $config-germanySeller1Id
$germanySeller1Company_user_id = $config-testAccount.germanySeller1.user.id.user.id
$greeceBuyer1Company_id = $config-greeceBuyer1Id
$greeceBuyer1Company_user_id = $config-testAccount.greeceBuyer1.user.id.user.id
$greeceSeller1Company_id = $config-greeceSeller1Id
$greeceSeller1Company_user_id = $config-testAccount.greeceSeller1.user.id.user.id
$cyprusBuyer1Company_id = $config-cyprusBuyer1Id
$cyprusBuyer1Company_user_id = $config-testAccount.cyprusBuyer1.user.id.user.id
$maltaBuyer1Company_id = $config-maltaBuyer1Id
$maltaBuyer1Company_user_id = $config-testAccount.maltaBuyer1.user.id.user.id


INSERT_UPDATE IoTCompany; uid[unique = true]            ; name                 ; country(isocode); store(uid); &compRef           ; bpmdId             ; communicationLanguage(isocode)
                        ; $portugalSeller1Company_id    ; Portugal Seller 1    ; PT              ; aastore   ; portugalSeller1    ; aseller1_pt_bpmdid ; pt
                        ; $portugalBuyerCompany_id      ; Portugal Buyer 1     ; PT              ; aastore   ; portugalBuyer1     ; abuyer1_pt_bpmdid  ; pt
                        ; $croatiaSeller1Company_id     ; Croatia Seller 1     ; HR              ; aastore   ; croatiaSeller1     ; aseller1_hr_bpmdid ; hr
                        ; $croatiaBuyer1Company_id      ; Croatia Buyer 1      ; HR              ; aastore   ; croatiaBuyer1      ; abuyer1_hr_bpmdid  ; hr
                        ; $sloveniaBuyer1Company_id     ; Slovenia Buyer 1     ; SI              ; aastore   ; sloveniaBuyer1     ; abuyer1_si_bpmdid  ; sl
                        ; $finlandSeller1Company_id     ; Finland Seller 1     ; FI              ; aastore   ; finlandSeller1     ; aseller1_fi_bpmdid ; fi
                        ; $finlandBuyer1Company_id      ; Finland Buyer 1      ; FI              ; aastore   ; finlandBuyer1      ; abuyer1_fi_bpmdid  ; fi
                        ; $estoniaBuyer1Company_id      ; Estonia Buyer 1      ; EE              ; aastore   ; estoniaBuyer1      ; abuyer1_ee_bpmdid  ; et
                        ; $lithuaniaBuyer1Company_id    ; Lithuania Buyer 1    ; LT              ; aastore   ; lithuaniaBuyer1    ; abuyer1_lt_bpmdid  ; lt
                        ; $latviaBuyer1Company_id       ; Latvia Buyer 1       ; LV              ; aastore   ; latviaBuyer1       ; abuyer1_lv_bpmdid  ; lv

                        ; $norwaySeller1Company_id      ; Norway Seller 1      ; NO              ; aastore   ; norwaySeller1      ; aseller1_no_bpmdid ; no
                        ; $norwayBuyer1Company_id       ; Norway Buyer 1       ; NO              ; aastore   ; norwayBuyer1       ; abuyer1_no_bpmdid  ; no
                        ; $swedenSeller1Company_id      ; Sweden Seller 1      ; SE              ; aastore   ; swedenSeller1      ; aseller1_se_bpmdid ; sv
                        ; $swedenBuyer1Company_id       ; Sweden Buyer 1       ; SE              ; aastore   ; swedenBuyer1       ; abuyer1_se_bpmdid  ; sv
                        ; $denmarkSeller1Company_id     ; Denmark Seller 1     ; DK              ; aastore   ; denmarkSeller1     ; aseller1_dk_bpmdid ; da
                        ; $denmarkBuyer1Company_id      ; Denmark Buyer 1      ; DK              ; aastore   ; denmarkBuyer1      ; abuyer1_dk_bpmdid  ; da
                        ; $icelandBuyer1Company_id      ; Iceland Buyer 1      ; IS              ; aastore   ; icelandBuyer1      ; abuyer1_is_bpmdid  ; da

                        ; $franceSeller1Company_id      ; France Seller 1      ; FR              ; aastore   ; franceSeller1      ; aseller1_fr_bpmdid ; fr
                        ; $franceBuyer1Company_id       ; France Buyer 1       ; FR              ; aastore   ; franceBuyer1       ; abuyer1_fr_bpmdid  ; fr
                        ; $belgiumSeller1Company_id     ; Belgium Seller 1     ; BE              ; aastore   ; belgiumSeller1     ; aseller1_be_bpmdid ; fr
                        ; $belgiumBuyer1Company_id      ; Belgium Buyer 1      ; BE              ; aastore   ; belgiumBuyer1      ; abuyer1_be_bpmdid  ; fr
                        ; $netherlandsSeller1Company_id ; Netherlands Seller 1 ; NL              ; aastore   ; netherlandsSeller1 ; aseller1_nl_bpmdid ; nl
                        ; $netherlandsBuyer1Company_id  ; Netherlands Buyer 1  ; NL              ; aastore   ; netherlandsBuyer1  ; abuyer1_nl_bpmdid  ; nl
                        ; $spainSeller1Company_id       ; Spain Seller 1       ; ES              ; aastore   ; spainSeller1       ; aseller1_es_bpmdid ; es
                        ; $spainBuyer1Company_id        ; Spain Buyer 1        ; ES              ; aastore   ; spainBuyer1        ; abuyer1_es_bpmdid  ; es

                        ; $germanySeller1Company_id     ; Germany Seller 1     ; DE              ; aastore   ; germanySeller1     ; aseller1_de_bpmdid ; de
                        ; $germanyBuyer1Company_id      ; Germany Buyer 1      ; DE              ; aastore   ; germanyBuyer1      ; abuyer1_de_bpmdid  ; de
                        ; $greeceSeller1Company_id      ; Greece Seller 1      ; GR              ; aastore   ; greeceSeller1      ; aseller1_gr_bpmdid ; el
                        ; $greeceBuyer1Company_id       ; Greece Buyer 1       ; GR              ; aastore   ; greeceBuyer1       ; abuyer1_gr_bpmdid  ; el
                        ; $cyprusBuyer1Company_id       ; Cyprus Buyer 1       ; CY              ; aastore   ; cyprusBuyer1       ; abuyer1_cy_bpmdid  ; el
                        ; $maltaBuyer1Company_id        ; Malta Buyer 1        ; MT              ; aastore   ; maltaBuyer1        ; abuyer1_mt_bpmdid  ; en


# Company contact addresses
INSERT_UPDATE Address; &addId                    ; owner(&compRef)[unique = true]; duplicate[unique = true]; town       ; country(isocode); billingAddress[unique = true]; contactAddress[unique = true]; company              ; email                       ; streetname                  ; streetnumber; postalcode
                     ; protugalSeller1Address    ; portugalSeller1               ; false                   ; Lisbon     ; PT              ; false                        ; true                         ; Portugal Seller 1    ; <EMAIL>    ; Burgplatz                   ; 32          ; 2136
                     ; protugalBuyer1Address     ; portugalBuyer1                ; false                   ; Lisbon     ; PT              ; false                        ; true                         ; Portugal Buyer 1     ; <EMAIL>     ; Burg                        ; 31          ; 6450
                     ; croatiaSeller1Address     ; croatiaSeller1                ; false                   ; Zagreb     ; HR              ; false                        ; true                         ; Croatia Seller 1     ; <EMAIL>     ; Paska                       ; 6           ; 1420
                     ; croatiaBuyer1Address      ; croatiaBuyer1                 ; false                   ; Zagreb     ; HR              ; false                        ; true                         ; Croatia Buyer 1      ; <EMAIL>      ; Paska                       ; 5           ; 1430
                     ; sloveniaBuyer1Address     ; sloveniaBuyer1                ; false                   ; Ljubljana  ; SI              ; false                        ; true                         ; Slovenia Buyer 1     ; <EMAIL>     ; Stritarjev                  ; 12          ; 1095
                     ; finlandSeller1Address     ; finlandSeller1                ; false                   ; Helsinki   ; FI              ; false                        ; true                         ; Finland Seller 1     ; <EMAIL>     ; Aleksanterinkatu            ; 16          ; 00170
                     ; finlandBuyer1Address      ; finlandBuyer1                 ; false                   ; Helsinki   ; FI              ; false                        ; true                         ; Finland Buyer 1      ; <EMAIL>      ; Fabianinkatu                ; 20          ; 00130
                     ; estoniaBuyer1Address      ; estoniaBuyer1                 ; false                   ; Tallinn    ; EE              ; false                        ; true                         ; Estonia Buyer 1      ; <EMAIL>      ; Valdeku                     ; 79          ; 11211
                     ; lithuaniaBuyer1Address    ; lithuaniaBuyer1               ; false                   ; Vilnius    ; LT              ; false                        ; true                         ; Lithuania Buyer 1    ; <EMAIL>    ; Taikos                      ; 5           ; 17228
                     ; latviaBuyer1Address       ; latviaBuyer1                  ; false                   ; Riga       ; LV              ; false                        ; true                         ; Latvia Buyer 1       ; <EMAIL>       ; Maskavas                    ; 256         ; 2121
                     ; norwaySeller1Address      ; norwaySeller1                 ; false                   ; Oslo       ; NO              ; false                        ; true                         ; Norway Seller 1      ; <EMAIL>      ; Skausnaret                  ; 4           ; 1262
                     ; norwayBuyer1Address       ; norwayBuyer1                  ; false                   ; Oslo       ; NO              ; false                        ; true                         ; Norway Buyer 1       ; <EMAIL>       ; Sundveien                   ; 35          ; 0198
                     ; swedenSeller1Address      ; swedenSeller1                 ; false                   ; Stockholm  ; SE              ; false                        ; true                         ; Sweden Seller 1      ; <EMAIL>      ; Lovholmsbrinken             ; 1           ; 11743
                     ; swedenBuyer1Address       ; swedenBuyer1                  ; false                   ; Stockholm  ; SE              ; false                        ; true                         ; Sweden Buyer 1       ; <EMAIL>       ; Rosenlundsgatan             ; 29          ; 11863
                     ; denmarkSeller1Address     ; denmarkSeller1                ; false                   ; Copenhagen ; DK              ; false                        ; true                         ; Denmark Seller 1     ; <EMAIL>     ; Fuglebakken                 ; 22          ; 8900
                     ; denmarkBuyer1Address      ; denmarkBuyer1                 ; false                   ; Copenhagen ; DK              ; false                        ; true                         ; Denmark Buyer 1      ; <EMAIL>      ; Hjortedalen                 ; 19          ; 4000
                     ; icelandBuyer1Address      ; icelandBuyer1                 ; false                   ; Reykjavik  ; IS              ; false                        ; true                         ; Iceland Buyer 1      ; <EMAIL>      ; Armuli                      ; 8           ; 130
                     ; franceSeller1Address      ; franceSeller1                 ; false                   ; Paris      ; FR              ; false                        ; true                         ; France Seller 1      ; <EMAIL>      ; Av. Anatole France 3è étage ; 5           ; 75007
                     ; franceBuyer1Address       ; franceBuyer1                  ; false                   ; Paris      ; FR              ; false                        ; true                         ; France Buyer 1       ; <EMAIL>       ; Av. Anatole France 2è étage ; 5           ; 75007
                     ; belgiumSeller1Address     ; belgiumSeller1                ; false                   ; Bruxelles  ; BE              ; false                        ; true                         ; Belgium Seller 1     ; <EMAIL>     ; Pl. de l'Atomium            ; 1           ; 1020
                     ; belgiumBuyer1Address      ; belgiumBuyer1                 ; false                   ; Bruxelles  ; BE              ; false                        ; true                         ; Belgium Buyer 1      ; <EMAIL>      ; Av. du Football             ; 1           ; 1020
                     ; netherlandsSeller1Address ; netherlandsSeller1            ; false                   ; Amsterdam  ; NL              ; false                        ; true                         ; Netherlands Seller 1 ; <EMAIL> ; Nieuwezijds Voorburgwal     ; 147         ; 1012
                     ; netherlandsBuyer1Address  ; netherlandsBuyer1             ; false                   ; Amsterdam  ; NL              ; false                        ; true                         ; Netherlands Buyer 1  ; <EMAIL>  ; Nieuwezijds Voorburgwal     ; 182         ; 1012
                     ; spainSeller1Address       ; spainSeller1                  ; false                   ; Madrid     ; ES              ; false                        ; true                         ; Spain Seller 1       ; <EMAIL>       ; Prta del Sol                ; 10          ; 28013
                     ; spainBuyer1Address        ; spainBuyer1                   ; false                   ; Madrid     ; ES              ; false                        ; true                         ; Spain Buyer 1        ; <EMAIL>        ; Prta del Sol                ; 11          ; 28013
                     ; germanySeller1Address     ; germanySeller1                ; false                   ; Berlin     ; DE              ; false                        ; true                         ; Germany Seller 1     ; <EMAIL>     ; Friedrichstraße             ; 17          ; 10178
                     ; germanyBuyer1Address      ; germanyBuyer1                 ; false                   ; Berlin     ; DE              ; false                        ; true                         ; Germany Buyer 1      ; <EMAIL>      ; Kurfürstendamm              ; 29          ; 10787
                     ; greeceSeller1Address      ; greeceSeller1                 ; false                   ; Athens     ; GR              ; false                        ; true                         ; Greece Seller 1      ; <EMAIL>      ; Odos Akropolis              ; 35          ; 10558
                     ; greeceBuyer1Address       ; greeceBuyer1                  ; false                   ; Athens     ; GR              ; false                        ; true                         ; Greece Buyer 1       ; <EMAIL>       ; Leoforos Plaka              ; 48          ; 10671
                     ; cyprusBuyer1Address       ; cyprusBuyer1                  ; false                   ; Nicosia    ; CY              ; false                        ; true                         ; Cyprus Buyer 1       ; <EMAIL>       ; Odos Ledras                 ; 1           ; 1010
                     ; maltaBuyer1Address        ; maltaBuyer1                   ; false                   ; Valletta   ; MT              ; false                        ; true                         ; Malta Buyer 1        ; <EMAIL>        ; Republic Street             ; 5           ; 1111


# Company billing addresses
INSERT_UPDATE Address; &addId                                  ; owner(&compRef)[unique = true]; streetname                          ; streetnumber; postalcode[unique = true]; duplicate[unique = true]; town       ; country(isocode); billingAddress[unique = true]; contactAddress[unique = true]; shippingAddress; unloadingAddress; firstname; lastname     ; email                                              ; phone1            ; visibleInAddressBook
                     ; portugalSeller1AddressBillingAddress    ; portugalSeller1               ; Alameda das Comunidades Portuguesas ; 8           ; 1700-111                 ; false                   ; Lisbon     ; PT              ; true                         ; false                        ; false          ; false           ; Florian  ; Bergmann     ; <EMAIL>    ; +351 *********    ; true
                     ; portugalBuyer1AddressBillingAddress     ; portugalBuyer1                ; Portual Buyer Street                ; 9           ; 56740                    ; false                   ; Lisbon     ; PT              ; true                         ; false                        ; false          ; false           ; Dirk     ; Hueber       ; <EMAIL>     ; +351 218413699    ; true
                     ; croatiaSeller1AddressBillingAddress     ; croatiaSeller1                ; Crpatia Seller Street               ; 1           ; 14204                    ; false                   ; Zagreb     ; HR              ; true                         ; false                        ; false          ; false           ; Marina   ; Perkovic     ; <EMAIL>     ; +385 194842224    ; true
                     ; croatiaBuyer1AddressBillingAddress      ; croatiaBuyer1                 ; Croatia Buyer Street                ; 12          ; 00422                    ; false                   ; Zagreb     ; HR              ; true                         ; false                        ; false          ; false           ; Toni     ; Kranjcar     ; <EMAIL>      ; +385 194579224    ; true
                     ; sloveniaBuyer1AddressBillingAddress     ; sloveniaBuyer1                ; Slovenia Buyer Street               ; 42          ; 19034                    ; false                   ; Ljubljana  ; SI              ; true                         ; false                        ; false          ; false           ; Anja     ; Ribic        ; <EMAIL>     ; +386 044200422    ; true
                     ; finlandSeller1AddressBillingAddress     ; finlandSeller1                ; Aleksanterinkatu                    ; 16          ; 00170                    ; false                   ; Helsinki   ; FI              ; true                         ; false                        ; false          ; false           ; Eero     ; Tuomainen    ; <EMAIL>     ; +358 412345678    ; true
                     ; finlandBuyer1AddressBillingAddress      ; finlandBuyer1                 ; Fabianinkatu                        ; 20          ; 00130                    ; false                   ; Helsinki   ; FI              ; true                         ; false                        ; false          ; false           ; Mila     ; Jarvinen     ; <EMAIL>      ; +358 326902149    ; true
                     ; estoniaBuyer1AddressBillingAddress      ; estoniaBuyer1                 ; Valdeku                             ; 79          ; 11211                    ; false                   ; Tallinn    ; EE              ; true                         ; false                        ; false          ; false           ; Kustas   ; Teder        ; <EMAIL>      ; +372 3946105      ; true
                     ; lithuaniaBuyer1AddressBillingAddress    ; lithuaniaBuyer1               ; Taikos                              ; 5           ; 17228                    ; false                   ; Vilnius    ; LT              ; true                         ; false                        ; false          ; false           ; Jonas    ; Miscikas     ; <EMAIL>    ; +370 68699562     ; true
                     ; latviaBuyer1AddressBillingAddress       ; latviaBuyer1                  ; Maskavas                            ; 256         ; 2121                     ; false                   ; Riga       ; LV              ; true                         ; false                        ; false          ; false           ; Andrs    ; Priede       ; <EMAIL>       ; +371 21296744     ; true
                     ; norwaySeller1AddressBillingAddress      ; norwaySeller1                 ; Skausnaret                          ; 4           ; 1262                     ; false                   ; Oslo       ; NO              ; true                         ; false                        ; false          ; false           ; Tove     ; Jensen       ; <EMAIL>      ; +47 47648112      ; true
                     ; norwayBuyer1AddressBillingAddress       ; norwayBuyer1                  ; Sundveien                           ; 35          ; 0198                     ; false                   ; Oslo       ; NO              ; true                         ; false                        ; false          ; false           ; Mathias  ; Haugen       ; <EMAIL>       ; +47 95185850      ; true
                     ; swedenSeller1AddressBillingAddress      ; swedenSeller1                 ; Lovholmsbrinken                     ; 1           ; 11743                    ; false                   ; Stockholm  ; SE              ; true                         ; false                        ; false          ; false           ; Therese  ; Holmberg     ; <EMAIL>      ; +46 478680113     ; true
                     ; swedenBuyer1AddressBillingAddress       ; swedenBuyer1                  ; Rosenlundsgatan                     ; 29          ; 11863                    ; false                   ; Stockholm  ; SE              ; true                         ; false                        ; false          ; false           ; Gustav   ; Hansson      ; <EMAIL>       ; +46 478142905     ; true
                     ; denmarkSeller1AddressBillingAddress     ; denmarkSeller1                ; Fuglebakken                         ; 22          ; 8900                     ; false                   ; Copenhagen ; DK              ; true                         ; false                        ; false          ; false           ; Hugo     ; Olsson       ; <EMAIL>     ; +45 53848420      ; true
                     ; denmarkBuyer1AddressBillingAddress      ; denmarkBuyer1                 ; Hjortedalen                         ; 19          ; 4000                     ; false                   ; Copenhagen ; DK              ; true                         ; false                        ; false          ; false           ; Camilla  ; Persson      ; <EMAIL>      ; +45 11426498      ; true
                     ; icelandBuyer1AddressBillingAddress      ; icelandBuyer1                 ; Armuli                              ; 8           ; 130                      ; false                   ; Reykjavik  ; IS              ; true                         ; false                        ; false          ; false           ; Arnkatla ; Blondal      ; <EMAIL>      ; +354 0048589      ; true
                     ; franceSeller1AddressBillingAddress      ; franceSeller1                 ; Av. Anatole France 3è étage         ; 5           ; 75007                    ; false                   ; Paris      ; FR              ; true                         ; false                        ; false          ; false           ; Léonie   ; Raymond      ; <EMAIL>      ; +33 6 32 18 07 71 ; true
                     ; franceBuyer1AddressBillingAddress       ; franceBuyer1                  ; Av. Anatole France 2è étage         ; 5           ; 75007                    ; false                   ; Paris      ; FR              ; true                         ; false                        ; false          ; false           ; Lucas    ; Marie-Ange   ; <EMAIL>       ; +33 7 94 30 81 30 ; true
                     ; belgiumSeller1AddressBillingAddress     ; belgiumSeller1                ; Pl. de l'Atomium                    ; 1           ; 1020                     ; false                   ; Bruxelles  ; BE              ; true                         ; false                        ; false          ; false           ; Cornélie ; Nicéphore    ; <EMAIL>     ; +32 459 82 24 35  ; true
                     ; belgiumBuyer1AddressBillingAddress      ; belgiumBuyer1                 ; Av. du Football                     ; 1           ; 1020                     ; false                   ; Bruxelles  ; BE              ; true                         ; false                        ; false          ; false           ; Loïc     ; Noham        ; <EMAIL>      ; +32 451 13 58 09  ; true
                     ; netherlandsSeller1AddressBillingAddress ; netherlandsSeller1            ; Nieuwezijds Voorburgwal             ; 147         ; 1012                     ; false                   ; Amsterdam  ; NL              ; true                         ; false                        ; false          ; false           ; Hilde    ; Jennigje     ; <EMAIL> ; +31 6 37225719    ; true
                     ; netherlandsBuyer1AddressBillingAddress  ; netherlandsBuyer1             ; Nieuwezijds Voorburgwal             ; 182         ; 1012                     ; false                   ; Amsterdam  ; NL              ; true                         ; false                        ; false          ; false           ; Agatha   ; Bart         ; <EMAIL>  ; +31 970 161 51290 ; true
                     ; spainSeller1AddressBillingAddress       ; spainSeller1                  ; Prta del Sol                        ; 10          ; 28013                    ; false                   ; Madrid     ; ES              ; true                         ; false                        ; false          ; false           ; Bruno    ; Julieta      ; <EMAIL>       ; +34 666 22 43 40  ; true
                     ; spainBuyer1AddressBillingAddress        ; spainBuyer1                   ; Prta del Sol                        ; 11          ; 28013                    ; false                   ; Madrid     ; ES              ; true                         ; false                        ; false          ; false           ; Juanita  ; Ercilia      ; <EMAIL>        ; +34 590 60 01 23  ; true
                     ; germanySeller1AddressBillingAddress     ; germanySeller1                ; Friedrichstraße                     ; 17          ; 10178                    ; false                   ; Berlin     ; DE              ; true                         ; false                        ; false          ; false           ; Timo     ; Fischer      ; <EMAIL>     ; +49 1598 7345621  ; true
                     ; germanyBuyer1AddressBillingAddress      ; germanyBuyer1                 ; Kurfürstendamm                      ; 29          ; 10787                    ; false                   ; Berlin     ; DE              ; true                         ; false                        ; false          ; false           ; Lena     ; Wagner       ; <EMAIL>      ; +49 1762 5894310  ; true
                     ; greeceSeller1AddressBillingAddress      ; greeceSeller1                 ; Odos Akropolis                      ; 35          ; 10558                    ; false                   ; Athens     ; GR              ; true                         ; false                        ; false          ; false           ; Nikos    ; Papadopoulos ; <EMAIL>      ; +30 6971 824569   ; true
                     ; greeceBuyer1AddressBillingAddress       ; greeceBuyer1                  ; Leoforos Plaka                      ; 48          ; 10671                    ; false                   ; Athens     ; GR              ; true                         ; false                        ; false          ; false           ; Eleni    ; Antoniou     ; <EMAIL>       ; +30 6943 215678   ; true
                     ; cyprusBuyer1AddressBillingAddress       ; cyprusBuyer1                  ; Odos Ledras                         ; 1           ; 1010                     ; false                   ; Nicosia    ; CY              ; true                         ; false                        ; false          ; false           ; Andreas  ; Constantinou ; <EMAIL>       ; +357 99 654123    ; true
                     ; maltaBuyer1AddressBillingAddress        ; maltaBuyer1                   ; Republic Street                     ; 5           ; 1111                     ; false                   ; Valletta   ; MT              ; true                         ; false                        ; false          ; false           ; Maria    ; Attard       ; <EMAIL>        ; +356 9978 4321    ; true


UPDATE IoTCompany; uid[unique = true]            ; billingAddress(&addId)                  ; contactAddress(&addId)    ;
                 ; $portugalSeller1Company_id    ; portugalSeller1AddressBillingAddress    ; protugalSeller1Address    ;
                 ; $portugalBuyerCompany_id      ; portugalBuyer1AddressBillingAddress     ; protugalBuyer1Address     ;
                 ; $croatiaSeller1Company_id     ; croatiaSeller1AddressBillingAddress     ; croatiaSeller1Address     ;
                 ; $croatiaBuyer1Company_id      ; croatiaBuyer1AddressBillingAddress      ; croatiaBuyer1Address      ;
                 ; $sloveniaBuyer1Company_id     ; sloveniaBuyer1AddressBillingAddress     ; sloveniaBuyer1Address     ;
                 ; $finlandSeller1Company_id     ; finlandSeller1AddressBillingAddress     ; finlandSeller1Address     ;
                 ; $finlandBuyer1Company_id      ; finlandBuyer1AddressBillingAddress      ; finlandBuyer1Address      ;
                 ; $estoniaBuyer1Company_id      ; estoniaBuyer1AddressBillingAddress      ; estoniaBuyer1Address      ;
                 ; $lithuaniaBuyer1Company_id    ; lithuaniaBuyer1AddressBillingAddress    ; lithuaniaBuyer1Address    ;
                 ; $latviaBuyer1Company_id       ; latviaBuyer1AddressBillingAddress       ; latviaBuyer1Address       ;
                 ; $norwaySeller1Company_id      ; norwaySeller1AddressBillingAddress      ; norwaySeller1Address      ;
                 ; $norwayBuyer1Company_id       ; norwayBuyer1AddressBillingAddress       ; norwayBuyer1Address       ;
                 ; $swedenSeller1Company_id      ; swedenSeller1AddressBillingAddress      ; swedenSeller1Address      ;
                 ; $swedenBuyer1Company_id       ; swedenBuyer1AddressBillingAddress       ; swedenBuyer1Address       ;
                 ; $denmarkSeller1Company_id     ; denmarkSeller1AddressBillingAddress     ; denmarkSeller1Address     ;
                 ; $denmarkBuyer1Company_id      ; denmarkBuyer1AddressBillingAddress      ; denmarkBuyer1Address      ;
                 ; $icelandBuyer1Company_id      ; icelandBuyer1AddressBillingAddress      ; icelandBuyer1Address      ;
                 ; $franceSeller1Company_id      ; franceSeller1AddressBillingAddress      ; franceSeller1Address      ;
                 ; $franceBuyer1Company_id       ; franceBuyer1AddressBillingAddress       ; franceBuyer1Address       ;
                 ; $belgiumSeller1Company_id     ; belgiumSeller1AddressBillingAddress     ; belgiumSeller1Address     ;
                 ; $belgiumBuyer1Company_id      ; belgiumBuyer1AddressBillingAddress      ; belgiumBuyer1Address      ;
                 ; $netherlandsSeller1Company_id ; netherlandsSeller1AddressBillingAddress ; netherlandsSeller1Address ;
                 ; $netherlandsBuyer1Company_id  ; netherlandsBuyer1AddressBillingAddress  ; netherlandsBuyer1Address  ;
                 ; $spainSeller1Company_id       ; spainSeller1AddressBillingAddress       ; spainSeller1Address       ;
                 ; $spainBuyer1Company_id        ; spainBuyer1AddressBillingAddress        ; spainBuyer1Address        ;
                 ; $germanySeller1Company_id     ; germanySeller1AddressBillingAddress     ; germanySeller1Address     ;
                 ; $germanyBuyer1Company_id      ; germanyBuyer1AddressBillingAddress      ; germanyBuyer1Address      ;
                 ; $greeceSeller1Company_id      ; greeceSeller1AddressBillingAddress      ; greeceSeller1Address      ;
                 ; $greeceBuyer1Company_id       ; greeceBuyer1AddressBillingAddress       ; greeceBuyer1Address       ;
                 ; $cyprusBuyer1Company_id       ; cyprusBuyer1AddressBillingAddress       ; cyprusBuyer1Address       ;
                 ; $maltaBuyer1Company_id        ; maltaBuyer1AddressBillingAddress        ; maltaBuyer1Address        ;



UPDATE IotCompany; uid[unique = true]            ; approvalStatus(code); operationalStage(code);
                 ; $portugalSeller1Company_id    ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $portugalBuyerCompany_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $croatiaSeller1Company_id     ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $croatiaBuyer1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $sloveniaBuyer1Company_id     ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $finlandSeller1Company_id     ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $finlandBuyer1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $estoniaBuyer1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $lithuaniaBuyer1Company_id    ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $latviaBuyer1Company_id       ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $norwaySeller1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $norwayBuyer1Company_id       ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $swedenSeller1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $swedenBuyer1Company_id       ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $denmarkSeller1Company_id     ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $denmarkBuyer1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $icelandBuyer1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $franceSeller1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $franceBuyer1Company_id       ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $belgiumSeller1Company_id     ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $belgiumBuyer1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $netherlandsSeller1Company_id ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $netherlandsBuyer1Company_id  ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $spainSeller1Company_id       ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $spainBuyer1Company_id        ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $germanySeller1Company_id     ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $germanyBuyer1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $greeceSeller1Company_id      ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $greeceBuyer1Company_id       ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $cyprusBuyer1Company_id       ; APPROVED_COMMERCIAL ; OPERATIONAL           ;
                 ; $maltaBuyer1Company_id        ; APPROVED_COMMERCIAL ; OPERATIONAL           ;


INSERT_UPDATE Developer; originalUid[unique = true]                ; uid[unique = true]                        ; name          ; description          ; sessionLanguage(isocode); sessionCurrency(isocode); company(uid)                  ; groups(uid[default = $developerGroup]); loginDisabled[default = false]; password[default = $defaultPassword]
                       ; $portugalSeller1Company_user_id@devcon    ; $portugalSeller1Company_user_id@devcon    ; User1 BoschPT ; Seller 1             ; pt                      ; EUR                     ; $portugalSeller1Company_id    ;
                       ; $portugalBuyer1Company_user_id@devcon     ; $portugalBuyer1Company_user_id@devcon     ; User1 BoschPT ; Buyer 1              ; pt                      ; EUR                     ; $portugalBuyerCompany_id      ;
                       ; $croatiaSeller1Company_user_id@devcon     ; $croatiaSeller1Company_user_id@devcon     ; User1 BoschHR ; Seller 1             ; hr                      ; EUR                     ; $croatiaSeller1Company_id     ;
                       ; $croatiaBuyer1Company_user_id@devcon      ; $croatiaBuyer1Company_user_id@devcon      ; User1 BoschHR ; Buyer 1              ; hr                      ; EUR                     ; $croatiaBuyer1Company_id      ;
                       ; $sloveniaBuyer1Company_user_id@devcon     ; $sloveniaBuyer1Company_user_id@devcon     ; User1 BoschSI ; Buyer 1              ; sl                      ; EUR                     ; $sloveniaBuyer1Company_id     ;
                       ; $finlandSeller1Company_user_id@devcon     ; $finlandSeller1Company_user_id@devcon     ; User1 BoschFI ; Seller Finland 1     ; fi                      ; EUR                     ; $finlandSeller1Company_id     ;
                       ; $finlandBuyer1Company_user_id@devcon      ; $finlandBuyer1Company_user_id@devcon      ; User1 BoschFI ; Buyer Finland 1      ; fi                      ; EUR                     ; $finlandBuyer1Company_id      ;
                       ; $estoniaBuyer1Company_user_id@devcon      ; $estoniaBuyer1Company_user_id@devcon      ; User1 BoschEE ; Buyer Estonia 1      ; et                      ; EUR                     ; $estoniaBuyer1Company_id      ;
                       ; $lithuaniaBuyer1Company_user_id@devcon    ; $lithuaniaBuyer1Company_user_id@devcon    ; User1 BoschLT ; Buyer Lithuania 1    ; lt                      ; EUR                     ; $lithuaniaBuyer1Company_id    ;
                       ; $latviaBuyer1Company_user_id@devcon       ; $latviaBuyer1Company_user_id@devcon       ; User1 BoschLV ; Buyer Latvia 1       ; lv                      ; EUR                     ; $latviaBuyer1Company_id       ;
                       ; $norwaySeller1Company_user_id@devcon      ; $norwaySeller1Company_user_id@devcon      ; User1 BoschNO ; Seller Norway 1      ; no                      ; EUR                     ; $norwaySeller1Company_id      ;
                       ; $norwayBuyer1Company_user_id@devcon       ; $norwayBuyer1Company_user_id@devcon       ; User1 BoschNO ; Buyer Norway 1       ; no                      ; EUR                     ; $norwayBuyer1Company_id       ;
                       ; $swedenSeller1Company_user_id@devcon      ; $swedenSeller1Company_user_id@devcon      ; User1 BoschSE ; Seller Sweden 1      ; sv                      ; EUR                     ; $swedenSeller1Company_id      ;
                       ; $swedenBuyer1Company_user_id@devcon       ; $swedenBuyer1Company_user_id@devcon       ; User1 BoschSE ; Buyer Sweden 1       ; sv                      ; EUR                     ; $swedenBuyer1Company_id       ;
                       ; $denmarkSeller1Company_user_id@devcon     ; $denmarkSeller1Company_user_id@devcon     ; User1 BoschDK ; Seller Denmark 1     ; da                      ; EUR                     ; $denmarkSeller1Company_id     ;
                       ; $denmarkBuyer1Company_user_id@devcon      ; $denmarkBuyer1Company_user_id@devcon      ; User1 BoschDK ; Buyer Denmark 1      ; da                      ; EUR                     ; $denmarkBuyer1Company_id      ;
                       ; $icelandBuyer1Company_user_id@devcon      ; $icelandBuyer1Company_user_id@devcon      ; User1 BoschIS ; Buyer Iceland 1      ; da                      ; EUR                     ; $icelandBuyer1Company_id      ;
                       ; $franceSeller1Company_user_id@devcon      ; $franceSeller1Company_user_id@devcon      ; User1 BoschFR ; Seller France 1      ; fr                      ; EUR                     ; $franceSeller1Company_id      ;
                       ; $franceBuyer1Company_user_id@devcon       ; $franceBuyer1Company_user_id@devcon       ; User1 BoschFR ; Buyer France 1       ; fr                      ; EUR                     ; $franceBuyer1Company_id       ;
                       ; $belgiumSeller1Company_user_id@devcon     ; $belgiumSeller1Company_user_id@devcon     ; User1 BoschBE ; Seller Belgium 1     ; fr                      ; EUR                     ; $belgiumSeller1Company_id     ;
                       ; $belgiumBuyer1Company_user_id@devcon      ; $belgiumBuyer1Company_user_id@devcon      ; User1 BoschBE ; Buyer Belgium 1      ; fr                      ; EUR                     ; $belgiumBuyer1Company_id      ;
                       ; $netherlandsSeller1Company_user_id@devcon ; $netherlandsSeller1Company_user_id@devcon ; User1 BoschNL ; Seller Netherlands 1 ; nl                      ; EUR                     ; $netherlandsSeller1Company_id ;
                       ; $netherlandsBuyer1Company_user_id@devcon  ; $netherlandsBuyer1Company_user_id@devcon  ; User1 BoschNL ; Buyer Netherlands 1  ; nl                      ; EUR                     ; $netherlandsBuyer1Company_id  ;
                       ; $spainSeller1Company_user_id@devcon       ; $spainSeller1Company_user_id@devcon       ; User1 BoschES ; Seller Spain 1       ; es                      ; EUR                     ; $spainSeller1Company_id       ;
                       ; $spainBuyer1Company_user_id@devcon        ; $spainBuyer1Company_user_id@devcon        ; User1 BoschES ; Buyer Spain 1        ; es                      ; EUR                     ; $spainBuyer1Company_id        ;
                       ; $germanySeller1Company_user_id@devcon     ; $germanySeller1Company_user_id@devcon     ; User1 BoschDE ; Seller Germany 1     ; de                      ; EUR                     ; $germanySeller1Company_id     ;
                       ; $germanyBuyer1Company_user_id@devcon      ; $germanyBuyer1Company_user_id@devcon      ; User1 BoschDE ; Buyer Germany 1      ; de                      ; EUR                     ; $germanyBuyer1Company_id      ;
                       ; $greeceSeller1Company_user_id@devcon      ; $greeceSeller1Company_user_id@devcon      ; User1 BoschGR ; Seller Greece 1      ; el                      ; EUR                     ; $greeceSeller1Company_id      ;
                       ; $greeceBuyer1Company_user_id@devcon       ; $greeceBuyer1Company_user_id@devcon       ; User1 BoschGR ; Buyer Greece 1       ; el                      ; EUR                     ; $greeceBuyer1Company_id       ;
                       ; $cyprusBuyer1Company_user_id@devcon       ; $cyprusBuyer1Company_user_id@devcon       ; User1 BoschCY ; Buyer Cyprus 1       ; el                      ; EUR                     ; $cyprusBuyer1Company_id       ;
                       ; $maltaBuyer1Company_user_id@devcon        ; $maltaBuyer1Company_user_id@devcon        ; User1 BoschMT ; Buyer Malta 1        ; en                      ; EUR                     ; $maltaBuyer1Company_id        ;


INSERT_UPDATE Integrator; originalUid[unique = true]              ; uid[unique = true]                      ; developer(uid)                            ; name                       ; description               ; sessionLanguage(isocode); sessionCurrency(isocode); company(uid)                  ; groups(uid[default = $integratorGroup]); loginDisabled[default = false]; password[default = $defaultPassword]
                        ; $portugalSeller1Company_user_id@shop    ; $portugalSeller1Company_user_id@shop    ; $portugalSeller1Company_user_id@devcon    ; Seller Portugal BoschAA    ; Bosch AA Seller User 1    ; de                      ; EUR                     ; $portugalSeller1Company_id    ;
                        ; $portugalBuyer1Company_user_id@shop     ; $portugalBuyer1Company_user_id@shop     ; $portugalBuyer1Company_user_id@devcon     ; Buyer1 Portugal BoschAA    ; Bosch AA Buyer User 1     ; de                      ; EUR                     ; $portugalBuyerCompany_id      ;
                        ; $croatiaSeller1Company_user_id@shop     ; $croatiaSeller1Company_user_id@shop     ; $croatiaSeller1Company_user_id@devcon     ; Seller Croatia BoschAA     ; Bosch AA Seller User 1    ; hr                      ; EUR                     ; $croatiaSeller1Company_id     ;
                        ; $croatiaBuyer1Company_user_id@shop      ; $croatiaBuyer1Company_user_id@shop      ; $croatiaBuyer1Company_user_id@devcon      ; Buyer1 Croatia BoschAA     ; Bosch AA Buyer User 1     ; hr                      ; EUR                     ; $croatiaBuyer1Company_id      ;
                        ; $sloveniaBuyer1Company_user_id@shop     ; $sloveniaBuyer1Company_user_id@shop     ; $sloveniaBuyer1Company_user_id@devcon     ; Buyer1 Slovenia BoschAA    ; Bosch AA Buyer User 1     ; sl                      ; EUR                     ; $sloveniaBuyer1Company_id     ;
                        ; $finlandSeller1Company_user_id@shop     ; $finlandSeller1Company_user_id@shop     ; $finlandSeller1Company_user_id@devcon     ; Seller Finland BoschAA     ; Bosch AA Seller FI User 1 ; fi                      ; EUR                     ; $finlandSeller1Company_id     ;
                        ; $finlandBuyer1Company_user_id@shop      ; $finlandBuyer1Company_user_id@shop      ; $finlandBuyer1Company_user_id@devcon      ; Buyer1 Finland BoschAA     ; Bosch AA Buyer FI User 1  ; fi                      ; EUR                     ; $finlandBuyer1Company_id      ;
                        ; $estoniaBuyer1Company_user_id@shop      ; $estoniaBuyer1Company_user_id@shop      ; $estoniaBuyer1Company_user_id@devcon      ; Buyer1 Estonia BoschAA     ; Bosch AA Buyer EE User 1  ; et                      ; EUR                     ; $estoniaBuyer1Company_id      ;
                        ; $lithuaniaBuyer1Company_user_id@shop    ; $lithuaniaBuyer1Company_user_id@shop    ; $lithuaniaBuyer1Company_user_id@devcon    ; Buyer1 Lithuania BoschAA   ; Bosch AA Buyer LT User 1  ; lt                      ; EUR                     ; $lithuaniaBuyer1Company_id    ;
                        ; $latviaBuyer1Company_user_id@shop       ; $latviaBuyer1Company_user_id@shop       ; $latviaBuyer1Company_user_id@devcon       ; Buyer1 Latvia BoschAA      ; Bosch AA Buyer LV User 1  ; lv                      ; EUR                     ; $latviaBuyer1Company_id       ;
                        ; $norwaySeller1Company_user_id@shop      ; $norwaySeller1Company_user_id@shop      ; $norwaySeller1Company_user_id@devcon      ; Seller Norway BoschAA      ; Bosch AA Seller NO User 1 ; no                      ; EUR                     ; $norwaySeller1Company_id      ;
                        ; $norwayBuyer1Company_user_id@shop       ; $norwayBuyer1Company_user_id@shop       ; $norwayBuyer1Company_user_id@devcon       ; Buyer1 Norway BoschAA      ; Bosch AA Buyer NO User 1  ; no                      ; EUR                     ; $norwayBuyer1Company_id       ;
                        ; $swedenSeller1Company_user_id@shop      ; $swedenSeller1Company_user_id@shop      ; $swedenSeller1Company_user_id@devcon      ; Seller Sweden BoschAA      ; Bosch AA Seller SE User 1 ; sv                      ; EUR                     ; $swedenSeller1Company_id      ;
                        ; $swedenBuyer1Company_user_id@shop       ; $swedenBuyer1Company_user_id@shop       ; $swedenBuyer1Company_user_id@devcon       ; Buyer1 Sweden BoschAA      ; Bosch AA Buyer SE User 1  ; sv                      ; EUR                     ; $swedenBuyer1Company_id       ;
                        ; $denmarkSeller1Company_user_id@shop     ; $denmarkSeller1Company_user_id@shop     ; $denmarkSeller1Company_user_id@devcon     ; Seller Denmark BoschAA     ; Bosch AA Seller DK User 1 ; da                      ; EUR                     ; $denmarkSeller1Company_id     ;
                        ; $denmarkBuyer1Company_user_id@shop      ; $denmarkBuyer1Company_user_id@shop      ; $denmarkBuyer1Company_user_id@devcon      ; Buyer1 Denmark BoschAA     ; Bosch AA Buyer DK User 1  ; da                      ; EUR                     ; $denmarkBuyer1Company_id      ;
                        ; $icelandBuyer1Company_user_id@shop      ; $icelandBuyer1Company_user_id@shop      ; $icelandBuyer1Company_user_id@devcon      ; Buyer1 Iceland BoschAA     ; Bosch AA Buyer IS User 1  ; da                      ; EUR                     ; $icelandBuyer1Company_id      ;
                        ; $franceSeller1Company_user_id@shop      ; $franceSeller1Company_user_id@shop      ; $franceSeller1Company_user_id@devcon      ; Seller France BoschAA      ; Bosch AA Seller FR User 1 ; fr                      ; EUR                     ; $franceSeller1Company_id      ;
                        ; $franceBuyer1Company_user_id@shop       ; $franceBuyer1Company_user_id@shop       ; $franceBuyer1Company_user_id@devcon       ; Buyer1 France BoschAA      ; Bosch AA Buyer FR User 1  ; fr                      ; EUR                     ; $franceBuyer1Company_id       ;
                        ; $belgiumSeller1Company_user_id@shop     ; $belgiumSeller1Company_user_id@shop     ; $belgiumSeller1Company_user_id@devcon     ; Seller Belgium BoschAA     ; Bosch AA Seller BE User 1 ; fr                      ; EUR                     ; $belgiumSeller1Company_id     ;
                        ; $belgiumBuyer1Company_user_id@shop      ; $belgiumBuyer1Company_user_id@shop      ; $belgiumBuyer1Company_user_id@devcon      ; Buyer1 Belgium BoschAA     ; Bosch AA Buyer BE User 1  ; fr                      ; EUR                     ; $belgiumBuyer1Company_id      ;
                        ; $netherlandsSeller1Company_user_id@shop ; $netherlandsSeller1Company_user_id@shop ; $netherlandsSeller1Company_user_id@devcon ; Seller Netherlands BoschAA ; Bosch AA Seller NL User 1 ; nl                      ; EUR                     ; $netherlandsSeller1Company_id ;
                        ; $netherlandsBuyer1Company_user_id@shop  ; $netherlandsBuyer1Company_user_id@shop  ; $netherlandsBuyer1Company_user_id@devcon  ; Buyer1 Netherlands BoschAA ; Bosch AA Buyer NL User 1  ; nl                      ; EUR                     ; $netherlandsBuyer1Company_id  ;
                        ; $spainSeller1Company_user_id@shop       ; $spainSeller1Company_user_id@shop       ; $spainSeller1Company_user_id@devcon       ; Seller Spain BoschAA       ; Bosch AA Seller ES User 1 ; es                      ; EUR                     ; $spainSeller1Company_id       ;
                        ; $spainBuyer1Company_user_id@shop        ; $spainBuyer1Company_user_id@shop        ; $spainBuyer1Company_user_id@devcon        ; Buyer1 Spain BoschAA       ; Bosch AA Buyer ES User 1  ; es                      ; EUR                     ; $spainBuyer1Company_id        ;
                        ; $germanySeller1Company_user_id@shop     ; $germanySeller1Company_user_id@shop     ; $germanySeller1Company_user_id@devcon     ; Seller Germany BoschAA     ; Bosch AA Seller DE User 1 ; de                      ; EUR                     ; $germanySeller1Company_id     ;
                        ; $germanyBuyer1Company_user_id@shop      ; $germanyBuyer1Company_user_id@shop      ; $germanyBuyer1Company_user_id@devcon      ; Buyer1 Germany BoschAA     ; Bosch AA Buyer DE User 1  ; de                      ; EUR                     ; $germanyBuyer1Company_id      ;
                        ; $greeceSeller1Company_user_id@shop      ; $greeceSeller1Company_user_id@shop      ; $greeceSeller1Company_user_id@devcon      ; Seller Greece BoschAA      ; Bosch AA Seller GR User 1 ; el                      ; EUR                     ; $greeceSeller1Company_id      ;
                        ; $greeceBuyer1Company_user_id@shop       ; $greeceBuyer1Company_user_id@shop       ; $greeceBuyer1Company_user_id@devcon       ; Buyer1 Greece BoschAA      ; Bosch AA Buyer GR User 1  ; el                      ; EUR                     ; $greeceBuyer1Company_id       ;
                        ; $cyprusBuyer1Company_user_id@shop       ; $cyprusBuyer1Company_user_id@shop       ; $cyprusBuyer1Company_user_id@devcon       ; Buyer1 Cyprus BoschAA      ; Bosch AA Buyer CY User 1  ; el                      ; EUR                     ; $cyprusBuyer1Company_id       ;
                        ; $maltaBuyer1Company_user_id@shop        ; $maltaBuyer1Company_user_id@shop        ; $maltaBuyer1Company_user_id@devcon        ; Buyer1 Malta BoschAA       ; Bosch AA Buyer MT User 1  ; en                      ; EUR                     ; $maltaBuyer1Company_id        ;


INSERT_UPDATE DpgSellerAccount; accountId[unique = true]               ; company(uid)                  ; user(uid)                                 ; paymentProvider(code); billingSystemStatus(code); status(code); dpgStatus(code)      ;
                              ; "completedPortugalSellerDpgAccount"    ; $portugalSeller1Company_id    ; $portugalSeller1Company_user_id@devcon    ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedCroatiaSellerDpgAccount"     ; $croatiaSeller1Company_id     ; $croatiaSeller1Company_user_id@devcon     ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedFinlandSellerDpgAccount"     ; $finlandSeller1Company_id     ; $finlandSeller1Company_user_id@devcon     ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedNorwaySellerDpgAccount"      ; $norwaySeller1Company_id      ; $norwaySeller1Company_user_id@devcon      ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedSwedenSellerDpgAccount"      ; $swedenSeller1Company_id      ; $swedenSeller1Company_user_id@devcon      ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedDenmarkSellerDpgAccount"     ; $denmarkSeller1Company_id     ; $denmarkSeller1Company_user_id@devcon     ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedFranceSellerDpgAccount"      ; $franceSeller1Company_id      ; $franceSeller1Company_user_id@devcon      ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedBelgiumSellerDpgAccount"     ; $belgiumSeller1Company_id     ; $belgiumSeller1Company_user_id@devcon     ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedNetherlandsSellerDpgAccount" ; $netherlandsSeller1Company_id ; $netherlandsSeller1Company_user_id@devcon ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedSpainSellerDpgAccount"       ; $spainSeller1Company_id       ; $spainSeller1Company_user_id@devcon       ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedGermanySellerDpgAccount"     ; $germanySeller1Company_id     ; $germanySeller1Company_user_id@devcon     ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;
                              ; "completedGreeceSellerDpgAccount"      ; $greeceSeller1Company_id      ; $greeceSeller1Company_user_id@devcon      ; DPG                  ; NEW                      ; ACTIVE      ; ACCOUNT_ID_GENERATED ;


INSERT_UPDATE AdyenSellerAccount; accountId[unique = true]; company(uid)              ; user(uid)                             ; paymentProvider(code); billingSystemStatus(code); status(code);
                                ; "MA_DE_BOSS_EIL"        ; $germanySeller1Company_id ; $germanySeller1Company_user_id@devcon ; ADYEN                ; NEW                      ; ACTIVE      ;


# Groups to be used in the Prod catalog. These groups need to be created beforehand in order to be assigned to app licenses.
INSERT_UPDATE UserGroup; uid[unique = true];
                       ; IDW000            ;
                       ; WD0000            ;
                       ; WD0001            ;
                       ; WD0002            ;
                       ; WD0003            ;
                       ; WD0004            ;
                       ; WD0005            ;
                       ; WD0006            ;
                       ; BCS000            ;
                       ; BDS000            ;
                       ; BDC000            ;
                       ; BM0000            ;
                       ; AC0000            ;
                       ; AT0000            ;
                       ; KA0001            ;
                       ; KA0002            ;
                       ; KA0003            ;
                       ; KA0004            ;
                       ; KA0005            ;
                       ; KA0006            ;
                       ; KA0007            ;
                       ; KA0008            ;
                       ; KA0009            ;
                       ; KA0010            ;
                       ; KA0011            ;
                       ; KA0012            ;
                       ; KA0013            ;
                       ; KA0014            ;
                       ; KA0015            ;
                       ; KA0016            ;
                       ; KA0017            ;
                       ; KA0018            ;
                       ; KA0019            ;
                       ; KA0020            ;
                       ; KA0021            ;
                       ; KA0022            ;
                       ; KA0023            ;
                       ; KA0024            ;
                       ; KA0025            ;
                       ; KA0026            ;
                       ; AUT000            ;
                       ; ISP000            ;


UPDATE IotCompany; uid[unique = true]           ; aaCustomerGroup(uid); groups(uid)[mode = merge]
                 ; $portugalBuyerCompany_id     ; IDW000              ; IDW000
                 ; $croatiaBuyer1Company_id     ; IDW000              ; IDW000
                 ; $sloveniaBuyer1Company_id    ; IDW000              ; IDW000
                 ; $finlandBuyer1Company_id     ; WD0001              ; WD0001
                 ; $estoniaBuyer1Company_id     ; WD0001              ; WD0001
                 ; $lithuaniaBuyer1Company_id   ; WD0001              ; WD0001
                 ; $latviaBuyer1Company_id      ; WD0001              ; WD0001
                 ; $norwayBuyer1Company_id      ; WD0001              ; WD0001
                 ; $swedenBuyer1Company_id      ; WD0001              ; WD0001
                 ; $denmarkBuyer1Company_id     ; WD0001              ; WD0001
                 ; $franceBuyer1Company_id      ; IDW000              ; IDW000
                 ; $belgiumBuyer1Company_id     ; IDW000              ; IDW000
                 ; $netherlandsBuyer1Company_id ; IDW000              ; IDW000
                 ; $spainBuyer1Company_id       ; IDW000              ; IDW000
                 ; $germanyBuyer1Company_id     ; IDW000              ; IDW000
                 ; $greeceBuyer1Company_id      ; IDW000              ; IDW000
                 ; $cyprusBuyer1Company_id      ; IDW000              ; IDW000
                 ; $maltaBuyer1Company_id       ; IDW000              ; IDW000


INSERT_UPDATE SellerContact; country(isocode)[unique = true]; name                          ; email                           ; phone
                           ; AT                             ; ** ESI[tronic] service line   ; <EMAIL> ; +491805011140
                           ; PT                             ; Assistência Técnica Automóvel ; <EMAIL>       ; +351 *********
                           ; HR                             ; Podrška u prodaji             ; <EMAIL>            ; +385(1)2958-022
                           ; SI                             ; Prodajna podpora              ; <EMAIL>            ; +385(1)2958-022
                           ; FI                             ; Myyntituki                    ; <EMAIL>            ; +45 44 89 83 10
                           ; EE                             ; Müügitugi                     ; <EMAIL>            ; +45 44 89 83 10
                           ; LT                             ; Pardavimų Pagalba             ; <EMAIL>            ; +45 44 89 83 10
                           ; LV                             ; Pārdošanas atbalsts           ; <EMAIL>            ; +45 44 89 83 10
                           ; FR                             ; Soutien aux ventes            ; <EMAIL>            ; +32 454 62 70 42
                           ; BE                             ; Soutien aux ventes            ; <EMAIL>           ; +32 459 49 97 02
                           ; NL                             ; Verkoopondersteuning          ; <EMAIL>       ; +31 970 528 24844
                           ; ES                             ; Soporte de ventas             ; <EMAIL>             ; +34 737 34 11 27


INSERT_UPDATE IotCompany; uid[unique = true]            ; sellerContacts(country(isocode))
                        ; $austriaSeller1Company_id     ; AT
                        ; $portugalSeller1Company_id    ; PT
                        ; $croatiaSeller1Company_id     ; HR, SI
                        ; $finlandSeller1Company_id     ; FI, EE, LT, LV
                        ; $franceSeller1Company_id      ; FR
                        ; $belgiumSeller1Company_id     ; BE
                        ; $netherlandsSeller1Company_id ; NL
                        ; $spainSeller1Company_id       ; ES
