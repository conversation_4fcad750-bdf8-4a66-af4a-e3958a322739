#
# Import the Solr CronJob Trigger configuration for the Product Catalog
#

INSERT_UPDATE SolrIndexerCronJob; code[unique = true]     ; job(code)      ; singleExecutable; sessionLanguage(isocode); active; facetSearchConfig(name); indexerOperation(code)
                                ; full-aa-index-cronJob   ; solrIndexerJob ; false           ; en                      ; false ; AaStoreSolrIndex       ; full
                                ; update-aa-index-cronJob ; solrIndexerJob ; false           ; en                      ; false ; AaStoreSolrIndex       ; update

INSERT_UPDATE Trigger; cronJob(code)[unique = true]; second; minute; hour; day; month; year; relative; active; maxAcceptableDelay
# Run the full index cronJob every hour
                     ; full-aa-index-cronJob       ; 0     ; 0     ; 1   ; -1 ; -1   ; -1  ; true    ; true  ; -1
# Run the update index cronJob every 5 minutes
                     ; update-aa-index-cronJob     ; 0     ; 5     ; -1  ; -1 ; -1   ; -1  ; true    ; true  ; -1
