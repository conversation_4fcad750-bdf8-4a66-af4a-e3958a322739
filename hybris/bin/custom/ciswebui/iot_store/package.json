{"name": "iot_store", "version": "1.0.0", "description": "", "private": true, "scripts": {"test": "jest", "test_command": "echo \"Running test command\" && exit 0", "build_js": "webpack --config webpack.config.js"}, "jest-junit": {"outputDirectory": "./testing/test_output/junit", "outputName": "junit.xml"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@intlify/eslint-plugin-vue-i18n": "^2.0.0", "@intlify/vue-i18n-loader": "^1.1.0", "@mdi/font": "^5.9.55", "@pinia/testing": "^0.1.7", "@types/jest": "^29.5.14", "@types/prettier": "2.7.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vue/test-utils": "^1.3.6", "@vue/vue2-jest": "^29.2.6", "ajv": "^6.12.6", "ajv-cli": "^3.3.0", "autoprefixer": "^10.4.21", "axios-mock-adapter": "^1.22.0", "babel-core": "^7.0.0-bridge", "babel-jest": "^29.7.0", "babel-loader": "^8.4.1", "chalk": "^2.4.2", "check-dependencies": "^1.1.1", "copy-webpack-plugin": "^9.1.0", "css-loader": "^5.2.7", "dateformat": "^3.0.3", "deepmerge": "^4.3.1", "eslint": "^8.57.1", "eslint-codeframe-formatter": "^1.0.2", "eslint-config-prettier": "^8.10.0", "eslint-config-standard-with-typescript": "^33.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^15.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-typescript": "^0.14.0", "eslint-plugin-vue": "^9.32.0", "eslint-webpack-plugin": "^3.2.0", "flush-promises": "^1.0.2", "glob": "^7.2.3", "html-webpack-plugin": "^5.6.3", "identity-obj-proxy": "^3.0.0", "ini": "^1.3.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-image-snapshot": "^6.4.0", "jest-junit": "^16.0.0", "license-checker": "^25.0.1", "license-webpack-plugin": "^2.3.21", "minimist": "^1.2.8", "mkdir": "0.0.2", "nodemon": "3.1.10", "postcss": "^8.5.6", "puppeteer": "1.19.0", "sass": "~1.32.13", "sass-loader": "^10.5.2", "standard": "^14.3.4", "svg-to-vue-component": "^0.3.8", "terser-webpack-plugin": "^5.3.14", "ts-jest": "^29.2.5", "ts-loader": "^8.4.0", "typescript-json-schema": "^0.65.1", "url-loader": "^4.1.1", "vue-eslint-parser": "^9.4.3", "vue-loader": "^15.11.1", "vue-plugin-load-script": "^1.3.6", "vue-template-compiler": "npm:@neverendingsupport/vue2@2.7.22-template-compiler", "vuetify-loader": "^1.9.2", "webpack": "^5.99.9", "webpack-cli": "^4.10.0", "webpack-dev-middleware": "^4.3.0", "webpack-dev-server": "^3.11.3", "yaml-loader": "^0.8.1", "yamljs": "^0.3.0"}, "dependencies": {"@types/animejs": "^3.1.13", "@types/chart.js": "^2.9.41", "@types/filesize": "^4.2.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^12.20.55", "animejs": "^3.2.2", "axios": "^1.10.0", "cd-system": "^6.1.0", "chart.js": "^2.9.4", "color-thief-browser": "^2.0.2", "core-js": "^3.40.0", "filesize": "^4.2.1", "fs-extra": "^10.1.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "pinia": "^2.3.1", "plyr": "^3.7.8", "portal-vue": "^2.1.7", "scrollmonitor": "^1.2.11", "typescript": "^4.9.5", "vue": "npm:@neverendingsupport/vue2@2.7.22", "vue-class-component": "^7.2.6", "vue-gallery-slideshow": "^1.5.2", "vue-i18n": "^8.28.2", "vue-i18n-bridge": "^9.14.4", "vue-plyr": "^6.0.4", "vue-property-decorator": "^9.1.2", "vue-router": "^3.6.5", "vue-runtime-helpers": "^1.1.2", "vue-window-size": "0.6.2", "vuetify": "^2.7.2"}, "packageManager": "yarn@4.6.0", "resolutions": {"postcss": "^8.5.6", "jsdom/tough-cookie": "^4.1.4", "glob-parent": "^5.1.2", "nth-check": "^2.1.1", "word-wrap": "^1.2.5", "fast-json-patch": "^3.1.1", "node-forge": "^1.3.1", "@babel/traverse": "^7.27.4", "vue": "npm:@neverendingsupport/vue2@2.7.22", "@vue/compiler-sfc": "npm:@neverendingsupport/vue2@2.7.22-compiler-sfc", "vue-server-renderer": "npm:@neverendingsupport/vue2@2.7.22-server-renderer", "vue-template-compiler": "npm:@neverendingsupport/vue2@2.7.22-template-compiler"}}