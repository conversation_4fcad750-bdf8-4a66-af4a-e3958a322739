<template>
  <span>
    {{ formattedPrice }}
  </span>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {commercefacades, PriceDTO, PriceFrequency} from 'common/generated-types/types';
import PriceData = commercefacades.PriceData;
import {i18nService} from 'common/services';

@Component
export default class MinPriceDisplay extends Vue {
    @Prop() minPrice!: PriceData | PriceDTO;

    get formattedPrice() {
        const { currencyIso, value, priceFrequency } = this.minPrice;
        const price = this.$n(value, 'price', i18nService.getLocaleForFormats());
        if (priceFrequency === PriceFrequency.YEARLY) {
            const frequency = this.$t('shop.products.perYear');
            return this.$t('shop.products.priceWithFrequency', { currency: currencyIso, price, frequency });
        }
        return this.$t('shop.products.price', { currency: currencyIso, price });
    }
}
</script>

