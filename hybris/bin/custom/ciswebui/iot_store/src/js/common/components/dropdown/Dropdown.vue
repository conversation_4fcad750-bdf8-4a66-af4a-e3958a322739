<template>
    <div v-on-document:click="closeSelectWindow"
         v-on:click.stop class="wrapper">

        <div v-on:click.stop="toggleSelectWindow" class="input justify-lg-end">
            <div v-if="$slots.prefix"><slot name="prefix"></slot></div>
            <div class="selected-text">{{selectedText}}</div>
            <div class="icon">
                <OpenIcon v-if="isOpen"></OpenIcon>
                <CloseIcon v-else></CloseIcon>
            </div>
        </div>
        <fade-transition>
            <div v-show="isOpen" class="select-window" :class="{ 'fixed-mobile' : $vuetify.breakpoint.smAndDown }">
                <slot></slot>
            </div>
        </fade-transition>
    </div>
</template>

<script lang="ts">
    import {Component, Model, Prop, Vue} from 'vue-property-decorator';
    import {FadeTransition} from 'common/components';
    import OpenIcon from 'common/images/boxed/icon-24-dropdown-open.svg';
    import CloseIcon from 'common/images/boxed/icon-24-dropdown-close.svg';

    export type Item = {
        id: number;
        label: string;
    }

    @Component({
        components: {
            OpenIcon,
            CloseIcon,
            FadeTransition
        }
    })
    export default class Dropdown extends Vue {
        @Model('change') readonly selected?: Item;
        @Prop({default: 'Select'}) readonly placeholder!: string;

        isOpen = false;

        toggleSelectWindow(): void {
            this.isOpen = !this.isOpen;
        }

        closeSelectWindow(): void {
            this.isOpen = false;
        }

        mounted(): void {
            this.$on('option-selected', this.handleSelected);
        }

        handleSelected(selected: Item): void {
            this.$emit('change', selected);
            this.closeSelectWindow();
        }

        get selectedText(): string {
            return this.selected && this.selected.label ? this.selected.label : this.placeholder;
        }
    }
</script>

<style lang="scss" scoped>
    @import "common/design";

    .input {
        font-weight: 500;
        font-size: 14px;
        display: flex;
        align-content: center;
        justify-content: flex-start;

        &:hover {
            cursor: pointer;
        }

        .selected-text {
            margin-left: 5px;
        }

        .icon {
            width: 35px;
            display: flex;
            justify-content: center;
            transform: translateY(-3px);
        }
    }

    .wrapper {
        position: relative;
    }

    .select-window {
        padding: 20px;
        display: flex;
        flex-direction: column;
        font-size: 14px;

        margin: 0 16px 16px 0;
        min-width: 180px;

        background-color: var(--v-grey-lighten5);
        border-radius: 0 6px 6px 6px;
        border: 1px solid var(--v-grey-lighten1);
        box-sizing: border-box;
        box-shadow: 1px 8px 16px rgba(176, 192, 237, 0.16), 0 2px 6px rgba(115, 125, 155, 0.16);

        position: absolute;
        bottom: -10px;
        right: -14px;
        transform: translateY(100%);
        z-index: 1;

        &.fixed-mobile {
          left: 0;
        }
    }

    .select-window > :deep(span:last-child) {
        margin-bottom: 0;
    }
</style>
