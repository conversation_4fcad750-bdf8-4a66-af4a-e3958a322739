<template>
  <div class="allCards" v-if="allCards">
    <icon-master></icon-master>
    <icon-visa></icon-visa>
    <icon-amex></icon-amex>
  </div>
  <div class="icon" v-else>
    <icon-amex v-if="paymentType === 'amex'"></icon-amex>
    <icon-bank v-else-if="paymentType === 'bank'"></icon-bank>
    <icon-master v-else-if="paymentType === 'master'"></icon-master>
    <icon-visa v-else-if="paymentType === 'visa'"></icon-visa>
    <icon-manage-by-seller v-else-if="paymentType === 'invoicebyseller'"></icon-manage-by-seller>
    <sepa-logo v-else-if="paymentType === PaymentMethodType.SEPA_DIRECTDEBIT"></sepa-logo>
    <icon-card v-else></icon-card>
  </div>
</template>

<script lang="ts">
  import {Component, Prop, Vue} from 'vue-property-decorator';
  import IconAmex from 'common/images/boxed/amex-logo.svg';
  import IconBank from 'common/images/boxed/bank-transfer.svg';
  import IconCard from 'common/images/boxed/icon-24-dummy-card.svg';
  import IconMaster from 'common/images/boxed/master-card-light.svg';
  import IconVisa from 'common/images/boxed/icon-20-visa-light.svg';
  import IconManageBySeller from 'common/images/boxed/icon-manage-by-seller.svg';
  import SepaLogo from 'common/images/boxed/sepa-logo.svg';
  import {PaymentMethodType} from 'common/generated-types/types';

  @Component({
    computed: {
      PaymentMethodType() {
        return PaymentMethodType;
      }
    },
    components: {
      IconAmex,
      IconBank,
      IconCard,
      IconMaster,
      IconVisa,
      SepaLogo,
      IconManageBySeller,
      PaymentMethodType
    }
  })
  export default class PaymentMethodIcons extends Vue {
    @Prop() paymentType!: string;
    @Prop() allCards!: boolean;
  }
</script>

<style lang="scss" scoped>
    .allCards {
        display: grid;
        grid-auto-flow: column;
        grid-gap: 0 10px;
        align-items: center;
    }
    .icon {
        height: 40px;
        width: 60px;
        svg {
            width: 100%;
            height: 100%;
        }
    }
</style>
