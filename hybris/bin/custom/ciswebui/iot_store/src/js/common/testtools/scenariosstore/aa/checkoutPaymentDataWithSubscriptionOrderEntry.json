{"paymentAddress": {"id": "8820669054999", "title": "", "titleCode": "", "firstName": "", "lastName": "", "companyName": "ESItronic Onboarded Test 1 GmbH", "line1": "Innsbrucker Str.", "line2": "56", "town": "<PERSON><PERSON><PERSON><PERSON>", "region": "", "district": "", "postalCode": "6300", "phone": "", "cellphone": "", "email": "", "country": {"isocode": "AT", "name": "Österreich", "canBuy": false}, "shippingAddress": true, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Innsbrucker Str., 56, <PERSON><PERSON><PERSON><PERSON>, 6300"}, "totalPrice": {"symbol": "EUR", "value": "900.00"}, "totalTax": {"symbol": "EUR", "value": "114.00"}, "totalPriceWithTax": {"symbol": "EUR", "value": "714.00"}, "entries": [{"appCode": "AA_04015100", "productName": "CRIN Bosch Hauptlizenz", "productCode": "AA_04015100_sub", "productUrl": "/p/AA_04015100", "companyName": "<PERSON>", "versionName": "", "licenseName": "Abonnement", "licenseType": "SUBSCRIPTION", "runtime": {"code": "runtime_subs_unlimited"}, "logoUrl": "/sample-data/aa/medium-logo.png", "smallLogoUrl": "/sample-data/aa/logo.png", "itemPrice": {"symbol": "EUR", "value": "300.00"}, "totalPrice": {"symbol": "EUR", "value": "600.00"}, "productFuturePrices": [{"symbol": "EUR", "value": "400.00"}], "quantity": 2, "entryNumber": 0, "scalePrices": [], "bundleInfo": null, "countryEulas": [], "addOnThl": "thlTestUserGroup", "addOnUg": "thlTestUserGroup"}, {"appCode": "AA_04015101", "sellerProductId": "1 687 P15 100", "productName": "Technical Hot Line CRIN Bosch Hauptlizenz", "productCode": "AA_04015101_sub", "productUrl": "/p/AA_04015101", "companyName": "<PERSON>", "versionName": "", "licenseName": "Abonnement", "licenseType": "SUBSCRIPTION", "runtime": {"code": "runtime_subs_unlimited"}, "logoUrl": "/sample-data/aa/medium-logo.png", "smallLogoUrl": "/sample-data/aa/logo.png", "itemPrice": {"symbol": "EUR", "value": "150.00"}, "totalPrice": {"symbol": "EUR", "value": "300.00"}, "quantity": 2, "entryNumber": 0, "scalePrices": [], "bundleInfo": null, "specialOffer": false, "addOnUg": "thlTestUserGroup"}], "showInvoiceNotes": true, "invoiceNoteSizeLimit": 50, "ownAppsPurchase": false, "listOfDistributors": [{"id": "id_1", "name": "Zeta distributor"}, {"id": "id_2", "name": "Alpha distributor"}, {"id": "id_3", "name": "Gamma distributor"}, null, {"id": "id_4", "name": "Beta distributor"}], "checkoutInfos": [{"paymentInfos": [{"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": true, "paymentProvider": "DPG", "enabled": true, "disableReason": "", "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "Deutsche Bank", "bic": "DEUTDEFFVAC"}], "paymentProvider": "DPG", "paymentMethod": "SEPA_CREDIT", "userActionParameters": {}, "userCreatable": false, "savableForReuse": false}]}