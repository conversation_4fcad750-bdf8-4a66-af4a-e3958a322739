<template>
    <div class="message-container">
        <div v-if="toastAlerts.length" class="toast-stack">
            <message-component
                    v-for="(item,i) in toastAlerts"
                    v-bind:key="item.message + i"
                    v-bind:message="item.message"
                    v-bind:toast="true"
                    v-bind:dismissible="item.dismissible"
                    v-bind:timeout="item.timeout"
                    v-bind:error="item.type === AlertType.ERROR"
                    v-bind:warning="item.type === AlertType.WARNING"
                    v-bind:success="item.type === AlertType.SUCCESS"
                    v-on:removealert="removeAlert(item)">
            </message-component>
        </div>
        <div v-if="inlineAlerts.length" class="inline-stack">
            <message-component
                    v-for="(item,i) in inlineAlerts"
                    v-bind:key="item.message + i"
                    v-bind:message="item.message"
                    v-bind:toast="false"
                    v-bind:dismissible="item.dismissible"
                    v-bind:timeout="item.timeout"
                    v-bind:error="item.type === AlertType.ERROR"
                    v-bind:warning="item.type === AlertType.WARNING"
                    v-bind:success="item.type === AlertType.SUCCESS"
                    v-on:removealert="removeAlert(item)">
            </message-component>
        </div>
    </div>
</template>

<script lang="ts">
    import {Component, Vue} from 'vue-property-decorator';
    import {Alert, messageService} from 'common/services';
    import {AlertType} from 'common/constants';
    import MessageComponent from 'common/components/MessageComponent.vue';

    @Component({components: {MessageComponent}})
    export default class AlertDisplay extends Vue {
        alertQueue: Alert[] = [];
        AlertType = AlertType;

        mounted(): void {
            messageService.subscribe(this.alertQueue);
        }

        get toastAlerts(): Alert[] {
            return this.alertQueue.filter(a => a.toast);
        }

        get inlineAlerts(): Alert[] {
            return this.alertQueue.filter(a => !a.toast);
        }

        removeAlert(item: Alert) {
            const idx = this.alertQueue.indexOf(item);
            if (idx >= 0) this.alertQueue.splice(idx, 1);
        }
    }
</script>

<style scoped lang="scss">
  @import "shop/core/constants";

  .toast-stack {
      position: fixed;
      top: $spacing-xl + $spacing-m;
      right: $spacing-m;
      padding-left: $spacing-m;
      display: flex;
      flex-direction: column;
      z-index: 2000;
  }
</style>
