import {breakpoints} from "common/breakpoints";

let pageWait = 1500;

const testExtraLargeScreensize = async function (height, wait = pageWait) {
    await page.setViewport({width: breakpoints.XL, height});
    await page.waitFor(wait);
    expect(await page.screenshot()).toMatchImageSnapshot("extra large size");
};

const testLargeScreensize = async function (height, wait = pageWait) {
    await page.setViewport({width: breakpoints.L, height});
    await page.waitFor(wait);
    expect(await page.screenshot()).toMatchImageSnapshot("large size");
};

const testMediumScreensize = async function (height, wait = pageWait) {
    await page.setViewport({width: breakpoints.M, height});
    await page.waitFor(wait);
    expect(await page.screenshot()).toMatchImageSnapshot("medium size");
};

const testSmallScreensize = async function (height, wait = pageWait) {
    await page.setViewport({width: breakpoints.S, height});
    await page.waitFor(wait);
    expect(await page.screenshot()).toMatchImageSnapshot("small size");
};

const testExtraSmallScreensize = async function (height, wait = pageWait) {
    await page.setViewport({width: breakpoints.XS, height});
    await page.waitFor(wait);
    expect(await page.screenshot()).toMatchImageSnapshot("extra small size");
};

const testAllScreenSizes = async function (height, wait = pageWait) {
    await testExtraLargeScreensize(height, wait);
    await testLargeScreensize(height, wait);
    await testMediumScreensize(height, wait);
    await testSmallScreensize(height, wait);
    await testExtraSmallScreensize(height, wait);
};

export {
    testExtraLargeScreensize,
    testLargeScreensize,
    testMediumScreensize,
    testSmallScreensize,
    testExtraSmallScreensize,
    testAllScreenSizes
}
