import {AlertType} from "common/constants";
import {StoreErrorResponseData, UserMessage, UserMessageLevel} from 'common/generated-types/types';
import VueI18n from "vue-i18n";

export interface Alert {
    message: string;
    type: AlertType;
    toast?: boolean;
    dismissible?: boolean;
    timeout?: number;
}

type SubscriberQueue = Alert[];

const STORAGE_KEY = 'alertMessage';

class UserMessageService {
    static instance: UserMessageService;

    private alertQueue: Alert[] = [];
    private subscriberQueue: SubscriberQueue[] = [];

    constructor() {
        if (UserMessageService.instance) {
            return UserMessageService.instance;
        }

        let item = localStorage.getItem(STORAGE_KEY);
        if (item) {
            this.alertQueue.push(...(JSON.parse(item) as Alert[]));
            localStorage.removeItem(STORAGE_KEY)
        }

        UserMessageService.instance = this;
        return UserMessageService.instance;
    }

    errorResponse(errorResponseData: StoreErrorResponseData, i18n: VueI18n, persistInLocalStorage = false, toast?: boolean, dismissible?: boolean, timeout?: number) {
        if(errorResponseData && errorResponseData.userMessages) {
            this.message(errorResponseData.userMessages, i18n, persistInLocalStorage, toast, dismissible, timeout);
        } else {
            this.error(i18n.t('shop.error.generic.serverError').toString());
        }
    }

    message(messages: UserMessage[] | UserMessage, i18n: VueI18n, persistInLocalStorage = false, toast?: boolean, dismissible?: boolean, timeout?: number) {
        if (!Array.isArray(messages)) {
            messages = [messages];
        }

        messages.forEach(message => {
            const messageText = this.translateOrDefault(message, i18n);
            switch (message.level) {
                case UserMessageLevel.INFO:
                    this.info(messageText, persistInLocalStorage, toast, dismissible, timeout);
                    break;
                case UserMessageLevel.WARNING:
                    this.warning(messageText, persistInLocalStorage, toast, dismissible, timeout);
                    break;
                case UserMessageLevel.ERROR:
                    this.error(messageText, persistInLocalStorage, toast, dismissible, timeout);
                    break;
                default:
                    this.error(messageText, persistInLocalStorage, toast, dismissible, timeout);
                    break;
            }
        });
    }

    error(message: string[] | string, persistInLocalStorage = false, toast?: boolean, dismissible?: boolean, timeout?: number) {
        if (typeof message === 'undefined') {
            return;
        }
        if (typeof message === 'string') {
            message = [message];
        }
        let alerts = message.map(item => {
            return {
                message: item,
                type: AlertType.ERROR,
                toast: toast,
                dismissible: dismissible,
                timeout: timeout,
            };
        });
        this.alert(alerts, persistInLocalStorage);
    }

    warning(message: string[] | string, persistInLocalStorage = false, toast?: boolean, dismissible?: boolean, timeout?: number) {
        if (typeof message === 'undefined') {
            return;
        }
        if (typeof message === 'string') {
            message = [message];
        }
        let alerts = message.map(item => {
            return {
                message: item,
                type: AlertType.WARNING,
                toast: toast,
                dismissible: dismissible,
                timeout: timeout,
            };
        });
        this.alert(alerts, persistInLocalStorage);
    }

    info(message: string[] | string, persistInLocalStorage = false, toast?: boolean, dismissible?: boolean, timeout?: number) {
        if (typeof message === 'undefined') {
            return;
        }
        if (typeof message === 'string') {
            message = [message];
        }
        let alerts = message.map(item => {
            return {
                message: item,
                type: AlertType.INFO,
                toast: toast,
                dismissible: dismissible,
                timeout: timeout,
            };
        });
        this.alert(alerts, persistInLocalStorage);
    }

    success(message: string[] | string, persistInLocalStorage = false, toast?: boolean, dismissible?: boolean, timeout?: number) {
        if (typeof message === 'undefined') {
            return;
        }
        if (typeof message === 'string') {
            message = [message];
        }
        let alerts = message.map(item => {
            return {
                message: item,
                type: AlertType.SUCCESS,
                toast: toast,
                dismissible: dismissible,
                timeout: timeout,
            };
        });
        this.alert(alerts, persistInLocalStorage);
    }

    translateOrDefault(message: UserMessage, i18n: VueI18n): string {
        debugger
        const messageKey = `shop.error.${message.code}`;
        const translationFound = message.code && i18n.te(messageKey)
            || (!!i18n.fallbackLocale && i18n.te(messageKey, i18n.fallbackLocale as string))
        if (translationFound) {
            return i18n.t(messageKey).toString();
        }
        return message.fallbackMessage;
    }

    private alert(alerts: Alert[], persistInLocalStorage: boolean) {
        if (persistInLocalStorage) {
            localStorage.setItem(STORAGE_KEY, JSON.stringify(alerts));
            return;
        }

        this.alertQueue.push(...alerts);
        for (let subscriber of this.subscriberQueue) {
            subscriber.push(...alerts);
        }
        window.scrollTo(0, 0);
    }

    clearAll() {
        if (this.alertQueue !== undefined) {
            this.alertQueue.splice(0, this.alertQueue.length);
        }

        if (this.subscriberQueue && this.subscriberQueue.length > 0) {
            for (let subscriber of this.subscriberQueue) {
                subscriber.splice(0, subscriber.length);
            }
        }
    }

    subscribe(subscribtionQueue: SubscriberQueue) {
        subscribtionQueue.push(...this.alertQueue);
        this.subscriberQueue.push(subscribtionQueue);
    }
}

const instance = new UserMessageService();

Object.freeze(instance);
export {instance as messageService};
