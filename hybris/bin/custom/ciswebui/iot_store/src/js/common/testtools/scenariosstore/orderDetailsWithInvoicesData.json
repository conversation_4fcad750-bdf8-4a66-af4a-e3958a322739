{"paymentAddress": {"id": "8796289925143", "title": null, "titleCode": null, "firstName": "<PERSON><PERSON>", "lastName": "User", "companyName": null, "line1": "Sample Street", "line2": "123", "town": "Sample Town", "region": null, "district": null, "postalCode": "12345", "phone": "+1234567890", "cellphone": null, "email": "<EMAIL>", "country": {"isocode": "DE", "name": "Germany", "currencyIsoCode": null, "canBuy": false, "sepaEnabled": false}, "shippingAddress": false, "billingAddress": true, "defaultAddress": false, "visibleInAddressBook": true, "formattedAddress": "Sample Street, 123, 12345, Sample Town, Germany"}, "totalPrice": {"symbol": "EUR", "value": "16,445.00"}, "totalTax": {"symbol": "EUR", "value": "3,124.55"}, "paymentMethod": "CREDIT_CARD", "totalPriceWithTax": {"symbol": "EUR", "value": "19,569.55"}, "entries": [{"appCode": "A_00001007", "productName": "Smoke App", "productCode": "A_00001007_full", "productUrl": "/p/A_00001007", "companyName": "De<PERSON> Apps", "versionName": "1.0.0", "licenseName": "Purchase", "licenseType": "FULL", "logoUrl": "/sample-data/logo.svg", "smallLogoUrl": "/sample-data/logo_small.jpeg", "itemPrice": {"symbol": "EUR", "value": "299.00"}, "totalPrice": {"symbol": "EUR", "value": "16,445.00"}, "quantity": 55}, {"appCode": "A_00001002", "productName": "Number Plate Reader", "productCode": "A_00001002_full", "productUrl": "/p/A_00001002", "companyName": "De<PERSON> Apps", "versionName": "8.0.0", "licenseName": "Purchase", "licenseType": "FULL", "logoUrl": "/sample-data/logo.svg", "smallLogoUrl": "/sample-data/logo_small.jpeg", "itemPrice": {"symbol": "EUR", "value": "89.00"}, "totalPrice": {"symbol": "EUR", "value": "445.00"}, "quantity": 5, "entryNumber": 1, "scalePrices": [], "sellerProductId": "00001012P12840"}], "placedBy": "<PERSON>", "ownAppsPurchase": false, "code": "00004003", "status": "completed", "date": "2022-03-01T20:15:53.879Z", "invoices": [{"externalId": "2510797230576634", "documentUrl": "/shop/order/invoices/invoice_2510797230576634_f1781d33-2922-4a74-8a13-f324888d511d", "displayName": "20220411_2510797230576634 invoice", "netAmount": {"symbol": "EUR", "value": "100.00"}, "taxAmount": {"symbol": "EUR", "value": "19.00"}, "grossAmount": {"symbol": "EUR", "value": "119.00"}, "creationTime": "2022-04-11T14:45:27.174Z", "invoiceStatus": "REFUNDED", "invoiceDate": "2022-04-11T00:00:00.000Z", "creditNotes": [{"externalId": "2510797230576635", "documentUrl": "/shop/order/invoices/invoice_2510797230576635_9ff40853-33e1-4f5a-a864-91709a60fb56", "displayName": "2510797230576635 credit note", "netAmount": {"symbol": "EUR", "value": "-50.00"}, "taxAmount": {"symbol": "EUR", "value": "-9.50"}, "grossAmount": {"symbol": "EUR", "value": "-59.50"}, "issuanceDate": "2022-04-12T00:00:00.000Z", "creationTime": "2022-04-12T13:27:13.000Z", "creditNoteType": "PARTIAL_REFUND"}, {"externalId": "2510797230576636", "documentUrl": "/shop/order/invoices/invoice_2510797230576636_9ff40853-33e1-4f5a-a864-91709a60fb56", "displayName": "2510797230576636 credit note", "netAmount": {"symbol": "EUR", "value": "-50.00"}, "taxAmount": {"symbol": "EUR", "value": "-9.50"}, "grossAmount": {"symbol": "EUR", "value": "-59.50"}, "issuanceDate": "2022-04-13T00:00:00.000Z", "creationTime": "2022-04-13T14:27:13.000Z", "creditNoteType": "PARTIAL_REFUND"}]}, {"externalId": "2510797230566635", "documentUrl": "/shop/order/invoices/invoice_2510797230566635_6a7ec485-8e19-493f-9a84-898bb3d7bb5a", "displayName": "20220410_2510797230566635 invoice", "netAmount": {"symbol": "EUR", "value": "100.00"}, "taxAmount": {"symbol": "EUR", "value": "19.00"}, "grossAmount": {"symbol": "EUR", "value": "119.00"}, "creationTime": "2022-04-10T12:20:01.305Z", "invoiceStatus": "REVERSED", "invoiceDate": "2022-04-10T00:00:00.000Z", "creditNotes": [{"externalId": "2510797230566636", "documentUrl": "/shop/order/invoices/invoice_2510797230566636_9ff40853-33e1-4f5a-a864-91709a60fb56", "displayName": "2510797230566636 credit note", "netAmount": {"symbol": "EUR", "value": "100.00"}, "taxAmount": {"symbol": "EUR", "value": "19.00"}, "grossAmount": {"symbol": "EUR", "value": "119.00"}, "issuanceDate": "2022-04-11T00:00:00.000Z", "creationTime": "2022-04-11T14:27:13.000Z", "creditNoteType": "REVERSAL"}]}, {"externalId": "2510797230556635", "documentUrl": "/shop/order/invoices/invoice_2510797230556635_6a7ec485-8e19-493f-9a84-898bb3d7bb5a", "displayName": "20220409_2510797230556635 invoice", "netAmount": {"symbol": "EUR", "value": "100.00"}, "taxAmount": {"symbol": "EUR", "value": "19.00"}, "grossAmount": {"symbol": "EUR", "value": "119.00"}, "creationTime": "2022-04-09T12:20:01.305Z", "invoiceStatus": "PENDING", "invoiceDate": "2022-04-09T00:00:00.000Z", "creditNotes": []}, {"externalId": "2510797230546635", "documentUrl": "/shop/order/invoices/invoice_2510797230546635_6a7ec485-8e19-493f-9a84-898bb3d7bb5a", "displayName": "20220408_2510797230546635 invoice", "netAmount": {"symbol": "EUR", "value": "100.00"}, "taxAmount": {"symbol": "EUR", "value": "19.00"}, "grossAmount": {"symbol": "EUR", "value": "119.00"}, "creationTime": "2022-04-08T12:20:01.305Z", "invoiceStatus": "PAID", "invoiceDate": "2022-04-08T00:00:00.000Z", "creditNotes": []}, {"externalId": "2510797230536635", "documentUrl": "/shop/order/invoices/invoice_2510797230536635_6a7ec485-8e19-493f-9a84-898bb3d7bb5a", "displayName": "20220307_2510797230536635 invoice", "netAmount": {"symbol": "EUR", "value": "100.00"}, "taxAmount": {"symbol": "EUR", "value": "19.00"}, "grossAmount": {"symbol": "EUR", "value": "119.00"}, "creationTime": "2022-03-07T12:20:01.305Z", "invoiceStatus": "OVERDUE", "invoiceDate": "2022-03-07T00:00:00.000Z", "creditNotes": []}]}