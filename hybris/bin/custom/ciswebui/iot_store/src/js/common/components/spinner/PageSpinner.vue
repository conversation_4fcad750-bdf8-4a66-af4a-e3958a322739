<template>
    <div class="spinner" ref="root">
        <div v-if="active" class="loader">
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
        </div>
        <div class="msg" v-if="msg && msg !== ''">
            <div><p>{{msg}}</p></div>
        </div>
    </div>
</template>

<script lang="ts">
    import {Component, Vue} from 'vue-property-decorator';
    import anime from 'animejs';
    import def from 'common/defaults';

    @Component
    export default class PageSpinner extends Vue {
        active = false;
        fadeIn?: anime.AnimeInstance;
        fadeInTimer?: number;
        fadeOut?: anime.AnimeInstance;
        fadeOutTimer?: number;
        forceRendering = 0;
        triggerStopCallback?: () => void;
        msg = '';

        $refs!: {
            root: HTMLElement;
        };

        start(delay: number, msg: string): void {
            if (this.active) {
                if (!!this.triggerStopCallback || !!this.fadeOut || !!this.fadeOutTimer) {
                    this.abortStop(msg);
                }
                return;
            }
            this.active = true;

            if (delay > 0) {
                this.fadeInTimer = window.setTimeout(this.performStart.bind(this, msg), delay);
            } else {
                this.performStart(msg);
            }
        }

        private abortStop(msg: string): void {
            if (this.triggerStopCallback) {
                delete this.triggerStopCallback;
                this.msg = msg;
            }
            if (this.fadeOutTimer) {
                clearTimeout(this.fadeOutTimer);
                delete this.fadeOutTimer;
                this.msg = msg;
            }
            if (this.fadeOut) {
                this.fadeOut.pause();
                delete this.fadeOut;
                this.performStart(msg);
            }
        }

        private performStart(msg: string): void {
            this.msg = msg;
            this.fadeIn = anime({
                targets: this.$refs.root,
                opacity: 1,
                duration: 150,
                changeBegin: () => {
                    this.$refs.root.style.display = 'block';
                    this.forceRendering = this.$refs.root.offsetHeight;
                },
                begin: () => {
                    delete this.fadeInTimer;
                },
                complete: () => {
                    delete this.fadeIn;
                    if (typeof this.triggerStopCallback === 'function') {
                        this.triggerStopCallback();
                    }
                },
                easing: def.animation.easeout
            });
        }

        stop(delay: number, killRunningStart: boolean): void {
            if (this.fadeOut || this.fadeOutTimer || !this.active) {
                return;
            }

            if(killRunningStart && this.fadeInTimer) {
                this.abortFadeInTimer();
                return;
            }

            if(this.fadeIn || this.fadeInTimer) {
                if (!this.triggerStopCallback) {
                    this.triggerStopCallback = () => {
                        this.stop(delay, killRunningStart);
                    };
                }
                return;
            }

            if (delay > 0) {
                this.fadeOutTimer = window.setTimeout(this.performStop.bind(this), delay);
            }
            else {
                this.performStop();
            }
        }

        private abortFadeInTimer(): void {
            clearTimeout(this.fadeInTimer);
            delete this.fadeInTimer;
            this.active = false;
            this.$refs.root.style.display = 'none';
            this.$forceUpdate();
        }

        performStop(): void {
            this.fadeOut = anime({
                targets: this.$refs.root,
                opacity: 0,
                duration: 150,
                easing: def.animation.easeout,
                begin: () => {
                    delete this.fadeOutTimer;
                },
                complete: () => {
                    this.$refs.root.style.display = 'none';
                    this.msg = '';
                    this.active = false;
                    delete this.fadeOut;
                }
            });
        }

        get isActive(): boolean {
            return this.active;
        }
    }
</script>

<style lang="scss" scoped>
    .msg {
        position: absolute;
        top: 25%;
        width: 100%;
        display: flex;
        justify-content: center;

        div {
            max-width: 800px;
            width: 100%;
            margin: 0 40px;
            border-radius: 2px;
            padding: 30px 40px;
            background-color: rgba(251, 251, 251, 0.9);
        }
    }

    .spinner {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        background-color: rgba(0, 0, 0, 0.3);
        width: 100%;
        height: 100%;
    }

    .loader {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .line {
        animation: expand 1s ease-in-out infinite;
        border-radius: 10px;
        display: inline-block;
        transform-origin: center center;
        margin: 0 3px;
        width: 1px;
        height: 35px;
    }

    .line:nth-child(1) {
        background: #fff;
    }

    .line:nth-child(2) {
        animation-delay: 180ms;
        background: #fff;
    }

    .line:nth-child(3) {
        animation-delay: 360ms;
        background: #fff;
    }

    .line:nth-child(4) {
        animation-delay: 540ms;
        background: #fff;
    }

    @keyframes expand {
        0% {
            transform: scale(1);
        }
        25% {
            transform: scale(2);
        }
    }
</style>
