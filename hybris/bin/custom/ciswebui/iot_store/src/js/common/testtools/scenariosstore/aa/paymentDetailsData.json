{"ccPaymentInfos": [{"id": "123", "accountHolderName": "Test User1", "cardTypeData": {"name": "Visa"}, "cardNumber": "***3232", "cardType": "visa", "expiryMonth": "4", "expiryYear": "2022", "saved": true, "defaultPaymentInfo": true, "billingAddress": {"companyName": "Sample Company", "line1": "Sample Street", "town": "Sample Town", "region": null, "postalCode": "12345", "country": {"isocode": "DE", "name": "Germany"}, "shippingAddress": false}}, {"id": "456", "accountHolderName": "Test User2", "cardTypeData": {"name": "MasterCard"}, "cardNumber": "***3232", "cardType": "master", "expiryMonth": "4", "expiryYear": "2022", "saved": true, "defaultPaymentInfo": false, "billingAddress": {"companyName": "Sample Company", "line1": "Sample Street", "town": "Sample Town", "region": null, "postalCode": "12345", "country": {"isocode": "DE", "name": "Germany"}, "shippingAddress": false}}], "invoicePaymentInfos": [{"id": "789", "paymentMethod": "INVOICE", "saved": true, "defaultPaymentInfo": false, "enabled": true}, {"id": "*************", "paymentMethod": "SEPA_CREDIT", "saved": true, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "BOSCH_TRANSFER", "enabled": true, "disableReason": null, "accountHolder": "Security & Safety Things GmbH", "iban": "**********************", "bankName": "First Ferengi Interplanetary", "bic": "BYLADEM1001"}], "directDebitPaymentInfos": [{"id": "*************", "paymentMethod": "SEPA_DIRECTDEBIT", "saved": true, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "PGW", "mandateReference": "PGWMD068E85OELFAV6PILVLL9QMFN", "iban": "**********************", "accountHolderName": "TESTER", "dateOfSignature": "2024-07-24T00:00:00.000Z"}, {"id": "*************", "paymentMethod": "SEPA_DIRECTDEBIT", "saved": true, "reusable": false, "defaultPaymentInfo": false, "paymentProvider": "PGW", "mandateReference": "PGWMD068EGCC6PSUUPIBV5EVAS0KL", "iban": "**********************", "accountHolderName": "TESTER 1", "dateOfSignature": "2024-07-25T00:00:00.000Z"}, {"id": "*************", "paymentMethod": "SEPA_DIRECTDEBIT", "saved": true, "reusable": false, "defaultPaymentInfo": true, "companyScope": true, "paymentProvider": "PGW", "mandateReference": "PGWMD068EGCC6PCSIBV5EVASAVED", "iban": "**********************", "accountHolderName": "TESTER 1", "dateOfSignature": "2024-07-25T00:00:00.000Z"}], "pendingPaymentInfoDrafts": []}