import {inputValidationService} from "common/services";

describe('Validation Service', () => {
    it('accepts only valid quantities', ()=>{
        expect(inputValidationService.isValidQuantity("0500")).toBe(true);
        expect(inputValidationService.isValidQuantity("9999")).toBe(true);
        expect(inputValidationService.isValidQuantity("99999")).toBe(false);
        expect(inputValidationService.isValidQuantity(" 0500 ")).toBe(false);
        expect(inputValidationService.isValidQuantity("0")).toBe(false);
        expect(inputValidationService.isValidQuantity("00")).toBe(false);
        expect(inputValidationService.isValidQuantity("0.0")).toBe(false);
        expect(inputValidationService.isValidQuantity("0,0")).toBe(false);
        expect(inputValidationService.isValidQuantity("d11")).toBe(false);
        expect(inputValidationService.isValidQuantity("1d")).toBe(false);
    });

    it('accepts only valid quantities with Max limit', ()=>{
        expect(inputValidationService.isPermissibleQuantity("0500",1000)).toBe(true);
        expect(inputValidationService.isPermissibleQuantity("9999",100)).toBe(false);
        expect(inputValidationService.isPermissibleQuantity("9999",-1)).toBe(true);
        expect(inputValidationService.isPermissibleQuantity(" 0500 ",100)).toBe(false);
        expect(inputValidationService.isPermissibleQuantity("0",100)).toBe(false);
        expect(inputValidationService.isPermissibleQuantity("00",100)).toBe(false);
        expect(inputValidationService.isPermissibleQuantity("0.0",100)).toBe(false);
        expect(inputValidationService.isPermissibleQuantity("0,0",100)).toBe(false);
        expect(inputValidationService.isPermissibleQuantity("d11",100)).toBe(false);
        expect(inputValidationService.isPermissibleQuantity("1d",100)).toBe(false);
    });

    it('accepts only valid quantities or zero', ()=>{
        expect(inputValidationService.isQuantityOrZero("0500")).toBe(true);
        expect(inputValidationService.isQuantityOrZero("9999")).toBe(true);
        expect(inputValidationService.isQuantityOrZero("99999")).toBe(false);
        expect(inputValidationService.isQuantityOrZero(" 0500 ")).toBe(false);
        expect(inputValidationService.isQuantityOrZero("0")).toBe(true);
        expect(inputValidationService.isQuantityOrZero("00")).toBe(true);
        expect(inputValidationService.isQuantityOrZero("0.0")).toBe(false);
        expect(inputValidationService.isQuantityOrZero("0,0")).toBe(false);
        expect(inputValidationService.isQuantityOrZero("d11")).toBe(false);
        expect(inputValidationService.isQuantityOrZero("1d")).toBe(false);
    });

    it('accepts only valid prices', ()=>{
        expect(inputValidationService.isValidDeveloperPrice("0")).toBe(true);
        expect(inputValidationService.isValidDeveloperPrice("00")).toBe(true);
        expect(inputValidationService.isValidDeveloperPrice("00000000")).toBe(true);
        expect(inputValidationService.isValidDeveloperPrice("10d")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("d10")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("010")).toBe(true);
        expect(inputValidationService.isValidDeveloperPrice("01d0")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("100")).toBe(true);
        expect(inputValidationService.isValidDeveloperPrice("100000000")).toBe(true);
        expect(inputValidationService.isValidDeveloperPrice("999999990")).toBe(true);
        expect(inputValidationService.isValidDeveloperPrice("01000000000")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("10000000000")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("10.")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("10.0")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("10,")).toBe(false);
        expect(inputValidationService.isValidDeveloperPrice("10,0")).toBe(false);
    })

    it('accepts only prices that are valid when rounded', ()=>{
        expect(inputValidationService.isPartiallyValidDeveloperPrice("0")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("00")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("00000000")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10d")).toBe(false);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("d10")).toBe(false);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("010")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("01d0")).toBe(false);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("100")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("100000000")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("999999990")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("01000000000")).toBe(false);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10000000000")).toBe(false);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10.")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10.0")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10.1")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10,")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10,0")).toBe(true);
        expect(inputValidationService.isPartiallyValidDeveloperPrice("10,1")).toBe(true);
    })
});