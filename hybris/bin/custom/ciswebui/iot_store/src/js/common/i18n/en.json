{"create": "Create", "cancel": "Cancel", "delete": "Delete", "remove": "Remove", "ok": "Ok", "or": "or", "by": "by ", "submit": "Submit", "back": "Back", "continue": "Continue", "payNow": "Pay now", "quantity": "Quantity", "update": "Update", "total": "Total", "select": "Select", "add": "Add", "edit": "Edit", "confirm": "Confirm", "send": "Send", "backendError": "Something went wrong. Please try again.", "default": "<PERSON><PERSON><PERSON>", "preference": "Preference", "country": {"AE": "U.A.Emirates", "AR": "Argentina", "AT": "Austria", "AU": "Australia", "BE": "Belgium", "BG": "Bulgaria", "BR": "Brazil", "CA": "Canada", "CH": "Switzerland", "CL": "Chile", "CY": "Cyprus", "CZ": "Czech Republic", "DE": "Germany", "DK": "Denmark", "EE": "Estonia", "ES": "Spain", "FI": "Finland", "FR": "France", "GB": "United Kingdom", "GR": "Greece", "HR": "Croatia", "HU": "Hungary", "ID": "Indonesia", "IE": "Ireland", "IL": "Israel", "IN": "India", "IS": "Iceland", "IT": "Italy", "JP": "Japan", "KR": "Republic of Korea", "LT": "Lithuania", "LU": "Luxembourg", "LV": "Latvia", "MT": "Malta", "MX": "Mexico", "MY": "Malaysia", "NL": "Netherlands", "NO": "Norway", "NZ": "New Zealand", "PL": "Poland", "PT": "Portugal", "RO": "Romania", "SA": "Saudi Arabia", "SE": "Sweden", "SG": "Singapore", "SI": "Slovenia", "SK": "Slovakia", "TH": "Thailand", "TR": "Turkey", "TW": "Taiwan", "US": "United States of America", "VN": "Vietnam", "ZA": "South Africa"}, "navigation": {"loggedInAs": "Logged in as", "copyright": "Copyright © 2021 Bosch Digital Commerce GmbH. All rights reserved", "devconTitle": "Developer<br class='mobileOnly'> Console", "storeTitle": "Application Store", "deviceManagementPortal": "Device Management Portal", "helpAndResources": "Help & Resources", "devconPageTitle": "Dev<PERSON>per Console – <PERSON>zena", "storePageTitle": "Application Store – Azena", "items": {"appProductCategory": "Apps", "deviceManagement": "Device Management Portal", "globalHelpContact": "Contact Us", "globalHelpContactEmail": "Contact Us", "globalHelpSupport": "Visit Help & Support", "globalImprint": "Corporate Information", "globalLegal": "Legal Note", "globalMyCompany": "My Company", "globalMyProfile": "My Profile", "globalSupport": "Support", "snstCameras": "Get your cameras", "snstHome": "Azena Home", "snstOurPlatform": "Get to know our platform", "storeHelpAndResources": "Help & Resources", "storeHelpAzenaHome": "Azena home", "storeHelpCompatibleCameras": "Compatible cameras", "storeHelpIntroToStore": "Introduction to Application Store", "storeHelpPlatformOverview": "Our platform", "storeHelpStoreDocs": "Application Store help", "storeInfrigment": "Report Infringement", "storeLogin": "Log in", "storeOrderHistory": "Order History", "storePaymentDetails": "Payment Details", "storePrivacyPolicy": "Privacy Policy", "storeRegister": "Sign up", "storeSignOut": "Sign Out", "storeTermsAndConditions": "Terms and Conditions", "toolProductCategory": "<PERSON><PERSON><PERSON>"}}, "shop": {"reviews": {"anonymous": "Anonymous"}, "license": {"evaluation": {"name": "Trial"}, "full": {"name": "Purchase"}, "subscription": {"name": "Subscription"}}, "products": {"header": "Welcome to Application Store. Find, test and buy apps that transform the potential of security cameras.", "title": "Applications", "searchProducts": "Search", "searchResults": "Search results for", "noSearchResults": "Sorry! We didn't find any matching apps. Let us know what you are looking for. {url}", "contactUs": "Contact us.", "loadMore": "Load more", "buyButton": "Purchase", "buyButtonWithPrice": "Purchase from {currency} {price}", "price": "from {currency} {price}", "sorting": {"prefix": "Sort by", "created": "Newest", "acquisitionCount": "Most purchased", "relevance": "Most relevant"}, "facets": {"availability": {"country": "Available in your country", "trial": "Trial", "privateapps": "Your private apps"}, "group": {"availability": {"name": "Availability"}, "usecases": {"name": "Use Cases"}, "industries": {"name": "Industries"}, "licenseruntimes": {"name": "License type", "values": {"runtime_subs_unlimited": "Subscription", "runtime_full_3y": "3-year contract", "runtime_full_unlimited": "One time purchase"}}}, "showMore": "Show more", "filters": "Filters", "filteredBy": "Filtered by", "filteredSearchResults": "Filtered search results", "filteredSearchResultsFor": "Filtered search results for "}}, "gridItem": {"notAvailableLabel": "Not available in your country", "notAvailableLabelShort": "Not available", "readOnlyLabel": "Coming soon", "privateAppLabel": "Private app"}, "productDetails": {"productHeader": {"trialButton": "Try for free", "tryFreeButton": "Try it free", "purchaseButton": "Buy now", "getFreeToolButton": "Get free tool", "byCompany": "By", "applicationStore": "Application Store", "reviewsOverview": {"review": "review | reviews", "install": "install | installs", "purchases": "{installs}+ Purchases"}, "noLicense": "Check out this app. It will be available soon.", "maintenance": {"title": "We are performing a scheduled maintenance.", "subtitle": "The purchase functionality of applications is not available at this moment."}, "privateAppLabel": "Private app", "integration": {"title": "Integration options:", "detail": "View details"}}, "gallerySection": {"title": "Preview"}, "packageDetailsSection": {"title": "Package in detail", "addons": "Related add-ons", "video": "View video"}, "pricingSection": {"addToCart": "Add to cart", "enableTool": "Enable <PERSON><PERSON><PERSON>", "addToCartSuccess": "Successfully added to your cart.", "addToCartSuccessWithDiscount": "Successfully added to your cart. {percent}% volume discount has been applied.", "addToCartGeneralError": "An error occurred. The product could not be added to the cart.", "pricing": "Pricing", "quantity": "Quantity", "permissionText": "You do not have permission to purchase applications.", "countryPermissionText": "This app is not available in your country", "integratorCountryBlockedText": "This app's commercial licenses are blocked in your country", "subscriptionPermissionText": "The subscriptions can currently only be ordered with the bank transfer payment method. For details, please contact our customer support.", "learnMore": "Learn more", "free": "Free", "pricetext": "one time", "perYear": "per year", "fullLicenseName": "Purchase", "fullLicenseSummary": "One purchased app can be installed on one camera and does not expire.", "evaluationLicenseName": "Trial", "evaluationLicenseSummary": "One app trial can be installed on one camera and will expire after 30 days.", "anonymousUserInfo": "{url} to see the pricing and test it free.", "logIn": "Log in", "trialNowFullSoon": "Check out our trial applications. Full purchasing option soon to come.", "quantityInvalid": "The maximum order quantity of a individual app is {0}.", "license": {"evaluation": {"name": "Trial", "summary": "Try the app for free for 30 days", "detailsMap": {"1": "Each camera can run each trial once", "2": "I confirm that I agree with the terms of use for this app", "3": "Extended trial can be requested for sales demonstration and simular purposes"}}, "full": {"name": "Purchase", "summary": "Get the app with a permanent license", "detailsMap": {"1": "One time purchase for a permanent license", "2": "One purchase can be only installed and used on one camera at one time"}}, "subscription": {"name": "Subscription", "summary": "Get the app on a subscription basis, billed every 12 months.", "detailsMap": {"1": "The subscription period starts on the date when the order is placed", "2": "Renews after 12 months", "3": "Cancel 30 days in advance", "4": "One subscription app can only be installed and used on one camera at one time"}, "futurePriceText": "The subscription price will change to {futurePrice} per 12 months after the first year due to a price change from {startDate}."}, "tool": {"name": "Free Azena <PERSON>l", "summary": "Get the app for free", "detailsMap": {"1": "Make this tool available for your company to install and use for free.", "2": "I confirm that I agree with the terms of use for this tool."}, "confirmationPopup": {"header": "Free tool now available to install", "contentLine1": "You can now install and use {toolName} for free.", "installInstructions": "If your cameras are already added to our {linkToDMP}, you can start the installation directly from there remotely.", "deviceManagementPortal": "Device Management Portal", "offlineInstallInstructions": "If your cameras are only connected to a local network, you’ll need to download our {linkToDMT} to install apps.", "deviceManagementTool": "Device Management Tool", "learnMore": "Visit our {linkToGuide} to learn more.", "guide": "guide", "doneButton": "Done"}}}, "volumeDiscounts": {"table": {"headings": {"quantity": "Quantity", "discount": "Discount", "pricePerApp": "Price per app"}}, "callout": "Save up to {num}% purchasing in bulk"}}, "getOwnAppSection": {"title": "Get own app", "description": "You can directly get your own app with permanent license(s).", "quantity": "Quantity", "getAppsButtonText": "Get apps", "infoText": "Invoice for platform fee will be sent to your billing mail."}, "descriptionSection": {"label": "About"}, "changelogSection": {"label": "Version history", "more": "Read more", "less": "Collapse", "version": "Version"}, "integrationsSection": {"title": "Integrations with external systems", "description": "Our platform provides the flexibility for apps to integrate with many different external systems. Find out more about the possible {url} in our Help Centre.", "appIntegrations": "app integrations", "missingIntegration": "Missing an integration?", "missingDescription": "Get in touch with the {email} to find out if the integrations you want to see can be supported.", "developer": "developer", "downloadPdf": "Download PDF", "customDescription": "This app supports integrations with custom video management systems and/or data analytics platforms.", "customIntegrationName": "Custom Integration"}, "documentsSection": {"title": "Documents"}, "appRequirementsSection": {"title": "Requirements", "requiredVersion": "Required OS version", "requiredVersionHint": "Minimum OS version, that needs to be installed on the camera, so the app runs without problems.", "orHigher": "or higher", "permissions": "App permissions", "permissionsHint": "Permissions, that the app requires and that will be agreed to upon purchase.", "noPermissionsRequired": "This app does not require special permissions.", "deviceCapabilities": "Device capabilities", "deviceCapabilitiesHint": "Camera features, that are required by the app.", "noDeviceCapabilities": "There are no required device capabilities specified for this app."}, "toolRequirementsSection": {"title": "Requirements", "requiredVersion": "Required OS version", "requiredVersionHint": "Minimum OS version, that needs to be installed on the camera, so the tool runs without problems.", "orHigher": "or higher", "permissions": "Tool permissions", "permissionsHint": "Permissions, that the tool requires and that will be agreed to upon purchase.", "noPermissionsRequired": "This tool does not require special permissions.", "deviceCapabilities": "Device capabilities", "deviceCapabilitiesHint": "Camera features, that are required by the tool.", "noDeviceCapabilities": "There are no required device capabilities specified for this tool."}, "companyProfileSection": {"title": "Company", "companyInfo": {"founded": "Founded", "size": "Company size", "headquarters": "Headquarters", "visitCompanyProfile": "Visit company profile"}}, "supportSection": {"title": "Support", "phone": "Phone", "email": "Email", "website": "Website"}, "followAppSection": {"app": {"label": "Follow app", "summary": "Get email notifications about new app versions.", "notification": {"success": {"follow": "You are now following {appName} and will be notified via email about app updates.", "unfollow": "You will no longer receive email notifications about {appName}."}}}, "tool": {"label": "Follow tool", "summary": "Get email notifications about new tool versions.", "notification": {"success": {"follow": "You are now following {appName} and will be notified via email about tool updates.", "unfollow": "You will no longer receive email notifications about {appName}."}}}}, "sidebar": {"version": "Current Version", "legalCompanyName": "Legal company name", "companyWebsite": "Company website", "productId": "Product ID", "partnumber": "Part number", "privacyPolicyApp": {"title": "Privacy policy", "description": "Bosch Digital Commerce GmbH's privacy policy does not apply to this application. Please refer to the application's privacy policy.", "link": "Application's privacy policy"}, "privacyPolicyTool": {"title": "Privacy policy", "description": "Bosch Digital Commerce GmbH's privacy policy does not apply to this tool. Please refer to the tool's privacy policy.", "link": "<PERSON><PERSON>'s privacy policy"}, "termsOfUseApp": {"title": "License terms", "link": "Application’s End User License Agreement", "appendix": "Appendix A: 3rd party software licenses", "standardMark": "Standard"}, "termsOfUseTool": {"title": "License terms", "link": "Tool’s End User License Agreement", "appendix": "Appendix A: 3rd party software licenses", "standardMark": "Standard"}, "countries": {"title": "Available in"}, "privateOffer": {"title": "Request custom offer", "description": "Reach out to the seller of this app to request custom conditions for your purchase.", "button": "Contact seller"}}, "reviewSection": {"title": "Reviews", "notReviewed": "This app has not been reviewed yet.", "addReview": "Add review", "wantToAddReview": "Add review", "shareReviewTeaser": "Share your experience with this app.", "buyApp": "Once your company has bought this app, you will be able to leave reviews."}, "reviewCreationForm": {"create": "Add review", "headline": "Review title", "comment": "Description", "ratingHeadline": "Your rating*", "rating": "Please enter a rating.", "showCompany": "Include company name in review.", "showName": "Include my name in review.", "thankYouTitle": "Thank you for your review.", "fieldValidation": "Please check your input.", "generalError": "Something went wrong. Please try again.", "required": "*Required fields"}, "dualUseInfo": {"header": "Export information", "linkText": "View all apps and their export control classification."}, "privateOfferDialog": {"header": "Request for offer", "description": "Request a quote of the seller of this app. They will be notified and will come back to you.", "requestedConditionTitle": "Requested conditions (optional)", "requestedConditionDescription": "Indicate desired prices for this app and how many licenses you are planning to buy.", "messageTitle": "Message to seller", "messageHelper": "Message", "fullLicensePriceHelper": "One-time purchase price", "fullLicenseQuantityHelper": "Est. number of app purchases", "subscriptionPriceHelper": "Subscription price", "subscriptionQuantityHelper": "Est. number of app subscriptions", "messageLengthError": "message length should between 0 to 1000 characters", "priceNegativeError": "price can not be negative", "priceEqualsError": "price can not equals listed price", "quantityError": "quantity can not be negative", "success": "Your private offer request has been successfully submitted", "projectRegistration": {"enable": "I want to register a project with the seller. {linkToGuide}", "learnMore": "Learn more", "projectData": {"title": "Project Information (optional)", "projectName": "Project name", "customerName": "Customer name", "startDate": "Planned Start Date"}, "addressData": {"title": "Site address (optional)", "country": "Country", "line1": "Street and house number", "postalCode": "Postal Code", "city": "City"}}}}, "checkout": {"header": "Secure Checkout", "pageTitle": "Check out – ", "finalReview": "Final Review", "cardHolder": "Card Holder", "cardName": "Name on Card", "cardNumber": "Card Number", "cardExpiry": "Expiry Date", "cardCvc": "Security Code", "saveCard": "Save Payment Info", "wrongValue": "The value is invalid", "wrongCardName": "The cardholder name length must be 2 to 26 characters including first name, last name and spaces.", "invoiceNote": {"title": "Add internal notes to the order document", "description": "Provide additional information such as internal purchase order number and project name. The notes will be displayed on the order document.", "noteLine": "Note line {line} (optional)"}, "ownApp": {"feeNote": "Platform fee will be charged"}, "orderEntries": {"futurePrice": "afterwards {futurePrice} per 12 months"}, "miniSummary": {"headerAddress": "Billing Address", "ownPurchaseHeader": "Billing email", "totalPrice": "Total net price", "taxNote": "Tax is specified on the order document", "taxNoteInvoiceBySeller": "Invoicing including tax calculation and payment process will be managed by seller.", "taxHint": "An additional amount will be reserved on your credit card to cover applicable taxes. After tax calculation, the invoiced gross amount will be captured from your credit card."}, "backToCart": "Back to Cart", "newCard": "New Credit Card", "paymentMethod": "Payment Method", "orderSummary": "Order Summary", "placeOrder": "Place Order", "placeOrderAndPay": "Place order and pay", "paymentMethodNotSavedHint": "This payment method will only be saved in your account after purchase.", "paymentMethodOverwriteHint": "Adding a further payment method will replace your previously temporarily added payment method.", "placeOrderConfirmation": "By placing this order I agree with the terms of use for the purchased apps.", "deletePaymentMethod": "Delete payment method?", "error": "Something went wrong with your order.", "byCompany": "By", "confirmation": {"confirmationHeader": "Confirmation", "confirmationLine1": "Your purchase has been successfully processed.", "confirmationLine2": "It may take a few minutes for it to appear in your apps list.", "confirmationLineInvoice": "The order document with the payment details will be available soon.", "confirmationLine3": "Order number", "confirmationLine3plural": "Order numbers", "followAppNotification": "You will be notified about future app updates or license terms changes via email. To disable this, unfollow the app(s) on the app detail page:", "continueShopping": "Continue Shopping", "installPrompt": "How do I begin installing an app?", "installInstructions": "If your cameras are already added to our {linkToPortal}, you can start the installation directly from there remotely.", "toolInstallInstructions": "If your cameras are only connected to a local network, you’ll need to download our Device Management Tool to install apps.", "deviceManagementPortal": "Device Management Portal", "learnMore": "Learn More"}}, "cart": {"cart": "<PERSON><PERSON>", "pageTitle": "<PERSON><PERSON> – ", "emptyCartMessage": "Your shopping cart is empty", "checkout": "checkout", "continueShopping": "Continue Shopping", "orderFrom": "Order from ", "subscriptionTitle": "Subscription"}, "cartTotal": {"total": "Net Price", "taxmessage": "* Your order does not include taxes", "tax": "Mwst."}, "bundleInfo": {"description": "{bundleName} ({bundleSize})", "size": "1 license | {n} licenses"}, "cartItem": {"itemRemovedSuccessMessage": "The item was removed from your cart.", "itemRemovedErrorMessage": "The item could not be removed from your cart. Please reload the page and try again.", "messageOnUpdateCart": "Your cart was updated successfully.", "messageOnUpdateCartWithDiscount": "Your cart was updated successfully. {percent}% volume discount has been applied.", "updateCartGeneralError": "An error occurred. The item quantity could not be updated.", "messageOnUpdateCartFailed": "The requested product was already removed from the cart.", "volumeDiscount": "Volume discount", "hint": "Buy {min}+ and get {percent}% off each", "per12Months": "per 12 months", "for12Months": "for 12 months", "ownApp": {"title": "My company app", "info": "Invoice for platform fee will be sent to your billing mail."}}, "exportInformation": {"title": "Global trade compliance", "pageTitle": "Global trade compliance – ", "appName": "App name", "companyName": "Developer company", "eccn": "ECCN"}, "orderHistory": {"title": "Order history", "pageTitle": "Order history – ", "order": "Order", "table": {"orderNumber": "Order number", "apps": "Apps", "placedBy": "Placed by", "orderDate": "Order date", "lastUpdated": "Last updated", "status": "Status"}, "noOrders": {"title": "No orders", "text": "If there are any placed orders, you'll find them listed here."}, "creditCardPaymentSuccess": "Credit card payment method successfully updated for the following orders: "}, "orderDetails": {"items": {"by": "By", "quantity": {"label": "Quantity", "tooltipText": "Quantity when the order was placed"}}, "orderInfo": {"label": "Order details", "placedDate": "Order placed", "placedBy": "Placed by", "payment": {"method": {"label": "Payment Method", "invoiceBySeller": "Managed By Seller", "sepaCredit": "SEPA Credit Transfer", "achInternational": "ACH Credit Transfer", "creditCard": "Credit Card", "sepaDirectdebit": "SEPA Direct Debit"}}, "paymentFailed": "There is a problem with your card ending with {ccEnding}.", "update": "Update"}, "invoices": {"label": {"default": "Invoices", "documents": "Documents"}, "table": {"headers": {"invoiceId": {"label": {"default": "Invoice ID", "document": "Document ID"}}, "invoiceDate": {"label": {"default": "Invoice date", "document": "Date"}}, "invoiceStatus": {"label": "Status"}, "totalAmount": {"label": {"default": "Total amount", "document": "Total net amount"}, "tooltipText": "Including taxes"}}, "data": {"invoiceStatus": {"paid": "Paid", "exempt": "Exempt", "pending": "Pending", "overdue": "Overdue", "notPaid": "Not paid", "reversed": "Reversed", "refunded": "Refunded", "partiallyRefunded": "Partially refunded", "paymentFailed": "Payment failed"}, "documentIssuedDate": {"label": "{documentType} document issued"}, "documentType": {"invoice": "Invoice", "reversal": "Reversal", "refund": "Refund", "partialRefund": "Partial refund", "transactionReport": "Transaction report"}}}}}, "orderStatus": {"progress": "In Progress", "completed": "Completed", "pending": "Payment pending", "overdue": "Payment overdue", "refunded": "Refunded", "partially_refunded": "Partially refunded"}, "payment": {"tooltip": {"bankTransferDisabled": "Payment by bank transfer is not enabled for your company. For details, contact the customer support.", "generalInfo": "An order document will be available after placing the order.", "overValue": "This order exceeds your company credit limit for SEPA Credit Transfer. For details, please contact our customer support.", "invoicebyseller": "Due to local Korean tax requirements, the seller will manage invoicing and payment process.", "companyScope": "Company-wide payment method, Any user with purchase permission in your company account can view and use this payment method."}, "notEnabled": "Not enabled", "removed": "Payment method was successfully removed.", "paymentDetailsHeader": "Payment details", "pageTitle": "Payment details – ", "savedPaymentMethods": "Saved payment methods", "creditCard": "Credit card", "bankTransfer": "Bank transfer", "directDebit": "Direct debit", "noPaymentDetails": "No saved payment methods", "noPaymentDetailsInfo": "You don’t have saved any payment methods yet. You can add and save new payment methods when purchasing apps.", "sepaTransfer": "SEPA Credit Transfer", "sepaDirectDebit": "SEPA Direct Debit", "sepaMandateForAccount": "SEPA Direct Debit for Account", "createDirectDebitPaymentInfo": "Add SEPA direct debit mandate", "sepaMandateNew": "New SEPA Direct Debit Mandate", "mandateReference": "Mandate Reference", "dateOfSignature": "Date of Signature", "achInternational": "ACH Credit Transfer", "transferTo": "Transfer to", "accountName": "Account Holder", "account": "Account", "bankName": "Bank Name", "iban": "IBAN No.", "bicSwift": "BIC/Swift No.", "routing": "Routing No.", "accountNumber": "Account No.", "invoicebyseller": "Managed By Seller", "setPreference": "Set as preference", "removePreference": "Unset as preference", "delete": "Delete", "setPreferenceSuccess": "The payment method is successfully set as preference.", "removePreferenceSuccess": "The payment method is successfully removed as preference.", "paymentRemoved": "The payment method was successfully removed.", "deletePaymentMethod": "Delete payment method?", "deletePaymentMethodInfo": "This payment method will be deleted. You can add new payment methods when purchasing apps.", "settingPreferenceFailed": "The payment method could not be set as preference. Please try again later or contact customer support.", "billingAddress": "Billing address", "billingAddressInfo": "Your company’s billing address. To change it {url}.", "contactCustomerSupport": "contact the customer support"}, "updatePayment": {"header": "Update Payment Method", "subtitle": "Please select a different payment method or add a new card.", "newCard": "New Credit Card", "finalReview": "Final Review", "cardHolder": "Card Holder", "cardName": "Name on Card", "cardNumber": "Card Number", "cardExpiry": "Expiry Date", "cardCvc": "Security Code", "saveCard": "Save as preferred card and update for all past due/future orders", "wrongValue": "The value is invalid", "wrongCardName": "The cardholder name length must be 2 to 26 characters including first name, last name and spaces.", "problemWithPayment": "There is a problem with your credit card ending with {ccEnding}.", "placeOrder": "Place Order", "placeOrderAndPay": "Place order and pay", "paymentMethodNotSavedHint": "This payment method will only be saved in your account after purchase.", "paymentMethodOverwriteHint": "Adding a further payment method will replace your previously temporarily added payment method.", "placeOrderConfirmation": "By placing this order I agree with the terms of use for the purchased apps.", "deletePaymentMethod": "Delete payment method?"}, "tools": {"pageTitle": "Tools – ", "header": {"title": "<PERSON><PERSON><PERSON>", "description": "Find free tools to empower your system"}, "container": {"title": "<PERSON><PERSON><PERSON>"}}, "companyProfile": {"companyInfo": {"founded": "Founded", "size": "Company size", "headquarters": "Headquarters", "visitWebsite": "Visit website", "visitLinkedinProfile": "Visit Linkedin profile", "about": "About"}, "contactInfo": {"sales": "Sales contact", "support": "Support", "phone": "Phone", "email": "E-mail", "supportPage": "Support page"}, "apps": {"title": "Apps", "noAppsFound": "No apps found."}}, "error": {"backend": "Something went wrong. Please try again.", "validation": "Some of your inputs are invalid.", "generic": {"serverError": "Something went wrong. Please try again.", "notFound": "The resource could not be found.", "badRequest": "The request was invalid and could not be processed.", "forbidden": "You do not have sufficient permissions to access the requested resource."}, "review": {"exists": "An employee of your company has already reviewed this app.", "appNotOwned": "You have to purchase the app in order to review it."}, "checkout": {"cartModified": "Your cart was modified. Please verify the changes and proceed to checkout one more time.", "placeOrderFailed": "Failed to place the order.", "paymentFailed": "There was an error with your payment. Please try again or use a different payment method.", "invalidCartState": "A technical error occurred, please try again later. Should the issue persist, please contact our customer support."}, "cart": {"unpayable": "Could not determine any suitable payment method for your cart. Please reduce the total amount of the cart.", "empty": "You cannot proceed to checkout with an empty cart.", "invalidQuantity": "The maximum order quantity of an individual app is {0} for permanent licenses, and {1} for subscription licenses.", "noSelfPurchases": "You can’t purchase apps from your own company.", "addFailed": "An error occurred while adding to cart.", "unsupportedLicense": "Product could not be added to the cart.", "preparationFailure": "A technical error occurred while preparing the checkout for your cart, please try again later. Should the issue persist, please contact our customer support."}, "licenseactivation": {"generic": "Error when activating license.", "notPurchasable": "License activation failed. License is not purchasable.", "notSupported": "License activation failed. License type is not supported."}, "companyprofile": {"notFound": "Company profile could not be found."}, "product": {"notFound": "The product could not be found."}}, "storeEditSuccessResponseMessage": "The data was saved successfully", "contactCustomerSupport": "Contact customer support"}}