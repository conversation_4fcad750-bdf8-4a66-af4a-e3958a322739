import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {SpecialOfferConditions} from 'aa/shop/core/components';
import {useCoreDataStore} from 'shop/store/coreData';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';

jest.mock('common/util');

const mountSpecialOfferConditions = () => wrapperComponentFactory(SpecialOfferConditions);

const coreDataStore = useCoreDataStore();

describe('SpecialOfferConditions', () => {

    beforeEach(() => {
        coreDataStore.coreData = {
            'specialOffer': {
                'priceListUrl': 'https://example.com'
            }
        };
    });

    describe('conditionally renders when priceListUrl', () => {
        const selector = `[data-id="container-special-offer-conditions"]`;

        it('is given', async () => {
            const wrapper = mountSpecialOfferConditions();

            expect(() => wrapper.get(selector)).not.toThrowError();
        });

        it('is not given', async () => {
            coreDataStore.coreData = {};

            const wrapper = mountSpecialOfferConditions();

            expect(() => wrapper.get(selector)).toThrowError();
        });
    });
});
