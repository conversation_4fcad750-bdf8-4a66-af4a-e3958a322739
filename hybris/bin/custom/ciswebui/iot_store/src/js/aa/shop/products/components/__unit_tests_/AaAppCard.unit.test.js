import 'common/test-directive';
import AaAppCard from 'aa/shop/products/components/AaAppCard.vue';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import {PriceFrequency} from 'common/generated-types/types';
import {cloneDeep} from "lodash";

jest.mock('common/util');
jest.mock('common/components');

const defaultTestAppCardData = {
    code: 'AA_04012999',
    img: '/sample-data/aa/big-logo.png',
    title: 'CRIN Bosch Zusatzlizenz',
    body: 'Description',
    priceInfo: {
        minPrice: {
            currencyIso: 'EUR',
            value: 100,
            priceFrequency: PriceFrequency.YEARLY
        },
        specialOffer: false
    },
    url: 'shop/product-selection/AA2_0401987_ESIDIAG'
};
const $t = jest.fn().mockImplementation((key, params) => {
    if (key === 'shop.products.buyButton') {
        return 'Buy Now';
    }
    if (key === 'shop.products.perYear') {
        return 'p.a.';
    }
    if (key === 'shop.products.buyButtonWithPriceFrequency') {
        return 'Purchase from EUR 100.00 p.a.';
    }

    if (key === 'shop.products.priceWithFrequency') {
        return 'from EUR 100.00 p.a.';
    }
});

const mountAppCard = (appCardData = defaultTestAppCardData) => wrapperComponentFactory(AaAppCard, {
    props: {
        appCardData
    },
    mocks: {
        $t
    },
    shallow: false
});

describe('AaAppCard', () => {
    describe('Price', () => {
        it('is displayed in localized format', async () => {
            const wrapper = mountAppCard();
            await wrapper.vm.$nextTick();

            const minPrice = wrapper.find(`[data-id="text-product-min-price-${defaultTestAppCardData.code}"]`);
            expect(minPrice.text()).toEqual('from EUR 100.00 p.a.');
        });

        it('is displayed in the button in mobile viewport', async () => {
            const wrapper = mountAppCard();
            wrapper.vm.$vuetify.breakpoint.smAndDown = true;
            await wrapper.vm.$nextTick();

            const minPrice = wrapper.find(`[data-id="button-buy-product-${defaultTestAppCardData.code}-mobile"]`);
            expect(minPrice.text()).toEqual('Purchase from EUR 100.00 p.a.');
        });
    });

    describe('Special offer chip', () => {
        let testAppCardData = {};
        beforeEach(() => {
            testAppCardData = cloneDeep(defaultTestAppCardData);
        });

        it('is displayed when price is a special offer', async () => {
            testAppCardData.priceInfo.specialOffer = true;
            const wrapper = mountAppCard(testAppCardData);
            await wrapper.vm.$nextTick();

            const specialPriceChip = wrapper.find(`[data-id="container-special-price-chip-product-${testAppCardData.code}"]`);
            expect(specialPriceChip.exists()).toBeTruthy();
        });

        it('is not displayed when price is not a special offer', async () => {
            testAppCardData.priceInfo.specialOffer = false;
            const wrapper = mountAppCard();
            await wrapper.vm.$nextTick();

            const specialPriceChip = wrapper.find(`[data-id="container-special-price-chip-product-${testAppCardData.code}"]`);
            expect(specialPriceChip.exists()).toBeFalsy();
        });
    });
});
