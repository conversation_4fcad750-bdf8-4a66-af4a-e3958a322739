<script setup lang="ts">
import {computed} from 'vue';
import {useI18n} from 'vue-i18n-bridge';
import {useCoreDataStore} from 'shop/store/coreData';

const { t } = useI18n();

const coreDataStore = useCoreDataStore();
const priceListUrl = computed(() => coreDataStore.coreData.specialOffer?.priceListUrl);
</script>

<template>
  <div v-if="priceListUrl" data-id="container-special-offer-conditions">
    <h5 class="text-h5 mb-3 d-flex align-center">
      <CDIcon class="mr-2" color="black" small>$discount</CDIcon>
      {{ t('shop.specialOffer.conditions.title') }}
    </h5>

    <i18n path="shop.specialOffer.conditions.body" tag="p" class="text-body-2">
      <a :href="priceListUrl" data-id="link-special-offer-conditions-price-list" target="_blank">
        {{ t('shop.specialOffer.conditions.link') }} </a>
    </i18n>
  </div>
</template>
