import {
    acceptCookieBanner,
    createInlineUiTestPage,
    resetMouseCoords
} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import {cloneDeep} from 'lodash';
import * as Sizes from 'common/testtools/testScreenSizes';
import {cookies_en_AT, coreDataRestHandler} from "common/testtools/testCookies";
import checkoutPaymentDataWithSubscriptionOrderEntry
    from "common/testtools/scenariosstore/aa/checkoutPaymentDataWithSubscriptionOrderEntry.json";
import checkoutPaymentDataWithCountryEulas
    from "common/testtools/scenariosstore/aa/checkoutPaymentDataWithCountryEulas.json";
import {BaseStores} from "../../../../common/constants";
import {testExtraLargeScreensize, testLargeScreensize} from "common/testtools/testScreenSizes";

describe('checkout payment page', () => {

    let data;
    let pageWait = 3000;
    let pageHeight = 3000;
    let coreData = {};

    beforeEach(async () => {
        data = cloneDeep(testMap.CheckoutPaymentAA);
        await page.setRequestInterception(true);
        page.on('request', coreDataRestHandler);
        await page.setCookie(...cookies_en_AT);
        data.coreData.basestore = BaseStores.AA;
    });

    it('display checkout payment with credit card payment method', async () => {
        data.pageData.paymentInfo = null;
        data.pageData.checkoutInfos = [{
            paymentInfos: [],
            paymentProvider: 'DPG',
            paymentMethod: 'CREDIT_CARD',
            userActionParameters: {
                dpgjsUrl: "http://127.0.0.1:8091/dpgjs/js",
                sessionConfig: "some client configuration",
                paymentId: "payment_WKU6o28LYfQYCp5iz5UgKx",
                sessionId: "1/DJ;9v{<f7kZQt*2G;8r*"
            },
            userCreatable: true,
            savableForReuse: true
        }]
        data.pageData.checkoutInfos[0].paymentInfos = [];
        data.pageData.checkoutInfos = [data.pageData.checkoutInfos[0]]
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight);
    });

    it('checkout payment default with credit card selected', async () => {
        data.pageData.paymentInfo = null;
        data.pageData.checkoutInfos[0].paymentInfos[0].defaultPaymentInfo = true;
        data.pageData.checkoutInfos[1].paymentInfos[0].defaultPaymentInfo = false;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight);
        await page.setViewport({width: 1200, height: 2000});
        await page.waitFor(pageWait);
        await page.hover('[data-id="tooltip-container"]');
        await page.waitFor(pageWait);
        expect(await page.screenshot()).toMatchImageSnapshot("default tooltip");
    });

    it('checkout payment default with credit card selected tooltip', async () => {
        data.pageData.paymentInfo = null;
        data.pageData.checkoutInfos[0].paymentInfos[0].defaultPaymentInfo = true;
        data.pageData.checkoutInfos[1].paymentInfos[0].defaultPaymentInfo = false;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.setViewport({width: 1200, height: 1400});
        await page.waitFor(pageWait);
        await page.hover('[data-id="tooltip-container"]');

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment default with bank transfer selected', async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(pageWait);
        await page.click("#sepaCreditInput");

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment default no distributor list', async () => {
        data.pageData.listOfDistributors = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(pageWait);
        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment default with bank transfer selected distributor list', async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(pageWait);
        await page.click('[data-id="distributor-change-button"]');

        await page.waitFor(pageWait);
        await page.click('[data-id="distributor-switch"]');

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment default with bank transfer selected distributor list and confirm', async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(pageWait);
        await page.click('[data-id="distributor-change-button"]');
        await page.waitFor(pageWait);
        await page.click('[data-id="distributor-switch"]');
        await page.waitFor(pageWait);
        await page.click('[role="option"]:nth-child(2)');

        await Sizes.testAllScreenSizes(pageHeight, pageWait);

        await page.click('[data-id="button-distributor-dialog-submit"]');

        await page.setViewport({width: 1200, height: 2400});

        expect(await page.screenshot()).toMatchImageSnapshot("distributor selected");
    });

    it('checkout payment default with bank transfer selected and company under review note is visible', async () => {
        data.coreData.userCompanyUnderReview=true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await page.waitFor(pageWait);
        await page.click("#sepaCreditInput");

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment no bank transfer section', async () => {
        data.pageData.paymentInfo = null;
        data.pageData.checkoutInfos[0].paymentInfos[0].defaultPaymentInfo = true;
        data.pageData.checkoutInfos = [data.pageData.checkoutInfos[0]];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment default with bank transfer selected dpg', async () => {
        data.pageData.paymentInfo = data.pageData.checkoutInfos[1].paymentInfos[0];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment default with bank transfer selected dpg arch', async () => {
        const achInfo = {
            id: '*************',
            paymentMethod: 'ACH_INTERNATIONAL',
            saved: true,
            reusable: false,
            defaultPaymentInfo: true,
            paymentProvider: 'DPG',
            enabled: true,
            disableReason: '',
            accountHolder: 'John Smith',
            accountNumber: '**********************',
            routingNumber: 'Routing number',
            bankName: 'Deutsche Bank',
            bic: 'DEUTDEMM'
        }
        data.pageData.checkoutInfos[1] = {
            paymentInfos: [achInfo],
            paymentProvider: 'DPG',
            paymentMethod: 'ACH_INTERNATIONAL',
            userActionParameters: {},
            userCreatable: false,
            savableForReuse: false
        }
        data.pageData.paymentInfo = achInfo;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment with sepa direct debit and other methods', async () => {
        const sepaDirectDebitInfo = {
            id: '*************',
            paymentMethod: 'SEPA_DIRECTDEBIT',
            saved: true,
            reusable: true,
            defaultPaymentInfo: false,
            paymentProvider: 'PGW',
            accountHolderName: 'Anderson Dawes',
            iban: '**********************',
            mandateReference: 'PgwSepaMandate_VVmrST64A7Y1RHAtyzRT56',
            dateOfSignature: '2024-02-14'
        }
        data.pageData.checkoutInfos.push({
            paymentInfos: [sepaDirectDebitInfo],
            paymentProvider: 'PGW',
            paymentMethod: 'SEPA_DIRECTDEBIT',
            userActionParameters: {},
            userCreatable: true,
            savableForReuse: true
        });
        data.pageData.paymentInfo = sepaDirectDebitInfo;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('display checkout payment with no stored infos and only sepa direct debit selectable', async () => {
        data.pageData.paymentInfo = null;
        data.pageData.checkoutInfos = [{
            paymentInfos: [],
            paymentProvider: 'PGW',
            paymentMethod: 'SEPA_DIRECTDEBIT',
            userActionParameters: {},
            userCreatable: true,
            savableForReuse: true
        }];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight);
    });

    it('checkout payment with company scope sepa direct debit and other methods', async () => {
        const sepaDirectDebitInfo = {
            id: '*************',
            paymentMethod: 'SEPA_DIRECTDEBIT',
            saved: true,
            reusable: true,
            companyScope: true,
            defaultPaymentInfo: false,
            paymentProvider: 'PGW',
            accountHolderName: 'Anderson Dawes',
            iban: '**********************',
            mandateReference: 'PgwSepaMandate_VVmrST64A7Y1RHAtyzRT56',
            dateOfSignature: '2024-02-14'
        }
        data.pageData.checkoutInfos.push({
            paymentInfos: [sepaDirectDebitInfo],
            paymentProvider: 'PGW',
            paymentMethod: 'SEPA_DIRECTDEBIT',
            userActionParameters: {},
            userCreatable: true,
            savableForReuse: true
        });
        data.pageData.paymentInfo = sepaDirectDebitInfo;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    it('checkout payment disabled bank transfer', async () => {
        data.pageData.checkoutInfos[1].paymentInfos[0].enabled = false;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await Sizes.testAllScreenSizes(pageHeight, pageWait);
    });

    describe('Checkout payment subscription order entry', () => {
        it('display order with entry for subscription license', async () => {
            data = cloneDeep(
                {
                    ...testMap.CheckoutPaymentAA,
                    pageData: checkoutPaymentDataWithSubscriptionOrderEntry
                }
            );
            data.coreData.basestore = BaseStores.AA;
            await createInlineUiTestPage(data)
            await acceptCookieBanner();

            await Sizes.testAllScreenSizes(pageHeight);
        });

        it('display subscription license with future prices', async () => {
            data = cloneDeep(
                {
                    ...testMap.CheckoutPaymentAA,
                    pageData: checkoutPaymentDataWithSubscriptionOrderEntry
                }
            );
            data.coreData.basestore = BaseStores.AA;
            data.pageData.entries[0].productFuturePrices = [
                {
                    "symbol":"EUR",
                    "value":"400.00"
                }
            ];

            await createInlineUiTestPage(data);
            await acceptCookieBanner();

            await Sizes.testAllScreenSizes(pageHeight);
        });
    });

    describe('Checkout payment with country eulas', () => {
        it('display checkout payment with eula acceptance', async () => {
            data = cloneDeep(
                {
                    ...testMap.CheckoutPaymentAA,
                    pageData: checkoutPaymentDataWithCountryEulas
                }
            );
            data.coreData.basestore = BaseStores.AA;

            await createInlineUiTestPage(data);
            await acceptCookieBanner();

            await Sizes.testAllScreenSizes(pageHeight, pageWait);
        });
    });

    describe('Checkout payment with two error messages', () => {
        it(' eula acceptance and distributor selection', async () => {
            data = cloneDeep(
                {
                    ...testMap.CheckoutPaymentAA,
                    pageData: checkoutPaymentDataWithCountryEulas
                }
            );
            data.coreData.basestore = BaseStores.AA;

            await createInlineUiTestPage(data);
            await acceptCookieBanner();

            await page.waitFor(pageWait);
            await page.click('[data-id="place-order-btn"]');

            await Sizes.testExtraLargeScreensize(pageHeight, pageWait);
        });
    });

    describe('Checkout payment order  items', () => {
        it('display bundle information when available', async () => {
            const data = cloneDeep(testMap.CheckoutPaymentAA);
            data.pageData.entries[0].bundleInfo = {
                code: 'BI_M_3',
                name: 'M',
                size: 3
            }
            data.coreData.basestore = BaseStores.AA;
            await createInlineUiTestPage(data);
            await acceptCookieBanner();
            await resetMouseCoords();

            await Sizes.testAllScreenSizes(pageHeight);
        });
    });
});
