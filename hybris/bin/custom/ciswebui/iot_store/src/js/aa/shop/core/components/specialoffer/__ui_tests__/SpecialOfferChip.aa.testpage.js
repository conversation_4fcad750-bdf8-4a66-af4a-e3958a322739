import 'common/testtools/ui_tests_mock';
import {createApp} from 'vue-demi';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import SpecialOfferChip from 'aa/shop/core/components/specialoffer/SpecialOfferChip.vue';

const options = {
    vuetify,
    components: {
        TestPageRoot,
        SpecialOfferChip
    },
    template: `
      <test-page-root>
        <div ref="mainwrap">
          <SpecialOfferChip />
          <SpecialOfferChip small />
        </div>
      </test-page-root>
    `
};

const app = createApp(options);
app.use(i18n);
app.mount('#vue-app');
