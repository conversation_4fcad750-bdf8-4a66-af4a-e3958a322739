<template>
  <div class="order-details-container">
    <v-container :fluid="$vuetify.breakpoint.lgAndDown" class="mt-4 mt-lg-16">
      <v-row v-if="!improvedNavigationEnabled" class="breadcrumb-container">
        <v-col>
          <a :href="backLink">{{ $t('shop.orderHistory.title') }}</a>&ensp;>&ensp;<span>{{ pageData.code }}</span>
        </v-col>
      </v-row>
      <v-row>
        <v-col>
          <span class="text-h1">{{ $t('shop.orderHistory.order') }} {{ pageData.code }}</span>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" lg="4">
          <order-info :order-details-data="pageData"></order-info>
          <order-items :order-entries="entries" class="mt-6"></order-items>
        </v-col>
        <v-col cols="12" lg="8">
          <order-invoices :order-details-data="pageData" :is-user-company-under-review="coreData.userCompanyUnderReview"></order-invoices>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import {ShopRoute} from 'common/constants';
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CartItemData, InvoiceData, OrderDetailsData, ShopCoreData} from 'common/generated-types/types';
import {lowerCase} from 'lodash';
import OrderItems from 'aa/shop/account/orderdetails/components/OrderItems.vue';
import OrderInfo from 'aa/shop/account/orderdetails/components/OrderInfo.vue';
import OrderInvoices from 'aa/shop/account/orderdetails/components/OrderInvoices.vue';
import {messageService} from 'common/services';
import {shopCoreDataProvider} from 'common/provider';

@Component({
  components: {
    OrderInvoices,
    OrderInfo,
    OrderItems
  }
})
export default class OrderDetails extends Vue {
  @Prop() coreData!: ShopCoreData;
  @Prop() pageData!: OrderDetailsData;

  get entries(): CartItemData[] {
    return this.pageData.entries;
  }

  get invoices(): InvoiceData[] {
    return this.pageData.invoices;
  }

  get backLink(): string {
    return ShopRoute.ORDER_HISTORY;
  }

  get currency(): string {
    return this.coreData.currentCurrency;
  }

  get formattedStatus(): string {
    return this.$t(`shop.orderStatus.${lowerCase(this.pageData.status)}`).toString();
  }

  get improvedNavigationEnabled(): boolean {
    return Boolean(shopCoreDataProvider.data.moduleConfig.ENABLE_IMPROVED_NAVIGATION);
  }

  created(): void {
    this.setPageTitle();

    const arr = this.$route.query['paid-orders'] as string;
    if (arr) {
      messageService.success(this.paidWithCreditCard(arr), false, true);
    }
  }

  paidWithCreditCard(arr: string): string {
    return `${this.$t('shop.orderHistory.creditCardPaymentSuccess')}` + arr.split(',').join(', ');
  }

  private setPageTitle(): void {
    document.title = `Order ${this.pageData.code} – ${this.$t('navigation.storePageTitle') as string}`;
  }
}
</script>
