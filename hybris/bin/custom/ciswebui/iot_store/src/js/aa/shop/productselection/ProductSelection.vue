<template>
  <v-container v-if="product" :fluid="$vuetify.breakpoint.lgAndDown" class="mb-16 mt-8 pa-4">
    <account-notification :userCompanyUnderReview="coreData.userCompanyUnderReview"></account-notification>
    <product-selection-header :product="product"></product-selection-header>
    <product-selection-container :product="product" @request-discount-confirmation="onRequestDiscountConfirmation">
    </product-selection-container>
    <product-selection-summary v-if="displaySelectionSummary">
    </product-selection-summary>
    <confirmation-dialog
        v-bind:confirmation="confirmationDialog"
        :fullscreen="$vuetify.breakpoint.mdAndDown"
        v-if="showConfirmationDialog"
        v-on:cancel="onCancel"
        v-on:confirm="onConfirm"
    ></confirmation-dialog>
  </v-container>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {useProductSelectionStore} from 'shop/store/productSelection';
import {ShopCoreData, SimpleProductData} from 'common/generated-types/types';
import ProductSelectionHeader from 'aa/shop/productselection/components/ProductSelectionHeader.vue';
import ProductSelectionContainer from 'aa/shop/productselection/components/ProductSelectionContainer.vue';
import ProductSelectionSummary from 'aa/shop/productselection/components/ProductSelectionSummary.vue';
import {ShopRoute} from 'common/constants';
import {messageService, userPermissionService} from 'common/services';
import AccountNotification from 'aa/shop/account/AccountNotification.vue';
import {CartResource, StoreDataProvider} from 'shop/resources';
import ConfirmationDialog from 'common/components/popups/ConfirmationDialog.vue';
import {ConfirmationDialogType} from 'common/types';

@Component({
  components: {
    ConfirmationDialog,
    AccountNotification,
    ProductSelectionContainer,
    ProductSelectionHeader,
    ProductSelectionSummary
  }
})
export default class ProductSelection extends Vue {
  @Prop() productCode!: string;

  coreData: ShopCoreData = StoreDataProvider.coreData;
  productSelectionStore = useProductSelectionStore();

  showConfirmationDialog = false;

  created(): void {
    this.setPageTitle();
  }

  async mounted() {
    this.fetchProduct();
    this.productSelectionStore.setCountry(this.coreData.currentCountry?.isocode);
    this.productSelectionStore.setUserGroup(this.coreData.currentCompany?.userGroup);
  }

  onRequestDiscountConfirmation() {
    this.showConfirmationDialog = true;
  }

  fetchProduct(): void {
    this.productSelectionStore.fetchProduct(this.productCode, () => {
      messageService.error(this.$t('api.error.product.notFound') as string, true);
      window.location.assign(ShopRoute.HOME);
    });
  }

  get product(): SimpleProductData | undefined {
    return this.productSelectionStore.product;
  }

  get displaySelectionSummary(): boolean {
    return userPermissionService.hasAccessToCart();
  }

  get confirmationDialog(): ConfirmationDialogType {
    return {
      header: this.$t('shop.thl.popup.header') as string,
      bodyLines: [
        this.$t('shop.thl.popup.body.line1',
            {
              productName: this.productSelectionStore?.appName,
              bundleProductName: this.productSelectionStore?.product?.addonProductName,
              discountPercentage: this.productSelectionStore.addonDiscount
            }) as string,
        this.$t('shop.thl.popup.body.line2',
            {
              productName: this.productSelectionStore?.appName,
              bundleProductName: this.productSelectionStore?.product?.addonProductName
            }) as string
      ],
      buttonLabel: this.$t('shop.thl.popup.button.confirmation') as string,
      cancelButtonLabel: this.$t('shop.thl.popup.button.cancel') as string,
    };
  }

  async onConfirm() {
    this.showConfirmationDialog = false;

    try {
      const response = await CartResource.setPromotionConsent(this.productSelectionStore?.selection?.[0].code as string);
      const promotionData = response.data;
      console.log('Promotion Data:', promotionData);
      this.productSelectionStore.setDiscountedPrices(promotionData);
      this.productSelectionStore.consent = true;
    } catch (error) {
      this.productSelectionStore.consent = null;
      console.error('Error fetching promotion consent:', error);
    }
  }

  onCancel() {
    this.showConfirmationDialog = false;
    this.productSelectionStore.consent = false;
    this.productSelectionStore.setDiscountedPrices(null);
  }

  private setPageTitle(): void {
    document.title =
        this.$t('shop.productSelection.pageTitle') as string
        + ' – ' + this.productCode
        + ' - ' + this.$t('navigation.storePageTitle') as string;
  }
};
</script>
