import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import CategoriesGroupsNavigation from 'aa/shop/productsoverview/components/CategoriesGroupsNavigation.vue';
import categories from 'common/testtools/scenariosstore/aa/categories.json';
import coreDataDefault from 'common/testtools/scenariosstore/aa/coreDataDefault.json';

Vue.use(VueI18n);
Vue.use(pinia);

window.frontendData.coreData = coreDataDefault;

const storeRootStore = useStoreRootStore();
storeRootStore.coreData = coreDataDefault;

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    pinia,
    components: {
        TestPageRoot,
        CategoriesGroupsNavigation
    },
    data: {
        categoriesGroups: categories.subcategories
    },
    template: `
      <test-page-root>
        <div style="padding: 60px;" ref="mainwrap">
          <categories-groups-navigation
              :categories-groups="categoriesGroups">
          </categories-groups-navigation>
        </div>
      </test-page-root>
    `
});
