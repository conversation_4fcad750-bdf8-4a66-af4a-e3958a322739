import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'aa/shop/i18n';
import vuetify from 'common/plugins/brands/bosch/vuetify';
import pinia from 'shop/store';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import PacketComparisonDialog from 'aa/shop/productsoverview/components/PacketComparisonDialog.vue';
import data from 'common/testtools/scenariosstore/aa/packetComparisonDialogData.json';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    pinia,
    components: {
        TestPageRoot,
        PacketComparisonDialog
    },
    data: {
        dialog: true,
        products: data.products,
        billsOfMaterials: data.billsOfMaterials
    },
    template: `
      <test-page-root>
        <div style="padding: 60px;" ref="mainwrap">
          <packet-comparison-dialog
              v-model="dialog"
              @close="dialog = false"
              :bills-of-materials="billsOfMaterials"
              :products="products">
          </packet-comparison-dialog>
        </div>
      </test-page-root>
    `
});