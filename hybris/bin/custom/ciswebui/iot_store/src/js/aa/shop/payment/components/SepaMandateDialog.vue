<template>
  <popup-dialog class="sepa-mandate-dialog"
                data-id="add-sepa-mandatepopup-dialog"
                v-bind="$attrs"
                v-on="$attrs"
                :close-button="{ text: t('cancel') }"
                :submit-button="submitButtonProps"
                @submit="submitForm"
                @close="closeDialog"
                :header-close="true"
  >
    <template #header>
      {{ t('shop.payment.sepaMandate.header') }}
    </template>

    <v-container class="mt-4 px-0">
      <CDBlock :border-bottom="true" :padding-all="false" :padding-bottom="true">
        <v-row>
          <v-col cols="12">
            <payment-method-icons class="mt-n2"
                                  :paymentType="PaymentMethodType.SEPA_DIRECTDEBIT"
            >
            </payment-method-icons>

            <CDInput v-model="accountHolderName"
                     :label="t('shop.payment.sepaMandate.accountHolderName')"
                     :error="Boolean(holderNameError)"
                     :error-messages="holderNameError"
                     required
                     outlined
                     class="sepa-input mb-4 mt-4"
                     data-id="sepa-mandate-account-holder-name"
                     :disabled="isSubmitting"
            >
            </CDInput>

            <CDInput v-model="iban"
                     :label="t('shop.payment.sepaMandate.iban')"
                     :error="Boolean(ibanError)"
                     :error-messages="ibanError"
                     required
                     outlined
                     data-id="sepa-mandate-iban"
                     class="sepa-input"
                     :disabled="isSubmitting"
            >
            </CDInput>

            <v-row>
              <v-col cols="11">
                <CDCheckbox v-model="useAsDefault"
                            :label="t('shop.payment.sepaMandate.defaultPaymentCheckbox')"
                            :color="useAsDefault ? 'primary' : 'grey'"
                            data-id="sepa-mandate-default-checkbox"
                            :disabled="isSubmitting"
                >
                  {{ t('shop.payment.sepaMandate.defaultPaymentCheckbox') }}
                </CDCheckbox>
              </v-col>
              <v-col cols="1" class="d-flex align-center pl-0">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on }">
                    <CDIcon v-on="on" dense>$info</CDIcon>
                  </template>
                  {{ t('shop.payment.sepaMandate.defaultTooltip') }}
                </v-tooltip>
              </v-col>
            </v-row>
          </v-col>
        </v-row>

        <v-row class="mt-4 mb-2">
          <v-col cols="12">
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.creditor') }}:
              </h5>
              <span>{{ props.creditor }}</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.creditorIdentifier') }}:
              </h5>
              <span>{{ props.creditorIdentifier }}</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.mandateReference') }}:
              </h5>
              <span v-if="currentMandateReference">{{ currentMandateReference }}</span>
              <span v-else class="grey--text">Loading...</span>
            </div>
            <div class="mb-4">
              <h5 class="d-inline-block mr-2">
                {{ t('shop.payment.sepaMandate.typeOfPayment') }}:
              </h5>
              <span>{{ props.typeOfPayment }}</span>
            </div>
          </v-col>
        </v-row>
      </CDBlock>

      <v-row class="mt-4">
        <v-col cols="12">
          <p>
            {{
              t('shop.payment.sepaMandate.legalText', {
                paymentProvider: 'Adyen',
                beneficiary: props.creditor
              })
            }}
          </p>
        </v-col>
      </v-row>
    </v-container>
  </popup-dialog>
</template>

<script setup lang="ts">
import {computed, onMounted, ref} from 'vue';
import {useI18n} from 'vue-i18n-bridge';
import {PaymentMethodType, ShopCoreData} from 'common/generated-types/types';
import {messageService, sepaMandateService} from 'common/services';
import {paymentResource} from 'shop/resources';
import {MandateStatus, SepaMandatePayload} from 'common/types';
import {PopupDialog} from 'common/components/popups';
import {PaymentMethodIcons} from 'common/components';

const props = defineProps<{
  creditor: string;
  creditorIdentifier: string;
  mandateReference: string;
  typeOfPayment: string;
  coreData: ShopCoreData;
}>();

const emit = defineEmits<{
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();

const {t} = useI18n();

const accountHolderName = ref('');
const iban = ref('');
const useAsDefault = ref(false);
const isSubmitting = ref(false);
const currentMandateReference = ref('');
const isInitialized = ref(false);
const mandateStatus = ref<MandateStatus | null>(null);


const ibanError = computed(() => {
  if (!iban.value) return '';
  return sepaMandateService.isValidIban(iban.value)
      ? ''
      : (t('shop.payment.sepaMandate.error.invalidIban') as string);
});

const holderNameError = computed(() => {
  if (!accountHolderName.value) return '';
  return sepaMandateService.isValidAccountHolderName(accountHolderName.value)
      ? ''
      : (t('shop.payment.sepaMandate.error.accountHolder') as string);
});

const submitButtonProps = computed(() => ({
  text: t('save'),
  isDisabled: isSubmitDisabled.value
}));

const isSubmitDisabled = computed(() => {
  return isSubmitting.value || (mandateStatus.value === MandateStatus.ACTIVE);
});

interface MandateData {
  accountHolderName?: string;
  iban?: string;
  mandateReference?: string;
  status?: MandateStatus;
}

const initializeMandate = async (): Promise<void> => {
  try {
    const response = await paymentResource.initializeSepaMandateDraft(props.coreData.currentCompany?.companyUid);
    const mandateData = response.data;

    if (mandateData?.mandateReference) {
      currentMandateReference.value = mandateData.mandateReference;
    }

    populateFormFields(mandateData, props.coreData.userName);
    isInitialized.value = true;
  } catch (initError) {
    messageService.error(t('backendError').toString());
    populateFormFields(null);
  }
};

const submitForm = async (): Promise<void> => {
  const validation = validateSubmission();
  if (!validation.isValid) return;

  isSubmitting.value = true;

  const payload: SepaMandatePayload = {
    accountHolderName: accountHolderName.value,
    iban: validation.trimmedIban!,
    useAsDefault: useAsDefault.value,
    signatureDate: new Date().toISOString().split('T')[0],
    companyId: props.coreData.currentCompany?.companyUid
  };

  try {
    const response = await paymentResource.updateOrFinalizeSepaMandateByReference(
        currentMandateReference.value,
        payload
    );

    if (!response?.data) {
      throw new Error(`Submission failed with status ${response.status}`);
    }

    handleResponse(response.data);
  } catch (error) {
    handleError(error);
  } finally {
    isSubmitting.value = false;
  }
};

const populateFormFields = (mandateData: MandateData | null, fallbackName?: string): void => {
  if (mandateData && Object.keys(mandateData).length > 0) {
    accountHolderName.value = mandateData.accountHolderName || fallbackName || '';
    iban.value = mandateData.iban || '';
    mandateStatus.value = mandateData.status || null;
    if (mandateData.mandateReference) {
      currentMandateReference.value = mandateData.mandateReference;
    }
  } else {
    accountHolderName.value = fallbackName || '';
    iban.value = '';
    mandateStatus.value = null;
  }
};

const validateSubmission = (): { isValid: boolean; trimmedIban?: string } => {
  if (isSubmitting.value || !isInitialized.value) {
    return {isValid: false};
  }

  const trimmedIban = iban.value.replace(/\s/g, '');

  if (!sepaMandateService.isValidIban(trimmedIban)) {
    messageService.error(
        t('shop.payment.sepaMandate.error.ibanRequired').toString()
    );
    return {isValid: false};
  }

  return {isValid: true, trimmedIban};
};

const handleResponse = (responseData: {
  accountHolderName?: string;
  iban?: string;
  mandateReference?: string;
  status?: MandateStatus;
} | null): void => {
  if (responseData && Object.keys(responseData).length > 0) {
    accountHolderName.value = responseData.accountHolderName || accountHolderName.value;
    iban.value = responseData.iban || iban.value;
    currentMandateReference.value = responseData.mandateReference || currentMandateReference.value;
    mandateStatus.value = responseData.status as MandateStatus || mandateStatus.value;
  }

  messageService.success(t('shop.payment.sepaMandate.success').toString());
  emit('confirm');
};

const handleError = (error: unknown): void => {
  const errorResponse = error as { response?: { status?: number; data?: { message?: string } } };

  if (errorResponse.response?.data?.message) {
    messageService.error(errorResponse.response.data.message);
  } else if (errorResponse.response?.status === 400) {
    messageService.error(t('shop.payment.sepaMandate.error.invalidInput').toString());
  } else {
    messageService.error(t('shop.payment.sepaMandate.error.submissionFailed').toString());
  }
};

const closeDialog = () => {
  emit('cancel');
};

onMounted(() => {
  initializeMandate();
});
</script>

<style scoped>
p,
span,
h5 {
  color: var(--v-grey-darken3);
}

.sepa-input :deep(.v-input__slot) {
  background-color: var(--v-grey-lighten3) !important;
  border-radius: unset;
  border-bottom: 1px solid var(--v-grey-darken3);
}
</style>