import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import VariantItem from '../VariantItem.vue';
import {useProductSelectionStore} from 'shop/store/productSelection';
import {cloneDeep} from "lodash";
import {useStoreRootStore} from 'aa/shop/store/storeRoot';


const storeRootStore = useStoreRootStore()

const mountVariantItem = (variant) => wrapperComponentFactory(VariantItem, {
    props: {
        variant: variant
    },
    mocks: {},
    shallow: false
});

const defaultCoreData = {
    moduleConfig: {
        DIRECT_SALES: true
    },
    currentCompany: {
        managedAccount: false
    },
    userName: 'auth_user',
}

describe('VariantItem', () => {
    let variantItem;
    let coreData

    beforeEach(() => {
        variantItem = {
            code: "AA2_0401987P12824",
            price: 1885.0,
            currencyIsocode: "EUR",
            licenseType: {
                code: "SUBSCRIPTION"
            },
            runtime: {
                code: "runtime_subs_unlimited"
            },
            specialOffer: false
        };
        coreData = cloneDeep(defaultCoreData);
        coreData.thlAppCodeInCart = 'some_code';
        storeRootStore.coreData = coreData;
        window.frontendData.coreData = coreData;
    });

    describe('Bundle Label', () => {
        it('is displayed when variant is a bundle', async () => {
            variantItem.bundleInfo = {
                code: 'BI_M_3',
                name: 'M',
                size: 3
            };

            const wrapper = await mountVariantItem(variantItem);

            const bundleLabel = wrapper.find('[data-id="text-bundle-label"]');
            expect(bundleLabel.exists()).toBeTruthy();
            expect(bundleLabel.text()).toBe('M (3 licenses)');
        });

        it('is not displayed when variant is not a bundle', async () => {
            variantItem.bundleInfo = {
                code: null,
                name: null,
                size: 0
            };

            const wrapper = await mountVariantItem(variantItem);

            const bundleLabel = wrapper.find('[data-id="text-bundle-label"]');
            expect(bundleLabel.exists()).toBeFalsy();
        });
    });

    describe('Add button', () => {
        beforeEach(() => {
            variantItem.bundleInfo = {
                code: 'BI_M_3',
                name: 'S',
                size: 1,
            };
            variantItem.code = 'BI_3_code';
        });

        it('is not disabled when variant is purchasable', async () => {
            variantItem.purchasability = 'PURCHASABLE';
            const wrapper = await mountVariantItem(variantItem);

            const addButton = wrapper.find('[data-id="button-add-variant-BI_3_code"]');
            expect(addButton.exists()).toBeTruthy();
            expect(addButton.attributes('disabled')).toBeFalsy();
        });

        it('is disabled when variant is not purchasable', async () => {
            variantItem.purchasability = 'UNAVAILABLE';
            const wrapper = await mountVariantItem(variantItem);

            const addButton = wrapper.find('[data-id="button-add-variant-BI_3_code"]');
            expect(addButton.exists()).toBeTruthy();
            expect(addButton.attributes('disabled')).toBeTruthy();
        });

        it('is disabled when variant is not purchasable and no price is provided', async () => {
            variantItem.purchasability = 'UNAVAILABLE';
            variantItem.price = null;
            const wrapper = await mountVariantItem(variantItem);

            const addButton = wrapper.find('[data-id="button-add-variant-BI_3_code"]');
            expect(addButton.exists()).toBeTruthy();
            expect(addButton.attributes('disabled')).toBeTruthy();
        });

        it('shows imported company tooltip when variant is not eligible due to migration', async () => {
            variantItem.purchasability = 'IMPORTED_COMPANY_NOT_ELIGIBLE';
            const wrapper = await mountVariantItem(variantItem);
            const addButton = wrapper.find('[data-id="button-add-variant-BI_3_code"]');
            const tooltip = wrapper.find(`[data-id="tooltip-variant-non-purchasable-${variantItem.code}"]`);

            expect(addButton.exists()).toBeTruthy();
            expect(addButton.attributes('disabled')).toBeTruthy();
            expect(tooltip.exists()).toBeTruthy();
        });
    });

    describe('Price Label', () => {
        it('is displayed when price is provided', async () => {
            const wrapper = await mountVariantItem(variantItem);

            const priceLabels = wrapper.findAll('[data-id^="text-variant-price-"]');
            expect(priceLabels).toHaveLength(1);
        });

        it('is not displayed when no price is provided', async () => {
            variantItem.price = null;

            const wrapper = await mountVariantItem(variantItem);

            const priceLabels = wrapper.findAll('[data-id^="text-variant-price-"]');
            expect(priceLabels).toHaveLength(0);
        });
    });

    describe('Special Offer Chip', () => {
        it('container is rendered when variant is a special offer', async () => {
            variantItem.specialOffer = true;
            const wrapper = await mountVariantItem(variantItem);

            const specialPriceChip = wrapper.find(`[data-id="container-special-price-chip-product-${variantItem.code}"]`);
            expect(specialPriceChip.exists()).toBeTruthy();
        });

        it('container is not rendered when variant is not special offer', async () => {
            variantItem.specialOffer = false;
            const wrapper = await mountVariantItem(variantItem);

            const specialPriceChip = wrapper.find(`[data-id="container-special-price-chip-product-${variantItem.code}"]`);
            expect(specialPriceChip.exists()).toBeFalsy();
        });

        it('container is rendered when other variant with same runtime is a special offer', async () => {
            variantItem.specialOffer = false;
            const productSelectionStore = useProductSelectionStore();
            productSelectionStore.product = {
                licenses: [
                    variantItem,
                    {
                        runtime: variantItem.runtime,
                        specialOffer: true
                    }
                ]
            };
            const wrapper = await mountVariantItem(variantItem);

            const specialPriceChip = wrapper.find(`[data-id="container-special-price-chip-product-${variantItem.code}"]`);
            expect(specialPriceChip.exists()).toBeTruthy();
        });
    });
});
