import DistributorPanel from 'aa/shop/checkout/components/DistributorPanel.vue';
import DistributorDialog from 'aa/shop/checkout/components/DistributorDialog.vue';
import { cloneDeep } from 'lodash';
import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';

const mountDistributorPanel = (listDistributors, currentDistributor) =>
    wrapperComponentFactory(DistributorPanel, {
        props: {
            listDistributors,
            currentDistributor
        },
        shallow: false
    });

describe('AA Distributor Panel', () => {
    let wrapper;
    let distributorList;
    let currentDist;

    beforeEach(() => {
        distributorList = [
            { name: '<PERSON>' },
            { name: '<PERSON>' },
            null,
            { name: 'Bravo' }
        ];
        currentDist = { name: 'Bravo' };
        wrapper = mountDistributorPanel(cloneDeep(distributorList), currentDist);
    });

    it('filters out falsy distributors and sorts by name', () => {
        const names = wrapper.vm.getOnlyValidDistributors.map(d => d.name);
        expect(names).toEqual(['Alpha', 'Bravo', 'Charlie']);
    });

    it('displays the current distributor name when provided', () => {
        const displayed = wrapper.find('[data-id="current-distributor-name"]').text();
        expect(displayed).toBe('Bravo');
    });

    it('opens the dialog when the change button is clicked', async () => {
        await wrapper.find('[data-id="distributor-change-button"]').trigger('click');
        expect(wrapper.vm.distributorDialog).toBeTruthy()
    });

    it('renders DistributorDialog when dialog is open', async () => {
        wrapper.setData({ distributorDialog: true });
        await wrapper.vm.$nextTick();
        const dialog = wrapper.findComponent(DistributorDialog);
        expect(dialog.exists()).toBeTruthy();
        expect(dialog.props('distributorsList')).toEqual(
            wrapper.vm.getOnlyValidDistributors
        );
    });
});