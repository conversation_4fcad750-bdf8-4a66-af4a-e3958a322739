<template>
  <div
    data-id="footer">
    <CDFooter
        class="mt-16 mt-md-0"
        :links="cdFooterItems"
        v-bind="footerProps">
    </CDFooter>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {NavigationItemData, NavigationItemGroup} from 'common/generated-types/types';
import {i18nService, navigationService} from 'common/services';
import {shopCoreDataProvider} from 'common/provider';
import {useLanguageSwitcherStore} from 'aa/shop/store/languageSwitcher';

type FooterItemUrl = {
  url: string;
  text: string;
  target?: string;
  attributes?: { [key: string]: string };
}

type FooterItemFn = {
  text: string;
  onClick: (event: Event) => void;
}

type FooterProps = {
  copyright?: string;
  'brand-label'?: string;
};

type FooterItem = FooterItemUrl | FooterItemFn;

@Component
export default class PageFooter extends Vue {
  isLanguageSwitcherFeatureOn = Boolean(shopCoreDataProvider.data.moduleConfig.LANGUAGE_SWITCHER);
  languageSwitcherStore = useLanguageSwitcherStore();

  get footerProps(): FooterProps {
    const isoCode = i18nService.getUserCountry();
    switch (isoCode) {
      case 'DE':
        return { copyright: this.$t('navigation.copyright.de').toString() };
      default:
        return { 'brand-label': 'Bosch' };
    }
  }

  get currentCountryName(): string {
    const isoCode = i18nService.getUserCountry();
    if (!isoCode) {
      return '';
    }
    return this.$t(`country.${isoCode}`).toString();
  }

  get cdFooterItems(): FooterItem[] {
    const footerData = navigationService.byGroupSorted(NavigationItemGroup.FOOTER);
    const footerItems: FooterItem[] = [];
    footerData.forEach(navItem => {
      if(!this.isLocation(navItem)) {
        footerItems.push(this.processAsFooterItemUrl(navItem));
      } else if (this.isLanguageSwitcherFeatureOn) {
        footerItems.push(this.processAsFooterItemFn(navItem));
      }
    });
    return footerItems;
  }

  processAsFooterItemFn(navItem: NavigationItemData): FooterItemFn {
    return {
      text: this.formatLocationNavigationItemLabel(navItem),
      onClick: () => { this.languageSwitcherStore.dialogOpened = true; },
      attributes: navItem.customAttributes
    } as FooterItemFn;
  }

  processAsFooterItemUrl(navItem: NavigationItemData): FooterItemUrl {
    return {
      url: navItem.url,
      text: this.translateItemText(navItem),
      target: navItem.target,
      attributes: navItem.customAttributes
    } as FooterItemUrl;
  }

  formatLocationNavigationItemLabel(navItem: NavigationItemData): string {
    return this.translateItemText(navItem) + ': ' + this.currentCountryName;

  }

  isLocation(navItem: NavigationItemData): boolean {
    return navItem.id === 'aa_storeRegionAndLanguageFooter';
  }

  translateItemText(navItem: NavigationItemData): string {
    return navigationService.translate(this.$i18n, navItem);
  }
}
</script>
