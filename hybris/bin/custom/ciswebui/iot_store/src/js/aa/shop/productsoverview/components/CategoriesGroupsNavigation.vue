<template>
  <v-row
      class="navigation-bar sticky-row"
      :class="{ 'under-store-header' : !improvedNavigationEnabled && storeHeaderStore.isActive }"
      data-id="container-categories-groups-navigation">

    <v-row v-if="$vuetify.breakpoint.mdAndDown"
           dense no-gutters
           class="search-row">
      <CDButtonTextIcon
          :href="searchAndFilterRoute"
          icon="$filter"
          color="primary"
          class="mx-4">
        {{ $t('shop.productsOverview.navigation.searchByHardware') }}
      </CDButtonTextIcon>
    </v-row>

    <v-row dense no-gutters class="fill-width align-center py-2 categories-navigation">
      <CDButtonText v-for="categoryGroup in categoriesGroups" :key="categoryGroup.code"
                    @click="$emit('categoryGroupLinkClicked', categoryGroup)"
                    class="category-group-link mx-4"
                    :class="{ activeHighlight : categoryGroup === categoryGroupInFocus }"
                    :data-id="`link-category-group-${categoryGroup.code}`">
        {{ categoryGroup.name }}
      </CDButtonText>

      <div v-if="$vuetify.breakpoint.lgAndUp"
           class="content-end d-flex">
        <CDButtonTextIcon
            :href="searchAndFilterRoute"
            icon="$search"
            color="primary">
          {{ $t('shop.productsOverview.navigation.searchByHardware') }}
        </CDButtonTextIcon>
      </div>
    </v-row>


  </v-row>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryHierarchyDTO} from 'common/generated-types/types';
import {ShopRoute} from 'common/constants';
import ProductsOverviewHeader from 'aa/shop/productsoverview/components/ProductsOverviewHeader.vue';
import CategoryGroupDetails from 'aa/shop/productsoverview/components/categorydetails/CategoryGroupDetails.vue';
import {useStoreHeaderStore} from 'aa/shop/store/storeHeader';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';


@Component({
  components: {CategoryGroupDetails, ProductsOverviewHeader}
})
export default class CategoriesGroupsNavigation extends Vue {
  @Prop() categoriesGroups!: CategoryHierarchyDTO[];
  @Prop() categoryGroupInFocus!: CategoryHierarchyDTO;

  storeHeaderStore = useStoreHeaderStore();
  storeRootStore = useStoreRootStore();

  searchAndFilterRoute: string = ShopRoute.PRODUCTS_SEARCH;

  get improvedNavigationEnabled(): boolean {
    return Boolean(this.storeRootStore.coreData.moduleConfig?.ENABLE_IMPROVED_NAVIGATION);
  }

};
</script>

<style lang="scss" scoped>
@import 'common/vuetify';
@import 'shop/core/constants';

.navigation-bar {
  top: 0;
  z-index: 2;
  background-color: white;
  transition: top 0.2s map-get($transition, 'fast-out-slow-in');

  &.under-store-header {
    top: $header-height;
  }
}

.fill-width {
  overflow-x: auto;
  flex-wrap: nowrap;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.sticky-row {
  position: sticky;
}

.categories-navigation {
  border-bottom: 1px solid;
  border-bottom-color: var(--v-grey-lighten1);

  .content-end {
    flex-grow: 1;
    justify-content: flex-end;
  }
}

.category-group-link {
  &.activeHighlight {
    &:after {
      content: "";
      display: block;
      position: absolute;
      bottom: -8px;
      width: 100%;
      height: 2px;
      background-color: var(--v-grey-darken3);
      @media #{map-get($display-breakpoints, 'lg-and-up')} {
        bottom: -13px;
      }
    }
  }
}

::-webkit-scrollbar {
  display: none;
}

.search-row {
  width: 100%
}
</style>
