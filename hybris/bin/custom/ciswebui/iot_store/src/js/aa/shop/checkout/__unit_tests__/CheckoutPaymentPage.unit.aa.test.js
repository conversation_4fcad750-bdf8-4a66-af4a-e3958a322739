import { wrapperComponentFactory } from 'common/testtools/unit-test-utils';
import { pageSpinner } from 'common/components/spinner';
import { testMap } from 'common/testtools/scenariosstore';
import { PaymentMethodType } from 'common/generated-types/types';
import CheckoutPaymentPage from '../CheckoutPaymentPage';
import checkoutPaymentDataWithCountryEulas from "common/testtools/scenariosstore/aa/checkoutPaymentDataWithCountryEulas.json";
import coreDataDefault from "common/testtools/scenariosstore/aa/coreDataDefault.json";
import {messageService} from "common/services";
import {cloneDeep} from "lodash";

jest.mock('common/services');
jest.mock('common/components/spinner', () => ({
    pageSpinner: { start: jest.fn(), stop: jest.fn() },
}));

describe('CheckoutPaymentPage no distributors', () => {
    let coreData = cloneDeep(coreDataDefault);
    let propsData;

    beforeEach(() => {
        propsData = cloneDeep(testMap.CheckoutPaymentAA);
        propsData.coreData = coreData;

        window.frontendData = {
            coreData: coreData
        };
    });

    it('does NOT show an error when no distributor is selected and distributor list is empty', async () => {
        const propsWithoutDistributors = cloneDeep(propsData);
        propsWithoutDistributors.pageData.listOfDistributors = [];

        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsWithoutDistributors,
            shallow: false,
            mocks: {
                $t: key => key,
                $n: key => key
            },
        });

        expect(wrapper.vm.hasDistributors).toBeFalsy();
        wrapper.vm.setDistributor(null);
        expect(wrapper.vm.distributorSelected()).toBeFalsy();

        await wrapper.vm.$nextTick();

        const placeOrderButton = wrapper.find('[data-id="place-order-btn"]');
        await placeOrderButton.trigger('click');

        await wrapper.vm.$nextTick();

        expect(messageService.error)
            .not
            .toHaveBeenCalledWith('shop.checkout.distributor.notSelected', false, true);
    });
});

describe('CheckoutPaymentPage distributors', () => {
    let coreData = cloneDeep(coreDataDefault);
    let propsData;

    beforeEach(() => {
        propsData = cloneDeep(testMap.CheckoutPaymentAA);
        propsData.coreData = coreData;

        window.frontendData = {
            coreData: coreData
        };
    });

    it('shows an error when no distributor is selected on submit', async () => {
        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
            shallow: false,
            mocks: {
                $t: key => key,
                $n: key => key
            },
        });

        const placeOrderButton = wrapper.find('[data-id="place-order-btn"]');
        wrapper.vm.distributorId = '';
        await wrapper.vm.$nextTick();
        await placeOrderButton.trigger('click');

        expect(messageService.error)
            .toHaveBeenCalledWith('shop.checkout.distributor.notSelected', false, true);
        expect(pageSpinner.stop).toHaveBeenCalled();
    });
});

describe('CheckoutPaymentPage ', () => {

    let propsData;

    beforeEach(() => {
        propsData = testMap.CheckoutPaymentAA;
    });

    it('set payment method', async () => {
        const selection = propsData.pageData.paymentInfo;
        selection.id = 'test_id';
        propsData.paymentMethod = PaymentMethodType.INVOICE;

        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
        });

        wrapper.vm.setPaymentMethod(selection);

        expect(wrapper.vm.currentPaymentMethodId).toEqual(selection.id);
        expect(wrapper.vm.currentPaymentMethodType).toEqual(PaymentMethodType.SEPA_CREDIT);
    });

    it('handles new credit card selection', async () => {
        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
        });

        wrapper.vm.enableNewCreditCard();

        expect(wrapper.vm.currentPaymentMethodId).toEqual(wrapper.vm.NEW_PAYMENT_METHOD);
        expect(wrapper.vm.currentPaymentMethodType).toEqual(PaymentMethodType.CREDIT_CARD);
    });

    it('handles new SEPA mandate selection', async () => {
        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
        });

        wrapper.vm.enableNewSepaMandate();

        expect(wrapper.vm.currentPaymentMethodId).toEqual(wrapper.vm.NEW_SEPA_MANDATE);
        expect(wrapper.vm.currentPaymentMethodType).toEqual(PaymentMethodType.SEPA_DIRECTDEBIT);
    });

    it('initializes payment selection correctly', async () => {
        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
        });

        wrapper.vm.initializeCurrentPaymentSelection();

        if (propsData.pageData.paymentInfo) {
            expect(wrapper.vm.currentPaymentMethodId).toEqual(propsData.pageData.paymentInfo.id);
            expect(wrapper.vm.currentPaymentMethodType).toEqual(propsData.pageData.paymentInfo.paymentMethod);
        } else {
            expect(wrapper.vm.currentPaymentMethodId).toEqual(wrapper.vm.NEW_PAYMENT_METHOD);
        }
    });

    it('confirms pending order correctly', async () => {
        propsData.pageData.confirmationPending = true;
        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
        });

        const confirmPendingOrderSpy = jest.spyOn(wrapper.vm, 'confirmPendingOrder');

        await wrapper.vm.confirmPendingOrder();

        expect(confirmPendingOrderSpy).toHaveBeenCalled();
        expect(wrapper.vm.confirmationPending).toBeFalsy();
    });

    it('correctly validates invoice notes', async () => {
        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
        });

        const validNote = 'This is a valid note';
        const invalidNote = 'This note exceeds the maximum length allowed for invoice notes';

        expect(wrapper.vm.invoiceNoteValid(validNote)).toBeTruthy();
        expect(wrapper.vm.invoiceNoteValid(invalidNote)).toBeFalsy();
    });

    it('confirms card deletion correctly', async () => {
        const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
            props: propsData,
        });

        const card = propsData.pageData.paymentInfo;
        wrapper.vm.confirmCardDelete(card);

        expect(wrapper.vm.confirmCard).toEqual(card);
        expect(wrapper.vm.showConfirmCard).toBeTruthy();
    });
});

describe('CheckoutPaymentPage with country EULAs', () => {
    const expectedCountryEulas = [
        {
            "label": "ESI[tronic] EULA",
            "url": "https://cdn.esitronic.de/eula/ESItronic/AT_EULA.html"
        },
        {
            "label": "THL EULA 1",
            "url": "https://cdn.esitronic.de/eula/THL/AT_EULA.html"
        },
        {
            "label": "THL EULA 2",
            "url": "https://cdn.esitronic.de/eula/THL/AT_EULA.html"
        },
        {
            "label": "THL EULA 3",
            "url": "https://cdn.esitronic.de/eula/THL/AT_EULA.html"
        }
    ];

    let coreData = cloneDeep(coreDataDefault);
    let propsData = {};

    coreData.moduleConfig.ENFORCE_EULA_ACCEPTANCE = true;
    window.frontendData = {
        coreData: coreData
    };

    propsData.pageData = checkoutPaymentDataWithCountryEulas;
    propsData.coreData = coreData;

    const wrapper = wrapperComponentFactory(CheckoutPaymentPage, {
        props: propsData,
        shallow: false,
        mocks: {
            $t: key => key,
            $n: key => key
        },
    });

    it('EULAs are rendered correctly', () => {
        wrapper.find('[data-id="checkbox-accept-eula"]').exists();

        expectedCountryEulas.forEach((expectedEula, index) => {
            const actualEula = wrapper.find(`[data-id="link-eula-${index}"]`);
            expect(actualEula.exists());
            expect(actualEula.attributes('href')).toEqual(expectedCountryEulas[index].url);
            expect(actualEula.text()).toEqual(expectedCountryEulas[index].label);
        });
    });

    it('error is shown when EULA is not accepted', async () => {
        const eulaCheckbox = wrapper.find('[data-id="checkbox-accept-eula"]');
        const placeOrderButton = wrapper.find('[data-id="place-order-btn"]');

        expect(eulaCheckbox.exists());
        expect(placeOrderButton.exists());

        await placeOrderButton.trigger('click');

        expect(messageService.error).toHaveBeenCalledWith('shop.error.eula.notAccepted', false, true);
    });
});


