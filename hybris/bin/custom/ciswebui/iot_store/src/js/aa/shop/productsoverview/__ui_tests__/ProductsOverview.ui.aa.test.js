import categories from 'common/testtools/scenariosstore/aa/categories.json';
import categories_with_long_pt_descriptions from 'common/testtools/scenariosstore/aa/categories_with_long_pt_descriptions.json';
import {acceptCookieBanner, createInlineUiTestPage, resetMouseCoords} from 'common/testtools/inlineTestPageCreator';
import {abortRequest, respondRequest} from 'common/testtools/testRequests';
import * as Sizes from 'common/testtools/testScreenSizes';
import {testMap} from 'common/testtools/scenariosstore';
import {breakpoints} from 'common/breakpoints';
import {cloneDeep} from 'lodash';
import {cookies_en_AT} from 'common/testtools/testCookies';
import coreData from 'common/testtools/scenariosstore/aa/coreDataFromRest.json';

const findCategory = (category, code) => {
    if (category.code === code) {
        return category;
    }

    for (const subcategory of category.subcategories) {
        const match = findCategory(subcategory, code);
        if (match) {
            return match;
        }
    }

    return null;
};
describe('Products Overview', () => {
    const REST_URL = '/shop/api/categories/main';
    const productsCategoriesRestHandler = respondRequest(REST_URL, {
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(categories)
    });

    beforeEach(async () => {
        await page.setRequestInterception(true);
        await page.setCookie(...cookies_en_AT);
    });

    it('looks as expected', async () => {
        page.on('request', productsCategoriesRestHandler);

        await createInlineUiTestPage(testMap.ProductsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(3000);
        await Sizes.testLargeScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testSmallScreensize(6000);
        await Sizes.testExtraSmallScreensize(6000);
    });

    it('looks as expected with company under review note is visible', async () => {
        page.on('request', productsCategoriesRestHandler);
        let data = cloneDeep(testMap.ProductsOverviewAA);
        data.coreData.userCompanyUnderReview=true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(3000);
        await Sizes.testLargeScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testSmallScreensize(6000);
        await Sizes.testExtraSmallScreensize(6000);
    });

    it('contains login cta banner when user not authenticated', async () => {
        page.on('request', productsCategoriesRestHandler);

        const coreData = cloneDeep(testMap.ProductsOverviewAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.userName = null;

        await createInlineUiTestPage({...testMap.ProductsOverviewAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2500);
    });

    it('contains managed account banner when user company is managed account', async () => {
        page.on('request', productsCategoriesRestHandler);

        const coreData = cloneDeep(testMap.ProductsOverviewAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.currentCompany.managedAccount = true;

        await createInlineUiTestPage({...testMap.ProductsOverviewAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2500);
    });

    it('contains managed account banner when user company is managed account and user is in another country store', async () => {
        page.on('request', productsCategoriesRestHandler);

        const coreData = cloneDeep(testMap.ProductsOverviewAA.coreData);
        coreData.moduleConfig.DIRECT_SALES = true;
        coreData.currentCompany.managedAccount = true;
        coreData.currentCountry.isocode = "PT";

        await createInlineUiTestPage({...testMap.ProductsOverviewAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testExtraLargeScreensize(2500);
    });

    it('navigation works as expected', async () => {
        page.on('request', productsCategoriesRestHandler);

        await createInlineUiTestPage(testMap.ProductsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.setViewport({width: breakpoints.L, height: 1000});
        await page.waitFor(1000);
        await page.click('[data-id="link-category-group-cat_3"]');
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'large size'
        });

        await page.setViewport({width: breakpoints.S, height: 800});
        await page.waitFor(1000);
        await page.click('[data-id="link-category-group-cat_4"]');
        await page.waitFor(1000);

        expect(await page.screenshot()).toMatchImageSnapshot({
            failureThreshold: 1,
            failureThresholdType: 'percent',
            customSnapshotIdentifier: 'small size'
        });
    });

    it('shows message for error response', async () => {
        page.on('request', respondRequest(REST_URL, {
                status: 400,
                contentType: 'application/json',
                body: JSON.stringify({userMessages: [{level: 'ERROR', code: 'generic.badRequest'}]})
            })
        );

        await createInlineUiTestPage(testMap.ProductsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for error response without error details', async () => {
        page.on('request', respondRequest(REST_URL, {
                status: 500,
                contentType: 'application/json',
                body: JSON.stringify({})
            })
        );

        await createInlineUiTestPage(testMap.ProductsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('shows message for aborted request', async () => {
        page.on('request', abortRequest(REST_URL));

        await createInlineUiTestPage(testMap.ProductsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        expect(await page.screenshot()).toMatchImageSnapshot('');
    });

    it('with long descriptions displayed as expected', async () => {
        page.on('request', respondRequest(REST_URL, {
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify(categories_with_long_pt_descriptions)
            })
        );

        await createInlineUiTestPage(testMap.ProductsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        await Sizes.testAllScreenSizes(2500);
    });

    it('thl category expanded', async () => {
        page.on('request', productsCategoriesRestHandler);

        await createInlineUiTestPage(testMap.ProductsOverviewAA);
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-expansion-panel-header-cat_1010102"]');

        await Sizes.testExtraLargeScreensize(3000);
        await Sizes.testLargeScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testSmallScreensize(6000);
        await Sizes.testExtraSmallScreensize(6000);
    });

    it('thl disabled because already in the cart', async () => {
        page.on('request', productsCategoriesRestHandler);

        const coreData = cloneDeep(testMap.ProductsOverviewAA.coreData);
        coreData.thlAppCodeInCart = "AA_04029271";

        await createInlineUiTestPage({...testMap.ProductsOverviewAA, coreData: coreData});
        await acceptCookieBanner();
        await resetMouseCoords();

        await page.click('[data-id="container-expansion-panel-header-cat_1010102"]');

        await Sizes.testExtraLargeScreensize(3000);
        await Sizes.testLargeScreensize(3000);
        await Sizes.testMediumScreensize(3000);
        await Sizes.testSmallScreensize(6000);
        await Sizes.testExtraSmallScreensize(6000);
    });

    describe('handles empty categories', () => {

        const clearProducts = category => {
            category.products = [];
            category.subcategories?.forEach(clearProducts);
        };

        const testAdjustedCategories = (adjustCategories) => async () => {
            const adjustedCategories = adjustCategories(cloneDeep(categories));

            page.on('request', respondRequest(REST_URL, {
                status: 200,
                contentType: 'application/json',
                body: JSON.stringify(adjustedCategories)
            }));

            await createInlineUiTestPage(testMap.ProductsOverviewAA);
            await acceptCookieBanner();
            await resetMouseCoords();

            await Sizes.testLargeScreensize(3000, 4000);
            await Sizes.testSmallScreensize(6000, 5000);

        };

        it('for section', testAdjustedCategories(categories => {
            clearProducts(findCategory(categories, 'cat_1'));
            return categories;
        }));

        it('for tab', testAdjustedCategories(categories => {
            clearProducts(findCategory(categories, 'cat_10101'));
            return categories;
        }));

        it('for accordion', testAdjustedCategories(categories => {
            clearProducts(findCategory(categories, 'cat_1010102'));
            return categories;
        }));
    });
    describe('categories with Special Offer and Conditions', () => {
        const productOverviewRestHandler = (request, responseCategories = categories, responseCoreData = coreData) => {
            if (request.url().includes(REST_URL)) {
                request.respond({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify(responseCategories)
                });
            } else if (request.url().endsWith('/shop/api/coredata')) {
                request.respond({
                    status: 200,
                    contentType: 'application/json',
                    body: JSON.stringify(responseCoreData)
                });
            } else {
                request.continue();
            }
        };
        const testSpecialOfferProductCategories = (callback, height = 4000) => async () => {
            const modifiedCategories = callback(cloneDeep(categories));

            page.on('request', (request) => productOverviewRestHandler(request,modifiedCategories));

            await createInlineUiTestPage(testMap.ProductsOverviewAA);
            await acceptCookieBanner();
            await resetMouseCoords();

            await Sizes.testAllScreenSizes(height, 4000);
        };
        const enableSpecialOffer = (category, productCode = null) => {
            if (productCode) {
                category.products?.forEach(product => {
                    if (product.code === productCode) {
                        product.specialOffer = true;
                    }
                });
            } else {
                category.products?.forEach(product => {
                    product.specialOffer = true;
                });
            }
            category.subcategories?.forEach(subcategory => {
                enableSpecialOffer(subcategory, productCode);
            });
        }

        it('and with hardware chips', testSpecialOfferProductCategories(categories => {
            enableSpecialOffer(findCategory(categories, 'cat_3'));
            return categories;
        }, 5000));

        it('no other chips', testSpecialOfferProductCategories(categories => {
            enableSpecialOffer(findCategory(categories, 'cat_101'),'AA_04012910');
            return categories;
        }, 3000));

        it('scroll to conditions', async () => {
            const modifiedCategories = cloneDeep(categories);
            enableSpecialOffer(findCategory(modifiedCategories, 'cat_101'),'AA_04012910');
            page.on('request', (request) => productOverviewRestHandler(request,modifiedCategories));

            await createInlineUiTestPage(testMap.ProductsOverviewAA);
            await acceptCookieBanner();
            await resetMouseCoords();
            let element = await page.waitForSelector('[data-id="container-special-offer-conditions"]');
            await page.evaluate(element => element.scrollIntoView(), element);
            await page.waitFor(1000);

            await Sizes.testExtraLargeScreensize(4000);
            await Sizes.testLargeScreensize(4000);

        });
    });
    describe('SEPA direct debit mandate banner', ()    => {
        it('contains sepa direct debit cta banner when user is authenticated and guideCustomerToProvideDirectDebitMandate', async () => {
            page.on('request', productsCategoriesRestHandler);

            const coreData = cloneDeep(testMap.ProductsOverviewAA.coreData);
            coreData.guideCustomerToProvideDirectDebitMandate = true;

            await createInlineUiTestPage({...testMap.ProductsOverviewAA, coreData: coreData});
            await acceptCookieBanner();
            await resetMouseCoords();

            await Sizes.testAllScreenSizes(2500);
        });
    });
    describe('Purchase disabled banner', ()    => {
        it('contains purchaseDisabledBanner when user is authenticated and guideCustomerToProvideDirectDebitMandate is not required', async () => {
            page.on('request', productsCategoriesRestHandler);

            const data= cloneDeep(testMap.ProductsOverviewAA);
            data.coreData.guideCustomerToProvideDirectDebitMandate = false;
            data.coreData.purchaseDisabledBannerInfo = {
                showPurchaseDisabledBanner: true,
                date: "01.01.2025"
            };

            await createInlineUiTestPage(data);
            await acceptCookieBanner();
            await resetMouseCoords();

            await Sizes.testAllScreenSizes(2500);
        });
    });
});


