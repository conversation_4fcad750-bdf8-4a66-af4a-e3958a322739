import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import NewStoreHeader from 'aa/shop/core/components/header/NewStoreHeader.vue';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils';
import {useStoreRootStore} from 'aa/shop/store/storeRoot';
import coreDataDefault from 'common/testtools/scenariosstore/coreDataDefault.json';
import {cloneDeep} from "lodash";

jest.mock('common/util');

const $route = {
    meta: {}
};

const mountComponent = () => wrapperComponentFactory(NewStoreHeader, {
    mocks: {$route},
    shallow: false
});

const storeRootStore = useStoreRootStore();

describe('NewStoreHeader', () => {

    let coreData;

    beforeEach(() => {
        coreData = cloneDeep(coreDataDefault)
        storeRootStore.coreData = coreData
        window.frontendData.coreData = coreData
    })

    describe('content-end slot', () => {

        it('contains content for authenticated users when user is authenticated', async () => {
            const wrapper = await mountComponent();
            await new Promise(process.nextTick);

            expect(wrapper.find('[data-id="container-authenticated-header-content"]').exists()).toBeTruthy();
        });

        it('contains content for unauthenticated users when user is unauthenticated', async () => {
            coreData.userName = null;

            const wrapper = await mountComponent();
            await new Promise(process.nextTick);

            expect(wrapper.find('[data-id="container-unauthenticated-header-content"]').exists()).toBeTruthy();
        });

        it('is empty when route uses minimal header', async () => {
            $route.meta.useMinimalHeader = true

            const wrapper = await mountComponent();
            await new Promise(process.nextTick);

            expect(wrapper.find('[data-id="container-authenticated-header-content"]').exists()).toBeFalsy();
        });

    });
});
