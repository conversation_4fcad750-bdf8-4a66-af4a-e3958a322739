import 'common/testtools/ui_tests_mock';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import vuetify from 'common/plugins/brands/azena/vuetify';
import {i18n} from 'common/i18n';

import TestPageRoot from 'common/testtools/TestPageRoot.vue';
import {ExportInformationOverview} from 'shop/exportinformation';

Vue.use(VueI18n);

new Vue({
    el: '#vue-app',
    i18n,
    vuetify,
    data: {
        pageData: {
            products: [{
                'appName': 'App name 1',
                'companyName': 'Company name 1',
                'eccn': 'eccn1',
                'link': 'p_1234'
            }, {
                'appName': 'App name 2',
                'companyName': 'Company name 2',
                'eccn': 'eccn2',
                'link': 'p_1235'
            }, {
                'appName': 'App name 3',
                'companyName': 'Company name 3',
                'eccn': 'eccn3',
                'link': 'p_1236'
            }]
        }
    },
    components: {
        TestPageRoot,
        ExportInformationOverview
    },
    methods: {},
    template: `
      <test-page-root>
        <div style="padding: 60px;">
            <export-information-overview 
                v-bind:page-data="pageData">
            </export-information-overview>
        </div>
      </test-page-root>
    `
});
