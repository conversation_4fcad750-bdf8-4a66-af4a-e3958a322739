import {acceptCookieBanner, createInlineUiTestPage} from 'common/testtools/inlineTestPageCreator';
import {testMap} from 'common/testtools/scenariosstore';
import {cloneDeep} from 'lodash';

describe("Payment details simple layout regression test", () => {
    let data;

    beforeEach(() => {
        data = cloneDeep(testMap.PaymentDetails);
    });

    it("with cc payment infos and invoice", async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: 576, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: 991, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("with cc payment infos and invoice default", async () => {
        data.pageData.ccPaymentInfos[0].defaultPaymentInfo = false;
        data.pageData.invoicePaymentInfos[0].defaultPaymentInfo = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: 576, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: 991, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("no cc payment infos with invoice", async () => {
        data.pageData.ccPaymentInfos = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });

    it("no cc payment infos with invoice dpg", async () => {
        data.pageData.ccPaymentInfos = [];
        const infoPaymentInfo = {
            enabled: true,
            accountHolder: 'John Smith',
            iban: '**********************',
            bankName: 'Deutsche Bank',
            bic: 'DEUTDEMM',
            id: '12345',
            paymentMethod: 'SEPA_CREDIT',
            saved: false,
            reusable: false,
            defaultPaymentInfo: true,
            paymentProvider: 'DPG'
        };
        data.pageData.invoicePaymentInfos[1] = infoPaymentInfo;
        data.pageData.invoicePaymentInfos[2] = infoPaymentInfo;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });

    it("no cc payment infos with invoice dpg arch", async () => {
        data.pageData.ccPaymentInfos = [];
        const infoPaymentInfo = {
            enabled: true,
            accountHolder: 'John Smith',
            accountNumber: '**********************',
            routingNumber: 'Routing number',
            bankName: 'Deutsche Bank',
            bic: 'DEUTDEMM',
            id: '12345',
            paymentMethod: 'ACH_INTERNATIONAL',
            saved: false,
            reusable: false,
            defaultPaymentInfo: true,
            paymentProvider: 'DPG'
        };
        data.pageData.invoicePaymentInfos[1] = infoPaymentInfo;
        data.pageData.invoicePaymentInfos[2] = infoPaymentInfo;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");
    });

    it("no cc payment infos with invoice default", async () => {
        data.pageData.ccPaymentInfos = [];
        data.pageData.invoicePaymentInfos[0].defaultPaymentInfo = true;
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: 576, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: 991, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("cc payment infos with no invoice", async () => {
        data.pageData.invoicePaymentInfos = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.click(".actions .actions-for-default button");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options");

        await page.setViewport({width: 576, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'S' viewport");

        await page.setViewport({width: 991, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'M' viewport");
    });

    it("cc payment infos with no invoice remove default", async () => {
        data.pageData.invoicePaymentInfos = [];
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.click(".actions .actions-for-non-default button");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options not default");

        await page.setViewport({width: 576, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'S' viewport not default");

        await page.setViewport({width: 991, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("action options on 'M' viewport not default");
    });

    it("with long card holder name", async () => {
        data.pageData.ccPaymentInfos[0].accountHolderName = "LongName of AccountHolderToFitTheScreen";
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: 576, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: 991, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

    it("cc payment delete confirmation", async () => {
        await createInlineUiTestPage(data);
        await acceptCookieBanner();

        await page.waitFor(600);
        await page.click(".card-data .actions .actions-for-non-default button");
        await page.waitFor(1000);
        await page.click(".menuable__content__active div[data-id='delete-non-default-payment']");
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init");

        await page.setViewport({width: 576, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'S' viewport");

        await page.setViewport({width: 991, height: 720});
        await page.waitFor(600);
        expect(await page.screenshot()).toMatchImageSnapshot("init on 'M' viewport");
    });

});
