describe("ReviewCreationForm", () => {
    it("looks as expected in initial state", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-ReviewCreationForm');
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("review creation form 1");
    });

    it("looks as expected in active state", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-ReviewCreationForm');
        await page.click(".star");
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("review creation form active");
    });
});