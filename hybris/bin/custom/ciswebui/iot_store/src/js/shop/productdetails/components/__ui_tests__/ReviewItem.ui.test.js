import { scrollIntoView } from '../../../../common/testtools/inlineTestPageCreator';

describe("ReviewItem", () => {
    it("looks as expected", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-ReviewItem');
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("review visualization 1");

        await page.$eval('.page-end', scrollIntoView);
        await page.waitFor(400);
        expect(await page.screenshot()).toMatchImageSnapshot("review visualization 2");
    });
});