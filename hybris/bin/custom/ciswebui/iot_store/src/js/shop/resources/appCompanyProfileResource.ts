import util from 'common/util';
import {AxiosResponse} from 'axios';
import {CompanyProfileData} from 'common/generated-types/types';

let axios = util.axios;

const basePath: string = '/shop/api/app';

function getPath(appCode: string): string {
    return `${basePath}/${appCode}/company-profile`;
}

let appCompanyProfileResource = {
    getCompanyProfile(appCode: string): Promise<AxiosResponse<CompanyProfileData>> {
        return axios.get(getPath(appCode));
    }
};

export default appCompanyProfileResource;
