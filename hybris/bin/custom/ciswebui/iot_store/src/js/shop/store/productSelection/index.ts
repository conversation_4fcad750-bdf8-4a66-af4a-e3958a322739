import type {
    ErrorMessageData,
    LicensePurchaseData,
    ProductLicenseData,
    SimpleProductData
} from 'common/generated-types/types';
import {ProductPriceDTO} from 'common/generated-types/types';
import type {AxiosResponse} from 'axios';
import {CartResource, pricesResource, productResource} from 'shop/resources';
import {defineStore} from 'pinia';
import {computed, ref} from 'vue';
import {messageService} from 'common/services';
import {DiscountedPricesByProductCode, DiscountProductPriceMap, DiscountProductType} from 'common/types';
import util from 'common/util';

export const useProductSelectionStore = defineStore('productSelection', () => {
    const product = ref<SimpleProductData | undefined>(undefined);

    const country = ref<string>('');

    const userGroup = ref<string>('');

    const prices = ref<Map<string, ProductPriceDTO> | undefined>(undefined);

    const items = ref<Array<ProductLicenseData & { quantity: number }>>([]);

    const consent = ref<boolean | null>(null);

    const selection = computed(() => items.value.filter(item => item.quantity > 0));

    function setCountry(val: string): void {
        country.value = val;
    }

    function setUserGroup(val: string): void {
        userGroup.value = val;
    }

    const isThlBundleAvailable = computed(() => {
        return util.isDACHRegion(country.value) &&
            product.value?.licenses.some((l) => l.addonThl) &&
            product.value?.licenses.some((license) => license?.thlDiscounts[userGroup.value]);
    });

    const addonDiscount = computed(() => {
        if (selection.value?.length <= 0) return undefined;
        const thlDiscounts = selection.value[0].thlDiscounts;
        return thlDiscounts?.[userGroup.value];
    });

    const appName = computed(() => {
        return product.value?.masterProductName || product.value?.name;
    });

    const addonProductName = computed(() => {
        return product.value?.addonProductName;
    });

    const sum = computed(() => {
        if (selection.value?.length <= 0) return undefined;

        const currencyIsocode = selection.value[0].currencyIsocode;
        const amount = selection.value
            .map(item => item.quantity * item.price)
            .reduce((a, b) => a + b, 0);

        return {currencyIsocode, amount};
    });

    const discountedPrices = ref<DiscountProductPriceMap | null>(null);

    const discountedPricesByProductCode = ref<DiscountedPricesByProductCode | null>(null);

    function setDiscountedPrices(priceData: DiscountProductPriceMap | null): void {
        if (priceData) {
            const priceByCodeData: DiscountedPricesByProductCode = {};

            const priceDataMaster = priceData[DiscountProductType.MASTER];
            if (priceData) {
                const licenseCodeMaster = priceDataMaster.licenseCode;
                priceByCodeData[licenseCodeMaster] = {
                    licenseCode: licenseCodeMaster,
                    price: priceDataMaster.price,
                    discountPrice: priceDataMaster.discountPrice,
                };
            }

            discountedPricesByProductCode.value = priceByCodeData;
            discountedPrices.value = priceData;
        } else {
            discountedPricesByProductCode.value = null;
        }
    }

    function getDiscountedPrice(productType: DiscountProductType): number {
        const priceData = discountedPrices.value?.[productType];
        return priceData?.discountPrice ?? 0;
    }

    function getOriginalThlPrice(productType: DiscountProductType): number {
        const priceData = discountedPrices.value?.[productType];
        return priceData?.price ?? 0;
    }

    function getDiscountedPriceByCode(productCode: string): number {
        const discountedPricesValue = discountedPricesByProductCode.value;

        if (productCode && discountedPricesValue?.[productCode]) {
            return discountedPricesValue[productCode].discountPrice;
        } else {
            return 0;
        }
    }

    function getDiscountedSum(): { currencyIsocode: string; amount: number } | undefined {
        if (selection.value.length <= 0) return undefined;

        const currencyIsocode = selection.value[0].currencyIsocode;
        const amount = selection.value
            .map((item) => {
                const discountedPrice = getDiscountedPriceByCode(item.code);
                const price = discountedPrice > 0 ? discountedPrice : item.price;
                return item.quantity * price;
            })
            .reduce((a, b) => a + b, 0);

        return { currencyIsocode, amount };
    }

    async function fetchProduct(productCode: string, notFoundHandler?: () => void): Promise<void> {
        productResource.getProduct(productCode)
            .then(response => {
                product.value = response?.data;
            })
            .catch(error => {
                if (notFoundHandler && error.response && error.response.status === 404) {
                    notFoundHandler();
                } else {
                    const messages = error.response?.data?.map((e: ErrorMessageData) => e.message);
                    messageService.error(messages);
                }
            });
    }

    async function fetchPrices(): Promise<void> {
        pricesResource.getPrices()
            .then(response => {
                prices.value = new Map(response?.data.map(priceDto => [priceDto.productCode, priceDto]));
            })
            .catch(error => {
                const messages = error.response?.data?.map((e: ErrorMessageData) => e.message);
                messageService.error(messages);
            });
    }

    function select(variant: ProductLicenseData, quantity: number): void {
        const item = items.value.find(i => i.code === variant.code);
        if (item != null) {
            item.quantity = quantity;
        } else {
            items.value.push({...variant, quantity});
        }
    }

    const quantity = computed(() => (variant: ProductLicenseData) => (
        items.value.find(item => item.code === variant.code)?.quantity ?? 0
    ));

    async function addToCart(): Promise<AxiosResponse<LicensePurchaseData>> {
        return await CartResource.addToCart(selection.value, consent.value);
    }

    return {
        product,
        prices,
        items,
        consent,
        selection,
        discountedPricesByProductCode,
        sum,
        quantity,
        isThlBundleAvailable,
        setCountry,
        setUserGroup,
        fetchProduct,
        fetchPrices,
        select,
        addToCart,
        getDiscountedPriceByCode,
        getOriginalThlPrice,
        getDiscountedPrice,
        getDiscountedSum,
        setDiscountedPrices,
        appName,
        addonProductName,
        addonDiscount
    };
});
