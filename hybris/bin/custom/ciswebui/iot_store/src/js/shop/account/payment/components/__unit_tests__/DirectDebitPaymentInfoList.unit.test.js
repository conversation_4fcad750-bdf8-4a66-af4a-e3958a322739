import 'common/testtools/unit_tests_mock';
import 'common/test-directive';
import {wrapperComponentFactory} from 'common/testtools/unit-test-utils'
import DirectDebitPaymentInfoList from 'shop/account/payment/components/DirectDebitPaymentInfoList.vue'
import paymentDetailsData from 'common/testtools/scenariosstore/aa/paymentDetailsData.json';
import {companyPermissionService} from 'common/services/companyPermissionService'

jest.mock('common/util');
jest.mock('common/services/companyPermissionService');

const defaultPaymentInfoList = paymentDetailsData.directDebitPaymentInfos

const mountComponent = (paymentInfoList = defaultPaymentInfoList) => wrapperComponentFactory(DirectDebitPaymentInfoList, {
    props: {
        directDebitPaymentInfoList: paymentInfoList
    },
    shallow: false
});

describe('DirectDebitPaymentInfoList', () => {


    it('should render DirectDebitPaymentInfoList component with one item', () => {
        const id = '*************';
        const iban = '**********************';
        const accountHolderName = 'TESTER';
        const mandateReference = 'PGWMD068E85OELFAV6PILVLL9QMFN';
        const paymentInfo = {
            id,
            mandateReference,
            iban,
            accountHolderName,
            ...defaultPaymentInfoList[0]
        };
        const wrapper = mountComponent([paymentInfo]);

        const paymentInfos = wrapper.findAll(`[data-id="container-sepa-dd-payment-info-${id}"]`);
        expect(paymentInfos).toHaveLength(1);
        expect(wrapper.find(`[data-id="text-sepa-dd-payment-info-iban-${id}"]`).text()).toBe(iban);
        expect(wrapper.find(`[data-id="text-sepa-dd-payment-info-name-${id}"]`).text()).toBe(accountHolderName);
        expect(wrapper.find(`[data-id="text-sepa-dd-payment-info-mandate-reference-${id}"]`).text()).toBe(mandateReference);
    });

    it('should render DirectDebitPaymentInfoList component with multiple payment infos', () => {
        const wrapper = mountComponent();

        const paymentInfos = wrapper.findAll(`[data-id^="container-sepa-dd-payment-info-"]`);
        expect(paymentInfos).toHaveLength(3);
    });

    it('should render DirectDebitPaymentInfoList component with empty list', () => {
        const wrapper = mountComponent([]);

        const paymentInfos = wrapper.findAll(`[data-id^="container-sepa-dd-payment-info-"]`);
        expect(paymentInfos).toHaveLength(0);
    })

    it('should render PaymentInfoCreationContainer when company is allowed to create payment infos', () => {
        companyPermissionService.allowCurrentCompanyCreationOfSepaDDPaymentInfo.mockImplementation(() => true);

        const wrapper = mountComponent();

        const piCreationContainer = wrapper.find(`[data-id="container-sepa-dd-payment-info-creation"]`);
        expect(piCreationContainer.exists()).toBeTruthy();
    });

    it('should not render PaymentInfoCreationContainer when company is not allowed to create payment infos', () => {
        companyPermissionService.allowCurrentCompanyCreationOfSepaDDPaymentInfo.mockImplementation(() => false);

        const wrapper = mountComponent();

        const piCreationContainer = wrapper.find(`[data-id="container-sepa-dd-payment-info-creation"]`);
        expect(piCreationContainer.exists()).toBeFalsy();
    });
});

