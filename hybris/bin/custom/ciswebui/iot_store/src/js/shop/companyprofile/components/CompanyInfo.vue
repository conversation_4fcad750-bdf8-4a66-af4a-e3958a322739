<template>
  <div>
    <v-container fluid>
      <v-row>
        <v-col cols="12">
          <p class="text-h1" data-id="text-company-name">{{ companyName }}</p>
          <p class="text-body-1" data-id="text-tag-line">{{ headerData.tagLine }}</p>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" lg="3" v-if="showFoundedIn" data-id="container-company-founded">
          <div>
            <p class="text--disabled">
              {{$t('shop.companyProfile.companyInfo.founded')}}
            </p>
            <p class="font-weight-bold" data-id="text-company-founded-in">{{ headerData.foundedIn }}</p>
          </div>
        </v-col>
        <v-col cols="12" lg="3" v-if="showCompanySize" data-id="container-company-size">
          <div>
            <p class="text--disabled">
              {{$t('shop.companyProfile.companyInfo.size')}}
            </p>
            <p class="font-weight-bold" data-id="text-company-size">{{ headerData.companySize.value }}</p>
          </div>
        </v-col>
        <v-col cols="12" lg="3">
          <div>
            <p class="text--disabled">
              {{$t('shop.companyProfile.companyInfo.headquarters')}}
            </p>
            <p class="font-weight-bold">{{ headquarters }}</p>
          </div>
        </v-col>
      </v-row>
      <v-row align="center">
        <v-col cols="12" lg="3" v-if="showCompanyWebsite">
          <CDButton :href="headerData.companyWebsite" target="_blank" class="external-link-btn" data-id="link-company-website">
            {{$t('shop.companyProfile.companyInfo.visitWebsite')}}
            <CDIcon right>$externallink</CDIcon>
          </CDButton>
        </v-col>
        <v-col cols="12" lg="3" v-if="showLinkedinProfile">
          <CDButtonText :href="headerData.linkedInProfileUrl" target="_blank" class="external-link-btn" data-id="link-company-linkedin-profile">
            <linked-in-logo></linked-in-logo>&nbsp;
            {{$t('shop.companyProfile.companyInfo.visitLinkedinProfile')}}
            <CDIcon right>$externallink</CDIcon>
          </CDButtonText>
        </v-col>
      </v-row>
      <v-row align="center" class="mt-8 mr-xl-16">
        <v-divider></v-divider>
      </v-row>
      <v-row class="mt-8">
        <v-col cols="12">
          <p class="text-h2">
            {{$t('shop.companyProfile.companyInfo.about')}}
          </p>
          <p class="text-body-2 profile-description" data-id="text-company-description">{{ aboutData.description }}</p>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AboutData, HeaderData} from 'common/generated-types/types';
import {isEmpty} from 'lodash';
import LinkedInLogo from 'common/images/boxed/linkedin-logo.svg';

@Component({
  components: {
    LinkedInLogo
  }
})
export default class CompanyInfo extends Vue {
  @Prop() headerData!: HeaderData;
  @Prop() aboutData!: AboutData;
  @Prop() companyName!: string;

  get showFoundedIn(): boolean {
    return this.headerData!.showFounded && Number.isInteger(this.headerData!.foundedIn);
  }

  get showCompanySize(): boolean {
    return this.headerData!.showCompanySize && !isEmpty(this.headerData!.companySize);
  }

  get headquarters(): string {
    const city : string = this.headerData!.city;
    const country : string = this.headerData!.country;
    return [city, country].filter(Boolean).join(', ');
  }

  get showCompanyWebsite(): boolean {
    return !isEmpty(this.headerData!.companyWebsite);
  }

  get showLinkedinProfile(): boolean {
    return !isEmpty(this.headerData!.linkedInProfileUrl);
  }
}
</script>
<style scoped lang="scss">
@import "common/design";
@import "shop/core/constants";

.external-link-btn:hover {
  text-decoration: none;
  color: inherit;
}

.profile-description {
  white-space: pre-wrap
}

</style>
