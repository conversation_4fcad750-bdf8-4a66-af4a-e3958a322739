<template>
  <div>
    <v-container fluid>
      <v-row v-if="showSalesInfo" class="mb-8">
        <v-col cols="12" class="pb-0">
          <p class="text-h3">
            {{ $t('shop.companyProfile.contactInfo.sales') }}
          </p>
        </v-col>
        <v-col cols="12" v-if="showSalesPhone">
          <div class="mb-1">
            <CDIcon color="black" left>$call</CDIcon>
            <span class="text-h4">
              {{ $t('shop.companyProfile.contactInfo.phone') }}
            </span>
          </div>
          <a :href="`tel:${contactData.salesPhone}`" data-id="link-sales-phone">{{ contactData.salesPhone }}</a>
        </v-col>
        <v-col cols="12" v-if="showSalesEmail">
          <div class="mb-1">
            <CDIcon color="black" left>$letter</CDIcon>
            <span class="text-h4">
              {{ $t('shop.companyProfile.contactInfo.email') }}
            </span>
          </div>
          <a :href="`mailto:${contactData.salesEmail}`" data-id="link-sales-email">{{ contactData.salesEmail }}</a>
        </v-col>
      </v-row>
      <v-row align="center" class="mb-8 d-xl-none" v-if="showSalesInfo">
        <v-divider></v-divider>
      </v-row>
      <v-row>
        <v-col cols="12" class="pb-0">
          <p class="text-h3">
            {{ $t('shop.companyProfile.contactInfo.support') }}
          </p>
        </v-col>
        <v-col cols="12" v-if="showSupportPhone">
          <div class="mb-1">
            <CDIcon color="black" left>$call</CDIcon>
            <span class="text-h4">
              {{ $t('shop.companyProfile.contactInfo.phone') }}
            </span>
          </div>
          <a :href="`tel:${contactData.supportPhone}`" data-id="link-support-phone">{{ contactData.supportPhone }}</a>
        </v-col>
        <v-col cols="12">
          <div class="mb-1">
            <CDIcon color="black" left>$letter</CDIcon>
            <span class="text-h4">
              {{ $t('shop.companyProfile.contactInfo.email') }}
            </span>
          </div>
          <a :href="`mailto:${contactData.supportEmail}`" data-id="link-support-email">{{ contactData.supportEmail }}</a>
        </v-col>
        <v-col cols="12" v-if="showSupportPage">
          <div class="mb-1">
            <CDIcon color="black" left>$globe</CDIcon>
            <span class="text-h4">
              {{ $t('shop.companyProfile.contactInfo.supportPage') }}
            </span>
          </div>
          <a :href="contactData.supportPageUrl" target="_blank" data-id="link-support-page-url">
            {{ contactData.supportPageUrl }}
          </a>
        </v-col>
      </v-row>

    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ContactData} from 'common/generated-types/types';
import {isEmpty} from 'lodash';

@Component
export default class ContactInfo extends Vue {
  @Prop() contactData!: ContactData;

  get showSalesPhone(): boolean {
    return !isEmpty(this.contactData.salesPhone);
  }

  get showSalesEmail(): boolean {
    return !isEmpty(this.contactData.salesEmail);
  }

  get showSalesInfo(): boolean {
    return this.showSalesPhone || this.showSalesEmail;
  }

  get showSupportPhone(): boolean {
    return !isEmpty(this.contactData.supportPhone);
  }

  get showSupportPage(): boolean {
    return !isEmpty(this.contactData.supportPageUrl);
  }
}
</script>
<style scoped lang="scss">
@import "common/design";
@import "shop/core/constants";

</style>
