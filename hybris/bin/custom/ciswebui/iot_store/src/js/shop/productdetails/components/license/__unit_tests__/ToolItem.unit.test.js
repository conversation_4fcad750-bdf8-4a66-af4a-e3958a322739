import {createLocalVue, mount} from '@vue/test-utils';
import {LicenseType} from "common/types";
import 'common/test-directive';
import { i18n } from 'common/i18n';
import ToolItem from '../ToolItem';
import {LicenseActivationStatus, LicensePurchasability} from 'common/generated-types/types';
import licenseActivationResource from 'shop/resources/licenseActivationResource';
import vuetify from 'common/plugins/brands/azena/vuetify';

jest.mock('common/util');
jest.mock('shop/resources/licenseActivationResource');

const localVue = createLocalVue();
localVue.use(i18n);

const licenseCode = 'A_000001_Tool'
const mountToolItem = (licenseType, purchasability) => mount(ToolItem, {
    mocks: {
        $t: () => 'mocked subscriptionPermissionText',
        $te: () => {},
        $n: () => {}
    },
    stubs: ['info-icon'],
    vuetify,
    localVue,
    propsData: {
        maxAllowedQuantity: 1000,
        company: {
            friendlyName: null,
            name: 'Bosch Digital Commerce'
        },

        license: {
            code: licenseCode,
            name: 'Free Azena Tool',
            currencyIsocode: 'USD',
            price: 0,
            licenseType: {
                code: licenseType,
                type: licenseType
            },
            purchasability: purchasability
        },
        productName: "Queue Management",
    }
});

licenseActivationResource.getLicenseActivationStatusForCurrent.mockImplementation(() => Promise.resolve({
    status: 200,
    data: {
        activationStatus: 'INACTIVE'
    }
}));


licenseActivationResource.setLicenseActivationStatusForCurrent.mockImplementation(() => Promise.resolve({
    status: 200,
    data: {}
}));

describe('Tool Item', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        window.frontendData = {
            coreData: {
                currentCompany: {
                    companyApproved: true,
                },
                allowedToBuy: true,
                navigationItems: [
                    {
                        "id": "globalSupport",
                        "itemCode": "globalSupport",
                        "url": ""
                    }
                ]
            }
        };
    });

    function verifyLicense(wrapper, present) {
        expect(wrapper.find('h2').exists()).toBe(present);
        expect(wrapper.find('[data-id="span-company-name"]').exists()).toBe(present);
        expect(wrapper.find('.cell-tool-action').exists()).toBe(present);
        expect(wrapper.find('.cell-license-details').exists()).toBe(present);
    }

    it('purchasable tool -> tool item displayed correctly', () => {
        const wrapper = mountToolItem(LicenseType.TOOL, LicensePurchasability.PURCHASABLE);

        verifyLicense(wrapper, true);
    });

    it('used with thirdParty App license -> tool item is empty', () => {
        const wrapper = mountToolItem(LicenseType.FULL, LicensePurchasability.PURCHASABLE);
        expect(wrapper.find('[data-id="container-tool-item"]').exists()).toBeFalsy();
    });

    it('tool enabled popup is displayed when checkbox is checked', async () => {
        const wrapper = mountToolItem(LicenseType.TOOL, LicensePurchasability.PURCHASABLE);
        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(false);

        await wrapper.find('[data-id="toggle-get-free-tool"]').vm.$emit('change', true);
        await wrapper.vm.$nextTick();

        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(true);
    });

    it('tool enabled popup is not displayed when checkbox is unchecked', async () => {
        const wrapper = mountToolItem(LicenseType.TOOL, LicensePurchasability.PURCHASABLE);

        await wrapper.find('[data-id="toggle-get-free-tool"]').vm.$emit('change', false);
        await wrapper.vm.$nextTick();

        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(false);
    });

    it('activate license request sent to resource when checkbox is checked', async () => {
        const wrapper = mountToolItem(LicenseType.TOOL, LicensePurchasability.PURCHASABLE);

        await wrapper.find('[data-id="toggle-get-free-tool"]').vm.$emit('change', true);
        await wrapper.vm.$nextTick();

        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(true);
        expect(licenseActivationResource.setLicenseActivationStatusForCurrent)
            .toHaveBeenCalledWith(licenseCode, LicenseActivationStatus.ACTIVE);
    });

    it('activate license request sent to resource when checkbox is unchecked', async () => {
        const wrapper = mountToolItem(LicenseType.TOOL, LicensePurchasability.PURCHASABLE);

        await wrapper.find('[data-id="toggle-get-free-tool"]').vm.$emit('change', false);

        await wrapper.vm.$nextTick();

        expect(wrapper.find('#tool-enabled-popup').exists()).toBe(false);
        expect(licenseActivationResource.setLicenseActivationStatusForCurrent)
            .toHaveBeenCalledWith(licenseCode, LicenseActivationStatus.INACTIVE);
    });

    it('given authenticated user, when component is created, then retrieve license activation status', async () => {
        mountToolItem(LicenseType.TOOL, LicensePurchasability.PURCHASABLE);

        expect(licenseActivationResource.getLicenseActivationStatusForCurrent).toHaveBeenCalledWith(licenseCode);
    });

    it('given anonymous user, when component is created, then do not retrieve license activation status', async () => {
        window.frontendData.coreData.userName = null;
        mountToolItem(LicenseType.TOOL, LicensePurchasability.PURCHASABLE);

        expect(licenseActivationResource.getLicenseActivationStatusForCurrent).not.toHaveBeenCalled();
    });
});
