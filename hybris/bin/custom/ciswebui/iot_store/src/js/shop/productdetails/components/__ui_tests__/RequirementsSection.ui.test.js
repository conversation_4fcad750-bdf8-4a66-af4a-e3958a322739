describe("RequirementsSection", () => {
    it("looks as expected in initial state", async () => {
        await page.goto(global.__DEV_ADDRESS__ + '/shop-productdetails-components-__ui_tests__-RequirementsSection');
        await page.waitFor(1000);
        expect(await page.screenshot()).toMatchImageSnapshot("requirements section");

        await page.setViewport({width: 576, height: 700});
        await page.evaluate(() => {location.reload(true);});
        await page.waitFor(1000);
        expect(await page.screenshot({clip: {
                x: 0,
                y: 0,
                width: 576,
                height: 700
            }
        })).toMatchImageSnapshot("small size");

        await page.hover('.key');
        await page.waitFor(1000);
        expect(await page.screenshot({clip: {
                x: 0,
                y: 0,
                width: 576,
                height: 700
            }
        })).toMatchImageSnapshot("small size hover");
    });
});