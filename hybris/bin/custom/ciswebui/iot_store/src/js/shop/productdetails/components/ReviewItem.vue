<template>
    <CDBlock :border-bottom="true" :padding-all="false" class="mb-4 py-4">
        <li class="review-item">
            <div class="review-head d-flex justify-space-between grey--text text--darken-2">
                <div class="author">{{displayName}}</div>
                <div class="date">{{ $d(new Date(review.date), 'short') }}</div>
            </div>
            <div class="review-rating">
                <star-rating v-bind:rating="review.rating"></star-rating>
                <div v-if="review.headline || review.comment" class="rating-spacer"></div>
            </div>
            <div class="title" v-html="review.headline"></div>
            <div v-if="review.headline && review.comment" class="spacer"></div>
            <div class="content" v-html="review.comment"></div>
        </li>
    </CDBlock>
</template>

<script lang="ts">
    import {Component, Prop, Vue} from 'vue-property-decorator';
    import StarRating from 'shop/core/components/starrating/StarRating.vue';
    import {commercefacades} from 'common/generated-types/types';
    import ReviewData = commercefacades.ReviewData;

    @Component({components: {StarRating}})
    export default class ReviewItem extends Vue {
        @Prop() review!: ReviewData;

        get displayName(): string {
            let displayName = this.review.showName ? this.review.alias : this.$t('shop.reviews.anonymous') as string;

            if (this.review.showCompany) {
                displayName = displayName + ' (' + this.review.company + ')';
            }
            return displayName;
        }
    }
</script>

<style scoped lang="scss">
    .review-item {
        list-style: none;
    }
</style>
