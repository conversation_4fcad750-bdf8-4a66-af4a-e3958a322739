<template>
  <div>
    <alert-static-important v-bind:centered="true" v-if="isMaintenanceOn" class="alert-info maintenance-banner">
      <p><strong>{{ $t('shop.productDetails.productHeader.maintenance.title') }}</strong></p>
      <p>{{ $t('shop.productDetails.productHeader.maintenance.subtitle') }}</p>
    </alert-static-important>
    <div class="tools-overview-header">
      <div class="tools-overview-header-content">
        <h1>{{ $t('shop.tools.header.title') }}</h1>
        <h2>{{ $t('shop.tools.header.description') }}</h2>
      </div>
      <banner-image class="banner-image"></banner-image>
    </div>
    <v-container :fluid="$vuetify.breakpoint.lgAndDown">
      <div class="tools-overview-container">
        <v-container fluid class="mb-16 mt-8">
          <v-row>
            <v-col cols="12">
              <v-row>
                <v-col cols="12" md="6" lg="4" xl="3" v-for="product in formattedProducts" :key="product.code">
                  <v-flex fill-height d-flex xs12>
                    <app-card :id="product.code" :appCardData="product"></app-card>
                  </v-flex>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <div class="d-none" id="total-tools">{{ totalProducts }}</div>
        </v-container>
      </div>
    </v-container>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {CategoryPageData, ShopCoreData, SimpleProductData} from 'common/generated-types/types';
import {messageService} from 'common/services';
import {StoreDataProvider, toolsResource} from 'shop/resources';
import {pageSpinner} from 'common/components/spinner';
import {AlertStaticImportant, StatusTag, AppCard} from 'common/components';
import {AppCardInterface} from 'common/types';
import BannerImage from 'common/images/boxed/store-tools-banner.svg';

@Component({
  components: {
    BannerImage,
    StatusTag,
    AlertStaticImportant,
    AppCard
  }
})
export default class Tools extends Vue {
  @Prop() pageData!: CategoryPageData;

  products: SimpleProductData[] = [];
  pageSpinner = pageSpinner;
  totalProducts = 0;

  coreData: ShopCoreData = StoreDataProvider.coreData;
  productsLoading = false;
  isMaintenanceOn = Boolean(this.coreData.moduleConfig.STORE_MAINTENANCE_BANNER);

  $refs!: {
    tags: HTMLElement;
  };

  created(): void {
    this.setPageTitle();
  }

  private setPageTitle(): void {
    document.title = this.$t('shop.tools.pageTitle') as string + this.$t('navigation.storePageTitle') as string;
  }

  async mounted(): Promise<void> {
    await this.getProducts();
  }

  getProducts(): Promise<void> {
    if (this.productsLoading) {
      return Promise.resolve();
    }
    this.productsLoading = true;
    return toolsResource.getTools().then(response => {
      const data = response.data;
      this.populateItems(data);
    }).catch(error => {
      messageService.errorResponse(error.response?.data, this.$i18n);
    }).finally(() => {
      this.pageSpinner.isActive && this.pageSpinner.stop();
      this.productsLoading = false;
    });
  }

  get formattedProducts(): AppCardInterface[] {
    return this.products.map(product => {
      return {
        title: product.name,
        img: product.logoUrl,
        body: product.shortDescription,
        sub: product.company.name,
        subPrefix: this.$i18n.t('by') as string,
        subAsLink: false,
        url: product.url,
        code: product.code,
        chips: []
      };
    });
  }

  private populateItems(pageData: CategoryPageData): void {
    if (pageData.totalNumberOfResults !== this.totalProducts) {
      this.totalProducts = pageData.totalNumberOfResults;
    }
    this.products.push(...pageData.products);
  }
}
</script>
<style scoped lang="scss">
@import "common/design";
@import "common/alerts";
@import "shop/core/constants";

.alert {
  &.maintenance-banner {
    background-color: var(--v-info-base);
    margin-bottom: 0px;
  }
}

.tools-overview-header {
  background-blend-mode: multiply;
  position: relative;
  background-color: var(--v-grey-lighten3);
  justify-content: center;
  align-items: center;
  display: flex;

  padding: $spacing-m;
  @include respond-to('M') {
    padding: $spacing-l;
  }

  &-content {
    position: relative;
    max-width: 100%;
    z-index: 2;

    @include respond-to('L') {
      margin: 50px 0 38px 0;
    }

    h1 {
      color: var(--v-grey-darken2);
      text-align: center;
      line-height: 1.2;
      font-size: 30px;
      font-weight: 500;
      margin-bottom: 25px;

      @include respond-to('M') {
        font-size: 36px;
      }

      @include respond-to('L') {
        margin: 0 150px 25px 150px;
      }
    }

    h2 {
      color: var(--v-grey-darken2);
      text-align: center;
      line-height: 1.2;
      font-size: 22px;
      font-weight: 500;

      @include respond-to('M') {
        font-size: 28px;
      }
    }
  }

  .banner-image {
    display: none;
    @include respond-to('L') {

      display: block;
      position: absolute;
      bottom: 0;
      right: 5%;
      z-index: 1;

    }
  }
}

.v-application {
  a:hover {
    color: inherit;
  }

  :deep {
    .v-card__subtitle .install-count {
      transform: translateY(-3px);
      display: inline-flex;
    }
  }
}
</style>
