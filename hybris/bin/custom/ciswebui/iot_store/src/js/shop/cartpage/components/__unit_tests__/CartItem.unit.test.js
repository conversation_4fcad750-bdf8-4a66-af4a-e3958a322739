import {createLocalVue, shallowMount} from '@vue/test-utils';
import 'common/testtools/unit_tests_mock';
import CartItem from 'shop/cartpage/components/CartItem.vue';
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {i18n} from 'common/i18n';
import 'common/test-directive';
import {messageService} from 'common/services';
import {CartResource} from 'shop/resources';
import {PiniaVuePlugin} from "pinia";
import {createTestingPinia} from '@pinia/testing';
import {useCartStore} from 'shop/store/cart';

jest.mock('common/util');
jest.mock('shop/resources/cartResource');

Vue.use(VueI18n);

const localVue = createLocalVue();

localVue.use(PiniaVuePlugin);
createTestingPinia();

const getItem = (number, options = {}) => {
    const basicData = {
        quantity: 1,
        logoUrl: 'logoUrl',
        entryNumber: number,
        productCode: 'productCode',
        productUrl: 'productUrl',
        productName: 'productName',
        companyName: 'companyName',
        licenseName: 'licenseName',
        licenseType: 'licenseType',
        itemPrice: {
            value: '19.99',
            symbol: 'USD'
        },
        totalPrice: {
            value: '19.99',
            symbol: 'USD'
        },
        scalePrices: [
            {
                currencyIso: "USD",
                value: 19.99,
                priceType: "BUY",
                formattedValue: "19.99",
                minQuantity: 1,
                maxQuantity: 4
            }, {
                currencyIso: "USD",
                value: 15.99,
                priceType: "BUY",
                formattedValue: "19.99",
                minQuantity: 5,
                maxQuantity: 9
            }, {
                currencyIso: "USD",
                value: 13.99,
                priceType: "BUY",
                formattedValue: "13.99",
                minQuantity: 10,
                maxQuantity: null
            }]
    };
    return {
        ...basicData,
        ...options
    };
};


let store;

const createWrapper = (number, options = {}) => {
    return shallowMount(CartItem, {
        mocks: {
            $t: () => {
                return 'Message'
            }
        },
        i18n,
        propsData: {
            item: getItem(number, options),
        },
        store,
        localVue
    });
};

const mockCartResourceMethod = (field, promise) => {
    const removeEntryMock = jest.fn().mockImplementation(() => promise);
    CartResource.mockImplementationOnce(() => {
        return {
            [field]: removeEntryMock
        }
    });
    return removeEntryMock;
};

describe('CartItem deleted', () => {
    let cartStore;

    beforeEach(() => {
        createTestingPinia();
        cartStore = useCartStore();
    });

    it('success case', async () => {
        let data = 'testData';
        const removeEntryMock = mockCartResourceMethod('removeEntry', Promise.resolve({
            data: data
        }));

        const wrapper = createWrapper(0);
        let messageToUser = wrapper.find('[data-id="container-alert-success"]');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await await wrapper.vm.deleteItem();
        expect(removeEntryMock).toBeCalledWith(0);
        expect(wrapper.emitted()["cart-data-changed"]).toEqual([[data]]);
        expect(cartStore.incrementQuantity).toHaveBeenCalledWith(-1);

        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('success');
        expect(globalMessages[0].toast).toBeTruthy();
    });

    it('failure case', async () => {
        const removeEntryMock = mockCartResourceMethod('removeEntry', Promise.reject({
            message: 'error'
        }));

        const wrapper = createWrapper(1);
        let messageToUser = wrapper.find('[data-id="container-alert-error"]');

        let globalMessages = [];
        messageService.subscribe(globalMessages);

        await await wrapper.vm.deleteItem();
        expect(removeEntryMock).toBeCalledWith(1);
        expect(wrapper.emitted().cartdatachanged).toBeFalsy();
        expect(cartStore.incrementQuantity).toBeCalledTimes(0);

        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('error');
        expect(globalMessages[0].toast).toBeFalsy();
    });
});

describe('CartItem edited', () => {
    let cartStore;

    beforeEach(() => {
        createTestingPinia();
        cartStore = useCartStore();
    });

    it('success case', async () => {
        let data = {
            cartItems: [{
                productCode: 'productCode',
                quantity: 3
            }]
        };
        const updateCartMock = mockCartResourceMethod('updateCart', Promise.resolve({
            data: data
        }));

        const wrapper = createWrapper(0);

        let messageToUser = wrapper.find('[data-id="container-alert-success"]');

        let globalMessages = [];
        let expectedItem = getItem(0);
        expectedItem.quantity = 3;

        messageService.subscribe(globalMessages);
        await await wrapper.vm.editItem(3);
        expect(updateCartMock).toBeCalledWith(expectedItem);
        expect(cartStore.incrementQuantity).toHaveBeenCalledWith(2);
        expect(wrapper.emitted()['cart-data-changed']).toEqual([[data]]);
        expect(messageToUser).toBeTruthy();
        expect(globalMessages).toHaveLength(1);
        expect(globalMessages[0].type).toEqual('success');
        expect(globalMessages[0].toast).toBeTruthy();
    });

    it('failure case', async () => {
        const updateCartMock = mockCartResourceMethod('updateCart', Promise.reject({
            response: {
                status: 400
            }
        }));
        const wrapper = createWrapper(0);

        let messageToUser = wrapper.find('[data-id="container-alert-error"]');

        let globalMessages = [];
        let expectedItem = getItem(0);
        expectedItem.quantity = 3;
        messageService.subscribe(globalMessages);

        await await wrapper.vm.editItem(3);
        expect(updateCartMock).toBeCalledWith(expectedItem);
        expect(wrapper.emitted().cartquantitychanged).toBeFalsy();
        expect(wrapper.emitted().cartdatachanged).toBeFalsy();
        expect(messageToUser).toBeTruthy();
    });

    it('failure case quantity', async () => {
        let data = {
            cartItems: [{
                productCode: 'productCode',
                quantity: 1001
            }]
        };
        mockCartResourceMethod('updateCart', Promise.resolve({
            data: data
        }));
        const wrapper = createWrapper(0);

        let messageToUser = wrapper.find('.quantity-validation');
        expect(messageToUser).toBeTruthy();
    });
});

describe('CartItem volume discount related methods', () => {
    it('showDiscount is false in case of 0 discount', async () => {
        const wrapper = await createWrapper(0);
        expect(wrapper.vm.showDiscount).toBeFalsy();
    });
    it('showDiscount is true in case of some discount', async () => {
        const wrapper = createWrapper(0, {
            quantity: 5,
            itemPrice: {
                value: '15.99',
                symbol: 'USD'
            }
        });
        expect(wrapper.vm.showDiscount).toBeTruthy();
        expect(wrapper.vm.basePrice)
    });

    it('basePrice returns price without discount', async () => {
        const wrapper = createWrapper(0, {
            quantity: 5,
            itemPrice: {
                value: '15.99',
                symbol: 'USD'
            }
        });
        expect(wrapper.vm.basePrice).toEqual({
            currencyIso: "USD",
            value: 19.99,
            priceType: "BUY",
            formattedValue: "19.99",
            minQuantity: 1,
            maxQuantity: 4
        });
    });

    it('discounts returns all except basic one', async () => {
        const wrapper = createWrapper(0, {
            quantity: 5,
            itemPrice: {
                value: '15.99',
                symbol: 'USD'
            }
        });
        expect(wrapper.vm.discounts).toEqual([{
            currencyIso: "USD",
            value: 15.99,
            priceType: "BUY",
            formattedValue: "19.99",
            minQuantity: 5,
            maxQuantity: 9
        }, {
            currencyIso: "USD",
            value: 13.99,
            priceType: "BUY",
            formattedValue: "13.99",
            minQuantity: 10,
            maxQuantity: null
        }]);
    });

    it('calcPercent returns right percents', async () => {
        const wrapper = createWrapper(0);
        expect(wrapper.vm.calcPercent(15.99)).toEqual(20);
        expect(wrapper.vm.calcPercent(13.99)).toEqual(30);
    });

    it('isActiveDiscount returns true/false for the right discount entry', async () => {
        const wrapper = createWrapper(0, {
            quantity: 5,
            itemPrice: {
                value: '15.99',
                symbol: 'USD'
            }
        });
        expect(wrapper.vm.isActiveDiscount({
            currencyIso: "USD",
            value: 15.99,
            priceType: "BUY",
            formattedValue: "19.99",
            minQuantity: 5,
            maxQuantity: 9
        })).toBeTruthy();
        expect(wrapper.vm.isActiveDiscount({
            currencyIso: "USD",
            value: 13.99,
            priceType: "BUY",
            formattedValue: "13.99",
            minQuantity: 10,
            maxQuantity: null
        })).toBeFalsy();
    });
});
