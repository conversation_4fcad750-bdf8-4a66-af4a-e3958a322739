import Vue from 'vue';
import VueI18n from 'vue-i18n';
import {mount} from '@vue/test-utils';
import {i18n} from 'common/i18n';
import 'common/testtools/unit_tests_mock';
import PrivateOfferDialog from 'shop/productdetails/components/PrivateOfferDialog';
import pageDataProductDetails from 'common/testtools/scenariosstore/pageDataProductDetails.json';
import 'common/test-directive';
import PrivateOfferResource from 'shop/resources/privateOfferResource';

jest.mock('common/util');
jest.mock('shop/resources/privateOfferResource');

Vue.use(VueI18n);

const mountComponent = () => mount(PrivateOfferDialog, {
    mocks: {
        $t: (word) => {
            return word;
        },
    },
    i18n,
    propsData: {
        show: true,
        product: pageDataProductDetails
    }
});

const mockPrivateOfferResourceMethod = (field, promise) => {
    const functionMock = jest.fn().mockImplementation(() => promise);
    PrivateOfferResource.mockImplementationOnce(() => {
        return {
            [field]: functionMock
        };
    });
    return functionMock;
};


describe('Private Offer Dialog', () => {

    it('submits registerProject set to false when registerProject flag is false', async () => {
        const createPrivateOfferMock = mockPrivateOfferResourceMethod('createPrivateOffer', Promise.reject({
            response: {
                status: 200
            }
        }));

        const wrapper = mountComponent();
        await wrapper.vm.$nextTick();

        wrapper.vm.registerProject = false;
        wrapper.vm.sendRequest();

        expect(createPrivateOfferMock).toHaveBeenCalled();
        const submittedPrivateOfferRequest = createPrivateOfferMock.mock.calls[0][0];
        expect(submittedPrivateOfferRequest.registerProject).toBeFalsy();
    });

    it('submits registerProject set to true when registerProject flag is true', async () => {
        const createPrivateOfferMock = mockPrivateOfferResourceMethod('createPrivateOffer', Promise.reject({
            response: {
                status: 200
            }
        }));

        const wrapper = mountComponent();
        await wrapper.vm.$nextTick();

        wrapper.vm.registerProject = true;
        wrapper.vm.sendRequest();

        expect(createPrivateOfferMock).toHaveBeenCalled();
        const submittedPrivateOfferRequest = createPrivateOfferMock.mock.calls[0][0];
        expect(submittedPrivateOfferRequest.registerProject).toBeTruthy();
    });

});
