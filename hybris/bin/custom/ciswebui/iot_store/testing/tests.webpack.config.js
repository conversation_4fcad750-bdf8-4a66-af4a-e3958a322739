'use strict';

const path = require('path');
const assert = require('assert');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { VuetifyLoaderPlugin } = require('vuetify-loader');
const glob = require('glob');
const os = require('os');

const PORT_EXTERNAL_DEV_SERVER = 9010;

const JS_SEP = path.sep + 'src' + path.sep + 'js' + path.sep;

module.exports = env => {
    assert(!(env.port === undefined), "port should not be undefined");

    let paths = ["src/js/shop", "src/js/common"];

    let htmlPages = [];
    let entries = {};
    let pages = [];

    for (const currentPath of paths) {
        let prefix;
        if (currentPath === "src/js/shop") {
            prefix = 'shop';
        } else {
            prefix = '';
        }

        let files = glob.sync(currentPath + '/**/*.testpage.js');
        for (const file of files) {
            let fullPath = path.resolve(path.join(__dirname, '..', file));
            let relPath = fullPath.split(JS_SEP)[1];
            let basename = relPath.replace(/\.testpage\.js/, '').replace(/\//g, '-');

            let title = 'UI test - ' + basename;
            let page = new HtmlWebpackPlugin({
                template: 'testing/assets/index.template.html',
                title,
                prefix,
                chunks: [basename, 'common'],
                filename: basename
            });
            htmlPages.push(page);
            entries[basename] = fullPath;
            pages.push({
                url: basename,
                title,
                ts: false
            });
        }

        files = glob.sync(currentPath + '/**/*.testpage.ts');
        for (const file of files) {
            let fullPath = path.resolve(path.join(__dirname, '..', file));
            let relPath = fullPath.split(JS_SEP)[1];
            let basename = relPath.replace(/\.testpage\.ts/, '').replace(/\//g, '-');

            let title = 'UI test - ' + basename;
            let page = new HtmlWebpackPlugin({
                template: 'testing/assets/index.template.html',
                title,
                prefix,
                chunks: [basename, 'common'],
                filename: basename
            });
            htmlPages.push(page);
            entries[basename] = fullPath;
            pages.push({
                url: basename,
                title,
                ts: true
            });
        }
    }

    const uiMain = path.resolve(__dirname, '../src/js/common/testtools/uiTestMain.ts');
    const uiMainAlias = 'vue-app';
    entries[uiMainAlias] = uiMain;

    let entrypoint = __dirname.split('/ciswebui')[0];
    let host = '**********';
    if (os.platform() === 'win32' || os.platform() === 'darwin' || env.port === PORT_EXTERNAL_DEV_SERVER) {
        host = '127.0.0.1'
    }
    let targetUrl = `http://${host}:${env.port}`;

    return {
        mode: 'development',
        entry: entries,
        output: {
            path: path.join(__dirname, 'test_output/dist'),
            filename: '[name].js'
        },
        devServer: {
            port: env.port,
            host: '0.0.0.0',
            disableHostCheck: true,
            noInfo: (env.debug === undefined),
            contentBase: entrypoint,
            hot: false,
            inline: false,
            proxy: [
                {
                    context: '/sample-data',
                    target: targetUrl,
                    pathRewrite: {'/sample-data': '/ciswebui/iot_store/testing/assets/sample-data/'}
                },
                {
                    context: '/assets/fonts/source-code-pro-v9-latin-700.woff2',
                    target: targetUrl,
                    pathRewrite: {'/assets/fonts/source-code-pro-v9-latin-700.woff2': '/cisshopfrontend/web/webroot/_ui//assets/fonts/source-code-pro-v9-latin-600.woff2'}
                },
                {
                    context: '/shop/_ui/assets',
                    target: targetUrl,
                    pathRewrite: {'^/shop/': '/cisshopfrontend/web/webroot/'}
                },
                {
                    context: '/_ui/assets',
                    target: targetUrl,
                    pathRewrite: {'^/_ui/': '/cisshopfrontend/web/webroot/_ui/'},
                },
                {
                    context(path, req) {
                        return path.match(/^\/(dc|shop)\//) && !path.match(/\.[\w\d]+$/);
                    },
                    target: targetUrl,
                    pathRewrite(path, req) {
                        return `/${uiMainAlias}`
                    }
                }
            ]
        },
        resolve: {
            modules: [path.resolve(__dirname, '../src/js'), path.join(__dirname, "../node_modules")],
            alias: {
                'vue$': 'vue/dist/vue.esm.js'
            },
            extensions: ['.js', '.vue', '.ts']
        },
        optimization: {
            splitChunks: {
                cacheGroups: {
                    commons: {
                        name: 'common',
                        chunks: 'initial',
                        minChunks: 2
                    }
                }
            }
        },
        plugins: [
            ...htmlPages,
            new HtmlWebpackPlugin({
                template: 'testing/assets/overview.template.html',
                inject: false,
                templateParameters: {
                    pages: pages
                }
            }),
            new HtmlWebpackPlugin({
                template: 'testing/assets/appCreator.html',
                title: 'App Creator',
                chunks: [uiMainAlias, 'common'],
                filename: uiMainAlias,
                inject: false
            }),
            new VueLoaderPlugin({
                compiler: require('vue-template-compiler')
            }),
            new VuetifyLoaderPlugin()
        ],
        module: {
            rules: [
                {
                    test: /\.svg$/,
                    issuer: /\.(vue|js|ts|svg)$/,
                    use: [
                        'vue-loader',
                        {
                            loader: 'svg-to-vue-component/loader',
                            options: {svgoConfig: false},
                        }
                    ]
                },
                {
                    test: /\.tsx?$/,
                    loader: 'ts-loader',
                    exclude: /node_modules/,
                    options: {
                        appendTsSuffixTo: [/\.vue$/],
                        configFile: path.resolve(__dirname, '../devserver.tsconfig.json')
                    }
                },
                {
                    test: /\.vue$/,
                    loader: 'vue-loader'
                },
                {
                    resourceQuery: /blockType=i18n/,
                    use: ['@intlify/vue-i18n-loader', 'yaml-loader']
                },
                {
                    test: /\.css$/,
                    use: [
                        'vue-style-loader',
                        'css-loader'
                    ]
                },
                {
                    test: /\.sass$/,
                    use: [
                        'vue-style-loader',
                        'css-loader',
                        {
                            loader: 'sass-loader',
                            options: {
                                implementation: require('sass'),
                                additionalData: "@import 'cd-system/tokens/scss/variables'"
                            }
                        }
                    ]
                },
                {
                    test: /\.scss$/,
                    use: [
                        'vue-style-loader',
                        'css-loader',
                        {
                            loader: 'sass-loader',
                            options: {
                                implementation: require('sass'),
                                additionalData: `
                                    @import 'cd-system/tokens/scss/mixins';
                                    @import 'cd-system/tokens/scss/common/core';
                                    @import 'cd-system/tokens/scss/azena/overrides';
                                `,
                                sassOptions: {
                                    includePaths: [path.resolve(__dirname, "../src/scss")]
                                }
                            }
                        }
                    ]
                },
                {
                    test: /\.(woff|woff2|ttf|eot)$/,
                    use: ['url-loader']
                },
                {
                    test: /\.js.map$/,
                    loader: 'ignore-loader'
                }
            ]
        }

    }
};
