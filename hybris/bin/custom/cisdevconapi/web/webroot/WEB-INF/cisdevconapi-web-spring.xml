<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
			  http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.sast.cis.devcon.api"/>

    <import resource="config/cache-config-spring.xml"/>
    <import resource="config/spring-filter-config.xml"/>
    <import resource="config/security-spring.xml"/>
    <import resource="config/error-config-spring.xml"/>

    <!-- Filter that catches and resolves exceptions thrown from other filters. Can be added to filter chain if you need such functionality -->
    <alias alias="exceptionTranslationFilter" name="defaultExceptionTranslationFilter"/>

    <bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
        <property name="basename" value="/WEB-INF/messages/base"/>
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="fallbackToSystemLocale" value="false"/>
    </bean>

    <bean id="themeSource" class="com.sast.cis.devcon.api.theme.DevconApiResourceBundleThemeSource">
        <constructor-arg name="messageSource" ref="messageSource"/>
    </bean>

    <bean id="defaultExceptionTranslationFilter" class="de.hybris.platform.webservicescommons.filter.ExceptionTranslationFilter">
        <property name="restHandlerExceptionResolver" ref="restHandlerExceptionResolver"/>
    </bean>

    <bean id="developerAppConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.devcon.api.data.DeveloperAppData"/>
        <property name="populators">
            <list>
                <ref bean="developerAppPopulator"/>
            </list>
        </property>
    </bean>
    <alias name="cisTrialExtensionRequestConverter" alias="trialExtensionRequestConverter"/>
    <bean id="cisTrialExtensionRequestConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.trialextension.data.TrialExtensionRequestData"/>
        <property name="populators">
            <list>
                <ref bean="trialExtensionRequestPopulator"/>
            </list>
        </property>
    </bean>
</beans>
