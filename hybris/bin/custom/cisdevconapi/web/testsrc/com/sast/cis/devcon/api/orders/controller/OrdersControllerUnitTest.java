package com.sast.cis.devcon.api.orders.controller;

import com.sast.cis.core.data.OrderManagementData;
import com.sast.cis.core.data.OrderManagementItemData;
import com.sast.cis.core.facade.CisOrderFacade;
import com.sast.cis.devcon.api.controller.BaseControllerUnitTest;
import com.sast.cis.devcon.api.controller.handlers.DevconRestApiExceptionHandler;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrdersControllerUnitTest extends BaseControllerUnitTest {

    @Mock
    private CisOrderFacade cisOrderFacade;
    @InjectMocks
    private OrdersController ordersController;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders
            .standaloneSetup(ordersController)
            .setControllerAdvice(new DevconRestApiExceptionHandler(null))
            .setMessageConverters(new MappingJackson2HttpMessageConverter(objectMapper))
            .build();
    }

    @Test
    public void getOrders_returnsListOfOrders() throws Exception {
        final var orderManagementData = new OrderManagementData()
            .withOrders(List.of(new OrderManagementItemData().withOrderId("0012344")));
        when(cisOrderFacade.getOrderManagementData()).thenReturn(orderManagementData);

        mockMvc.perform(get("/orders")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(toString(orderManagementData)));
    }
}