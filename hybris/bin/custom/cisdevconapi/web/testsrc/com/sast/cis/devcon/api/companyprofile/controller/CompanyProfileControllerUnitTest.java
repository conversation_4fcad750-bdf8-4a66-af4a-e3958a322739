package com.sast.cis.devcon.api.companyprofile.controller;

import com.sast.cis.companyprofile.data.AboutData;
import com.sast.cis.companyprofile.data.CompanyProfileData;
import com.sast.cis.companyprofile.data.ContactData;
import com.sast.cis.companyprofile.data.HeaderData;
import com.sast.cis.companyprofile.enums.CompanyProfileStatus;
import com.sast.cis.companyprofile.facade.CompanyProfileFacade;
import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.data.ErrorMessageData;
import com.sast.cis.core.data.IotCompanyData;
import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.exceptions.web.DevconErrorMessagesException;
import com.sast.cis.core.service.TranslationService;
import com.sast.cis.devcon.api.controller.BaseControllerUnitTest;
import com.sast.cis.devcon.api.controller.handlers.DevconRestApiExceptionHandler;
import de.hybris.bootstrap.annotations.UnitTest;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Set;

import static com.sast.cis.core.constants.devcon.DevconInputField.DESCRIPTION;
import static com.sast.cis.core.constants.devcon.DevconInputField.EMAIL_KEY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CompanyProfileControllerUnitTest extends BaseControllerUnitTest {
    private static final String BASE_PATH = "/company-profile";
    private static final String MOCKED_ERROR_MESSAGE = "Mocked Error Message";

    @Mock
    private CompanyProfileFacade companyProfileFacade;

    @Mock
    private TranslationService translationService;

    @InjectMocks
    private CompanyProfileController companyProfileController;

    @Before
    public void setUp() {
        when(translationService.translateAll(anySetOf(DevconErrorMessage.class)))
            .thenReturn(Set.of(new ErrorMessageData().withMessage(MOCKED_ERROR_MESSAGE)));
        this.mockMvc = MockMvcBuilders
            .standaloneSetup(companyProfileController)
            .setControllerAdvice(new DevconRestApiExceptionHandler(translationService))
            .build();
    }

    @Test
    @SneakyThrows
    public void getCompanyProfile_existsCompanyProfile_returnsCompanyProfileResponse() {
        HeaderData headerData = new HeaderData();
        headerData.setCompanyWebsite("https://wwww.test.com");
        CompanyProfileData companyProfileData = new CompanyProfileData()
            .withAboutData(new AboutData().withDescription("description"))
            .withContactData(new ContactData().withSalesEmail("<EMAIL>"))
            .withStatus(CompanyProfileStatus.DRAFT.getCode())
            .withHeaderData(headerData);
        when(companyProfileFacade.getCurrentCompanyProfile())
            .thenReturn(companyProfileData);

        String jsonResponse = mockMvc.perform(get(BASE_PATH)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andReturn().getResponse().getContentAsString();

        CompanyProfileData actualData = toObject(jsonResponse, CompanyProfileData.class);
        assertThat(actualData).isNotNull();
        assertThat(actualData).usingRecursiveComparison().isEqualTo(companyProfileData);
        verify(companyProfileFacade).getCurrentCompanyProfile();
    }

    @Test
    public void addAboutData_descriptionBiggerThanAllowed_returnsBadRequestHttpStatus() throws Exception {
        var aboutData = new AboutData().withDescription(RandomStringUtils.random(2100, true, true));
        DevconErrorMessage descriptionTooLong = new DevconErrorMessage().withCode(DevconErrorCode.COMPANY_PROFILE_DESCRIPTION_TOO_LONG)
            .withField(DESCRIPTION);
        Set<DevconErrorMessage> errorMessages = Set
            .of(descriptionTooLong);
        doThrow(new DevconErrorMessagesException(errorMessages)).when(companyProfileFacade).saveAboutData(any());
        mockMvc.perform(post("/company-profile/about")
            .contentType(MediaType.APPLICATION_JSON)
            .content(toJson(aboutData)))
            .andExpect(status().isBadRequest());
    }

    @Test
    public void addContactData_hasInvalidData_returnsBadRequestHttpStatus() throws Exception {
        DevconErrorMessage invalidSupportEmailMessage = new DevconErrorMessage()
            .withCode(DevconErrorCode.COMPANY_PROFILE_SUPPORT_EMAIL_NOT_VALID)
            .withField(EMAIL_KEY);
        Set<DevconErrorMessage> errorMessages = Set.of(invalidSupportEmailMessage);
        doThrow(new DevconErrorMessagesException(errorMessages)).when(companyProfileFacade).saveContactData(any());
        var contactData = new ContactData();
        mockMvc.perform(post("/company-profile/contact")
            .contentType(MediaType.APPLICATION_JSON)
            .content(toJson(contactData)))
            .andExpect(status().isBadRequest());
    }
}
