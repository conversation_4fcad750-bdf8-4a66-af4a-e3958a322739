package com.sast.cis.devcon.api.controller;

import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.exceptions.web.NotFoundException;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.service.TranslationService;
import lombok.AllArgsConstructor;

@AllArgsConstructor
abstract public class AbstractRestController {
    private final TranslationService translationService;
    private final ErrorMessageService errorMessageService;

    public BadRequestException badRequestException(DevconErrorCode devconErrorCode) {
        return new BadRequestException(translationService.translate(errorMessageService.createErrorMessage(devconErrorCode)));
    }

    public NotFoundException notFoundException(DevconErrorCode devconErrorCode) {
        return new NotFoundException(translationService.translate(errorMessageService.createErrorMessage(devconErrorCode)));
    }
}
