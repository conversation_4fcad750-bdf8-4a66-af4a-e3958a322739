package com.sast.cis.devcon.api.facade;

import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppService;
import com.sast.cis.devcon.api.data.DeveloperAppData;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class DeveloperFacade {
    private final AppService appService;
    private final Converter<AppModel, DeveloperAppData> developerAppConverter;

    public List<DeveloperAppData> getDeveloperApps(Set<String> packageNames) {
        return appService.getAllAppsByPackageNames(packageNames)
            .stream()
            .map(developerAppConverter::convert)
            .collect(Collectors.toList());
    }
}
