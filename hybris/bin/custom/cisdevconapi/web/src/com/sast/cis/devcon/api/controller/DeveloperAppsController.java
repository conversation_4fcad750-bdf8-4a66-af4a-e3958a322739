package com.sast.cis.devcon.api.controller;

import com.sast.cis.core.data.ErrorMessageData;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.devcon.api.annotation.DeveloperPermission;
import com.sast.cis.devcon.api.data.DeveloperAppData;
import com.sast.cis.devcon.api.facade.DeveloperFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/sold-apps")
@Api(tags = "Navigation")
@AllArgsConstructor
@DeveloperPermission
public class DeveloperAppsController {
    private final DeveloperFacade developerFacade;

    @GetMapping
    @ApiOperation(value = "Get developer apps", notes = "returns developer apps.")
    public List<DeveloperAppData> getDeveloperApps(@RequestParam("applicationIds") Set<String> applicationIds) {
        if (CollectionUtils.isEmpty(applicationIds)) {
            throw new BadRequestException(new ErrorMessageData().withMessage("Application id must be specified"));
        }

        return developerFacade.getDeveloperApps(applicationIds);
    }
}
