package com.sast.cis.devcon.api.companyprofile.controller;

import com.sast.cis.companyprofile.data.CompanyProfileData;
import com.sast.cis.companyprofile.data.CompanySizeList;
import com.sast.cis.companyprofile.facade.CompanyProfileFacade;
import com.sast.cis.devcon.api.annotation.DeveloperPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api(tags = "CompanySize")
@RequestMapping("/company-sizes")
@AllArgsConstructor
@DeveloperPermission
public class CompanySizeController {
    private final CompanyProfileFacade companyProfileFacade;

    @GetMapping
    @ApiOperation("get all company sizes")
    public CompanySizeList getCompanySizes() {
        return companyProfileFacade.getCompanySizeList();
    }

}
