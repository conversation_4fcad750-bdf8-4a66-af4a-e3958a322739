# SEPA Mandate Service Configuration
sepa.mandate.service.url=http://localhost:8081

sepa.mandate.api.url=http://localhost:8081
sepa.mandate.auth.idp.url=http://localhost:8080/auth/realms/baam
sepa.mandate.auth.token.endpoint.url=${sepa.mandate.auth.idp.url}/protocol/openid-connect/token
sepa.mandate.auth.client.id=iotstore-sepa-mandate
sepa.mandate.auth.client.secret=O5TklNz1iyijURCVTgf9SLl0oSqnwUXA

# Specifies the location of the spring context file putted automatically to the global platform application context.
cispaymentsepa.application-context=cismmsintegration-spring.xml
