package com.sast.cis.integration.sepa.client.api;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@RequestMapping("/api/v1/mandates")
public interface SepaMandateApi {
    /**
     * Create a draft mandate on user attempt to create a mandate (automatically generating the reference)
     * <p>
     * If a mandate already exists for the companyId, return the existing mandate
     */
    @PostMapping("/initialize")
    ResponseEntity<SepaMandateApiDto> createMandate(@RequestParam String companyId);

    /**
     * If the provided dto contains all required fields, then finalize the draft mandate.
     * <p>
     * Otherwise, update the draft mandate with additional input.
     * <p>
     * this can be ongoing process, and while in progress the mandate will be in draft status.
     */
    @PostMapping("/{reference}/activate")
    ResponseEntity<SepaMandateApiDto> activateMandate(@PathVariable String reference, @RequestBody(required = false) SepaMandateApiDto dto);

    @GetMapping("/{reference}")
    ResponseEntity<SepaMandateApiDto> getMandateByReference(@PathVariable String reference,
                                                            @RequestParam(defaultValue = "false") boolean includeDrafts);

    @GetMapping
    ResponseEntity<List<SepaMandateApiDto>> getAllMandates(@RequestParam(defaultValue = "false") boolean includeDrafts);
}
