package com.sast.cis.integration.sepa.jalo;

import com.sast.cis.integration.sepa.constants.CisPaymentSepaConstants;
import de.hybris.platform.jalo.JaloSession;
import de.hybris.platform.jalo.extension.ExtensionManager;
import org.apache.log4j.Logger;

public class CisPaymentSepaManager extends GeneratedCisPaymentSepaManager
{
	@SuppressWarnings("unused")
	private static final Logger log = Logger.getLogger( CisPaymentSepaManager.class.getName() );
	
	public static final CisPaymentSepaManager getInstance()
	{
		ExtensionManager em = JaloSession.getCurrentSession().getExtensionManager();
		return (CisPaymentSepaManager) em.getExtension(CisPaymentSepaConstants.EXTENSIONNAME);
	}
	
}
