package com.sast.cis.integration.sepa.tools;

import com.sast.cis.integration.sepa.client.api.SepaMandateApiDto;
import com.sast.cis.integration.sepa.dto.SepaMandateDto;


public class SepaMandateConverter {

    public static SepaMandateDto toDto(SepaMandateApiDto apiDto) {
        if (apiDto == null) {
            return null;
        }

        return SepaMandateDto.builder()
                .mandateReference(apiDto.getMandateReference())
                .companyId(apiDto.getCompanyId())
                .accountHolderName(apiDto.getAccountHolderName())
                .iban(apiDto.getIban())
                .signatureDate(apiDto.getSignatureDate())
                .status(apiDto.getStatus())
                .build();
    }


    public static SepaMandateApiDto toApiDto(SepaMandateDto dto) {
        if (dto == null) {
            return null;
        }

        return SepaMandateApiDto.builder()
                .mandateReference(dto.getMandateReference())
                .companyId(dto.getCompanyId())
                .accountHolderName(dto.getAccountHolderName())
                .iban(dto.getIban())
                .signatureDate(dto.getSignatureDate())
                .status(dto.getStatus())
                .build();
    }


}