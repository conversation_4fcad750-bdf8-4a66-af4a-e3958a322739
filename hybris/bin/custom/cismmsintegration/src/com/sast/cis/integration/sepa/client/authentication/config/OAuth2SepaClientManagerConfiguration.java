package com.sast.cis.integration.sepa.client.authentication.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;

import static org.springframework.security.oauth2.core.AuthorizationGrantType.CLIENT_CREDENTIALS;

@Configuration
@RequiredArgsConstructor
public class OAuth2SepaClientManagerConfiguration {

    private final SepaGatewayConfigProvider sepaGatewayConfigProvider;

    @Bean
    public ClientRegistration sepaClientRegistration() {
        final SepaMandateConfig.AuthenticationConfig authenticationConfig = sepaGatewayConfigProvider.getConfig().authenticationConfig();
        return ClientRegistration
            .withRegistrationId(authenticationConfig.clientRegistrationId())
            .tokenUri(authenticationConfig.tokenEndpointUrl())
            .clientId(authenticationConfig.clientId())
            .clientSecret(authenticationConfig.clientSecret())
            .authorizationGrantType(CLIENT_CREDENTIALS)
            .build();
    }

    @Bean
    public ClientRegistrationRepository sepaClientRegistrationRepository(final ClientRegistration sepaClientRegistration) {
        return new InMemoryClientRegistrationRepository(sepaClientRegistration);
    }

    @Bean
    public OAuth2AuthorizedClientService sepaAuthorizedClientService(final ClientRegistrationRepository sepaClientRegistrationRepository) {
        return new InMemoryOAuth2AuthorizedClientService(sepaClientRegistrationRepository);
    }

    @Bean
    public AuthorizedClientServiceOAuth2AuthorizedClientManager sepaAuthorizedClientManager(
        final ClientRegistrationRepository sepaClientRegistrationRepository,
        final OAuth2AuthorizedClientService sepaAuthorizedClientService) {

        return new AuthorizedClientServiceOAuth2AuthorizedClientManager(sepaClientRegistrationRepository, sepaAuthorizedClientService);
    }
}
