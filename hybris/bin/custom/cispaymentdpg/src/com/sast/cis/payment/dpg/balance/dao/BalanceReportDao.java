package com.sast.cis.payment.dpg.balance.dao;

import com.sast.cis.payment.dpg.model.DpgAccountBalanceModel;
import com.sast.cis.payment.dpg.model.DpgSellerAccountModel;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
@Slf4j
public class BalanceReportDao {

    private final FlexibleSearchService flexibleSearchService;

    public BalanceReportDao(FlexibleSearchService flexibleSearchService) {
        this.flexibleSearchService = flexibleSearchService;
    }

    public List<DpgAccountBalanceModel> getDpgDailyPendingBalance(DpgSellerAccountModel dpgSellerAccount) {
        DpgAccountBalanceModel example = new DpgAccountBalanceModel();
        example.setSellerAccount(dpgSellerAccount);
        List<DpgAccountBalanceModel> balances = flexibleSearchService.getModelsByExample(example);

        if (balances.size() == 0) {
            return Collections.emptyList();
        }

        return balances;
    }
}

