package com.sast.cis.payment.dpg.methods.ach;

import com.google.common.base.Preconditions;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.model.*;
import com.sast.cis.payment.dpg.collectionaccounts.CompanyCollectionAccountService;
import com.sast.cis.payment.dpg.methods.PaymentInstrumentService;
import com.sast.cis.payment.dpg.model.AchCollectionBankAccountModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.model.ModelService;
import org.springframework.stereotype.Service;

@Service
public class DpgAchPaymentInstrumentService implements
    PaymentInstrumentService<AchInternationalCreditTransferPaymentInfoModel, AchTransferPaymentInstrumentModel> {
    private final ModelService modelService;
    private final CompanyCollectionAccountService companyCollectionAccountService;
    private final Converter<AchCollectionBankAccountModel, AchTransferPaymentInstrumentModel> dpgAchTransferPaymentInstrumentConverter;

    public DpgAchPaymentInstrumentService(ModelService modelService,
        CompanyCollectionAccountService companyCollectionAccountService,
        Converter<AchCollectionBankAccountModel, AchTransferPaymentInstrumentModel> dpgAchTransferPaymentInstrumentConverter) {
        this.modelService = modelService;
        this.companyCollectionAccountService = companyCollectionAccountService;
        this.dpgAchTransferPaymentInstrumentConverter = dpgAchTransferPaymentInstrumentConverter;
    }

    @Override
    public AchTransferPaymentInstrumentModel createPaymentInstrument(AchInternationalCreditTransferPaymentInfoModel paymentInfo) {
        Preconditions.checkArgument(paymentInfo != null, "Given payment info is null");
        IoTCompanyModel company = getCompany(paymentInfo);
        AchCollectionBankAccountModel collectionAccount = (AchCollectionBankAccountModel) companyCollectionAccountService
            .getOrCreateAccount(company, PaymentMethodType.ACH_INTERNATIONAL);
        AchTransferPaymentInstrumentModel paymentInstrument = dpgAchTransferPaymentInstrumentConverter.convert(collectionAccount);
        modelService.save(paymentInstrument);
        return paymentInstrument;
    }

    private IoTCompanyModel getCompany(PaymentInfoModel paymentInfo) {
        Preconditions.checkArgument(paymentInfo.getUser() instanceof IntegratorModel, "Payment info user is not an integrator");
        return ((IntegratorModel) paymentInfo.getUser()).getCompany();
    }
}
