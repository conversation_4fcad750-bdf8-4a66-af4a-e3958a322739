package com.sast.cis.payment.dpg.service;

import com.sast.cis.payment.dpg.client.PaymentRestService;
import com.sast.cis.payment.dpg.client.PurchaseRestService;
import com.sast.cis.payment.dpg.client.ReportRestService;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.RequiredArgsConstructor;
import org.glassfish.jersey.client.authentication.HttpAuthenticationFeature;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.WebTarget;

@Service
@RequiredArgsConstructor
public class  DpgRestServiceFactory {
    public static final String URL_PROPERTY = "dpg.dbpGateway.url";
    public static final String USER_PROPERTY = "dpg.dbpGateway.user";
    public static final String PASSWORD_PROPERTY = "dpg.dbpGateway.password";

    private final ConfigurationService configurationService;
    private final Client dbpGatewayClient;

    public PurchaseRestService getDbpPurchaseRestService() {
        return WebResourceFactory.newResource(PurchaseRestService.class, getDbpGatewayTarget());
    }

    public ReportRestService getDbpReportRestService() {
        return WebResourceFactory.newResource(ReportRestService.class, getDbpGatewayTarget());
    }

    public PaymentRestService getDbpPaymentRestService() {
        return WebResourceFactory.newResource(PaymentRestService.class, getDbpGatewayTarget());
    }

    private WebTarget getDbpGatewayTarget() {
        return dbpGatewayClient
                       .register(HttpAuthenticationFeature.basic(
                               configurationService.getConfiguration().getString(USER_PROPERTY),
                               configurationService.getConfiguration().getString(PASSWORD_PROPERTY)))
                       .target(configurationService.getConfiguration().getString(URL_PROPERTY));
    }
}
