package com.sast.cis.payment.dpg.email;

import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.cis.email2.service.EmailLanguageHelper;
import com.sast.cis.email2.typehandler.AbstractEmailTypeHandler;
import com.sast.cis.payment.dpg.dto.DpgSellerOnboarding;
import com.sast.email.client.dto.EmailDto;
import com.sast.email.client.dto.TenantDto;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SellerOnboardingEmailHandler extends AbstractEmailTypeHandler {
    private static final String EMAIL_SUBJECT = "Merchant Implementation Request - Azena";

    private final String recipient;
    private final String bccRecipientCor;

    public SellerOnboardingEmailHandler(
        final ConfigurationService configurationService,
        final FeatureToggleService featureToggleService,
        @Value("${email.recipients.team.support}") String recipient,
        @Value("${email.recipients.team.cor}") String bccRecipientCor) {

        super(configurationService, featureToggleService);
        this.recipient = recipient;
        this.bccRecipientCor = bccRecipientCor;
    }

    @Override
    public EmailType getType() {
        return EmailType.INITIATE_DPG_SELLER_ONBOARDING;
    }

    @Override
    public EmailDto getEmailDto(Object data) {
        DpgSellerOnboarding sellerOnboarding = (DpgSellerOnboarding) data;

        Map<String, Object> properties = new HashMap<>();
        properties.put("contactPersonName", sellerOnboarding.getConactPersonName());
        properties.put("emailId", sellerOnboarding.getEmailId());
        properties.put("companyId", sellerOnboarding.getCompanyId());
        properties.put("companyName", sellerOnboarding.getCompanyName());
        properties.put("currency", sellerOnboarding.getCurrency());
        properties.put("country", sellerOnboarding.getCountry());
        properties.put("taxId", sellerOnboarding.getTaxId());
        properties.put("businessStreetName", sellerOnboarding.getBusinessAddress().getStreetName());
        properties.put("businessHouseNumber", sellerOnboarding.getBusinessAddress().getHouseNumber());
        properties.put("businessPostCode", sellerOnboarding.getBusinessAddress().getPostCode());
        properties.put("businessCity", sellerOnboarding.getBusinessAddress().getCity());

        return super.getEmailDto(getType(), TenantDto.AZENA, EmailLanguageHelper.FALLBACK_LANGUAGE)
                .subject(EMAIL_SUBJECT)
                .properties(properties)
                .to(ListUtils.emptyIfNull(List.of(recipient)))
                .bcc(ListUtils.emptyIfNull(List.of(bccRecipientCor)));
    }
}
