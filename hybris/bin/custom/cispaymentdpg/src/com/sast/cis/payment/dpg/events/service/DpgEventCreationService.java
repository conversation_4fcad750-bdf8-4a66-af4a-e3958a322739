package com.sast.cis.payment.dpg.events.service;

import com.sast.cis.payment.dpg.enums.DpgEntityStatus;
import com.sast.cis.payment.dpg.enums.DpgTransitionEntityType;
import com.sast.cis.payment.dpg.events.exceptions.DpgEventCreationException;
import com.sast.cis.payment.dpg.events.exceptions.DpgEventDiscardException;
import com.sast.cis.payment.dpg.model.DpgEventModel;
import com.sast.dbpg.api.dto.InternalNotificationDto;
import de.hybris.platform.servicelayer.exceptions.ModelCreationException;
import de.hybris.platform.servicelayer.exceptions.ModelSavingException;
import de.hybris.platform.servicelayer.exceptions.SystemException;
import de.hybris.platform.servicelayer.interceptor.InterceptorException;
import de.hybris.platform.servicelayer.interceptor.impl.UniqueAttributesInterceptor;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.util.Utilities;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static java.util.Date.from;

@Service
@Slf4j
@AllArgsConstructor
public class DpgEventCreationService {
    private final ModelService modelService;

    /**
     * Consumes a given DPG event. New events are stored, duplicate events are discarded, validation or processing errors
     * throw exceptions.
     *
     * @param notificationDto Received DPG event notification
     * @throws DpgEventCreationException if event could not be consumed
     * @throws IllegalArgumentException if provided event is invalid
     */
    public DpgEventModel createDpgEvent(InternalNotificationDto notificationDto) {
        validateEvent(notificationDto);
        return persistEvent(notificationDto);
    }

    private void validateEvent(InternalNotificationDto notificationDto) {
        if (StringUtils.isBlank(notificationDto.getPublicEventId())) {
            throw new IllegalArgumentException("EventId is required for the DPG Event");
        }

        if (notificationDto.getTransitionEntityId() == null) {
            throw new IllegalArgumentException("DPG entity id cannot be null for the DPG Event");
        }

        if (notificationDto.getTransitionEntityType() == null) {
            throw new IllegalArgumentException("Event Type cannot be null for the DPG Event");
        }

        if (notificationDto.getNewStatus() == null) {
            throw new IllegalArgumentException("New Status cannot be null for the DPG Event");
        }

        if (notificationDto.getEventDateTime() == null) {
            throw new IllegalArgumentException("Received DPG event without event date time");
        }
    }

    private DpgEventModel persistEvent(InternalNotificationDto notificationDto) {
        DpgEventModel dpgEvent = modelService.create(DpgEventModel.class);
        dpgEvent.setEventId(notificationDto.getPublicEventId());
        dpgEvent.setEventStatus(DpgEntityStatus.valueOf(notificationDto.getNewStatus().name()));
        dpgEvent.setEventDate(from(notificationDto.getEventDateTime()));
        dpgEvent.setEntityType(DpgTransitionEntityType.valueOf(notificationDto.getTransitionEntityType().name()));
        dpgEvent.setDpgEntityId(notificationDto.getTransitionEntityId());
        dpgEvent.setEntityId(notificationDto.getExternalTransitionEntityId());

        try {
            modelService.save(dpgEvent);
            LOG.info("Stored DPG event with id={} for later processing.", dpgEvent.getEventId());
        } catch (ModelSavingException | ModelCreationException e) {
            if (uniqueConstraintViolated(e)) {
                throw new DpgEventDiscardException("We already have a stored event with same eventId.", notificationDto);
            } else {
                throw new DpgEventCreationException("Unable to store DPG event for further processing", e);
            }
        }
        return dpgEvent;
    }

    // lets call this "idiomatic hybris code". Taken from de.hybris.platform.apiregistryservices.services.impl.DefaultEventDlqService
    private boolean uniqueConstraintViolated(SystemException exception) {
        return modelService.isUniqueConstraintErrorAsRootCause(exception) || (
            Utilities.getRootCauseOfType(exception, InterceptorException.class) != null
                && (((InterceptorException) Utilities.getRootCauseOfType(exception, InterceptorException.class))
                .getInterceptor() instanceof UniqueAttributesInterceptor));
    }
}
