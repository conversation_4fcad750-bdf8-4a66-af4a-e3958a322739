package com.sast.cis.payment.dpg.events.exceptions;

import com.sast.dbpg.api.dto.InternalNotificationDto;

/**
 * If this exception is thrown, error must be caught in JMS listener and message must be discarded, not rejected.
 */
public class DpgEventDiscardException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    public DpgEventDiscardException(String reason, InternalNotificationDto internalNotificationDto) {
        super(String.format("Discarding event of type %s with id %s - reason: %s",
            internalNotificationDto.getTransitionEntityType(), internalNotificationDto.getPublicEventId(), reason));
    }
}
