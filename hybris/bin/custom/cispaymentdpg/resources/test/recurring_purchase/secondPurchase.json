{"amount": {"amount": 250.0, "currency": "EUR"}, "cancellationReason": null, "creationDateTime": "2023-02-09T09:44:48Z", "description": "CC3_09022023-2", "distributions": [{"amount": {"amount": 175.0, "currency": "EUR"}, "externalSellerId": null, "marketplaceCommission": {"amount": 75.0, "currency": "EUR"}, "sellerId": "AAEAAAc0ONmMwSRaVnftXi9r"}], "externalPurchaseId": "022760004727", "id": "AAEAAAc0ONq-SEhur62KfZGA", "payments": [{"amount": {"amount": 250.0, "currency": "EUR"}, "cancelReason": null, "creationDateTime": "2023-02-09T09:44:49Z", "id": "AAEAAAc0OPmvxxlrX5ly-V0r", "paymentDistributionInfos": null, "paymentRefundInfo": null, "paymentStatus": "SUCCESS", "paymentType": "PREAUTHORIZATION", "purchase": null, "merchantReference": null, "paymentCategoryCode": 0, "paymentContext": "MarketplacePayIn-5", "paymentInstrument": {"additionalData": null, "creationDateTime": "2023-02-09T08:20:37Z", "currencyCode": null, "customType": null, "displayValue": null, "internalAdditionalData": null, "paymentInstrumentCapabilities": [], "paymentInstrumentId": "AAEAAADs-aZGRrt8fsfG8ZPV", "paymentInstrumentPublicId": "AAEAAAQtUw4ADlCV0-A3Nd-N", "paymentInstrumentStatus": "Active", "paymentInstrumentType": "CREDITCARD", "paymentInstrumentVerificationStatus": "UNVERIFIED", "preferred": false, "publicPaymentInstrumentInfo": null, "searchValue": null, "userAlias": null, "walletId": "AAEAAAMuA8UDQ2laI1mF59zr"}, "paymentMethod": "CARDPAYMENT", "paymentTransactionId": "AAEAAAcf-CtWj8De8GvoKtrl", "reconciledAmount": null, "reconciliationStatus": null, "referencedPaymentId": null, "virtualAccountId": null}, {"amount": {"amount": 250.0, "currency": "EUR"}, "cancelReason": null, "creationDateTime": "2023-02-09T09:45:19Z", "id": "AAEAAAc0OPmv-O_4TisvV9kf", "paymentDistributionInfos": null, "paymentRefundInfo": null, "paymentStatus": "SUCCESS", "paymentType": "CAPTURE", "purchase": null, "merchantReference": null, "paymentCategoryCode": 0, "paymentContext": null, "paymentInstrument": null, "paymentMethod": "CARDPAYMENT", "paymentTransactionId": "AAEAAAcf-CtWjMNDvf-fcTWu", "reconciledAmount": null, "reconciliationStatus": "SUCCESS", "referencedPaymentId": "AAEAAAc0OPmvxxlrX5ly-V0r", "virtualAccountId": null}], "paymentReferences": ["7PszgEW4U4MTiH6f9mVqn9"], "purchaseStatus": "COMPLETED", "purchaseType": "REGULAR", "reconciliationStatus": "SUCCESS"}