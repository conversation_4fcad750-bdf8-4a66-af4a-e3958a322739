package com.sast.cis.payment.dpg.service;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.data.UmpUserData;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.customer.InternalDeveloperUidTranslationService;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.core.service.singlesignon.UserDataQueryService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.cis.email2.service.CisEmailService;
import com.sast.cis.payment.dpg.dto.DpgSellerOnboarding;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.user.AddressModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import generated.com.sast.cis.core.model.DeveloperBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.core.model.user.AddressBuilder;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class DpgSellerOnboardingEmailServiceUnitTest {

    private static final String TEST_USER_ID = "TestUserId";
    private static final String TEST_DEVELOPER_ID_DEVCON = TEST_USER_ID + "@devcon";
    private static final String DEVELOPER_EMAIL_ID = "<EMAIL>";

    @Mock
    private CisEmailService cisEmailService;

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private DeveloperService developerService;

    @Mock
    private UserDataQueryService userDataQueryService;

    @Mock
    private InternalDeveloperUidTranslationService internalDeveloperUidTranslationService;

    @Mock
    private Configuration configuration;

    @Mock
    private CountryModel country_de;

    @Mock
    private CurrencyModel eur;

    private IoTCompanyModel sellerCompany;

    private AddressModel businessAddress;
    private DeveloperModel developer;

    @InjectMocks
    private DpgSellerOnboardingEmailService dpgSellerOnboardingEmailService;

    @Before
    public void setup() {
        businessAddress = AddressBuilder.generate()
            .withStreetname("BusinessStr")
            .withStreetnumber("121")
            .withPostalcode("33333")
            .withTown("BusinessCity")
            .buildMockInstance();
        sellerCompany = IoTCompanyBuilder.generate()
            .withUid("TestSellerCompanyId")
            .withName("Testcompany")
            .withVatID("Sample_VAT_Id")
            .withCountry(country_de)
            .withContactAddress(businessAddress)
            .withAddresses(ImmutableList.of(businessAddress))
            .buildMockInstance();
        developer = DeveloperBuilder.generate()
            .withName("TestSeller")
            .withUid(TEST_DEVELOPER_ID_DEVCON)
            .withEmailAddress(DEVELOPER_EMAIL_ID)
            .withCompany(sellerCompany)
            .buildMockInstance();
        when(developerService.getCurrentDeveloper()).thenReturn(developer);
        when(internalDeveloperUidTranslationService.translateToSsoId(eq(developer))).thenReturn(TEST_USER_ID);
        UmpUserData umpUserData = new UmpUserData();
        umpUserData.setEmail(DEVELOPER_EMAIL_ID);
        when(country_de.getName()).thenReturn("GERMANY");
        when(country_de.getCurrency()).thenReturn(eur);
        when(eur.getIsocode()).thenReturn("EUR");
        when(configurationService.getConfiguration()).thenReturn(configuration);
    }

    @Test
    public void sendOnboardingEmailToRecipient_works_as_expected() {
        dpgSellerOnboardingEmailService.sendOnboardingEmailToRecipient(sellerCompany);

        verify(cisEmailService).sendEmail(eq(EmailType.INITIATE_DPG_SELLER_ONBOARDING), any(DpgSellerOnboarding.class));
        verify(cisEmailService).sendEmail(eq(EmailType.SELLER_ONBOARDING_USER_EMAIL), any(DeveloperModel.class));
    }

    @Test
    public void sendPspActivationEmail_works_as_expected() {
        dpgSellerOnboardingEmailService.sendPspActivationEmailToRecipient(developer);

        verify(cisEmailService, only()).sendEmail(eq(EmailType.PSP_ACTIVATION_EMAIL), any(DeveloperModel.class));
    }
}
