package com.sast.cis.payment.dpg.methods.sepa

import com.sast.cis.core.model.SepaTransferPaymentInstrumentModel
import com.sast.cis.payment.dpg.model.IbanCollectionBankAccountModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.dto.converter.ConversionException
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class SepaTransferPaymentInstrumentPopulatorUnitSpec extends JUnitPlatformSpecification {
    private static final String DUMMY_IBAN = '************************'
    private static final String DUMMY_BIC = 'FOREEE2X'
    private static final String DUMMY_BANK_NAME = 'Amazing Bank Inc.'

    private SepaTransferPaymentInstrumentPopulator sepaTransferPaymentInstrumentPopulator

    private IbanCollectionBankAccountModel collectionAccount = Mock(IbanCollectionBankAccountModel)
    private SepaTransferPaymentInstrumentModel paymentInstrument = Mock(SepaTransferPaymentInstrumentModel)

    def setup() {
        sepaTransferPaymentInstrumentPopulator = new SepaTransferPaymentInstrumentPopulator()
        collectionAccount.getIban() >> DUMMY_IBAN
        collectionAccount.getBic() >> DUMMY_BIC
        collectionAccount.getBankName() >> DUMMY_BANK_NAME
    }

    @Test
    def 'populate sets values from given collection account'() {
        when:
        sepaTransferPaymentInstrumentPopulator.populate(collectionAccount, paymentInstrument)

        then:
        1 * paymentInstrument.setIban(DUMMY_IBAN)
        1 * paymentInstrument.setBic(DUMMY_BIC)
        1 * paymentInstrument.setBankName(DUMMY_BANK_NAME)
    }

    @Test
    def 'populate called without collection account, exception is thrown'() {
        when:
        sepaTransferPaymentInstrumentPopulator.populate(null, paymentInstrument)

        then:
        thrown(ConversionException)
    }

    @Test
    def 'populate called without payment instrument, exception is thrown'() {
        when:
        sepaTransferPaymentInstrumentPopulator.populate(collectionAccount, null)

        then:
        thrown(ConversionException)
    }
}
