package com.sast.cis.payment.dpg.events.service


import com.sast.cis.payment.dpg.enums.DpgEntityStatus
import com.sast.cis.payment.dpg.enums.DpgTransitionEntityType
import com.sast.cis.payment.dpg.events.DpgEventHandler
import com.sast.cis.payment.dpg.events.DpgEventService
import com.sast.cis.payment.dpg.events.dao.DpgEventDao
import com.sast.cis.payment.dpg.events.exceptions.DpgEventDiscardException
import com.sast.cis.payment.dpg.model.DpgEventModel
import com.sast.dbpg.api.dto.EntityStatus
import com.sast.dbpg.api.dto.InternalNotificationDto
import com.sast.dbpg.api.dto.TransitionEntityType
import de.hybris.bootstrap.annotations.UnitTest
import generated.com.sast.cis.payment.dpg.model.DpgEventBuilder
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

import java.time.Instant
import java.time.temporal.ChronoUnit

@UnitTest
class DpgEventServiceUnitSpec extends JUnitPlatformSpecification {
    private static final DPG_ENTITY_ID = "DpgEntityId01"

    private DpgEventDao dpgEventDao = Mock(DpgEventDao)
    private DpgEventCreationService dpgEventCreationService = Mock(DpgEventCreationService)
    private DpgEventHandler dpgAccountUpdateEventHandler = Mock(DpgEventHandler)
    private DpgEventModel existingDpgEvent
    private DpgEventModel newDpgEvent
    private DpgEventService dpgEventService

    def setup() {
        dpgAccountUpdateEventHandler.getType() >> DpgTransitionEntityType.MARKETPLACESELLER
        dpgEventService = new DpgEventService(dpgEventDao, dpgEventCreationService, List.of(dpgAccountUpdateEventHandler))
        existingDpgEvent = createDpgEvent(DPG_ENTITY_ID)
        newDpgEvent = createDpgEvent("dpgEntity")
        dpgEventDao.getLatestEventForDpgEntityId(DPG_ENTITY_ID, DpgTransitionEntityType.MARKETPLACESELLER) >> Optional.of(existingDpgEvent)
        dpgEventCreationService.createDpgEvent(_ as InternalNotificationDto) >> newDpgEvent
    }

    @Test
    def 'handle update seller account event as new event, creates dpg event'() {
        given:
        InternalNotificationDto notificationDto = createNotificationDto(TransitionEntityType.MARKETPLACESELLER)

        when:
        dpgEventService.handleEvent(notificationDto)

        then:
        dpgEventDao.getLatestEventForDpgEntityId(DPG_ENTITY_ID, DpgTransitionEntityType.MARKETPLACESELLER) >> Optional.empty()
        1 * dpgEventCreationService.createDpgEvent(notificationDto) >> newDpgEvent
        1 * dpgAccountUpdateEventHandler.handleEvent(newDpgEvent)
    }

    @Test
    def 'handle update seller account event with existing event with date earlier than the notification event date, creates new dpg event'() {
        given:
        InternalNotificationDto notificationDto = createNotificationDto(TransitionEntityType.MARKETPLACESELLER)
        existingDpgEvent.setEventDate(Date.from(Instant.now().minus(1, ChronoUnit.DAYS)))

        when:
        dpgEventService.handleEvent(notificationDto)

        then:
        1 * dpgEventCreationService.createDpgEvent(notificationDto) >> newDpgEvent
        1 * dpgAccountUpdateEventHandler.handleEvent(newDpgEvent)

    }

    @Test
    def 'handle update seller account event with existing event with date later than the notification event date, discards the dpg event'() {
        given:
        InternalNotificationDto notificationDto = createNotificationDto(TransitionEntityType.MARKETPLACESELLER)
        notificationDto.setEventDateTime(Instant.now().minus(1, ChronoUnit.DAYS))

        when:
        dpgEventService.handleEvent(notificationDto)

        then:
        thrown(DpgEventDiscardException)
        0 * dpgEventCreationService.createDpgEvent(_ as InternalNotificationDto)
        0 * dpgAccountUpdateEventHandler.handleEvent(_)
    }

    @Test
    def 'event with unsupported entity type does not trigger a handler'() {
        given:
        InternalNotificationDto notificationDto = createNotificationDto(TransitionEntityType.MARKETPLACEPAYMENT)

        when:
        dpgEventService.handleEvent(notificationDto)

        then:
        0 * dpgEventCreationService.createDpgEvent(_ as InternalNotificationDto)
        thrown(DpgEventDiscardException)
    }

    @Test
    def 'process stored events processes all stored events for given entity and type'() {
        given:
        def givenEntityId = 'Amazing entity ID'
        def givenEntityType = DpgTransitionEntityType.MARKETPLACESELLER
        def firstEvent = createDpgEvent(givenEntityId)
        def secondEvent = createDpgEvent(givenEntityId)

        when:
        dpgEventService.processStoredEventsForEntity(givenEntityId, givenEntityType)

        then:
        dpgEventDao.getEventsForEntityId(givenEntityId, givenEntityType) >> [firstEvent, secondEvent]
        1 * dpgAccountUpdateEventHandler.handleEvent(firstEvent)
        1 * dpgAccountUpdateEventHandler.handleEvent(secondEvent)
    }

    @Test
    def 'process stored events skips events without handler'() {
        given:
        def givenEntityId = 'Amazing entity ID'
        def givenEntityType = DpgTransitionEntityType.MARKETPLACEPAYMENT
        def firstEvent = createDpgEvent(givenEntityId)
        firstEvent.setEntityType(givenEntityType)
        def secondEvent = createDpgEvent(givenEntityId)
        secondEvent.setEntityType(givenEntityType)

        when:
        dpgEventService.processStoredEventsForEntity(givenEntityId, givenEntityType)

        then:
        dpgEventDao.getEventsForEntityId(givenEntityId, givenEntityType) >> [firstEvent, secondEvent]
        0 * dpgAccountUpdateEventHandler.handleEvent(firstEvent)
        0 * dpgAccountUpdateEventHandler.handleEvent(secondEvent)
    }

    private InternalNotificationDto createNotificationDto(TransitionEntityType entityType) {
        return new InternalNotificationDto.Builder()
                .publicEventId("eventId")
                .transitionEntityType(entityType)
                .eventDateTime(Instant.now())
                .newStatus(EntityStatus.SUCCESS)
                .transitionEntityId(DPG_ENTITY_ID)
                .externalTransitionEntityId("DeveloperCompany")
                .build()
    }

    private DpgEventModel createDpgEvent(String dpgEntityId) {
        return DpgEventBuilder.generate()
                .withDpgEntityId(dpgEntityId)
                .withEventStatus(DpgEntityStatus.PENDING)
                .withEntityType(DpgTransitionEntityType.MARKETPLACESELLER)
                .withEventDate(new Date())
                .buildInstance()
    }
}
