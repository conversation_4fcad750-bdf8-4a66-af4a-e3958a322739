package com.sast.cis.payment.dpg.methods.cc

import com.sast.cis.core.data.CreditCardPaymentInfoData
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.paymentintegration.paymentinfo.IntegratorPaymentInfoService
import com.sast.cis.payment.dpg.exception.DpgException
import com.sast.cis.payment.dpg.model.DpgCreditCardPaymentInfoModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.payment.dpg.model.DpgCreditCardPaymentInfoBuilder
import org.junit.Test

import static com.sast.cis.core.enums.PaymentProvider.DPG

@UnitTest
class DpgCreditCardPaymentInfoServiceUnitSpec extends JUnitPlatformSpecification {
    private static final String CARD_TOKEN = 'iAmAToken'
    private static final String CARD_HOLDER_NAME = 'Bertl Kartenzahler'
    private static final String CARD_NUMBER = '***********1234'
    private static final String EXPIRY_MONTH = '12'
    private static final String EXPIRY_YEAR = '35'
    private static final String PAYMENT_INSTRUMENT_ID = 'somePaymentInstrument'



    private IntegratorPaymentInfoService integratorPaymentInfoService = Mock()
    private ModelService modelService = Mock()

    private DpgCreditCardPaymentInfoService dpgCreditCardPaymentInfoService

    private IntegratorModel mockIntegrator = Mock(IntegratorModel)
    private CartModel mockCart = Mock(CartModel)

    private DpgCreditCardPaymentInfoModel firstSavedInfo = Mock()
    private DpgCreditCardPaymentInfoModel secondSavedInfo = Mock()
    private DpgCreditCardPaymentInfoModel cartPaymentInfo = Mock()

    def setup() {
        dpgCreditCardPaymentInfoService = new DpgCreditCardPaymentInfoService(integratorPaymentInfoService, modelService)

        integratorPaymentInfoService.getPaymentInfos(mockIntegrator, DPG, DpgCreditCardPaymentInfoModel) >> ([firstSavedInfo, secondSavedInfo] as Set)
        integratorPaymentInfoService.getDefaultPaymentInfo(mockIntegrator, DPG, DpgCreditCardPaymentInfoModel) >> Optional.of(secondSavedInfo)
        integratorPaymentInfoService.getCartPaymentInfo(mockCart, DPG, DpgCreditCardPaymentInfoModel) >> Optional.of(cartPaymentInfo)
    }

    @Test
    void 'given integrator has appropriate payment info, it is returned'() {
        when:
        def actualInfos = dpgCreditCardPaymentInfoService.getPaymentInfos(mockIntegrator)

        then:
        actualInfos == ([firstSavedInfo, secondSavedInfo] as Set)
    }

    @Test
    void 'given integrator has no appropriate payment info, empty set is returned'() {
        when:
        def actualInfos = dpgCreditCardPaymentInfoService.getPaymentInfos(mockIntegrator)

        then:
        integratorPaymentInfoService.getPaymentInfos(mockIntegrator, DPG, DpgCreditCardPaymentInfoModel) >> ([] as Set)
        actualInfos == ([] as Set)
    }

    @Test
    void 'given integrator has appropriate payment info as default, it is returned'() {
        when:
        def actualDefault = dpgCreditCardPaymentInfoService.getDefaultPaymentInfo(mockIntegrator)

        then:
        actualDefault == Optional.of(secondSavedInfo)
    }

    @Test
    void 'given integrator has no appropriate default payment info, empty optional is returned'() {
        when:
        def actualDefault = dpgCreditCardPaymentInfoService.getDefaultPaymentInfo(mockIntegrator)

        then:
        integratorPaymentInfoService.getDefaultPaymentInfo(mockIntegrator, DPG, DpgCreditCardPaymentInfoModel) >> Optional.empty()
        actualDefault == Optional.empty()
    }

    @Test
    void 'given cart has appropriate saved payment info, it is returned'() {
        given:
        cartPaymentInfo.isSaved() >> true

        when:
        def actualCartInfo = dpgCreditCardPaymentInfoService.getCartPaymentInfo(mockCart)

        then:
        actualCartInfo == Optional.of(cartPaymentInfo)
    }

    @Test
    void 'given cart has appropriate unsaved payment info, empty optional is returned'() {
        given:
        cartPaymentInfo.isSaved() >> false

        when:
        def actualCartInfo = dpgCreditCardPaymentInfoService.getCartPaymentInfo(mockCart)

        then:
        actualCartInfo == Optional.empty()
    }

    @Test
    void 'given cart has no appropriate payment info, empty optional is returned'() {
        when:
        def actualCartInfo = dpgCreditCardPaymentInfoService.getCartPaymentInfo(mockCart)

        then:
        integratorPaymentInfoService.getCartPaymentInfo(mockCart, DPG, DpgCreditCardPaymentInfoModel) >> Optional.empty()
        actualCartInfo == Optional.empty()
    }

    @Test
    void 'createPaymentInfo creates a new payment info for the given data'() {
        given:
        CreditCardPaymentInfoData givenPaymentInfoData = new CreditCardPaymentInfoData()
            .withAccountHolderName(CARD_HOLDER_NAME)
            .withCardNumber(CARD_NUMBER)
            .withExpiryMonth(EXPIRY_MONTH)
            .withExpiryYear(EXPIRY_YEAR)
            .withToken(CARD_TOKEN)
            .withReusable(false)
        DpgCreditCardPaymentInfoModel mockInfo = Mock()

        when:
        def actualPaymentInfo = dpgCreditCardPaymentInfoService.createPaymentInfo(mockIntegrator, givenPaymentInfoData)

        then:
        1 * modelService.create(DpgCreditCardPaymentInfoModel) >> mockInfo

        1 * mockInfo.setCode(_)
        1 * mockInfo.setToken(CARD_TOKEN)
        1 * mockInfo.setPaymentProvider(DPG)
        1 * mockInfo.setSaved(false)
        1 * mockInfo.setUser(mockIntegrator)
        1 * mockInfo.setCcOwner(CARD_HOLDER_NAME)
        1 * mockInfo.setNumber(CARD_NUMBER)
        1 * mockInfo.setValidToMonth(EXPIRY_MONTH)
        1 * mockInfo.setValidToYear(EXPIRY_YEAR)
        1 * mockInfo.setSubscriptionValidated(true)
        1 * mockInfo.setSubscriptionId(CARD_TOKEN)

        1 * modelService.save(mockInfo)
        1 * modelService.refresh(mockIntegrator)

        actualPaymentInfo == mockInfo
    }

    @Test
    void 'createPaymentInfo throws if no integrator is given'() {
        given:
        CreditCardPaymentInfoData givenPaymentInfoData = new CreditCardPaymentInfoData()
                .withAccountHolderName(CARD_HOLDER_NAME)
                .withCardNumber(CARD_NUMBER)
                .withExpiryMonth(EXPIRY_MONTH)
                .withExpiryYear(EXPIRY_YEAR)
                .withToken(CARD_TOKEN)
                .withReusable(false)

        when:
        dpgCreditCardPaymentInfoService.createPaymentInfo(null, givenPaymentInfoData)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createPaymentInfo throws if no paymentInfoData is given'() {
        when:
        dpgCreditCardPaymentInfoService.createPaymentInfo(mockIntegrator, null)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createPaymentInfo throws if given paymentInfoData has no token'() {
        given:
        CreditCardPaymentInfoData givenPaymentInfoData = new CreditCardPaymentInfoData()
                .withAccountHolderName(CARD_HOLDER_NAME)
                .withCardNumber(CARD_NUMBER)
                .withExpiryMonth(EXPIRY_MONTH)
                .withExpiryYear(EXPIRY_YEAR)
                .withReusable(false)

        when:
        dpgCreditCardPaymentInfoService.createPaymentInfo(mockIntegrator, givenPaymentInfoData)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'removePaymentInfo deletes the paymentInfo and refreshes the user'() {
        given:
        def givenInfo = DpgCreditCardPaymentInfoBuilder.generate()
                .withUser(mockIntegrator)
                .buildInstance()

        when:
        dpgCreditCardPaymentInfoService.removePaymentInfo(givenInfo)

        then:
        1 * modelService.remove(givenInfo)
        1 * modelService.refresh(mockIntegrator)
    }

    @Test
    void 'removePaymentInfo deletes the paymentInfo does not attempt to refresh the user if the given info has no user'() {
        given:
        def givenInfo = DpgCreditCardPaymentInfoBuilder.generate()
                .buildInstance()

        when:
        dpgCreditCardPaymentInfoService.removePaymentInfo(givenInfo)

        then:
        1 * modelService.remove(givenInfo)
        0 * modelService.refresh(_)
    }

    @Test
    void 'removePaymentInfo does not delete the paymentInfo if it is saved'() {
        given:
        def givenInfo = DpgCreditCardPaymentInfoBuilder.generate()
                .withUser(mockIntegrator)
                .buildInstance()

        when:
        dpgCreditCardPaymentInfoService.removePaymentInfo(givenInfo)

        then:
        1 * modelService.remove(givenInfo)
        1 * modelService.refresh(mockIntegrator)
    }

    @Test
    void 'removePaymentInfo throws if no paymentInfo is given'() {
        when:
        dpgCreditCardPaymentInfoService.removePaymentInfo(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'updatePaymentInfoAfterAuthorization updates the paymentInstrumentId if given info has none yet'() {
        given:
        DpgCreditCardPaymentInfoModel givenPaymentInfo = Mock()

        when:
        dpgCreditCardPaymentInfoService.updatePaymentInfoAfterAuthorization(givenPaymentInfo, PAYMENT_INSTRUMENT_ID)

        then:
        1 * givenPaymentInfo.setPaymentInstrumentId(PAYMENT_INSTRUMENT_ID)
        1 * modelService.save(givenPaymentInfo)
    }

    @Test
    void 'updatePaymentInfoAfterAuthorization does not update if paymentInfo is saved'() {
        given:
        DpgCreditCardPaymentInfoModel givenPaymentInfo = Mock()
        givenPaymentInfo.isSaved() >> true

        when:
        dpgCreditCardPaymentInfoService.updatePaymentInfoAfterAuthorization(givenPaymentInfo, PAYMENT_INSTRUMENT_ID)

        then:
        0 * givenPaymentInfo.setPaymentInstrumentId(_)
        0 * modelService.save(givenPaymentInfo)
    }

    @Test
    void 'updatePaymentInfoAfterCompletion sets saved attribute if given info is marked reusable and is not saved'() {
        given:
        DpgCreditCardPaymentInfoModel userPaymentInfo = Mock()
        userPaymentInfo.isReusable() >> true
        userPaymentInfo.isSaved() >> false

        when:
        dpgCreditCardPaymentInfoService.updatePaymentInfoAfterCompletion(cartPaymentInfo)

        then:
        1 * integratorPaymentInfoService.getNonDuplicateOriginalInfo(cartPaymentInfo, DpgCreditCardPaymentInfoModel.class) >> Optional.of(userPaymentInfo)
        1 * userPaymentInfo.setSaved(true)
        1 * modelService.save(userPaymentInfo)
        0 * modelService._
    }

    @Test
    void 'updatePaymentInfoAfterCompletion does not update if given info is marked reusable and is saved'() {
        given:
        DpgCreditCardPaymentInfoModel userPaymentInfo = Mock()
        userPaymentInfo.isReusable() >> true
        userPaymentInfo.isSaved() >> true

        when:
        dpgCreditCardPaymentInfoService.updatePaymentInfoAfterCompletion(cartPaymentInfo)

        then:
        1 * integratorPaymentInfoService.getNonDuplicateOriginalInfo(cartPaymentInfo, DpgCreditCardPaymentInfoModel.class) >> Optional.of(userPaymentInfo)
        0 * userPaymentInfo.setSaved(_)
        0 * modelService._
    }

    @Test
    void 'updatePaymentInfoAfterCompletion does not update if given info is not marked reusable'() {
        given:
        DpgCreditCardPaymentInfoModel userPaymentInfo = Mock()
        userPaymentInfo.isReusable() >> false
        userPaymentInfo.isSaved() >> false

        when:
        dpgCreditCardPaymentInfoService.updatePaymentInfoAfterCompletion(cartPaymentInfo)

        then:
        1 * integratorPaymentInfoService.getNonDuplicateOriginalInfo(cartPaymentInfo, DpgCreditCardPaymentInfoModel.class) >> Optional.of(userPaymentInfo)
        0 * userPaymentInfo.setSaved(_)
        0 * modelService._
    }

    @Test
    void 'updatePaymentInfoAfterAuthorization throws if given info already has a different paymentInstrumentId'() {
        given:
        DpgCreditCardPaymentInfoModel givenPaymentInfo = DpgCreditCardPaymentInfoBuilder.generate()
                .withPaymentInstrumentId('someOtherId')
                .buildInstance()

        when:
        dpgCreditCardPaymentInfoService.updatePaymentInfoAfterAuthorization(givenPaymentInfo, PAYMENT_INSTRUMENT_ID)

        then:
        thrown(DpgException)
    }

    @Test
    void 'updatePaymentInfoAfterAuthorization doesnt do anything if given info already has the same payment instrument ID'() {
        given:
        DpgCreditCardPaymentInfoModel givenPaymentInfo = DpgCreditCardPaymentInfoBuilder.generate()
                .withPaymentInstrumentId(PAYMENT_INSTRUMENT_ID)
                .buildInstance()

        when:
        dpgCreditCardPaymentInfoService.updatePaymentInfoAfterAuthorization(givenPaymentInfo, PAYMENT_INSTRUMENT_ID)

        then:
        noExceptionThrown()
        0 * modelService._
        0 * givenPaymentInfo.setPaymentInstrumentId(_)
    }

    @Test
    void 'getPaymentProvider returns DPG'() {
        when:
        def actualPaymentProvider = dpgCreditCardPaymentInfoService.getPaymentProvider()

        then:
        actualPaymentProvider == DPG
    }
}
