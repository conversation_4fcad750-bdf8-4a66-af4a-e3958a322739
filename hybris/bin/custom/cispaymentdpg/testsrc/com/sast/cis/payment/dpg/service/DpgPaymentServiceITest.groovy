package com.sast.cis.payment.dpg.service

import com.sast.cis.core.constants.CiscoreConstants
import com.sast.cis.core.data.CreditCardPaymentInfoData
import com.sast.cis.core.enums.Feature
import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.model.*
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter
import com.sast.cis.core.paymentintegration.data.AuthorizationStatus
import com.sast.cis.core.service.customer.developer.DeveloperService
import com.sast.cis.core.service.customer.integrator.IntegratorService
import com.sast.cis.payment.dpg.enums.DpgSellerAccountStatus
import com.sast.cis.payment.dpg.exception.DpgException
import com.sast.cis.payment.dpg.methods.cc.DpgCreditCardTransactionService
import com.sast.cis.payment.dpg.methods.cc.DpgPersistedPurchaseStatus
import com.sast.cis.payment.dpg.model.DpgSellerAccountModel
import com.sast.cis.test.utils.*
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.catalog.model.CatalogVersionModel
import de.hybris.platform.commercefacades.user.data.AddressData
import de.hybris.platform.commercefacades.user.data.CountryData
import de.hybris.platform.core.enums.CreditCardType
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.core.model.user.AddressModel
import de.hybris.platform.order.CartService
import de.hybris.platform.payment.dto.TransactionStatus
import de.hybris.platform.payment.dto.TransactionStatusDetails
import de.hybris.platform.payment.enums.PaymentTransactionType
import de.hybris.platform.product.UnitService
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.site.BaseSiteService
import de.hybris.platform.store.BaseStoreModel
import de.hybris.platform.store.services.BaseStoreService
import generated.com.sast.cis.core.model.AchInternationalCreditTransferPaymentInfoBuilder
import generated.com.sast.cis.core.model.IoTCompanyBuilder
import generated.com.sast.cis.core.model.SepaCreditTransferPaymentInfoBuilder
import generated.com.sast.cis.payment.dpg.model.DpgSellerAccountBuilder
import generated.de.hybris.platform.core.model.user.AddressBuilder
import org.junit.Rule
import org.junit.Test

import javax.annotation.Resource

import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.LicenseType.FULL
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_DEVELOPER_A1_SSOID
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_DEVELOPER_A1_UID
import static com.sast.cis.test.utils.UmpWireMockRule.UMP_BASE_URL_KEY
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED

@IntegrationTest
class DpgPaymentServiceITest extends ServicelayerTransactionalSpockSpecification {
    private static final BigDecimal AUTH_AMOUNT = BigDecimal.valueOf(24999.00)
    private static final String CONFIGURED_ACH_ACCOUNT = '********'
    private static final String CONFIGURED_ACH_ROUTING = '*********'
    private static final String CONFIGURED_ACH_BIC = 'DEUTUS33XXX'
    private static final String CONFIGURED_ACH_BANK = 'Deutsche Bank'
    private static final String CONFIGURED_SEPA_BIC = 'DEUTDEFFVAC'
    private static final String CONFIGURED_SEPA_BANK = 'Deutsche Bank'
    private static final String CONFIGURED_SEPA_IBAN_PATTERN = /DE([0-9]{2})*************([0-9]{5})/


    @Resource
    private IntegratorService integratorService

    @Resource
    private CartService cartService

    @Resource
    private ModelService modelService

    @Resource
    private UserService userService

    @Resource
    private DpgSellerOnboardingEmailService dpgSellerOnboardingEmailService

    @Resource
    private ConfigurationService configurationService

    @Resource
    private DeveloperService developerService

    @Resource
    private DpgPaymentService dpgPaymentService

    @Resource
    private UnitService unitService

    @Resource
    private BaseStoreService baseStoreService

    @Resource
    private BaseSiteService baseSiteService

    @Resource
    private DpgCreditCardTransactionService dpgCreditCardTransactionService

    @Rule
    public UmpWireMockRule umpWireMockRule = new UmpWireMockRule()

    @Rule
    public FeatureToggleRule featureToggleRule = new FeatureToggleRule()

    private IoTCompanyModel sellerCompany

    private AddressModel businessAddress
    private CountryModel country_de
    private SampleDataCreator sampleDataCreator = new SampleDataCreator()
    private DpgSellerAccountModel dpgSellerAccount
    private IntegratorModel integrator
    private AppLicenseModel license

    def setup() {
        integrator = integratorService.getIntegratorByInternalUserId(TestDataConstants.SAMPLE_DATA_INTEGRATOR_UID)
        featureToggleRule.enable(Feature.FEATURE_EMAIL_CLIENT_ENABLED);
        country_de = sampleDataCreator.getCountry(Country.GERMANY)
        sellerCompany = IoTCompanyBuilder.generate()
                .withUid("TestSellerCompanyId")
                .withName("Testcompany")
                .withVatID("Sample_VAT_Id")
                .withCountry(country_de)
                .withContactAddress(businessAddress)
                .buildIntegrationInstance()
        modelService.save(sellerCompany)
        businessAddress = AddressBuilder.generate()
                .withStreetname("BusinessStr")
                .withStreetnumber("121")
                .withPostalcode("33333")
                .withTown("BusinessCity")
                .withOwner(sellerCompany)
                .buildIntegrationInstance()
        sellerCompany.setAddresses(List.of(businessAddress))
        modelService.saveAll(businessAddress, sellerCompany)
        configurationService.getConfiguration().setProperty(UMP_BASE_URL_KEY, umpWireMockRule.getUmpUrl())
        umpWireMockRule.prepareGetUserDataResponse(SAMPLE_DATA_DEVELOPER_A1_SSOID, sellerCompany.getUid())
        DeveloperModel currentDeveloper = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID)
        currentDeveloper.setEmailAddress("<EMAIL>")
        userService.setCurrentUser(currentDeveloper)

        AppModel app = sampleDataCreator.createApp("app", "sample.package.name", ONLINE)
        license = sampleDataCreator.createAppLicense("app version", app, APPROVED, FULL)
        license.setPriceQuantity(1.0)
        setCurrentBaseStore(license.getCatalogVersion())
    }

    @Test
    def 'createSellerAccount create new Dpg Seller Account when no Dpg Seller Account exists for the given Seller Company'() {
        when:
        sellerCompany.setPspSellerAccounts(List.of())
        modelService.save(sellerCompany)

        dpgPaymentService.createSellerAccount(sellerCompany)

        then:
        sellerCompany.getPspSellerAccounts().size() == 1
        ((DpgSellerAccountModel) sellerCompany.getPspSellerAccounts().get(0)).getDpgStatus() == DpgSellerAccountStatus.EMAIL_SENT
    }

    @Test
    def 'createSellerAccount uses existing Dpg Seller Account for the given Seller Company'() {
        when:
        dpgSellerAccount = DpgSellerAccountBuilder.generate()
                .withStatus(PspSellerAccountStatus.ONBOARDING)
                .withCompany(sellerCompany)
                .withPaymentProvider(PaymentProvider.DPG)
                .withDpgStatus(DpgSellerAccountStatus.ERROR)
                .buildIntegrationInstance()
        sellerCompany.setPspSellerAccounts(List.of(dpgSellerAccount))
        modelService.saveAll(dpgSellerAccount, sellerCompany)

        sellerCompany.getPspSellerAccounts().size() == 1
        dpgPaymentService.createSellerAccount(sellerCompany)

        then:
        thrown(DpgException)
        sellerCompany.getPspSellerAccounts().size() == 1
    }

    @Test
    def 'payment can be initialized and authorized for a cart with ACH payment info'() {
        given:
        def authParameter = createAuthorizationParameter()
        attachAchInfo(authParameter.abstractOrder())

        when:
        def authorizationEntry = dpgPaymentService.authorize(authParameter)
        def authorizationResult = dpgPaymentService.getAuthorizationResult(authorizationEntry)

        then:
        authorizationResult.authorizationStatus == AuthorizationStatus.SUCCESS

        with(authorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
                amount == AUTH_AMOUNT
                currency == integrator.company.country.currency
                !cartHash.isBlank()
                time != null
            }
        }

        with(authorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.DPG.name()
                plannedAmount == AUTH_AMOUNT
                currency == integrator.company.country.currency
                info == authParameter.abstractOrder().paymentInfo
                order == authParameter.abstractOrder()
            }
        }

        with(authorizationResult.paymentTransactionEntry.paymentTransaction.paymentInstrument as AchTransferPaymentInstrumentModel) {
            verifyAll {
                accountNumber == CONFIGURED_ACH_ACCOUNT
                routingNumber == CONFIGURED_ACH_ROUTING
                bic == CONFIGURED_ACH_BIC
                bankName == CONFIGURED_ACH_BANK
            }
        }
    }

    @Test
    def 'payment can be initialized and authorized for a cart with SEPA payment info'() {
        given:
        def authParameter = createAuthorizationParameter()
        attachSepaInfo(authParameter.abstractOrder())

        when:
        def authorizationEntry = dpgPaymentService.authorize(authParameter)
        def authorizationResult = dpgPaymentService.getAuthorizationResult(authorizationEntry)

        then:
        authorizationResult.authorizationStatus == AuthorizationStatus.SUCCESS

        with(authorizationResult.paymentTransactionEntry) {
            verifyAll {
                type == PaymentTransactionType.AUTHORIZATION
                transactionStatus == TransactionStatus.ACCEPTED.name()
                transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
                amount == AUTH_AMOUNT
                currency == integrator.company.country.currency
                !cartHash.isBlank()
                time != null
            }
        }

        with(authorizationResult.paymentTransactionEntry.paymentTransaction) {
            verifyAll {
                paymentProvider == PaymentProvider.DPG.name()
                plannedAmount == AUTH_AMOUNT
                currency == integrator.company.country.currency
                info == authParameter.abstractOrder().paymentInfo
                order == authParameter.abstractOrder()
            }
        }

        with(authorizationResult.paymentTransactionEntry.paymentTransaction.paymentInstrument as SepaTransferPaymentInstrumentModel) {
            verifyAll {
                bankName == CONFIGURED_SEPA_BANK
                bic == CONFIGURED_SEPA_BIC
                iban ==~ CONFIGURED_SEPA_IBAN_PATTERN
            }
        }
    }

    @Test
    def 'payment can be initiated and authorized for a cart with CC payment info'() {
        given:
        featureToggleRule.enable(Feature.FEATURE_ENABLE_DPG_CC)
        def authParameter = createAuthorizationParameter()
        def cart = authParameter.abstractOrder()

        when: 'user visits checkout page'
        dpgPaymentService.prepareCheckout(authParameter, PaymentMethodType.CREDIT_CARD)

        and: 'card details are submitted'
        def paymentInfo = dpgPaymentService.createPaymentInfo(integrator, creditCardInfoData)
        cart.setPaymentInfo(paymentInfo)
        modelService.save(cart)
        dpgPaymentService.confirmSelectedPaymentInfo(authParameter)

        and: 'order is placed'
        def authorizationEntry = dpgPaymentService.authorize(authParameter)
        def authorizationResult = dpgPaymentService.getAuthorizationResult(authorizationEntry)

        then:
        authorizationResult.authorizationStatus == AuthorizationStatus.SUCCESS
    }

    @Test
    def 'current purchase is marked for cancellation and new purchase created and authorized if user revisits payment method page'() {
        given:
        featureToggleRule.enable(Feature.FEATURE_ENABLE_DPG_CC)
        def authParameter = createAuthorizationParameter()
        def cart = authParameter.abstractOrder()

        when: 'Checkout is created'
        dpgPaymentService.prepareCheckout(authParameter, PaymentMethodType.CREDIT_CARD)
        and: 'Checkout is revisited'
        dpgPaymentService.prepareCheckout(authParameter, PaymentMethodType.CREDIT_CARD)
        and: 'card details are submitted'
        def paymentInfo = dpgPaymentService.createPaymentInfo(integrator, creditCardInfoData)
        cart.setPaymentInfo(paymentInfo)
        modelService.save(cart)
        dpgPaymentService.confirmSelectedPaymentInfo(authParameter)

        and: 'order is placed'
        def authorizationEntry = dpgPaymentService.authorize(authParameter)
        def authorizationResult = dpgPaymentService.getAuthorizationResult(authorizationEntry)

        then:
        def transactionStatusList = cart.getPaymentTransactions().stream()
            .map {dpgCreditCardTransactionService.getTransactionStatus(it)}
            .toList()
        transactionStatusList == [DpgPersistedPurchaseStatus.CANCELLATION_PENDING, DpgPersistedPurchaseStatus.PREAUTHORIZED]
        authorizationResult.authorizationStatus == AuthorizationStatus.SUCCESS
        authorizationResult.paymentTransactionEntry == authorizationEntry
    }

    @Test
    def 'current purchase is marked for cancellation and new purchase created and authorized if user revisits payment method page after CC submission'() {
        given:
        featureToggleRule.enable(Feature.FEATURE_ENABLE_DPG_CC)
        def authParameter = createAuthorizationParameter()
        def cart = authParameter.abstractOrder()

        when: 'Checkout is created'
        dpgPaymentService.prepareCheckout(authParameter, PaymentMethodType.CREDIT_CARD)
        and: 'Card details are submitted'
        def paymentInfo = dpgPaymentService.createPaymentInfo(integrator, creditCardInfoData)
        cart.setPaymentInfo(paymentInfo)
        modelService.save(cart)
        dpgPaymentService.confirmSelectedPaymentInfo(authParameter)

        and: 'Checkout is repeated'
        dpgPaymentService.prepareCheckout(authParameter, PaymentMethodType.CREDIT_CARD)
        and: 'card details are submitted again'
        def secondPaymentInfo = dpgPaymentService.createPaymentInfo(integrator, creditCardInfoData)
        cart.setPaymentInfo(secondPaymentInfo)
        modelService.save(cart)
        dpgPaymentService.confirmSelectedPaymentInfo(authParameter)

        and: 'order is placed'
        def authorizationEntry = dpgPaymentService.authorize(authParameter)
        def authorizationResult = dpgPaymentService.getAuthorizationResult(authorizationEntry)

        then:
        def transactionStatusList = cart.getPaymentTransactions().stream()
                .map {dpgCreditCardTransactionService.getTransactionStatus(it)}
                .toList()
        transactionStatusList == [DpgPersistedPurchaseStatus.CANCELLATION_PENDING, DpgPersistedPurchaseStatus.PREAUTHORIZED]
        authorizationResult.authorizationStatus == AuthorizationStatus.SUCCESS
        authorizationResult.paymentTransactionEntry == authorizationEntry
        authorizationEntry.paymentTransaction.info == secondPaymentInfo
    }

    def getCreditCardInfoData() {
        def cardPaymentInfo = new CreditCardPaymentInfoData()
                .withSubscriptionId("DPG_SUBS_ID")
                .withAccountHolderName('HansDampf')

        // can't be set with fluent setters because the return type is wrong
        cardPaymentInfo.setCardNumber("****************")
        cardPaymentInfo.setExpiryMonth("01")
        cardPaymentInfo.setExpiryYear("2030")
        cardPaymentInfo.setCardType(CreditCardType.VISA.name())
        cardPaymentInfo.setPaymentMethod(PaymentMethodType.CREDIT_CARD)
        cardPaymentInfo.setBillingAddress(defaultBillingAddress)
        cardPaymentInfo.setReusable(true)
        cardPaymentInfo.setToken("some_token_1234_1")
        cardPaymentInfo
    }

    private static getDefaultBillingAddress() {
        new AddressData()
                .withFirstName('Hans')
                .withLastName('Dampf')
                .withCountry(new CountryData().withIsocode('DE'))
                .withCompanyName('Dampf Enterprises')
                .withLine1('Dorfplatz 23')
                .withTown('Hinterpfuiteufel')
                .withPostalCode('54321')
                .withBillingAddress(true)
    }

    def createAuthorizationParameter() {
        userService.setCurrentUser(integrator)
        def cart = cartService.getSessionCart()
        AuthorizationParameter.builder()
            .abstractOrder(cart)
            .plannedAmount(AUTH_AMOUNT)
            .build()
    }

    def attachAchInfo(AbstractOrderModel abstractOrder) {
        def paymentInfo = AchInternationalCreditTransferPaymentInfoBuilder.generate()
            .withUser(integrator)
            .withCode('foo')
            .withPaymentProvider(PaymentProvider.DPG)
            .buildIntegrationInstance()
        modelService.save(paymentInfo)
        abstractOrder.setPaymentInfo(paymentInfo)
        modelService.save(abstractOrder)
    }

    def attachSepaInfo(AbstractOrderModel abstractOrder) {
        def paymentInfo = SepaCreditTransferPaymentInfoBuilder.generate()
                .withUser(integrator)
                .withCode('foo')
                .withPaymentProvider(PaymentProvider.DPG)
                .buildIntegrationInstance()
        modelService.save(paymentInfo)
        abstractOrder.setPaymentInfo(paymentInfo)
        modelService.save(abstractOrder)
    }

    private void setCurrentBaseStore(CatalogVersionModel catalogVersion) {
        BaseStoreModel store = baseStoreService.getBaseStoreForUid(
                catalogVersion
                        .getCatalog()
                        .getBaseStores()
                        .stream()
                        .findFirst()
                        .orElseThrow(() -> new IllegalStateException(String.format("No store found for %s", CiscoreConstants.CIS_PRODUCT_CATALOG)))
                        .getUid())
        baseSiteService.setCurrentBaseSite(store.getCmsSites().stream()
                .filter(site -> site.getUid().equals(store.getUid())).findFirst()
                .orElseThrow(() -> new IllegalStateException(String.format("No site found for %s", store.getUid()))), false)
    }
}


