
<!DOCTYPE html PUBLIC "-//thestyleworks.de//DTD XHTML 1.0 Custom//EN" "../dtd/xhtml1-custom.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="de" lang="de">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF8">
	<link rel="stylesheet" href="styles/hybris_main.css">
	<title>Webservice examples - hybris Platform </title>
</head>

	<body>
		<div id="head">&nbsp;</div>
		<div id="rightmargin">&nbsp;</div>
		<div id="headsystem" class="header"></div>
		<div id="headtop">
			<img name ="head_E-Business_Software" src="images/HEAD_e-business_platform.gif" /><br/>
	 		<div class="header">Webservices Examples</div>
			<div class="header" style="font-size: 8px;"><br/><br/></div>
		</div>
	
		<div id="MainNav"></div>
		<div id="Scaleback">&nbsp;</div>
	
		<div id="main">
			<div id="right"></div>

			<div id="content">
				<div id="absatz" align="left">
					&nbsp;
				</div>

				<div class="absatz" style="width: 720px;">
					<h1>Webservice</h1>
					<table>
						<tr><td><b>Specification: </b></td> <td><a href="http://jcp.org/aboutJava/communityprocess/final/jsr311/index.html">JSR311</a></td> </tr>
						<tr><td><b>Implementation: </b></td> <td><a href="https://jersey.dev.java.net/">Jersey Framework</a></td></tr>
						<tr><td><b>Usage: </b></td> <td><a href="https://wiki.hybris.com/x/_8UsAg">hybris implementation</a></td></tr>
						<tr><td><b>API: </b></td><td><a href="https://wiki.hybris.com/x/Vw_9Ag">hybris API</a> </td></tr>
						<tr><td><b>Test client: </b></td><td><a href="http://code.google.com/p/rest-client/">RESTClient</a></td></tr>
					</table>
				
					<br><br>
						
					<h1>Examples</h1>
					Here you can find examples how to use the bundled <b>Jersey Webservice Framework</b> with the hybris Platform. <br> 
					The source code of these examples is part of this extension, you can find it in the src directory of the <b>platformwebservices</b> extension. <br>
					<br><br>
					
					<b>IMPORTANT:</b> 
					<ul>
					
					<li>In order to try our below provided examples you have to download<a href="http://code.google.com/p/rest-client/downloads/list">[here]</a> and run the RESTClient.</li>
					<li>Use admin(default password: nimda) account which has the ability to obtain all of WebServices resources,</li>
					<li>or any other user which should have set the read access right to the requested type|resource.</li>
					<li>The <b>ext-hybris/sampledata</b> must be loaded.</li>
					</ul>
					<br><br>
															
					<table>
						<tr>
							<td><b>How to get resource via RESTClient</b></td>
							<td><b>How to set credentials via RESTClient</b></td>
						</tr>
						<tr>
							<td><img name="RESTClientMethod" src="images/RESTClientMethod.png" /></td>
							<td><img name="RESTClientAuth" src="images/RESTClientAuth.png" /></td>
						</tr>
					</table>
					<br><br>
					
					<h2>Some URL Examples:</h2>
					<br>
					<table class="borders" border="1" align="center">
						<tr><td class="borders"><b>Example 1. User operations</b>
						<table>
							<tr><td>GET all users: </td><td> <i>uri</i>/ws410/rest/users</td></tr>						
							<tr><td>GET demo user: </td><td> <i>uri</i>/ws410/rest/users/<i>demo</i></td></tr>							
						</table></td>
						
						<td class="borders"><b>Example 2. Country operations</b>
						<table>
							<tr><td>GET all countries: </td><td> <i>uri</i>/ws410/rest/countries</td></tr>
							<tr><td>GET Germany: </td><td> <i>uri</i>/ws410/rest/countries/<i>de</i></td></tr>							
						</table></td></tr>	
	
						<tr><td class="borders"><b>Example 3. Region operations</b>
						<table>
							<tr><td>GET all regions: </td><td> <i>uri</i>/ws410/rest/countries/de/regions</td></tr>
							<tr><td>GET Bavaria region: </td><td> <i>uri</i>/ws410/rest/countries/de/regions/DE-BY</td></tr>
						</table></td>

						<td class="borders"><b>Example 4. Language operations</b>
						<table>
							<tr><td>GET all languages: </td><td> <i>uri</i>/ws410/rest/languages</td></tr>
							<tr><td>GET english language: </td><td> <i>uri</i>/ws410/rest/languages/en</td></tr>							
						</table></td></tr>

						<tr><td colspan="2"  class="borders"><b>Example 5. Catalog operations</b>
						<table>
							<tr><td>GET all catalogs: </td><td> <i>uri</i>/ws410/rest/catalogs</td></tr>
							<tr><td>GET all available catalog versions: </td><td> <i>uri</i>/ws410/rest/catalogs/hwcatalog</td></tr>
							<tr><td>GET all root categories: </td><td> <i>uri</i>/ws410/rest/catalogs/hwcatalog/Online</td></tr>
							<tr><td>GET category: </td><td> <i>uri</i>/ws410/rest/catalogs/hwcatalog/Online/categories/HW1000</td></tr>
							<tr><td>GET product: </td><td> <i>uri</i>/ws410/rest/catalogs/hwcatalog/Online/products/HW2300-2356</td></tr>																					
						</table></td></tr>
					</table>
					<br><br><br><br>
				</div>
			</div>
		</div>			
			
	</body>

</html>
