package com.sast.cis.aa.core.migration;

import com.sast.cis.core.migration.service.OrderMigrationBusinessProcessService;
import com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel;
import com.sast.cis.aa.core.model.ProcessResultDetailsModel;
import com.sast.cis.core.billingintegration.dto.OrderExportData;
import com.sast.cis.core.order.service.OrderStatusTransitionService;
import com.sast.cis.core.service.order.export.MigrationOrderExportResponseHandler;
import com.sast.cis.core.util.BusinessProcessUtil;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.processengine.enums.ProcessState;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

@Slf4j
@Component
@RequiredArgsConstructor
@Qualifier("migrationOrderExportResponseHandler")
public class DefaultMigrationOrderExportResponseHandler implements MigrationOrderExportResponseHandler {

    private static final String EVENT_ORDER_CREATED = "order-response-event";

    private final ModelService modelService;
    private final BusinessProcessUtil businessProcessUtil;
    private final OrderMigrationBusinessProcessService orderMigrationBusinessProcessService;
    private final OrderStatusTransitionService orderStatusTransitionService;

    @Transactional
    public void handleOrder(@NonNull final OrderModel order, @NonNull final OrderExportData orderExportData) {
        if (!order.isMigrationOrder()) {
            LOG.error("ALERT: Order with code={} is not a migration order", order.getCode());
            return;
        }

        final String orderCode = order.getCode();
        orderMigrationBusinessProcessService.findByOrderCode(orderCode)
            .ifPresentOrElse(
                bp -> updateAndWakeupBusinessProcess(bp, order, orderExportData),
                () -> {
                    throw new IllegalStateException("No Migration Order business process found for order: %s".formatted(orderCode));
                }
            );
    }

    private void updateAndWakeupBusinessProcess(
        final OrderMigrationBusinessProcessModel orderMigrationBusinessProcess,
        final OrderModel order,
        final OrderExportData orderExportData) {

        saveBrimErrorMessage(orderMigrationBusinessProcess, orderExportData);

        final OrderStatus currentStatus = order.getStatus();
        final OrderStatus newStatus = OrderStatus.valueOf(orderExportData.getOrderExportResult().getName());

        orderStatusTransitionService.transitionStatus(order, newStatus);

        if (!isBusinessProcessInWaitingState(orderMigrationBusinessProcess)) {
            LOG.warn(
                "ALERT: Business process with code={} is not in waiting state. Received BRIM Data={}",
                orderMigrationBusinessProcess.getCode(), orderExportData
            );
            return;
        }

        final String eventChoice = getEventChoice(currentStatus, newStatus);
        businessProcessUtil.triggerBusinessProcessEvent(orderMigrationBusinessProcess.getCode(), EVENT_ORDER_CREATED, eventChoice);
    }

    private boolean isBusinessProcessInWaitingState(OrderMigrationBusinessProcessModel bp) {
        return ProcessState.WAITING.equals(bp.getProcessState());
    }

    private void saveBrimErrorMessage(final OrderMigrationBusinessProcessModel businessProcess, final OrderExportData orderExportData) {
        ProcessResultDetailsModel details = businessProcess.getResultDetails();
        if (details == null) {
            details = modelService.create(ProcessResultDetailsModel.class);
            businessProcess.setResultDetails(details);
        }

        final String errorMessage = String.join(System.lineSeparator(), emptyIfNull(orderExportData.getErrors()));
        details.setBrimErrorMessage(errorMessage);
        modelService.saveAll(details, businessProcess);
    }

    private String getEventChoice(final OrderStatus currentStatus, final OrderStatus newStatus) {
        return orderStatusTransitionService.isSuccessStatusTransition(currentStatus, newStatus) ? "success" : "fail";
    }
}
