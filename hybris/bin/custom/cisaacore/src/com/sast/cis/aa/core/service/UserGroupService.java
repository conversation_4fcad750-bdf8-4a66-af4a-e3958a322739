package com.sast.cis.aa.core.service;

import com.sast.cis.aa.core.data.UmpCustomerGroupData;
import com.sast.cis.aa.core.events.CustomerGroupChangedEvent;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.pricegroup.service.UserPriceGroupService;
import com.sast.cis.core.service.PrincipalCreationService;
import de.hybris.platform.core.model.security.PrincipalGroupModel;
import de.hybris.platform.core.model.user.UserGroupModel;
import de.hybris.platform.europe1.enums.UserPriceGroup;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.event.EventService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.daos.UserGroupDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserGroupService {
    private static final String AA_USER_PRICE_GROUP_PREFIX = BaseStoreEnum.AA.getBaseStoreUid() + "_";
    private final EventService eventService;
    private final ModelService modelService;
    private final UserPriceGroupService userPriceGroupService;
    private final Converter<UmpCustomerGroupData, UserGroupModel> customerGroupDataToUserGroupModelConverter;
    private final PrincipalCreationService<UserGroupModel> principalCreationService;
    private final UserGroupDao userGroupDao;

    public UserGroupModel getOrCreateUserGroup(UmpCustomerGroupData umpCustomerGroupData) {
        UserGroupModel userGroup = userGroupDao.findUserGroupByUid(umpCustomerGroupData.getCustomizationId());
        try {
            if (userGroup == null) {
                userGroup = principalCreationService.createOrFind(customerGroupDataToUserGroupModelConverter.convert(umpCustomerGroupData),
                        UserGroupModel::new);
            }
        } catch (Exception e) {
            LOG.debug("Error creating or retrieving usergroup due to exception {}", e.getMessage(), e);
        }
        return userGroup;
    }

    public Optional<UserGroupModel> getUserGroup(String uid) {
        Objects.requireNonNull(uid);
        UserGroupModel userGroup = userGroupDao.findUserGroupByUid(uid);
        return userGroup == null ? Optional.empty() : Optional.of(userGroup);
    }

    @Transactional
    public UserGroupModel createOrUpdateUserGroup(IoTCompanyModel company, UmpCustomerGroupData customerGroupData) {
        UserGroupModel previousUserGroup = company.getAaCustomerGroup();
        UserGroupModel newUserGroup = getOrCreateUserGroup(customerGroupData);

        if (newUserGroup != null) {
            if (previousUserGroup != null && !previousUserGroup.equals(newUserGroup)) {
                eventService.publishEvent(new CustomerGroupChangedEvent(company, previousUserGroup, newUserGroup));
            }
            newUserGroup.setName(customerGroupData.getGroupName());
            company.setAaCustomerGroup(newUserGroup);
            UserPriceGroup userPriceGroup = userPriceGroupService.getOrCreateUserPriceGroup(
                AA_USER_PRICE_GROUP_PREFIX + newUserGroup.getUid());
            newUserGroup.setUserPriceGroup(userPriceGroup);
            addUserGroup(newUserGroup, company);
            modelService.saveAll(newUserGroup, company, userPriceGroup);
            modelService.refresh(userPriceGroup);
        }
        return newUserGroup;
    }

    private void addUserGroup(UserGroupModel userGroup, IoTCompanyModel company) {
        company.setGroups(Set.of(userGroup));
    }

    @Transactional
    public void removeUserGroup(UserGroupModel userGroup, IoTCompanyModel company) {
        Set<PrincipalGroupModel> existingGroups = company.getGroups();
        if (existingGroups.contains(userGroup)) {
            Set<PrincipalGroupModel> newUserGroups = new HashSet<>(Set.copyOf(existingGroups));
            newUserGroups.remove(userGroup);
            company.setGroups(newUserGroups);
            modelService.save(company);
            UserPriceGroup userPriceGroup = userGroup.getUserPriceGroup();
            if (userPriceGroup != null) {
                if (!userPriceGroupService.deleteUserPriceGroup(userPriceGroup.getCode())) {
                    LOG.error("Error deleting the UserPriceGroup with code {}", userPriceGroup.getCode());
                }
            }
        }
    }
}
