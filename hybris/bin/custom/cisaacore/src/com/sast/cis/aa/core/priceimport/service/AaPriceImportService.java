package com.sast.cis.aa.core.priceimport.service;

import com.sast.cis.aa.core.priceimport.customergroup.AaPricePartitionResult;
import com.sast.cis.aa.core.priceimport.customergroup.AaPricesPartitioner;
import com.sast.cis.aa.core.priceimport.dto.PriceImportDto;
import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.service.FeatureToggleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

import static org.apache.commons.collections4.MapUtils.isNotEmpty;

@Service
@RequiredArgsConstructor
@Slf4j
public class AaPriceImportService {

    private final AaPricesPartitioner aaPricesPartitioner;
    private final CustomerGroupPriceImportDelegate customerGroupPriceImportDelegate;
    private final ListPriceImportDelegate listPriceImportDelegate;
    private final FeatureToggleService featureToggleService;

    public boolean importPrices(final Map<String, Set<PriceImportDto>> priceImportDtos) {
        if (featureToggleService.isEnabledOrDefault(Feature.FEATURE_CREATE_IDW_PRICES_AS_GROUP_PRICES, false)) {
            return importPricesWithoutPartition(priceImportDtos);
        } else {
            return importPricesWithPartition(priceImportDtos);
        }
    }

    /**
     * Import prices with partitioning them into list and group prices.
     * It separates the List prices from the Customer Group prices, and creates the appropriate price drafts for each type.
     *
     * @param priceImportDtos price data grouped by country
     * @return {@code true} if prices have been imported, {@code false} if nothing was imported
     */
    public boolean importPricesWithPartition(final Map<String, Set<PriceImportDto>> priceImportDtos) {
        final AaPricePartitionResult aaPricePartitionResult = aaPricesPartitioner.partitionListAndGroupPrices(priceImportDtos);

        boolean didImport = false;

        final Map<String, Set<PriceImportDto>> customerGroupPrices = aaPricePartitionResult.customerGroupPrices();
        if (isNotEmpty(customerGroupPrices)) {
            LOG.info("there are {} group price entries to import ", customerGroupPrices.values().stream().mapToInt(Set::size).sum());
            customerGroupPriceImportDelegate.importCustomerGroupPrices(customerGroupPrices);
            didImport = true;
        }

        final Map<String, Set<PriceImportDto>> listPrices = aaPricePartitionResult.listPrices();
        if (isNotEmpty(listPrices)) {
            LOG.info("there are {} list price entries to import", listPrices.values().stream().mapToInt(Set::size).sum());
            listPriceImportDelegate.importListPrices(listPrices);
            didImport = true;
        }

        return didImport;
    }

    /**
     * Import prices without partitioning them into list and group prices.
     * It creates price drafts for Customer Group prices only.
     *
     * @param priceImportDtos price data grouped by country
     * @return {@code true} if prices have been imported, {@code false} if nothing was imported
     */
    public boolean importPricesWithoutPartition(final Map<String, Set<PriceImportDto>> priceImportDtos) {
        boolean didImport = false;

        if (isNotEmpty(priceImportDtos)) {
            LOG.info("there are {} group price entries to import ", priceImportDtos.values().stream().mapToInt(Set::size).sum());
            customerGroupPriceImportDelegate.importCustomerGroupPrices(priceImportDtos);
            didImport = true;
        }

        return didImport;
    }
}
