package com.sast.cis.aa.core.priceimport.service;

import com.sast.cis.aa.core.priceimport.dto.PriceImportDto;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.PriceDraftModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.ProductCatalogIdentifierService;
import com.sast.cis.core.util.PriceUtil;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.user.UserGroupModel;
import de.hybris.platform.europe1.enums.UserPriceGroup;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.core.pricegroup.service.BillingPriceCodeService.generateBillingPriceCode;
import static org.apache.commons.collections4.SetUtils.emptyIfNull;

@Service
@Slf4j
@RequiredArgsConstructor
public class AaPriceDraftService {

    private final CommonI18NService commonI18NService;
    private final UserService userService;
    private final ProductCatalogIdentifierService productCatalogIdentifierService;
    private final ModelService modelService;
    private final AppLicenseService appLicenseService;
    private final PriceUtil priceUtil;

    public Set<PriceDraftModel> createGroupPriceDrafts(Map<String, Set<PriceImportDto>> groupPriceImportDtos) {
        return createPriceDrafts(groupPriceImportDtos, this::createGroupPriceDraft);
    }

    public Set<PriceDraftModel> createListPriceDrafts(final Map<String, Set<PriceImportDto>> listPriceImportDtos) {
        return createPriceDrafts(listPriceImportDtos, this::createListPriceDraft);
    }

    private Set<PriceDraftModel> createPriceDrafts(
        final Map<String, Set<PriceImportDto>> priceImportDtos, final Function<PriceImportDto, PriceDraftModel> priceDraftCreator) {

        final Set<PriceDraftModel> priceDrafts = new HashSet<>();
        emptyIfNull(priceImportDtos.entrySet()).forEach(entry -> {
            final String country = entry.getKey();
            final Set<PriceImportDto> countryPriceImportDtos = entry.getValue();
            try {
                Set<PriceDraftModel> countryPriceDrafts = emptyIfNull(countryPriceImportDtos)
                    .stream()
                    .map(priceDraftCreator)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
                priceDrafts.addAll(countryPriceDrafts);
                LOG.info("Created {} price drafts for country({})", countryPriceDrafts.size(), country);
            } catch (final Exception e) {
                LOG.error("Error occurred during price drafts creation for country '{}'. Skipping all prices for country.", country, e);
            }
        });
        return priceDrafts;
    }

    private PriceDraftModel createGroupPriceDraft(PriceImportDto priceImportDto) {
        AppLicenseModel appLicense = validateMaterialNumber(priceImportDto.materialNumber(), priceImportDto.countryIsoCode());
        validateCountryIsoCode(priceImportDto.countryIsoCode(), appLicense);
        CurrencyModel currency = validateCurrencyIsoCode(priceImportDto.currencyIsoCode());
        UserPriceGroup userPriceGroup = validateCustomizationId(priceImportDto.customizationId());
        Date startDate = validateStartDate(priceImportDto.startDate());
        validatePrice(priceImportDto.price());
        return createPriceDraft(currency, startDate, priceImportDto.price(), appLicense, userPriceGroup, priceImportDto.promotion());
    }

    private PriceDraftModel createListPriceDraft(final PriceImportDto priceImportDto) {
        final AppLicenseModel appLicense = validateMaterialNumber(priceImportDto.materialNumber(), priceImportDto.countryIsoCode());
        validateCountryIsoCode(priceImportDto.countryIsoCode(), appLicense);
        final CurrencyModel currency = validateCurrencyIsoCode(priceImportDto.currencyIsoCode());
        final Date startDate = validateStartDate(priceImportDto.startDate());
        validatePrice(priceImportDto.price());
        return createPriceDraft(currency, startDate, priceImportDto.price(), appLicense, priceImportDto.promotion());
    }

    private PriceDraftModel createPriceDraft(CurrencyModel currency, Date startDate, BigDecimal price, AppLicenseModel appLicense,
                                             Boolean promotion) {
        return createPriceDraft(currency, startDate, price, appLicense, null, promotion);
    }

    private PriceDraftModel createPriceDraft(
        CurrencyModel currency, Date startDate, BigDecimal price, AppLicenseModel appLicense, UserPriceGroup userPriceGroup,
        Boolean promotion) {

        PriceDraftModel priceDraft = modelService.create(PriceDraftModel.class);
        priceDraft.setCurrency(currency);
        priceDraft.setAmount(price.doubleValue());
        priceDraft.setMinQuantity(1);
        priceDraft.setValidFrom(startDate);
        priceDraft.setAppLicenseCode(appLicense.getCode());
        priceDraft.setPromotion(Optional.ofNullable(promotion).orElse(false));
        if (userPriceGroup != null) {
            priceDraft.setUserPriceGroup(userPriceGroup);
            priceDraft.setBillingPriceCode(
                generateBillingPriceCode(appLicense.getBrimId(), userPriceGroup.getCode(), currency.getIsocode())
            );
        }

        return priceDraft;
    }

    private AppLicenseModel validateMaterialNumber(String materialNumber, String countryIsoCode) {
        if (ObjectUtils.isEmpty(materialNumber)) {
            LOG.error("PriceImportDto is invalid as MaterialNumber is not provided");
            throw new IllegalArgumentException("MaterialNumber is not provided");
        }

        if (ObjectUtils.isEmpty(countryIsoCode)) {
            LOG.error("PriceImportDto is invalid as CountryIsoCode is not provided");
            throw new IllegalArgumentException("CountryIsoCode is not provided");
        }

        CatalogVersionModel catalogVersion = productCatalogIdentifierService.getBaseStoreCatalogVersion(BaseStoreEnum.AA, STAGED);
        Optional<AppLicenseModel> product = appLicenseService.getAppLicenseForSellerProductIdAndCountry(materialNumber,
            countryIsoCode, catalogVersion.getCatalog().getId());

        if (product.isEmpty()) {
            LOG.error("Cannot create Group Price Drafts as AppLicense cannot be found by materialNumber({})", materialNumber);
            throw new IllegalStateException("AppLicense cannot be found by materialNumber");
        }

        return product.get();
    }

    private CurrencyModel validateCurrencyIsoCode(String currencyIsoCode) {
        if (ObjectUtils.isEmpty(currencyIsoCode)) {
            LOG.error("PriceImportDto is invalid as CurrencyIsoCode is not provided");
            throw new IllegalArgumentException("CurrencyIsoCode is not provided");
        }

        CurrencyModel currency = commonI18NService.getCurrency(currencyIsoCode);
        if (!priceUtil.getActiveCurrencies().contains(currency)) {
            LOG.error("Cannot create Group Price Drafts as the given currency ({}) is not active", currency.getIsocode());
            throw new IllegalStateException("Given currency is not active");
        }

        return currency;
    }

    private Date validateStartDate(Date startDate) {
        if (ObjectUtils.isEmpty(startDate)) {
            LOG.error("PriceImportDto is invalid as StartDate is not provided");
            throw new IllegalArgumentException("StartDate is not provided");
        }

        if (!startDate.after(new Date())) {
            LOG.error("Cannot create Group Price Drafts as start date is not in the future");
            throw new IllegalArgumentException("StartDate is not in the future");
        }

        return startDate;
    }

    private UserPriceGroup validateCustomizationId(String customizationId) {
        if (ObjectUtils.isEmpty(customizationId)) {
            LOG.error("PriceImportDto is invalid as CustomizationId ({}) is not provided", customizationId);
            throw new IllegalArgumentException("CustomizationId is not provided");
        }

        UserGroupModel userGroup = userService.getUserGroupForUID(customizationId);
        UserPriceGroup userPriceGroup = userGroup.getUserPriceGroup();
        if (userPriceGroup == null) {
            LOG.error("Cannot create Group Price Drafts as UserGroup ({}) does not have a user price group", customizationId);
            throw new IllegalStateException("UserPriceGroup does not exist for the given group");
        }

        return userPriceGroup;
    }

    private CountryModel validateCountryIsoCode(String countryIsoCode, AppLicenseModel appLicense) {
        if (ObjectUtils.isEmpty(countryIsoCode)) {
            LOG.error("PriceImportDto is invalid as CountryIsoCode is not provided");
            throw new IllegalArgumentException("CountryIsoCode is not provided");
        }

        CountryModel country = commonI18NService.getCountry(countryIsoCode);
        if (!appLicense.getEnabledCountries().contains(country)) {
            LOG.error("Cannot create Group Price Drafts as AppLicense ({}) does not have the given country ({}) enabled",
                appLicense.getCode(), country.getIsocode());
            throw new IllegalStateException("Given country is not enabled for the AppLicense");
        }

        return country;
    }

    private void validatePrice(BigDecimal price) {
        if (price == null) {
            LOG.error("PriceImportDto is invalid as Price is not provided");
            throw new IllegalArgumentException("Price is not provided");
        }

        if (price.doubleValue() < 0) {
            LOG.error("PriceImportDto is invalid as Price is not valid");
            throw new IllegalArgumentException("Price is not valid");
        }
    }
}
