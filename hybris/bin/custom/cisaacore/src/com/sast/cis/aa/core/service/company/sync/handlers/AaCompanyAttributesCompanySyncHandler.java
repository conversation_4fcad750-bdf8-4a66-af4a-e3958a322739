package com.sast.cis.aa.core.service.company.sync.handlers;

import com.sast.cis.aa.core.service.UserGroupService;
import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.distributor.AaDistributorService;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.company.sync.CompanySyncHandler;
import de.hybris.platform.core.model.user.UserGroupModel;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@RequiredArgsConstructor
@Slf4j
public class AaCompanyAttributesCompanySyncHandler implements CompanySyncHandler {
    private final ModelService modelService;
    private final UserGroupService userGroupService;
    private final AaDistributorService aaDistributorService;
    private final IotCompanyService ioTCompanyService;

    @Override
    public void syncCompanyData(IoTCompanyModel company, UmpCompanyData umpCompany) {
        // externalCustomerId is optional
        // when it's not blank, check if it's already used by another company other than the supplied one.
        if (isNotBlank(umpCompany.getExternalCustomerId()) && ioTCompanyService.isUsedByAnyCompanyOtherThan(
            umpCompany.getExternalCustomerId(), company)) {
            LOG.error("Alert: Cannot Sync uid={} AaExternalCustomerId={}, its already used by another company.",
                umpCompany.getCompanyId(), umpCompany.getExternalCustomerId());
            throw new IllegalStateException("AaExternalCustomerId is already used by another company");
        }
        company.setAaExternalCustomerId(umpCompany.getExternalCustomerId());

        if (umpCompany.getDistributor() != null) {
            company.setDefaultAaDistributor(aaDistributorService.createOrGetDistributor(umpCompany.getDistributor()));
        } else {
            company.setDefaultAaDistributor(null);
        }

        syncAaCustomerGroup(umpCompany, company);
        company.setLicensingEmail(umpCompany.getLicensingEmail());
        ofNullable(umpCompany.getImported()).ifPresent(company::setAaImported);
        company.setCompanyEmail(umpCompany.getCompanyEmail());

        modelService.save(company);
    }

    private void syncAaCustomerGroup(UmpCompanyData umpCompany, IoTCompanyModel company) {
        if (umpCompany.isIsOwnAppsPurchaseEnabled()) {
            LOG.error("Invalid state - Self purchase enabled for AA company (uid={})", umpCompany.getCompanyId());
        } else if (umpCompany.getCustomerGroup() != null) {
            userGroupService.createOrUpdateUserGroup(company, umpCompany.getCustomerGroup());
        } else {
            UserGroupModel aaUsergroup = company.getAaCustomerGroup();
            if (aaUsergroup != null) {
                userGroupService.removeUserGroup(aaUsergroup, company);
                company.setAaCustomerGroup(null);
            }
        }
    }
}
