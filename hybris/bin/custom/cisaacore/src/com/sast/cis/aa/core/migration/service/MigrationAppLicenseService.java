package com.sast.cis.aa.core.migration.service;

import com.sast.cis.core.model.AppLicenseModel;
import de.hybris.platform.core.model.c2l.CountryModel;

import java.util.Optional;

public interface MigrationAppLicenseService {
    boolean isAppLicenseActive(final AppLicenseModel appLicense);

    Optional<AppLicenseModel> findAppLicense(final String materialId, final CountryModel country);

    AppLicenseModel findAppLicenseOrThrow(final String materialId, final CountryModel country);
}
