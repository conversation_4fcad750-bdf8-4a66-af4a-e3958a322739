package com.sast.cis.aa.core.converter;

import com.sast.cis.aa.core.category.data.MaterialDTO;
import com.sast.cis.aa.core.data.MaterialData;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import org.springframework.stereotype.Component;

@Component
public class MaterialDtoPopulator implements Populator<MaterialData, MaterialDTO> {

    @Override
    public void populate(final MaterialData materialData, final MaterialDTO materialDTO) throws ConversionException {
        if (materialData == null) {
            return;
        }
        materialDTO.setCode(materialData.getCode());
        materialDTO.setName(materialData.getName());
        materialDTO.setVideoUrl(materialData.getVideoUrl());
        materialDTO.setDescription(materialData.getDescription());
    }
}
