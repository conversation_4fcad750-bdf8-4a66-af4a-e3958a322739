package com.sast.cis.aa.core.migration;

import com.sast.cis.aa.core.migration.exception.MigrationOrderCreationException;
import com.sast.cis.aa.core.migration.service.IntegratorSelectionService;
import com.sast.cis.aa.core.migration.service.MigrationOrderEntryDraftService;
import com.sast.cis.aa.core.migration.service.MigrationOrderPaymentStatusService;
import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.core.exceptions.BaseSiteNotFoundException;
import com.sast.cis.core.model.AaDistributorCompanyModel;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.ProductCatalogIdentifierService;
import com.sast.cis.core.service.contract.ContractService;
import de.hybris.platform.basecommerce.model.site.BaseSiteModel;
import de.hybris.platform.catalog.model.CatalogModel;
import de.hybris.platform.commerceservices.impersonation.ImpersonationContext;
import de.hybris.platform.commerceservices.impersonation.ImpersonationService;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.product.UnitModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.order.CalculationService;
import de.hybris.platform.order.exceptions.CalculationException;
import de.hybris.platform.order.impl.DefaultOrderService;
import de.hybris.platform.servicelayer.keygenerator.KeyGenerator;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.time.TimeService;
import de.hybris.platform.site.BaseSiteService;
import de.hybris.platform.store.BaseStoreModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.Set;

import static com.sast.cis.core.constants.BaseSiteEnum.AA;
import static java.util.Optional.ofNullable;

@Component
@RequiredArgsConstructor
@Slf4j
public class MigrationOrderCreationService {

    private final DefaultOrderService defaultOrderService;

    private final CalculationService calculationService;

    private final IntegratorSelectionService integratorSelectionService;

    private final MigrationOrderPaymentStatusService migrationOrderPaymentStatusService;

    private final ContractService contractService;

    private final MigrationOrderPromotionService migrationOrderPromotionService;

    private final ImpersonationService impersonationService;

    private final ProductCatalogIdentifierService productCatalogIdentifierService;

    private final BaseSiteService baseSiteService;

    private final KeyGenerator orderCodeGenerator;

    private final ModelService modelService;

    private final TimeService timeService;

    private final MigrationOrderEntryDraftService migrationOrderEntryDraftService;

    public OrderModel createOrderFromDraft(final MigrationOrderDraftModel draft) {
        final String orderCode = String.valueOf(orderCodeGenerator.generate());
        final IoTCompanyModel company = draft.getCompany();
        final AaDistributorCompanyModel aaDistributor = draft.getAaDistributor();
        final UserModel user = integratorSelectionService.selectIntegrator(company);
        final CurrencyModel currency = company.getCountry().getCurrency();
        final BaseStoreModel store = company.getStore();
        final BaseSiteModel baseSite = getBaseSite();
        final Date orderDate = timeService.getCurrentTime();

        final OrderModel order = modelService.create(OrderModel.class);
        order.setMigrationOrder(true);
        order.setCode(orderCode);
        order.setCompany(company);
        order.setAaDistributorCompany(aaDistributor);
        order.setUser(user);
        order.setCurrency(currency);
        order.setStore(store);
        order.setSite(baseSite);
        order.setDate(orderDate);

        if (draft.getManagedAccount() != null) {
            order.setFirstInvoiceNote(draft.getManagedAccount().getAaExternalCustomerId());
        }

        order.setEntries(new ArrayList<>());

        draft.getEntries().forEach(entryDraft -> createOrderEntryFromEntryDraft(order, entryDraft));
        migrationOrderPromotionService.applyPromotions(order);
        calculate(draft, order);

        order.setStatus(OrderStatus.CREATED);
        order.setPaymentStatus(migrationOrderPaymentStatusService.getPaymentStatusForOrder(order));
        draft.setResultingOrder(order);

        contractService.createContractsForOrder(order);

        draft.getEntries()
            .forEach(migrationOrderEntryDraftService::updateBuyerContractEndDateIfEntryHasInactiveProducts);

        modelService.saveAll(order, draft);
        modelService.saveAll(draft.getEntries());
        return order;
    }

    private void calculate(final MigrationOrderDraftModel draft, final OrderModel order) {
        final BaseStoreModel store = order.getStore();
        final CatalogModel productCatalog = productCatalogIdentifierService.findProductCatalog(store.getCatalogs());
        impersonationService.executeInContext(
            new ImpersonationContext().withCatalogVersions(Set.of(productCatalog.getActiveCatalogVersion())),
            () -> {
                try {
                    calculationService.calculate(order);
                } catch (final CalculationException e) {
                    throw MigrationOrderCreationException.forOrderDraft(draft, e);
                }
                return order;
            }
        );
    }

    private void createOrderEntryFromEntryDraft(final OrderModel order, final MigrationOrderEntryDraftModel entryDraft) {
        final AppLicenseModel appLicense = entryDraft.getAppLicense();
        final Integer quantity = entryDraft.getQuantity();
        final UnitModel unit = appLicense.getUnit();

        final OrderEntryModel orderEntry = defaultOrderService.addNewEntry(order, appLicense, quantity, unit);
        entryDraft.setResultingOrderEntry(orderEntry);
    }

    private BaseSiteModel getBaseSite() {
        final String baseSiteUid = AA.getBaseSiteUid();
        return ofNullable(baseSiteService.getBaseSiteForUID(baseSiteUid))
            .orElseThrow(() -> BaseSiteNotFoundException.forBaseSiteId(baseSiteUid));
    }
}
