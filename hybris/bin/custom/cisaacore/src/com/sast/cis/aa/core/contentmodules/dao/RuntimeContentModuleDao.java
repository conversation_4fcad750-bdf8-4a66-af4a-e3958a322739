package com.sast.cis.aa.core.contentmodules.dao;

import com.sast.cis.aa.core.model.ContentModuleModel;
import com.sast.cis.aa.core.model.RuntimeContentModuleModel;
import com.sast.cis.core.model.RuntimeModel;
import com.sast.cis.core.service.DaoQueryService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class RuntimeContentModuleDao {

    private final DaoQueryService daoQueryService;

    public Optional<RuntimeContentModuleModel> findByRuntimeAndContentModule(
        @NonNull final RuntimeModel runtime,
        @NonNull final ContentModuleModel contentModule) {

        final RuntimeContentModuleModel runtimeContentModule = new RuntimeContentModuleModel();
        runtimeContentModule.setRuntime(runtime);
        runtimeContentModule.setContentModule(contentModule);
        return daoQueryService.getSingleModelByExample(runtimeContentModule);
    }
}
