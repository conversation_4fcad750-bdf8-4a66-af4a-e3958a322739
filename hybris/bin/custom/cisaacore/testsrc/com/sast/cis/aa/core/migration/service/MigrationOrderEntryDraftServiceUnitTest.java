package com.sast.cis.aa.core.migration.service;

import com.sast.cis.aa.core.model.MigrationContractGroupModel;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.BuyerContractModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.servicelayer.model.ModelService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Date;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@UnitTest
public class MigrationOrderEntryDraftServiceUnitTest {
    final Date endDate = new Date();
    @Mock
    private MigrationAppLicenseService migrationAppLicenseService;
    @Mock
    private ModelService modelService;
    @InjectMocks
    private MigrationOrderEntryDraftService migrationOrderEntryDraftService;
    @Mock
    private MigrationOrderEntryDraftModel entryDraft;
    @Mock
    private OrderEntryModel orderEntry;
    @Mock
    private AppLicenseModel appLicense;
    @Mock
    private BuyerContractModel buyerContract;
    @Mock
    private MigrationContractGroupModel migrationContractGroup;
    @Mock
    private MigrationContractModel migrationContract;
    @Mock
    private MigrationContractModel anotherMigrationContract;

    @Before
    public void setUp() {
        when(entryDraft.getContractGroups()).thenReturn(Set.of(migrationContractGroup));
        when(migrationContractGroup.getMigrationContracts()).thenReturn(Set.of(migrationContract));
        when(entryDraft.getResultingOrderEntry()).thenReturn(orderEntry);
        when(orderEntry.getProduct()).thenReturn(appLicense);
        when(migrationAppLicenseService.isAppLicenseActive(appLicense)).thenReturn(false);
        when(orderEntry.getBuyerContracts()).thenReturn(Collections.singletonList(buyerContract));

        when(migrationContract.getEndDate()).thenReturn(endDate);
        when(anotherMigrationContract.getEndDate()).thenReturn(endDate);
    }

    @Test
    public void updatesEndDateWhenValidEndDateExistsAndAppLicenseIsInactive() {

        migrationOrderEntryDraftService.updateBuyerContractEndDateIfEntryHasInactiveProducts(entryDraft);

        verify(buyerContract).setEndDate(endDate);
        verify(modelService).saveAll(Collections.singletonList(buyerContract));
    }

    @Test
    public void doesNothingIfAppLicenseIsActive() {
        when(migrationAppLicenseService.isAppLicenseActive(appLicense)).thenReturn(true);

        migrationOrderEntryDraftService.updateBuyerContractEndDateIfEntryHasInactiveProducts(entryDraft);

        verify(modelService, never()).saveAll(anyList());
    }

    @Test
    public void throwsIfNoEndDatesFound() {
        when(entryDraft.getContractGroups()).thenReturn(Collections.emptySet());

        assertThatThrownBy(() -> migrationOrderEntryDraftService.updateBuyerContractEndDateIfEntryHasInactiveProducts(entryDraft))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("No migration contract end date found.");
    }

    @Test
    public void throwsIfMultipleDistinctEndDatesFound() {
        Date endDate2 = new Date(endDate.getTime() + 1000);
        when(anotherMigrationContract.getEndDate()).thenReturn(endDate2);
        when(migrationContractGroup.getMigrationContracts()).thenReturn(Set.of(migrationContract, anotherMigrationContract));

        assertThatThrownBy(() -> migrationOrderEntryDraftService.updateBuyerContractEndDateIfEntryHasInactiveProducts(entryDraft))
            .isInstanceOf(IllegalStateException.class)
            .hasMessageContaining("Multiple distinct end dates found");
    }
}
