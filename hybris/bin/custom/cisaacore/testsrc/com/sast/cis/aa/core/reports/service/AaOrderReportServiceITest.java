package com.sast.cis.aa.core.reports.service;

import com.sast.cis.aa.core.reports.dto.*;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.data.UmpDistributorData;
import com.sast.cis.core.distributor.AaDistributorService;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.enums.StoreEnum;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.test.utils.Country;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import com.sast.cis.test.utils.TestDataConstants;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.user.AddressModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.store.services.BaseStoreService;
import generated.com.sast.cis.core.model.FixedTermContractBuilder;
import generated.com.sast.cis.core.model.SubscriptionContractBuilder;
import generated.de.hybris.platform.core.model.c2l.RegionBuilder;
import generated.de.hybris.platform.core.model.user.AddressBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.sast.cis.core.constants.Currency.EUR;
import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_DEVELOPER_UID;
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_INTEGRATOR_UID;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static de.hybris.platform.core.enums.OrderStatus.COMPLETED;
import static java.time.Month.DECEMBER;
import static java.time.Month.JANUARY;
import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class AaOrderReportServiceITest extends ServicelayerTransactionalTest {

    private static final String TEST_ORDER_CODE_1 = "testOrderCode1";
    private static final String TEST_BILLING_EMAIL = "testBillingEmail";
    private static final String TEST_BUYER_COMPANY = "Austria Buyer 1";
    private static final String TEST_STREET_NAME = "testStreetName";
    private static final String TEST_CITY = "testCity";
    private static final String TEST_POSTALCODE = "80808";
    private static final String TEST_REGION = "testRegion";
    private static final String AUSTRIA_ISOCODE = "AT";
    private static final String TEST_DISTRIBUTOR = "testDistributor";
    private static final String TEST_DISTRIBUTOR_ID = "testDistributorId";
    private static final String TEST_ORDER_DISTRIBUTOR = "testOrderDistributor";
    private static final String TEST_ORDER_DISTRIBUTOR_ID = "testOrderDistributorId";
    private static final String TEST_BPMD_ID = "testBpmdId";
    private static final String TEST_BUYERCOMPANY_UID = "197575f5-ec56-40af-84dc-c07d95899c52";
    private static final String TEST_BUYERCOMPANY_VAT_ID = "testVATId";
    private static final String TEST_AA_CUSTOMER_ID = "testAACustomerId";
    private static final String TEST_APP_CODE = "testAppCode";
    private static final String TEST_SELLER_PRODUCT_ID = "testSellerProductId";
    private static final String TEST_SELLER_PRODUCT_ID_2 = "testSellerProductId2";
    private static final String TEST_APP_NAME = "name_" + TEST_APP_CODE;
    private static final String TEST_SUBSCRIPTION_UUID_1 = "testSubscriptionUuid1";
    private static final String TEST_SUBSCRIPTION_UUID_2 = "testSubscriptionUuid2";
    private static final String TEST_ONETIMEPURCHASE_CODE_1 = "testOneTimePurchaseCode1";
    private static final String TEST_ONETIMEPURCHASE_CODE_2 = "testOneTimePurchaseCode2";
    private static final Date TEST_SUBSCRIPTION_CREATION_TIME = Date.from(LocalDate.of(2023, JANUARY, 1).atStartOfDay().toInstant(UTC));
    private static final Date TEST_SUBSCRIPTION_END_DATE = Date.from(LocalDate.of(2023, DECEMBER, 31).atTime(23, 59, 0).toInstant(UTC));
    private static final Date TEST_SUBSCRIPTION_CANCELLED_DATE = Date.from(LocalDate.of(2023, JANUARY, 31).atStartOfDay().toInstant(UTC));
    private static final String CANCELLATION_REASON = "accidentally ordered";
    @Rule
    public final SessionCatalogRule sessionCatalogRule = SessionCatalogRule.onlineCatalog();
    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();
    @Resource
    private AaReportingService aaOrderReportService;
    @Resource
    private ModelService modelService;
    @Resource
    private DeveloperService developerService;
    @Resource
    private IntegratorService integratorService;
    @Resource
    private BaseStoreService baseStoreService;

    @Resource
    private AaDistributorService aaDistributorService;

    private OrderModel order1;
    private AaDistributorCompanyModel aaDistributor;

    @Before
    public void setup() {
        DeveloperModel developer = developerService.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID);
        AppModel app = sampleDataCreator.createApp(TEST_APP_CODE, "package.aa.app.name", developer,
            TestDataConstants.AA_PRODUCT_CATALOG, CatalogVersion.ONLINE, APPROVED);
        AppLicenseModel appLicenseFull = sampleDataCreator
            .createAppLicense(app.getCode() + "_full", app, ArticleApprovalStatus.APPROVED, LicenseType.FULL);
        AppLicenseModel appLicenseSubscription = sampleDataCreator
            .createAppLicense(app.getCode() + "_subscription", app, ArticleApprovalStatus.APPROVED, LicenseType.SUBSCRIPTION);

        appLicenseFull.setSellerProductId(TEST_SELLER_PRODUCT_ID);
        appLicenseSubscription.setSellerProductId(TEST_SELLER_PRODUCT_ID_2);
        modelService.saveAll(app, appLicenseFull, appLicenseSubscription);
        IntegratorModel integrator = integratorService.getIntegratorByInternalUserId(AA_AUSTRIA1_COMPANY_INTEGRATOR_UID);

        CurrencyModel eurCurrency = sampleDataCreator.getCurrency(EUR);
        CountryModel austria = sampleDataCreator.getCountry(Country.AUSTRIA);
        austria.setCurrency(eurCurrency);

        aaDistributor = aaDistributorService
            .createOrGetDistributor(new UmpDistributorData().withName(TEST_DISTRIBUTOR).withId(TEST_DISTRIBUTOR_ID));
        IoTCompanyModel buyerCompany = integrator.getCompany();
        buyerCompany.setBillingEmail(TEST_BILLING_EMAIL);
        buyerCompany.setBpmdId(TEST_BPMD_ID);
        buyerCompany.setVatID(TEST_BUYERCOMPANY_VAT_ID);
        buyerCompany.setAaExternalCustomerId(TEST_AA_CUSTOMER_ID);
        buyerCompany.setDefaultAaDistributor(aaDistributor);
        AddressModel billingAddress = AddressBuilder.generate()
            .withOwner(buyerCompany)
            .withStreetname(TEST_STREET_NAME)
            .withStreetnumber("0001")
            .withPostalcode(TEST_POSTALCODE)
            .withTown(TEST_CITY)
            .withRegion(
                RegionBuilder.generate().withName(TEST_REGION).withCountry(austria).withIsocode("testRegion").buildIntegrationInstance())
            .withCountry(austria)
            .buildIntegrationInstance();
        buyerCompany.setContactAddress(billingAddress);

        modelService.saveAll(austria, billingAddress, app, buyerCompany);

        order1 = sampleDataCreator.createOrder(integrator, COMPLETED);
        order1.setStore(baseStoreService.getBaseStoreForUid(StoreEnum.AASTORE.getCode()));
        order1.setCode(TEST_ORDER_CODE_1);
        order1.setCompany(buyerCompany);
        order1.setCurrency(eurCurrency);
        modelService.save(order1);

        SubscriptionContractModel subscription1 = SubscriptionContractBuilder.generate()
            .withCode(TEST_SUBSCRIPTION_UUID_1)
            .withCreationtime(TEST_SUBSCRIPTION_CREATION_TIME)
            .withStartDate(TEST_SUBSCRIPTION_CREATION_TIME)
            .withEndDate(TEST_SUBSCRIPTION_END_DATE)
            .withTimezone(UTC.getId())
            .withCancelledDate(TEST_SUBSCRIPTION_CANCELLED_DATE)
            .withCancellationReason(CANCELLATION_REASON)
            .buildIntegrationInstance();

        SubscriptionContractModel subscription2 = SubscriptionContractBuilder.generate()
            .withCode(TEST_SUBSCRIPTION_UUID_2)
            .withCreationtime(TEST_SUBSCRIPTION_CREATION_TIME)
            .withStartDate(TEST_SUBSCRIPTION_CREATION_TIME)
            .withEndDate(TEST_SUBSCRIPTION_END_DATE)
            .withTimezone(UTC.getId())
            .withCancelledDate(null)
            .withCancellationReason(null)
            .buildIntegrationInstance();

        FixedTermContractModel fixedTermContract1 = FixedTermContractBuilder.generate()
            .withCode(TEST_ONETIMEPURCHASE_CODE_1)
            .withBrimId(TEST_ONETIMEPURCHASE_CODE_1)
            .withCreationtime(TEST_SUBSCRIPTION_CREATION_TIME)
            .withStartDate(TEST_SUBSCRIPTION_CREATION_TIME)
            .withEndDate(TEST_SUBSCRIPTION_END_DATE)
            .buildIntegrationInstance();
        FixedTermContractModel fixedTermContract2 = FixedTermContractBuilder.generate()
            .withCode(TEST_ONETIMEPURCHASE_CODE_2)
            .withBrimId(TEST_ONETIMEPURCHASE_CODE_2)
            .withCreationtime(TEST_SUBSCRIPTION_CREATION_TIME)
            .withStartDate(TEST_SUBSCRIPTION_CREATION_TIME)
            .withEndDate(null)
            .buildIntegrationInstance();

        OrderEntryModel orderEntry1 = sampleDataCreator.createOrderEntry(order1, appLicenseFull, 1L);
        orderEntry1.setBasePrice(100d);
        orderEntry1.setBuyerContracts(List.of(fixedTermContract1, fixedTermContract2));
        fixedTermContract1.setOrderEntry(orderEntry1);
        fixedTermContract2.setOrderEntry(orderEntry1);

        OrderEntryModel orderEntry2 = sampleDataCreator.createOrderEntry(order1, appLicenseSubscription, 1L);
        orderEntry2.setBasePrice(10d);
        orderEntry2.setBuyerContracts(List.of(subscription1, subscription2));
        subscription1.setOrderEntry(orderEntry2);
        subscription2.setOrderEntry(orderEntry2);

        modelService.saveAll(subscription1, subscription2, fixedTermContract1, fixedTermContract2, orderEntry1, orderEntry2);
    }

    @Test
    public void testGetAllAaReportObjects_retrievesAll_createsExportableObjectList() {
        List<AaExportReportObject> actualAaReportObjects = aaOrderReportService.getAllAaReportObjects();
        assertThat(actualAaReportObjects).isNotEmpty();
        assertThat(actualAaReportObjects).hasSize(4);
        assertAaContract(actualAaReportObjects);
        assertAaDistributor(actualAaReportObjects.get(0).aaReportDistributor(), aaDistributor);
        assertAaProduct(actualAaReportObjects.get(0).aaProduct());
        assertThat(actualAaReportObjects.stream().map(AaExportReportObject::aaProduct).map(AaProduct::externalProductId)
            .collect(Collectors.toSet())).containsExactlyInAnyOrder(TEST_SELLER_PRODUCT_ID, TEST_SELLER_PRODUCT_ID_2);
        assertSoldToParty(actualAaReportObjects.get(0).soldToParty());
    }

    public void testGetAllAaReportObjects_with_order_time_distributor_retrievesAll_createsExportableObjectList() {
        AaDistributorCompanyModel aaOrderDistributor = aaDistributorService
            .createOrGetDistributor(new UmpDistributorData().withName(TEST_ORDER_DISTRIBUTOR).withId(TEST_ORDER_DISTRIBUTOR_ID));
        List<AaExportReportObject> actualAaReportObjects = aaOrderReportService.getAllAaReportObjects();
        assertThat(actualAaReportObjects).isNotEmpty();
        assertThat(actualAaReportObjects).hasSize(4);
        assertAaContract(actualAaReportObjects);
        assertAaDistributor(actualAaReportObjects.get(0).aaReportDistributor(), aaOrderDistributor);
        assertAaProduct(actualAaReportObjects.get(0).aaProduct());
        assertThat(actualAaReportObjects.stream().map(AaExportReportObject::aaProduct).map(AaProduct::externalProductId)
            .collect(Collectors.toSet())).containsExactlyInAnyOrder(TEST_SELLER_PRODUCT_ID, TEST_SELLER_PRODUCT_ID_2);
        assertSoldToParty(actualAaReportObjects.get(0).soldToParty());
    }

    private void assertAaContract(List<AaExportReportObject> actualAaReportObjects) {
        actualAaReportObjects.forEach(actualAaReport -> assertAaContract(actualAaReport.aaContract()));
    }

    private void assertAaContract(AaContract actualAaContract) {
        assertThat(actualAaContract.contractDate()).isEqualTo(TEST_SUBSCRIPTION_CREATION_TIME);
        assertThat(actualAaContract.startDate()).isEqualTo(TEST_SUBSCRIPTION_CREATION_TIME);

        switch (actualAaContract.contractId()) {
            case TEST_ONETIMEPURCHASE_CODE_1 -> {
                assertThat(actualAaContract.endDate()).isEqualTo(TEST_SUBSCRIPTION_END_DATE);
                assertThat(actualAaContract.cancellationDate()).isNull();
                assertThat(actualAaContract.cancellationReason()).isNull();
            }
            case TEST_ONETIMEPURCHASE_CODE_2 -> {
                assertThat(actualAaContract.contractId()).isEqualTo(TEST_ONETIMEPURCHASE_CODE_2);
                assertThat(actualAaContract.endDate()).isNull();
                assertThat(actualAaContract.cancellationDate()).isNull();
                assertThat(actualAaContract.cancellationReason()).isNull();
            }
            case TEST_SUBSCRIPTION_UUID_1 -> {
                assertThat(actualAaContract.cancellationDate()).isEqualTo(TEST_SUBSCRIPTION_CANCELLED_DATE);
                assertThat(actualAaContract.cancellationReason()).isEqualTo(CANCELLATION_REASON);
                assertThat(actualAaContract.endDate()).isEqualTo(TEST_SUBSCRIPTION_END_DATE);
            }
            case TEST_SUBSCRIPTION_UUID_2 -> {
                assertThat(actualAaContract.cancellationDate()).isNull();
                assertThat(actualAaContract.cancellationReason()).isNull();
                assertThat(actualAaContract.endDate()).isEqualTo(TEST_SUBSCRIPTION_END_DATE);
            }
        }
    }

    private void assertAaDistributor(AaReportDistributor aaReportDistributor, AaDistributorCompanyModel expectedDistributor) {
        assertThat(aaReportDistributor.companyName()).isEqualTo(expectedDistributor.getCompanyName());
        assertThat(aaReportDistributor.id()).isEqualTo(expectedDistributor.getUmpId());
    }

    private void assertAaProduct(AaProduct aaProduct) {
        assertThat(aaProduct.productCode()).isEqualTo(TEST_APP_CODE);
        assertThat(aaProduct.productName()).isEqualTo(TEST_APP_NAME);
    }

    private void assertSoldToParty(SoldToParty soldToParty) {
        assertThat(soldToParty.bpmdId()).isEqualTo(TEST_BPMD_ID);
        assertThat(soldToParty.companyName()).isEqualTo(TEST_BUYER_COMPANY);
        assertThat(soldToParty.kp1Id()).isEqualTo(TEST_AA_CUSTOMER_ID);
        assertThat(soldToParty.vatId()).isEqualTo(TEST_BUYERCOMPANY_VAT_ID);
        assertThat(soldToParty.marketplaceId()).isEqualTo(TEST_BUYERCOMPANY_UID);
        assertAaAddress(soldToParty.aaAddress());
    }

    private void assertAaAddress(AaAddress aaAddress) {
        assertThat(aaAddress.street()).isEqualTo(TEST_STREET_NAME);
        assertThat(aaAddress.city()).isEqualTo(TEST_CITY);
        assertThat(aaAddress.postalCode()).isEqualTo(TEST_POSTALCODE);
        assertThat(aaAddress.region()).isEqualTo(TEST_REGION);
        assertThat(aaAddress.countryIsoCode()).isEqualTo(AUSTRIA_ISOCODE);
    }
}
