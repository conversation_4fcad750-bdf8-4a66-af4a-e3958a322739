package com.sast.cis.aa.core.service.handlers

import com.sast.cis.aa.core.data.UmpCustomerGroupData
import com.sast.cis.aa.core.service.UserGroupService
import com.sast.cis.aa.core.service.company.sync.handlers.AaCompanyAttributesCompanySyncHandler
import com.sast.cis.core.data.UmpCompanyData
import com.sast.cis.core.data.UmpDistributorData
import com.sast.cis.core.distributor.AaDistributorService
import com.sast.cis.core.model.AaDistributorCompanyModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.company.IotCompanyService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test
import spock.lang.Unroll

@UnitTest
class AaCompanyAttributesCompanySyncHandlerUnitSpec extends JUnitPlatformSpecification {
    private static final String DISTRIBUTOR_NAME = 'Some Distributor Inc.'
    private static final String DISTRIBUTOR_ID = 'abc123'
    private static final String AA_CUSTOMER_ID = 'KP1-1234'
    private static final String AA_CUSTOMER_GROUP = 'KEY_ACCOUNT'
    private static final String AA_CUSTOMIZATION_ID = 'KAXXX'

    private ModelService modelService = Mock()
    private UserGroupService userGroupService = Mock()
    private AaDistributorService aaDistributorService = Mock()
    private IotCompanyService iotCompanyService = Mock()

    private IoTCompanyModel mockCompany = Mock()
    private AaDistributorCompanyModel mockDistributor = Mock()
    private UserGroupModel mockUserGroup = Mock()

    private AaCompanyAttributesCompanySyncHandler aaCompanyAttributesCompanySyncHandler

    def setup() {
        aaCompanyAttributesCompanySyncHandler = new AaCompanyAttributesCompanySyncHandler(modelService, userGroupService, aaDistributorService, iotCompanyService)
        iotCompanyService.isUsedByAnyCompanyOtherThan(_ as String, _ as IoTCompanyModel) >> false
    }

    @Test
    def 'if given ump data has distributor and stored company has none, set it on company'() {
        given:
        def givenUmpData = new UmpCompanyData()
        def givenDistributorData = new UmpDistributorData()
        givenDistributorData.setName(DISTRIBUTOR_NAME)
        givenDistributorData.setId(DISTRIBUTOR_ID)
        givenUmpData.setExternalCustomerId(AA_CUSTOMER_ID)
        givenUmpData.setDistributor(givenDistributorData)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        1 * aaDistributorService.createOrGetDistributor(givenDistributorData) >> mockDistributor
        1 * mockCompany.setDefaultAaDistributor(mockDistributor)
        1 * mockCompany.setAaExternalCustomerId(AA_CUSTOMER_ID)
        1 * modelService.save(mockCompany)
    }

    @Test
    def 'if given ump data has distributor and stored company has one, update it on company'() {
        given:
        def givenUmpData = new UmpCompanyData()
        def givenDistributorData = new UmpDistributorData()
        givenDistributorData.setName(DISTRIBUTOR_NAME)
        givenDistributorData.setId(DISTRIBUTOR_ID)
        givenUmpData.setExternalCustomerId(AA_CUSTOMER_ID)
        givenUmpData.setDistributor(givenDistributorData)
        mockCompany.getAaDistributor() >> mockDistributor

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        1 * aaDistributorService.createOrGetDistributor(givenDistributorData) >> mockDistributor
        1 * mockCompany.setDefaultAaDistributor(mockDistributor)
        1 * mockCompany.setAaExternalCustomerId(AA_CUSTOMER_ID)
        1 * modelService.save(mockCompany)
    }

    @Test
    def 'if given ump data has no distributor, dont set a distributor'() {
        given:
        def givenUmpData = new UmpCompanyData()
        givenUmpData.setExternalCustomerId(AA_CUSTOMER_ID)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        0 * aaDistributorService._
        1 * mockCompany.setDefaultAaDistributor(null)
        1 * mockCompany.setAaExternalCustomerId(AA_CUSTOMER_ID)
        1 * modelService.save(mockCompany)
    }

    @Test
    def 'if given ump data has no distributor but stored company has, distributor is removed'() {
        given:
        def givenUmpData = new UmpCompanyData()
        givenUmpData.setExternalCustomerId(AA_CUSTOMER_ID)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        0 * aaDistributorService._
        1 * mockCompany.setDefaultAaDistributor(null)
        1 * mockCompany.setAaExternalCustomerId(AA_CUSTOMER_ID)
        1 * modelService.save(mockCompany)
    }

    @Test
    def 'if given ump data has enabled self purchase flag, company will not set or update customer group'() {
        given:
        def givenUmpData = new UmpCompanyData().withIsOwnAppsPurchaseEnabled(true)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        0 * userGroupService.createOrUpdateUserGroup(mockCompany, _ as UmpCustomerGroupData)
        0 * mockCompany.getAaCustomerGroup()
    }

    @Test
    def 'if given ump data has disabled self purchase flag and no customer group exists for the company, customer group is not set or removed'() {
        given:
        def givenUmpData = new UmpCompanyData().withIsOwnAppsPurchaseEnabled(false)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        0 * userGroupService.createOrUpdateUserGroup(mockCompany, _ as UmpCustomerGroupData)
        1 * mockCompany.getAaCustomerGroup()
        0 * userGroupService.removeUserGroup(_ as UserGroupModel, mockCompany)
    }

    @Test
    def 'if given ump data has disabled self purchase flag and no customer group set then existing customer group of company is removed'() {
        given:
        def givenUmpData = new UmpCompanyData().withIsOwnAppsPurchaseEnabled(false)
        mockCompany.getAaCustomerGroup() >> mockUserGroup

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        0 * userGroupService.createOrUpdateUserGroup(mockCompany, _ as UmpCustomerGroupData)
        1 * userGroupService.removeUserGroup(_ as UserGroupModel, mockCompany)
        1 * mockCompany.setAaCustomerGroup(null)
        (1.._) * modelService.save(mockCompany)
    }

    @Test
    def 'if given ump data has customer group and stored company has none, set it on company'() {
        given:
        def givenUmpData = new UmpCompanyData().withIsOwnAppsPurchaseEnabled(false)
        def givenCustomerGroupData = new UmpCustomerGroupData()
        givenCustomerGroupData.setCustomizationId(AA_CUSTOMIZATION_ID)
        givenCustomerGroupData.setGroupName(AA_CUSTOMER_GROUP)
        givenUmpData.setCustomerGroup(givenCustomerGroupData)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        1 * userGroupService.createOrUpdateUserGroup(mockCompany, givenCustomerGroupData)
        0 * userGroupService.removeUserGroup(_ as UserGroupModel, mockCompany)
    }

    @Test
    @Unroll
    def 'licensing email value #givenLicensingEmail is set to company'() {
        given:
        def givenUmpData = new UmpCompanyData().withLicensingEmail(givenLicensingEmail)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        1 * mockCompany.setLicensingEmail(givenLicensingEmail)
        1 * modelService.save(mockCompany)

        where:
        givenLicensingEmail << ['<EMAIL>', null]
    }

    @Test
    @Unroll
    def 'imported field value #givenImportedValue is set to company aaImported field'() {
        given:
        def givenUmpData = new UmpCompanyData().withImported(givenImportedValue)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        1 * mockCompany.setAaImported(givenImportedValue)
        1 * modelService.save(mockCompany)

        where:
        givenImportedValue << [true, false]
    }

    @Test
    def 'given imported field value is null when sync then skip'() {
        given:
        def givenUmpData = new UmpCompanyData().withImported(null)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        0 * mockCompany.setAaImported(_)
    }

    @Test
    def 'given ExternalCustomerId is used by another company when sync then skip'() {
        given:
        def givenUmpData = new UmpCompanyData().withExternalCustomerId("alreadyUsed")


        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        thrown(IllegalStateException)
        0 * mockCompany.setAaExternalCustomerId(_)
        iotCompanyService.isUsedByAnyCompanyOtherThan(_, mockCompany) >> true
    }

    @Test
    @Unroll
    def 'company email value #givenCompanyEmail is set to company'() {
        given:
        def givenUmpData = new UmpCompanyData().withCompanyEmail(givenCompanyEmail)

        when:
        aaCompanyAttributesCompanySyncHandler.syncCompanyData(mockCompany, givenUmpData)

        then:
        1 * mockCompany.setCompanyEmail(givenCompanyEmail)
        1 * modelService.save(mockCompany)

        where:
        givenCompanyEmail << ['<EMAIL>', null]
    }

}
