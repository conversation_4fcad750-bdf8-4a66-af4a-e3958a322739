package com.sast.cis.aa.core.specialoffer.service

import com.sast.cis.aa.core.specialoffer.dao.PriceListUrlDao
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class PriceListUrlServiceUnitSpec extends JUnitPlatformSpecification {

    final static ISOCODE = 'FI'

    def priceListUrlDao = Mock(PriceListUrlDao)
    def commonI18NService = Mock(CommonI18NService)
    def county = Mock(CountryModel)

    def priceListUrlService = new PriceListUrlService(commonI18NService, priceListUrlDao)

    void setup() {
        commonI18NService.getCountry(ISOCODE) >> county
    }

    def 'a country model gets passed to the dao'() {
        when:
        priceListUrlService.findPriceListUrl(county)

        then:
        1 * priceListUrlDao.findByCountry(county)
    }

    def 'a null country model raises an exception'() {
        when:
        priceListUrlService.findPriceListUrl(null as CountryModel)

        then:
        thrown(IllegalArgumentException)
    }

    def 'a known isocode gets mapped to a country model'() {
        when:
        priceListUrlService.findPriceListUrl(ISOCODE)

        then:
        1 * priceListUrlDao.findByCountry(county)
    }

    def 'a thrown exception gets rethrown'() {
        when:
        priceListUrlService.findPriceListUrl(ISOCODE)

        then:
        commonI18NService.getCountry(ISOCODE) >> { throw new IllegalArgumentException() }
        thrown(IllegalArgumentException)
    }

    def 'a null isocode raises an exception'() {
        when:
        priceListUrlService.findPriceListUrl(null as String)

        then:
        thrown(IllegalArgumentException)
    }
}
