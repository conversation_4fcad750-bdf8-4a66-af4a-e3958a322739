package com.sast.cis.aa.core.contentmodules.dao;

import com.sast.cis.aa.core.model.ContentModuleModel;
import com.sast.cis.aa.core.model.RuntimeContentModuleModel;
import com.sast.cis.core.model.RuntimeModel;
import com.sast.cis.core.runtime.dao.RuntimeDao;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class RuntimeContentModuleDaoITest extends ServicelayerTransactionalTest {

    @Resource
    private RuntimeDao runtimeDao;

    @Resource
    private ContentModuleDao contentModuleDao;

    @Resource
    private RuntimeContentModuleDao runtimeContentModuleDao;

    @Test
    public void givenRuntimeAndContentModule_whenFindBy_thenRetrieveRuntimeContentModule() {
        final RuntimeModel runtime = runtimeDao.findByCode("runtime_subs_unlimited").orElseThrow();
        final ContentModuleModel contentModule = contentModuleDao.findByCode("CM_040A").orElseThrow();

        final Optional<RuntimeContentModuleModel> result = runtimeContentModuleDao.findByRuntimeAndContentModule(runtime, contentModule);

        assertThat(result).isPresent();
        assertThat(result.get().getRuntime()).isEqualTo(runtime);
        assertThat(result.get().getContentModule()).isEqualTo(contentModule);
        assertThat(result.get().getContentModuleIds()).containsExactly("1987P12410999");
    }

    @Test
    public void givenNoRuntimeAndContentModuleMapping_whenFindBy_thenReturnEmpty() {
        final RuntimeModel runtime = runtimeDao.findByCode("runtime_full_unlimited").orElseThrow();
        final ContentModuleModel contentModule = contentModuleDao.findByCode("CM_040A").orElseThrow();

        final Optional<RuntimeContentModuleModel> result = runtimeContentModuleDao.findByRuntimeAndContentModule(runtime, contentModule);

        assertThat(result).isNotPresent();
    }
}
