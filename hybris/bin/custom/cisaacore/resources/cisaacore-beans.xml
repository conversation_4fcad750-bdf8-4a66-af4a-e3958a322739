<?xml version="1.0" encoding="ISO-8859-1"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="beans.xsd">
    <bean class="com.sast.cis.aa.core.data.MaterialData">
        <property name="code" type="String" equals="true"/>
        <property name="name" type="String" equals="true"/>
        <property name="description" type="String" equals="true"/>
        <property name="videoUrl" type="String" equals="true"/>
    </bean>

    <bean class="com.sast.cis.aa.core.data.BoltonData">
        <property name="code" type="String" equals="true"/>
        <property name="name" type="String" equals="true"/>
        <property name="description" type="String" equals="true"/>
        <property name="boltonLicenseData" type="java.util.List&lt;com.sast.cis.core.data.ProductLicenseData&gt;"/>
        <property name="parentApps" type="java.util.List&lt;String&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.DetailProductData">
        <property name="billOfMaterials" type="java.util.List&lt;com.sast.cis.aa.core.data.MaterialData&gt;"/>
        <property name="boltons" type="java.util.List&lt;com.sast.cis.aa.core.data.BoltonData&gt;"/>
        <property name="bundles" type="java.util.List&lt;com.sast.cis.core.data.DetailProductData&gt;"/>
        <property name="sellerProductId" type="String" equals="true"/>
        <property name="specialOffer" type="boolean"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.product.data.ProductData">
        <property name="billOfMaterials" type="java.util.List&lt;com.sast.cis.aa.core.data.MaterialData&gt;"/>
        <property name="boltons" type="java.util.List&lt;com.sast.cis.aa.core.data.BoltonData&gt;"/>
        <property name="bundles" type="java.util.List&lt;de.hybris.platform.commercefacades.product.data.ProductData&gt;"/>
        <property name="sellerProductId" type="String"/>
        <property name="specialOffer" type="boolean"/>
        <property name="order" type="int"/>
        <property name="productNote" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.data.UmpCompanyData">
        <property name="customerGroup" type="com.sast.cis.aa.core.data.UmpCustomerGroupData"/>
        <property name="communicationLanguage" type="String"/>
        <property name="operationalStage" type="String"/>
        <property name="imported" type="Boolean">
            <description>
                Indicates if the company is imported. Companies created as part of the AA Migration process are considered imported.
            </description>
        </property>
        <property name="companyEmail" type="String"/>
    </bean>

    <bean class="com.sast.cis.aa.core.data.UmpCustomerGroupData">
        <property name="groupName" type="String"/>
        <property name="customizationId" type="String"/>
    </bean>

    <bean class="com.sast.cis.aa.core.category.data.MaterialDTO">
        <description>Representation of a Material</description>
        <hints>
            <hint name="restResourceRelated"/>
            <hint name="alias">Material</hint>
        </hints>
        <property name="code" type="String"/>
        <property name="name" type="String"/>
        <property name="description" type="String"/>
        <property name="videoUrl" type="String"/>
    </bean>

    <bean class="com.sast.cis.core.category.data.ProductDTO">
        <property name="billsOfMaterials"
                  type="java.util.List&lt;com.sast.cis.aa.core.category.data.MaterialDTO&gt;">
            <description>List of Materials in Product</description>
        </property>
        <property name="specialOffer" type="boolean"/>
        <property name="order" type="int"/>
        <property name="productNote" type="String"/>
    </bean>
    <bean class="com.sast.cis.core.data.OrderSummaryData">
        <property name="listOfDistributors"
                  type="java.util.List&lt;com.sast.cis.core.data.UmpDistributorData&gt;">
            <description>List of AA distributors available in the country</description>
        </property>
    </bean>
    <bean class="de.hybris.platform.commercefacades.order.data.OrderEntryData">
        <property name="specialOffer" type="boolean"/>
    </bean>
</beans>
