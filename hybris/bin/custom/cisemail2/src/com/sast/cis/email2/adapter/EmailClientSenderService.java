package com.sast.cis.email2.adapter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sast.cis.email2.service.Attachment;
import com.sast.email.client.EmailServiceAttachmentClient;
import com.sast.email.client.EmailServiceClient;
import com.sast.email.client.dto.EmailDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.URI;
import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class EmailClientSenderService implements EmailSenderService {
    private final EmailServiceClient emailServiceClient;
    private final EmailServiceAttachmentClient attachmentClient;


    @Override public void send(EmailDto emailDto) {
        try {
            emailServiceClient.send(emailDto);
        } catch (JsonProcessingException e) {
            LOG.error("ALERT Error occurred while sending email:", e);
        }
    }

    @Override public URI uploadAttachment(InputStream input, String filename) {
        return attachmentClient.upload(input, filename);
    }
}
