package com.sast.cis.email2.typehandler.order;

import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.model.SepaMandatePaymentInfoModel;
import com.sast.cis.core.paymentintegration.util.PaymentMethodTypeResolver;
import com.sast.cis.email2.dto.aa.AaEmailPaymentData;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import org.apache.commons.lang3.StringUtils;

public class EmailPaymentDataFactory {
    private EmailPaymentDataFactory() {}

    public static AaEmailPaymentData forPaymentInfo(final PaymentInfoModel paymentInfo) {
        PaymentMethodType paymentMethod = PaymentMethodTypeResolver.forPaymentInfo(paymentInfo)
                .orElseThrow(() -> new IllegalArgumentException(
                        "Cannot determine payment method for payment info: %s".formatted(paymentInfo)));

        String identifier = null;

        if (paymentInfo instanceof SepaMandatePaymentInfoModel sepaMandatePaymentInfo) {
            identifier = "····" + StringUtils.right(sepaMandatePaymentInfo.getIBAN(), 4);
        }

        return AaEmailPaymentData.builder()
                .method(paymentMethod)
                .identifier(identifier)
                .build();
    }
}
