package com.sast.cis.email2.paymentreminder.handler;

import com.sast.cis.core.config.WebsiteUrlProvider;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.email2.constants.EmailType;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import org.springframework.stereotype.Component;

@Component
public class SecondPaymentReminderHandler extends AbstractPaymentReminderHandler {

    private static final String REMINDER_KEY = "paymentreminder.second.offsetindays";

    public SecondPaymentReminderHandler(ConfigurationService configurationService,
                                        FeatureToggleService featureToggleService,
                                        WebsiteUrlProvider websiteUrlProvider) {
        super(configurationService, featureToggleService, websiteUrlProvider);
    }

    @Override
    public EmailType getType() {
        return EmailType.SECOND_PAYMENT_REMINDER;
    }

    @Override
    public int getOffsetInDays() {
        return configurationService.getConfiguration().getInt(REMINDER_KEY);
    }
}
