package com.sast.cis.email2.paymentreminder.handler

import com.sast.cis.core.config.WebsiteUrlProvider
import com.sast.cis.core.constants.shop.ShopConfig
import com.sast.cis.core.enums.Feature
import com.sast.cis.core.enums.StoreEnum
import com.sast.cis.core.model.InvoiceModel
import com.sast.cis.core.service.FeatureToggleService
import com.sast.cis.email2.constants.EmailType
import com.sast.email.client.dto.EmailDto
import com.sast.email.client.dto.TenantDto
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.InvoiceBuilder
import generated.com.sast.cis.core.model.IoTCompanyBuilder
import generated.de.hybris.platform.core.model.c2l.LanguageBuilder
import generated.de.hybris.platform.core.model.order.OrderBuilder
import generated.de.hybris.platform.core.model.user.UserBuilder
import generated.de.hybris.platform.store.BaseStoreBuilder
import org.apache.commons.configuration.Configuration
import org.apache.commons.lang3.time.DateUtils

abstract class AbstractPaymentReminderHandlerUnitSpec extends JUnitPlatformSpecification {
    final static ORDER_CODE = "1234"
    final static USER_NAME = "Customer Name"
    final static ORDER_DETAIL_URL = "https://store.azena.com/shop/my-account/orders/1234"
    final static ORDER_DATE = "07 mars 2024"
    final static INVOICE_DATE = "07 mars 2024"
    final static DUE_DATE = "10 mars 2024"
    final static SUPPORT_URL = "https://support.azena.com"
    final static BASE_STORE_UID = StoreEnum.IOTSTORE.getCode()
    final static AZENA_TEMPLATE_PREFIX = 'store-azena/'
    final static LANGUAGE = 'fr'

    ConfigurationService configurationService = Mock()
    FeatureToggleService featureToggleService = Mock()
    WebsiteUrlProvider websiteUrlProvider = Mock()
    Configuration mockConfig = Mock()

    InvoiceModel invoice

    abstract AbstractPaymentReminderHandler handler()
    abstract EmailType expectedType()

    def setup() {
        mockConfig.getString(ShopConfig.BASE_STORE_URL_PATTERN.getValue().formatted("iotstore")) >> 'https://store.azena.com/shop'
        mockConfig.getString(ShopConfig.HELP_CENTER.getValue()) >> 'https://support.azena.com'
        mockConfig.getInt(ShopConfig.TERM_OF_PAYMENT_IN_DAYS.getValue()) >> 3
        configurationService.getConfiguration() >> mockConfig
        featureToggleService.isEnabled(Feature.FEATURE_EMAIL_CLIENT_ENABLED) >> true

        def invoiceOrder = OrderBuilder.generate()
                .withCode(ORDER_CODE)
                .withDate(DateUtils.parseDate(ORDER_DATE, Locale.forLanguageTag(LANGUAGE),"dd MMMM yyyy"))
                .withUser(UserBuilder.generate()
                        .withName(USER_NAME)
                        .buildMockInstance())
                .withCompany(IoTCompanyBuilder.generate()
                        .withCommunicationLanguage(LanguageBuilder.generate()
                                .withIsocode(LANGUAGE)
                                .buildMockInstance())
                        .buildMockInstance())
                .withStore(BaseStoreBuilder.generate()
                        .withUid(BASE_STORE_UID)
                        .buildMockInstance())
                .buildMockInstance()

        invoice = InvoiceBuilder.generate()
                .withOrder(Set.of(invoiceOrder))
                .withInvoiceDate(DateUtils.parseDate(INVOICE_DATE, Locale.forLanguageTag(LANGUAGE), "dd MMMM yyyy"))
                .buildMockInstance()
        websiteUrlProvider.getOrderDetails(invoiceOrder) >> new URI(ORDER_DETAIL_URL)
    }

    def 'getType should return expected type'() {
        when:
        def emailType = handler().getType()

        then:
        emailType == expectedType()
    }

    def 'getEmailData should return expected data'() {
        when:
        def emailData = handler().getEmailDto(invoice)
        def properties = emailData.getProperties()

        then:
        emailData instanceof EmailDto
        verifyAll(emailData) {
            tenant == TenantDto.AZENA
            templateName == AZENA_TEMPLATE_PREFIX + expectedType().getTemplate()
            locale == LANGUAGE
            properties == [
                    "orderCode"     : ORDER_CODE,
                    "name"          : USER_NAME,
                    "orderDetailUrl": ORDER_DETAIL_URL,
                    "orderDate"     : ORDER_DATE,
                    "dueDate"       : DUE_DATE,
                    "supportUrl": SUPPORT_URL
            ]
        }
    }
}
