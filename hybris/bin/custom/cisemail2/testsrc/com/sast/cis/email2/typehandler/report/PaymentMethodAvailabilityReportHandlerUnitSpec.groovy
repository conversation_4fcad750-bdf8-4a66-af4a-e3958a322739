package com.sast.cis.email2.typehandler.report


import com.sast.cis.core.service.FeatureToggleService
import com.sast.cis.email2.dto.aa.PaymentMethodAvailabilityReportData
import com.sast.email.client.dto.EmailDto
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.apache.commons.configuration.Configuration
import org.junit.Test

import static com.sast.cis.core.enums.Feature.FEATURE_EMAIL_CLIENT_ENABLED
import static com.sast.cis.email2.constants.Cisemail2Constants.AA_EMAIL_SENDER_PROPERTY
import static com.sast.cis.email2.constants.EmailType.PAYMENT_METHOD_AVAILABILITY_REPORT
import static com.sast.email.client.dto.TenantDto.BAAM

@UnitTest
class PaymentMethodAvailabilityReportHandlerUnitSpec extends JUnitPlatformSpecification {
    private static final String RECIPIENT = "<EMAIL>"
    private static final String CC = "<EMAIL>"
    private static final String FROM = 'Bosch Automotive Aftermarket<<EMAIL>>'
    private static final String REPORT_CREATION_DATE = '2024-04-26T11:00:00'

    private ConfigurationService configurationService = Mock()
    private FeatureToggleService featureToggleService = Mock()
    private Configuration mockConfig = Mock()

    private PaymentMethodAvailabilityReportHandler handler
    private PaymentMethodAvailabilityReportData paymentMethodAvailabilityReport

    void setup() {
        mockConfig.getString(AA_EMAIL_SENDER_PROPERTY) >> FROM
        configurationService.getConfiguration() >> mockConfig
        featureToggleService.isEnabled(FEATURE_EMAIL_CLIENT_ENABLED) >> true
        handler = new PaymentMethodAvailabilityReportHandler(configurationService, featureToggleService, RECIPIENT, CC)
        paymentMethodAvailabilityReport = new PaymentMethodAvailabilityReportData(REPORT_CREATION_DATE)
    }

    @Test
    void 'getType should return expected type'() {
        when:
        def emailType = handler.getType()

        then:
        emailType == PAYMENT_METHOD_AVAILABILITY_REPORT
    }

    @Test
    void 'getEmailData should return expected data'() {
        when:
        def emailData = handler.getEmailDto(paymentMethodAvailabilityReport)
        def properties = emailData.getProperties()

        then:
        emailData instanceof EmailDto
        with(emailData) {
            verifyAll {
                tenant == BAAM
                from == FROM
                cc == [CC]
                to == [RECIPIENT]
                templateName == 'store-aa/' + PAYMENT_METHOD_AVAILABILITY_REPORT.getTemplate()
                properties == [
                        "reportCreationDate": REPORT_CREATION_DATE
                ]
            }
        }
    }
}
