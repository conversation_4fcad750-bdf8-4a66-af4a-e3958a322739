package com.sast.cis.email2.service.handlers

import com.sast.cis.core.enums.Feature
import com.sast.cis.core.enums.StoreEnum
import com.sast.cis.core.service.FeatureToggleService
import com.sast.cis.email2.constants.EmailType
import com.sast.cis.email2.dto.FailedPayment
import com.sast.cis.email2.typehandler.payment.FailedPaymentEmailHandler
import com.sast.email.client.dto.EmailDto
import com.sast.email.client.dto.TenantDto
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.apache.commons.configuration.Configuration
import org.junit.Test

@UnitTest
class FailedPaymentEmailHandlerUnitSpec extends JUnitPlatformSpecification {

    private static final String ORDER_NUMBER = "order_number_123"
    private static final String CUSTOMER_NAME = "Customer Name"
    private static final String DUE_DATE = "2023.03.24"
    private static final String RECIPIENT = "<EMAIL>"
    private static final String URL = "https://link.to.cc.update"
    private static final String BASE_STORE_UID = StoreEnum.IOTSTORE.getCode()
    private static final String AZENA_TEMPLATE_PREFIX = 'store-azena/'
    private static final String LANGUAGE = 'pt'

    ConfigurationService configurationService = Mock()
    FeatureToggleService featureToggleService = Mock()
    Configuration mockConfig = Mock()

    private FailedPaymentEmailHandler handler
    private FailedPayment failedPayment

    void setup() {
        configurationService.getConfiguration() >> mockConfig
        featureToggleService.isEnabled(Feature.FEATURE_EMAIL_CLIENT_ENABLED) >> true
        handler = new FailedPaymentEmailHandler(configurationService, featureToggleService)
        failedPayment = new FailedPayment(
                CUSTOMER_NAME,
                RECIPIENT,
                ORDER_NUMBER,
                DUE_DATE,
                URL,
                BASE_STORE_UID,
                LANGUAGE
        )
    }

    @Test
    void 'getType should return expected type'() {
        when:
        def emailType = handler.getType()

        then:
        emailType == EmailType.FAILED_PAYMENT
    }

    @Test
    void 'getEmailData should return expected data'() {
        when:
        def emailData = handler.getEmailDto(failedPayment)
        def properties = emailData.getProperties()

        then:
        emailData instanceof EmailDto
        verifyAll (emailData) {
            tenant == TenantDto.AZENA
            templateName == AZENA_TEMPLATE_PREFIX + EmailType.FAILED_PAYMENT.getTemplate()
            properties == [
                    "customerName": CUSTOMER_NAME,
                    "orderNumber": ORDER_NUMBER,
                    "dueDate"    : DUE_DATE,
                    "url"        : URL
            ]
            locale == LANGUAGE
        }
    }
}
