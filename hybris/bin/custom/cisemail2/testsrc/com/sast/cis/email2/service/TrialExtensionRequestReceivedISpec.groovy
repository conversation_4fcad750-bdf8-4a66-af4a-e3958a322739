package com.sast.cis.email2.service

import com.amazonaws.services.sns.AmazonSNS
import com.amazonaws.services.sqs.AmazonSQS
import com.sast.cis.core.enums.Feature
import com.sast.cis.core.model.DeveloperModel
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.service.ObjectMapperService
import com.sast.cis.core.service.customer.InternalCustomerUidTranslationService
import com.sast.cis.email2.adapter.EmailSenderStrategy
import com.sast.cis.email2.app.trialextension.dto.TrialExtensionRequestReceivedEmailData
import com.sast.cis.email2.app.trialextension.handler.TrialExtensionRequestReceivedTemplateHandler
import com.sast.cis.email2.constants.EmailType
import com.sast.cis.email2.service.aws.SnsSqsHelper
import com.sast.cis.email2.service.dto.SQSEmailMessage
import com.sast.cis.test.utils.FeatureToggleRule
import com.sast.email.client.EmailServiceAttachmentClient
import com.sast.email.client.EmailServiceClient
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import de.hybris.platform.testframework.RunListeners
import de.hybris.platform.testframework.runlistener.ItemCreationListener
import org.junit.Rule
import org.junit.Test

import javax.annotation.Resource

@IntegrationTest
@RunListeners([ItemCreationListener])
class TrialExtensionRequestReceivedISpec extends ServicelayerSpockSpecification {

    private final static String SQS_QUEUE_NAME = 'email-service-send-email-queue'
    private final static String SNS_TOPIC_ARN = 'arn:aws:sns:eu-central-1:000000000000:email-service-send-email'

    private final static String INTEGRATOR_NAME = "Quark, the magnificent Ferengi"
    private final static String INTEGRATOR_EMAIL = "quark@quarks.ds9"
    private final static String INTEGRATOR_COMPANY_ID = "1234"
    private final static String INTEGRATOR_COMPANY_NAME = "Quark's Bar, Grill, Gaming House and Holosuite Arcade"
    private final static String APP_NAME = "Test App Name"
    private final static String LINK_TO_DEVCON = "https://link.to.devcon"
    private final static int CAMERA_COUNT = 1
    private final static String EXTENSION_DURATION = "90"
    private final static String MESSAGE = "3 bars of gold press latinum please!"
    private final static Date REQUEST_DATE = new Date(2022 - 1900, 8, 2, 16, 16, 0)
    private final static String FORMATTED_DATE = "2022-09-02"

    @Rule
    public FeatureToggleRule featureToggleRule = new FeatureToggleRule()

    @Resource
    EmailSenderStrategy emailSenderStrategy

    @Resource
    EmailServiceClient emailServiceClient

    @Resource
    EmailServiceAttachmentClient emailServiceAttachmentClient

    @Resource
    InternalCustomerUidTranslationService<IntegratorModel> internalIntegratorUidTranslationService

    @Resource
    InternalCustomerUidTranslationService<DeveloperModel> internalDeveloperUidTranslationService

    @Resource
    ConfigurationService configurationService

    @Resource
    AmazonSQS amazonSQS

    @Resource
    AmazonSNS amazonSNS

    @Resource
    ObjectMapperService objectMapperService

    @Resource
    CommonI18NService commonI18NService

    @Resource
    TrialExtensionRequestReceivedTemplateHandler trialExtensionRequestReceivedTemplateHandler

    private SnsSqsHelper snsSqsHelper
    private CisEmailService emailService
    private String sqsEmailServiceQueueUrl


    def setup() {
        featureToggleRule.enable(Feature.FEATURE_EMAIL_CLIENT_ENABLED)
        snsSqsHelper = new SnsSqsHelper(amazonSQS, amazonSNS, objectMapperService)

        sqsEmailServiceQueueUrl = snsSqsHelper.getQueueURI(SQS_QUEUE_NAME)

        emailService = new CisEmailService(
                [trialExtensionRequestReceivedTemplateHandler],
                internalIntegratorUidTranslationService,
                internalDeveloperUidTranslationService,
                emailSenderStrategy)

        snsSqsHelper.purgeQueue(sqsEmailServiceQueueUrl)
    }

    def cleanup() {
        snsSqsHelper.purgeQueue(sqsEmailServiceQueueUrl)
    }

    @Test
    def 'correctly formatted message is sent to the SQS queue'() {
        given: 'we send a new extension email'

        final TrialExtensionRequestReceivedEmailData emailData =
                TrialExtensionRequestReceivedEmailData.builder()
                        .integratorName(INTEGRATOR_NAME)
                        .integratorEmail(INTEGRATOR_EMAIL)
                        .integratorCompanyId(INTEGRATOR_COMPANY_ID)
                        .integratorCompanyName(INTEGRATOR_COMPANY_NAME)
                        .appName(APP_NAME)
                        .linkToDevCon(LINK_TO_DEVCON)
                        .cameraCount(CAMERA_COUNT)
                        .extensionDuration(EXTENSION_DURATION)
                        .message(MESSAGE)
                        .requestDate(REQUEST_DATE)
                        .build()

        final SendEmailData email = SendEmailData.builder()
                .emailType(EmailType.TRIAL_EXTENSION_REQUEST_RECEIVED)
                .messageContext(emailData)
                .to(["<EMAIL>"])
                .bcc(["<EMAIL>"])
                .build()


        emailService.sendEmail(email)
        SQSEmailMessage emailMessage = snsSqsHelper.retrieveSqsEmailMessage(sqsEmailServiceQueueUrl)

        expect: 'sqs message'
        emailMessage != null
        verifyAll {
            emailMessage.body.properties == [
                    integratorName       : INTEGRATOR_NAME,
                    integratorEmail      : INTEGRATOR_EMAIL,
                    integratorCompanyId  : INTEGRATOR_COMPANY_ID,
                    integratorCompanyName: INTEGRATOR_COMPANY_NAME,
                    appName              : APP_NAME,
                    linkToDevCon         : LINK_TO_DEVCON,
                    cameraCount          : CAMERA_COUNT,
                    extensionDuration    : EXTENSION_DURATION,
                    message              : MESSAGE,
                    requestDate          : FORMATTED_DATE,
                    hasMessage           : true
            ] as Map
        }
    }
}
