package com.sast.cis.thl.service;

import com.sast.cis.aa.core.service.s3.S3BucketProcessorFactoryService;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.ProductCatalogIdentifierService;
import com.sast.cis.thl.dto.ThlConfigurationDto;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.servicelayer.exceptions.UnknownIdentifierException;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static com.sast.cis.core.dao.CatalogVersion.STAGED;

@Service
@RequiredArgsConstructor
@Slf4j
public class ThlConfigurationImportService {

    private final ThlConfigurationDtoConverter thlConfigurationDtoConverter;
    private final ThlConfigurationValidator thlConfigurationValidator;
    private final S3BucketProcessorFactoryService s3BucketProcessorFactoryService;
    private final AppLicenseService appLicenseService;
    private final ProductCatalogIdentifierService productCatalogIdentifierService;
    private final ModelService modelService;

    @Value("${media.folder.thlConfiguration.bucketId}")
    private String thlConfigurationBucketName;

    public Set<ThlConfigurationDto> read() {
        Set<ThlConfigurationDto> configurations = new HashSet<>();
        s3BucketProcessorFactoryService.createExcelFileProcessor(thlConfigurationBucketName, false)
            .process(inputStream -> configurations.addAll(readFile(inputStream)));
        return configurations;
    }

    public void importDtos(Collection<ThlConfigurationDto> dtos) {
        dtos.forEach(dto -> {
            AppLicenseModel master = loadLicense(dto.getMasterProductLicenseCode());
            updateMaster(dto, master);

            AppLicenseModel thl = loadLicense(dto.getThlProductLicenseCode());
            updateThl(dto, thl);

            modelService.saveAll(master, thl);
        });
    }

    private void updateThl(ThlConfigurationDto dto, AppLicenseModel thl) {
        if (thl.getThlGroupDiscount() == null || thl.getThlGroupDiscount().isEmpty()) {
            thl.setThlGroupDiscount(new HashMap<>());
        }
        thl.getThlGroupDiscount().put(dto.getCustomerGroup(), String.format("%.2f", dto.getThlProductLicenseDiscountPercentage()));
    }

    private void updateMaster(ThlConfigurationDto dto, AppLicenseModel master) {
        master.setAddonThl(dto.getThlProductLicenseCode());
        if (master.getThlGroupDiscount() == null || master.getThlGroupDiscount().isEmpty()) {
            master.setThlGroupDiscount(new HashMap<>());
        }
        master.getThlGroupDiscount().put(dto.getCustomerGroup(), String.format("%.2f", dto.getMasterProductLicenseDiscountPercentage()));
    }

    private AppLicenseModel loadLicense(String code) {
        final CatalogVersionModel catalogVersion = productCatalogIdentifierService.getBaseStoreCatalogVersion(BaseStoreEnum.AA, STAGED);
        final AppLicenseModel appLicenseModel = appLicenseService.getAppLicenseForCode(code, catalogVersion.getCatalog().getId(), STAGED.getVersionName())
            .orElseThrow(() -> new UnknownIdentifierException("No app license with code=" + code + " in staged catalog"));
        return appLicenseModel;
    }

    private Set<ThlConfigurationDto> readFile(InputStream objectData) {
        Set<ThlConfigurationDto> configurations = new HashSet<>();
        Workbook workbook = getWorkbook(objectData);
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Set<ThlConfigurationDto> dtos = processSheet(workbook.getSheetAt(i));
            configurations.addAll(dtos);
        }

        discardMultipleMasterConfigurations(configurations);
        discardMultipleThlConfigurations(configurations);

        return configurations;
    }

    private Set<ThlConfigurationDto> processSheet(final Sheet sheet) {
        LOG.info("Processing sheet %s".formatted(sheet.getSheetName()));

        if (sheet.getLastRowNum() < 1) {
            LOG.warn("Input file does not contain any data for the customer Group ID " + sheet.getSheetName());
            return Collections.emptySet();
        }

        return processRows(sheet);
    }

    private Set<ThlConfigurationDto> processRows(Sheet sheet) {
        Set<ThlConfigurationDto> configurations = new HashSet<>();

        for (int currentRow = 1; currentRow <= sheet.getLastRowNum(); currentRow++) {
            Row row = sheet.getRow(currentRow);
            try {
                ThlConfigurationDto aConfiguration = thlConfigurationDtoConverter.convert(row);
                thlConfigurationValidator.validate(aConfiguration);
                configurations.add(aConfiguration);
            }
            catch (IllegalStateException | UnknownIdentifierException ex) {
                LOG.error("Invalid data at row index {}", currentRow, ex);
            }
        }

        return configurations;
    }

    /**
     * <p>A Thl product license code and customer group pair must exist once.</p>
     * The examples are in the form of masterLicenseCode, thlLicenseCode, customerGroup <br/>
     * Valid: <br/>
     * AA2_0401987_ESIMASTER, AA2_0401987_THLPKWTRK, IDW000 <br/>
     * AA2_0401987_ESIMASTER, AA2_0401987_THLPKWTRK, WD0000 <br/>
     * Invalid: <br/>
     * AA2_0401987_ESIMASTER, AA2_0401987_THLPKWTRK, IDW000 <br/>
     * AA2_0301987_ESIADV,    AA2_0401987_THLPKWTRK, IDW000 <br/>
     **/
    private void discardMultipleThlConfigurations(Set<ThlConfigurationDto> configurations) {
        Set<String> toBeCleanedThl = new HashSet<>(configurations.size());
        Map<String, String> thlCgPairs = new HashMap<>(configurations.size());
        configurations.forEach(c-> {
            if (!toBeCleanedThl.contains(c.getThlProductLicenseCode())) {
                if (thlCgPairs.get(c.getThlProductLicenseCode()) == null) {
                    thlCgPairs.put(c.getThlProductLicenseCode(), c.getCustomerGroup());
                } else {
                    LOG.warn("THL product license code {} has more than one thl configuration pair. Discarding all configurations with this code.", c.getThlProductLicenseCode());
                    toBeCleanedThl.add(c.getThlProductLicenseCode());
                }
            }
        });

        toBeCleanedThl.forEach((thlProductCode) -> {
            configurations.removeIf(aConfiguration -> aConfiguration.getThlProductLicenseCode().equals(thlProductCode));
        });
    }

    /**
     * <p>A Master product license code can have one thl product license.</p>
     * The examples are in the form of masterLicenseCode, thlLicenseCode, customerGroup <br/>
     * Valid: <br/>
     * AA2_0401987_ESIMASTER, AA2_0401987_THLPKWTRK, IDW000 <br/>
     * AA2_0401987_ESIMASTER, AA2_0401987_THLPKWTRK, WD0000 <br/>
     * Invalid: <br/>
     * AA2_0401987_ESIMASTER, AA2_0401987_THLPKWTRK, IDW000 <br/>
     * AA2_0401987_ESIMASTER, AA2_0401987_THLPKW   , IDW000 <br/>
     **/
    private void discardMultipleMasterConfigurations(Set<ThlConfigurationDto> configurations) {
        Set<String> toBeCleanedMaster = new HashSet<>(configurations.size());
        Map<String, String> masterThlPairs = new HashMap<>(configurations.size());
        configurations.forEach(c-> {
            if (!toBeCleanedMaster.contains(c.getMasterProductLicenseCode())) {
                String currentThlProductLicenseCode = c.getThlProductLicenseCode();
                String existingThlProductLicenseCode = masterThlPairs.getOrDefault(c.getMasterProductLicenseCode(), currentThlProductLicenseCode);
                if (!existingThlProductLicenseCode.equals(currentThlProductLicenseCode)) {
                    LOG.warn("Master product license code {} has more than one thl configuration pair. Discarding all configurations with this code.", c.getMasterProductLicenseCode());
                    toBeCleanedMaster.add(c.getMasterProductLicenseCode());
                } else {
                    masterThlPairs.put(c.getMasterProductLicenseCode(), currentThlProductLicenseCode);
                }
            }
        });

        toBeCleanedMaster.forEach((masterProductCode) -> {
            configurations.removeIf(aConfiguration -> aConfiguration.getMasterProductLicenseCode().equals(masterProductCode));
        });
    }

    private Workbook getWorkbook (InputStream objectData) {
        try {
            return new XSSFWorkbook(objectData);
        } catch (IOException e) {
            LOG.info("Can't process the file: {}", e.getMessage());
            throw new IllegalStateException("Couldn't open the input file", e);
        }
    }

    public String getThlConfigurationBucketName() {
        return thlConfigurationBucketName;
    }
}
