package com.sast.cis.thl.populator;

import com.sast.cis.core.factory.CisPriceDataFactory;
import de.hybris.platform.commercefacades.order.data.AbstractOrderData;
import de.hybris.platform.commercefacades.product.data.PriceDataType;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;

@Component
public class ThlOrderPopulator implements Populator<AbstractOrderModel, AbstractOrderData> {

    @Resource
    private CisPriceDataFactory priceDataFactory;

    @Override
    public void populate(AbstractOrderModel order, AbstractOrderData data) throws ConversionException {
        if (order.getOriginalTotalPrice()!=null) {
            data.setOriginalTotalPrice(priceDataFactory.create(PriceDataType.BUY,
                BigDecimal.valueOf(order.getOriginalTotalPrice()), order.getCurrency()));
        }
    }
}
