package com.sast.cis.thl.service;

import com.sast.cis.core.CisTimeService;
import com.sast.cis.core.dao.buyercontract.BuyerContractDao;
import com.sast.cis.core.enums.TerminationRuleUnit;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.SubscriptionContractModel;
import com.sast.cis.core.model.TerminationRulePeriodModel;
import com.sast.cis.core.service.ExtractMasterWithThlContractsService;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.core.util.BusinessProcessUtil;
import com.sast.cis.thl.enums.MigrationStatus;
import com.sast.cis.thl.model.EsiMasterTHLMigrationProcessModel;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.processengine.BusinessProcessService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.ESIMASTER_THL_MIGRATION_PROCESS;
import static com.sast.cis.core.enums.Feature.FEATURE_THL_MASTER_EXTRACT_JOB_TEST_MODE;
import static java.lang.Boolean.TRUE;

@Service("extractMasterWithThlContractsService")
@Slf4j
public class DefaultExtractMasterWithThlContractsService implements ExtractMasterWithThlContractsService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy - HH:mm:ss Z");
    private final BuyerContractDao buyerContractDao;
    private final CisTimeService cisTimeService;
    private final ZonedDateTime thlFeatureLiveDate;
    private final ModelService modelService;
    private final FeatureToggleService featureToggleService;
    private final BusinessProcessService businessProcessService;
    private final UserService userService;
    private final BusinessProcessUtil businessProcessUtil;

    public DefaultExtractMasterWithThlContractsService(BuyerContractDao buyerContractDao,
        CisTimeService cisTimeService, ModelService modelService, FeatureToggleService featureToggleService,
        BusinessProcessService businessProcessService, UserService userService, BusinessProcessUtil businessProcessUtil) {
        this.buyerContractDao = buyerContractDao;
        this.cisTimeService = cisTimeService;
        this.modelService = modelService;
        this.featureToggleService = featureToggleService;
        thlFeatureLiveDate = cisTimeService.getZonedDate(2025, 1, 1);
        this.businessProcessService = businessProcessService;
        this.userService = userService;
        this.businessProcessUtil = businessProcessUtil;
    }

    public void extract(String licenseCode, TerminationRulePeriodModel licenseNoticePeriod, int scanWindowInYears) {

        for (int yearsBack = 1; yearsBack <= scanWindowInYears; yearsBack++) {

            List<SubscriptionContractModel> list = fetchSubscriptions(licenseCode,
                licenseNoticePeriod, yearsBack);

            LOG.info("Uncancelled subscriptions count is {}", list.size());

            for (SubscriptionContractModel s : list) {
                if (s.getThlMigrationStatus() == null && hasThl(s) && !isThlInTheOrder(s)) {

                    migrate(s);

                    if (isTestMode()) {
                        // do it once
                        return;
                    }

                } else {
                    LOG.info("Subscription with code {} already has a thl", s.getCode());
                }
            }
        }

    }

    private boolean isTestMode() {
        return featureToggleService.isEnabledOrDefault(FEATURE_THL_MASTER_EXTRACT_JOB_TEST_MODE, TRUE);
    }

    public void migrate(SubscriptionContractModel subscription) {
        LOG.info("Invoking THL Contract migration process for subscription{}", subscription.getCode());
        EsiMasterTHLMigrationProcessModel thlMigrationProcess = createThlMigrationProcess(subscription);
        subscription.setThlMigrationStatus(MigrationStatus.CONTRACT_CANCEL_INITIATED);
        subscription.setBusinessProcessPk(thlMigrationProcess.getPk());
        modelService.save(subscription);
        modelService.refresh(thlMigrationProcess);
        businessProcessService.startProcess(thlMigrationProcess);
    }

    private EsiMasterTHLMigrationProcessModel createThlMigrationProcess(SubscriptionContractModel subscription) {
        String processId = businessProcessUtil.generateProcessId(ESIMASTER_THL_MIGRATION_PROCESS.getValue(), "thl-migration",
            subscription.getCode());
        EsiMasterTHLMigrationProcessModel esiMasterTHLMigrationProcess = businessProcessService.createProcess(processId,
            ESIMASTER_THL_MIGRATION_PROCESS.getValue());
        esiMasterTHLMigrationProcess.setUser(userService.getAdminUser());
        esiMasterTHLMigrationProcess.setSubscriptionContract(subscription);
        modelService.save(esiMasterTHLMigrationProcess);
        return esiMasterTHLMigrationProcess;
    }

    private List<SubscriptionContractModel> fetchSubscriptions(String licenseCode, TerminationRulePeriodModel noticePeriod, int yearsBack) {
        ZonedDateTime end = cisTimeService.getCurrentUtcTime().minusYears(yearsBack);
        ZonedDateTime start = getStartOfTheScanWindow(end, noticePeriod);

        // no need to hit db after the thl feature go-live date.
        if (start.isAfter(thlFeatureLiveDate)) {
            return Collections.emptyList();
        }

        // no need to get records after the thl feature go-live date.
        if (end.isAfter(thlFeatureLiveDate)) {
            end = thlFeatureLiveDate;
        }

        LOG.info("Fetching subscriptions between {} and {}", start.format(DATE_TIME_FORMATTER), end.format(DATE_TIME_FORMATTER));

        return buyerContractDao.listUncancelledUnmigratedSubscriptions(licenseCode,
            Date.from(Instant.from(start)), Date.from(Instant.from(end)));
    }

    private ZonedDateTime getStartOfTheScanWindow(ZonedDateTime end, TerminationRulePeriodModel noticePeriod) {

        if (noticePeriod.getUnit() == TerminationRuleUnit.MONTH) {
            return end.minusMonths(noticePeriod.getValue());
        } else if (noticePeriod.getUnit() == TerminationRuleUnit.YEAR) {
            return end.minusYears(noticePeriod.getValue());
        }

        return end.minusDays(noticePeriod.getValue());
    }

    private boolean isThlInTheOrder(SubscriptionContractModel subs) {
        AppLicenseModel master = (AppLicenseModel) subs.getOrderEntry().getProduct();
        String thlCode = master.getAddonThl();

        for (AbstractOrderEntryModel oe : subs.getOrderEntry().getOrder().getEntries()) {
            AppLicenseModel candidate = (AppLicenseModel) oe.getProduct();
            if (candidate.getCode().equals(thlCode)) {
                return true;
            }
        }
        return false;
    }

    private boolean hasThl(SubscriptionContractModel subs) {
        AppLicenseModel master = (AppLicenseModel) subs.getOrderEntry().getProduct();
        String thlCode = master.getAddonThl();

        // skip the subscription, as it does not have any thl add-on
        return !StringUtils.isBlank(thlCode);
    }
}
