<?xml version="1.0" encoding="ISO-8859-1"?>

<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="beans.xsd">

    <bean class="de.hybris.platform.commercefacades.order.data.OrderEntryData">
        <property name="addOnUg" type="String"/>
        <property name="addOnThl" type="String"/>
        <property name="totalPriceWithoutDiscount" type="de.hybris.platform.commercefacades.product.data.PriceData"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.order.data.AbstractOrderData">
        <property name="originalTotalPrice" type="de.hybris.platform.commercefacades.product.data.PriceData"/>
    </bean>

    <bean class="com.sast.cis.core.data.CartItemData">
        <property name="addOnUg" type="String"/>
        <property name="addOnThl" type="String"/>
        <property name="totalPriceWithoutDiscount" type="com.sast.cis.core.data.PriceData"/>
    </bean>

    <bean class="com.sast.cis.core.data.DetailProductData">
        <property name="addonThl" type="String"/>
        <property name="thlDiscounts" type="java.util.Map&lt;String,String&gt;"/>
    </bean>

    <bean class="de.hybris.platform.commercefacades.product.data.ProductData">
        <property name="addonThl" type="String"/>
        <property name="thlDiscounts" type="java.util.Map&lt;String,String&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.ProductLicenseData">
        <property name="addonThl" type="String" equals="true"/>
        <property name="thlDiscounts" type="java.util.Map&lt;String,String&gt;"/>
    </bean>

    <bean class="com.sast.cis.core.data.SimpleProductData">
        <property name="addonProductName" type="String"/>
        <property name="masterProductName" type="String"/>
    </bean>

</beans>
