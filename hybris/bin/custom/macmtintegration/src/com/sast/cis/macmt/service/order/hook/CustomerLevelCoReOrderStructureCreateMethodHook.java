package com.sast.cis.macmt.service.order.hook;

import com.sast.cis.aa.core.model.ContractMigrationProcessModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.macmt.service.order.data.MigrationOrderDraftData;
import com.sast.cis.macmt.service.order.data.MigrationOrderEntryCoReGroupData;
import com.sast.cis.macmt.service.order.data.MigrationOrderEntryDraftData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * It is a hook that is called after the order structure is created.
 * It filters the entries based on the country of the buyer company and groups them based on the CoRe type.
 * handover the grouped entries to the MigrationOrderEntryCoReGroupHandler.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CustomerLevelCoReOrderStructureCreateMethodHook implements OrderStructureCreateMethodHook {
    private final IotCompanyService iotCompanyService;
    private final CoReAsEmbeddedOrIndependentEntriesFilter coReAsEmbeddedOrIndependentEntriesFilter;
    private final MigrationOrderEntryCoReGroupHandler migrationOrderEntryCoReGroupHandler;

    @Override
    public void afterCreate(ContractMigrationProcessModel process, List<MigrationOrderDraftData> migrationOrderDrafts) {
        IoTCompanyModel buyerCompany = getCompany(process.getOwnerCompanyId());

        List<MigrationOrderEntryDraftData> allEntries = migrationOrderDrafts.stream()
            .flatMap(migrationOrderDraft -> migrationOrderDraft.migrationOrderEntries().stream())
            .collect(Collectors.toList());
        MigrationOrderEntryCoReGroupData migrationOrderEntryCoReGroupData = coReAsEmbeddedOrIndependentEntriesFilter.filter(
            allEntries, buyerCompany.getCountry());

        migrationOrderEntryCoReGroupHandler.handle(migrationOrderDrafts, migrationOrderEntryCoReGroupData);
    }

    private IoTCompanyModel getCompany(final String aaExternalCompanyId) {
        return iotCompanyService.getCompanyByAaExternalCompanyIdOrThrow(aaExternalCompanyId);
    }
}
