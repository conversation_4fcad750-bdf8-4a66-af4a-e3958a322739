package com.sast.cis.macmt.notification.converter;

import com.sast.cis.aa.core.enums.MigrationMode;
import com.sast.cis.aa.core.model.ContractMigrationProcessModel;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.core.util.Base58UUIDCodeGenerator;
import com.sast.cis.macmt.notification.dto.ContractMigrationRequestDto;
import com.sast.cis.macmt.notification.dto.MigrationContractDto;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;

import static java.time.ZoneOffset.UTC;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
@RequiredArgsConstructor
public class ContractMigrationRequestToProcessConverter {

    private final ModelService modelService;

    private final MigrationContractDtoConverter migrationContractDtoConverter;

    public ContractMigrationProcessModel convert(final @NonNull ContractMigrationRequestDto contractMigrationRequestDto) {
        final String code = Base58UUIDCodeGenerator.generateCode("ContractMigrationProcess");
        final String customerId = contractMigrationRequestDto.customerId();
        final LocalDateTime creationDate = contractMigrationRequestDto.creationDate();
        final Long processExecutionId = contractMigrationRequestDto.processExecutionId();
        final List<MigrationContractDto> migrationContractDtoList = contractMigrationRequestDto.migrationContracts();
        final MigrationMode migrationMode = toMigrationMode(contractMigrationRequestDto.migrationMode());

        final ContractMigrationProcessModel contractMigrationProcess = modelService.create(ContractMigrationProcessModel.class);
        contractMigrationProcess.setCode(code);
        contractMigrationProcess.setOwnerCompanyId(customerId);
        contractMigrationProcess.setCountryIsocode(contractMigrationRequestDto.countryIsocode());
        contractMigrationProcess.setCmtProcessExecutionId(String.valueOf(processExecutionId));
        contractMigrationProcess.setCmtProcessExecutionCreationDate(toDate(creationDate));
        contractMigrationProcess.setMigrationMode(migrationMode);

        final Set<MigrationContractModel> migrationContracts = migrationContractDtoConverter.convertAll(migrationContractDtoList);
        migrationContracts.forEach(migrationContract -> migrationContract.setContractMigrationProcess(contractMigrationProcess));
        contractMigrationProcess.setMigrationContracts(migrationContracts);

        return contractMigrationProcess;
    }

    private Date toDate(final LocalDateTime creationDate) {
        return Date.from(creationDate.atZone(UTC).toInstant());
    }

    private MigrationMode toMigrationMode(final String migrationMode) {
        if (isBlank(migrationMode)) {
            return null;
        }
        return MigrationMode.valueOf(migrationMode.toUpperCase());
    }
}
