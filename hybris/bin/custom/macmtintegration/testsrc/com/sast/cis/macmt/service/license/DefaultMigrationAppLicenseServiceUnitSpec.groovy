package com.sast.cis.macmt.service.license


import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.service.AppLicenseService
import com.sast.cis.core.service.ProductCatalogIdentifierService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.catalog.model.CatalogModel
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.testframework.JUnitPlatformSpecification

import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.LicenseAvailabilityStatus.PUBLISHED
import static com.sast.cis.core.enums.LicenseAvailabilityStatus.UNPUBLISHED
import static com.sast.cis.core.enums.StoreAvailabilityMode.RESTRICTED_BUYER_GROUP
import static com.sast.cis.core.enums.StoreAvailabilityMode.UNAVAILABLE

@UnitTest
class DefaultMigrationAppLicenseServiceUnitSpec extends JUnitPlatformSpecification {

    private AppLicenseService appLicenseService = Mock()
    private ProductCatalogIdentifierService productCatalogIdentifierService = Mock()

    private DefaultMigrationAppLicenseService migrationAppLicenseService

    private AppLicenseModel appLicense = Mock()
    private AppModel app = Mock()
    private CountryModel country = Mock(CountryModel)
    private CatalogModel catalog = Mock()

    private final String materialId = "1987P12051"

    def setup() {
        migrationAppLicenseService = new DefaultMigrationAppLicenseService(appLicenseService, productCatalogIdentifierService)

        appLicense.getAvailabilityStatus() >> PUBLISHED
        appLicenseService.getApp(appLicense) >> app
        app.isMasterEnabled() >> true
        app.getStoreAvailabilityMode() >> RESTRICTED_BUYER_GROUP

        catalog.getId() >> "aa_catalog"
        productCatalogIdentifierService.getSessionProductCatalog() >> catalog

        country.getIsocode() >> "DE"
        appLicenseService.getAppLicenseForSellerProductIdAndCountry(materialId, "DE", "aa_catalog", ONLINE) >> Optional.of(appLicense)
    }

    def "isAppLicenseActive should return true for active app license"() {
        when:
        def result = migrationAppLicenseService.isAppLicenseActive(appLicense)

        then:
        result
    }

    def "given app license with availability status UNPUBLISHED when isAppLicenseActive then return false"() {
        when:
        def result = migrationAppLicenseService.isAppLicenseActive(appLicense)

        then:
        appLicense.getAvailabilityStatus() >> UNPUBLISHED
        !result
    }

    def "given app license with parent app not master enabled when isAppLicenseActive then return false"() {
        when:
        def result = migrationAppLicenseService.isAppLicenseActive(appLicense)

        then:
        app.isMasterEnabled() >> false
        !result
    }

    def "given app license with UNAVAILABLE parent app when isAppLicenseActive then return false"() {
        when:
        def result = migrationAppLicenseService.isAppLicenseActive(appLicense)

        then:
        app.getStoreAvailabilityMode() >> UNAVAILABLE
        !result
    }

    def "findAppLicense should return optional app license if found"() {
        when:
        def result = migrationAppLicenseService.findAppLicense(materialId, country)

        then:
        result.isPresent()
        result.get() == appLicense
    }

    def "findAppLicense should return empty if app license not found"() {
        when:
        def result = migrationAppLicenseService.findAppLicense(materialId, country)

        then:
        appLicenseService.getAppLicenseForSellerProductIdAndCountry(materialId, "DE", "aa_catalog", ONLINE) >> Optional.empty()
        result.isEmpty()
    }

    def "given material id with 999 suffix when findAppLicense then return result"() {
        when:
        def result = migrationAppLicenseService.findAppLicense(materialId + "999", country)

        then:
        result.isPresent()
        result.get() == appLicense
    }
}
