package com.sast.cis.onboarding.listener

import com.sast.cis.core.billingintegration.dto.CompanyExportResult
import com.sast.cis.core.billingintegration.dto.CompanyExportedData
import com.sast.cis.core.billingintegration.events.PspAccountExportedEvent
import com.sast.cis.core.enums.BillingSystemStatus
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.model.DeveloperModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.PspSellerAccountModel
import com.sast.cis.core.util.BusinessProcessUtil
import com.sast.cis.email2.service.CisEmailService
import com.sast.cis.email2.typehandler.pspintegraiton.SuccessfulPSPOnboardingHandler
import com.sast.cis.test.utils.TestLogListener
import com.sast.email.client.dto.EmailDto
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import de.hybris.platform.util.logging.HybrisLogger
import org.apache.log4j.Level
import org.junit.Test
import spock.lang.Unroll

@UnitTest
class PspAccountExportedListenerUnitSpec extends JUnitPlatformSpecification {
    private static final String STRIPE_ACCOUNT_ID = 'This is a stripe account. Really.'
    private static final String DPG_ACCOUNT_ID = 'And i\'m a PC'
    private static final String EVENT_CHOICE_SUCCESS = "success"
    private static final String EVENT_NAME = "company-export-response-event"
    private static final String PROCESS_NAME = "export-psp-account"

    private ModelService modelService = Mock(ModelService)
    private BusinessProcessUtil businessProcessUtil = Mock(BusinessProcessUtil)
    private CisEmailService cisEmailService = Mock()
    private SuccessfulPSPOnboardingHandler onboardingEmailTemplateHandler = Mock()

    private PspAccountExportedListener pspAccountExportedListener = Mock(PspAccountExportedListener)

    private PspSellerAccountModel mockPspSellerAccount = Mock(PspSellerAccountModel)
    private IoTCompanyModel mockCompany = Mock(IoTCompanyModel)
    private DeveloperModel mockDeveloper = Mock()

    private TestLogListener testLogListener

    def setup() {
        testLogListener = new TestLogListener()
        HybrisLogger.addListener(testLogListener)

        pspAccountExportedListener = new PspAccountExportedListener(modelService, businessProcessUtil,
                cisEmailService, onboardingEmailTemplateHandler)
    }

    def cleanup() {
        HybrisLogger.removeListener(testLogListener)
    }

    @Test
    def 'event for STRIPE account sets status to in sync and sends business process event'() {
        given:
        mockPspSellerAccount.getPaymentProvider() >> PaymentProvider.STRIPE
        mockPspSellerAccount.getAccountId() >> STRIPE_ACCOUNT_ID
        mockPspSellerAccount.getUser() >> mockDeveloper
        def givenEvent = new PspAccountExportedEvent(CompanyExportedData
                .builder(mockCompany, CompanyExportResult.SUCCESS)
                .pspSellerAccount(mockPspSellerAccount)
                .errors(List.of())
                .build())
        def givenMailData = Mock(EmailDto)

        when:
        pspAccountExportedListener.onEvent(givenEvent)

        then:
        1 * mockPspSellerAccount.setBillingSystemStatus(BillingSystemStatus.IN_SYNC)
        1 * modelService.save(mockPspSellerAccount)
        1 * businessProcessUtil.triggerBusinessProcessEvent(PROCESS_NAME, STRIPE_ACCOUNT_ID, EVENT_NAME, EVENT_CHOICE_SUCCESS)
        1 * onboardingEmailTemplateHandler.getEmailDto(mockPspSellerAccount) >> givenMailData
        1 * cisEmailService.sendEmail(mockDeveloper, givenMailData)
    }

    @Test
    def 'event for STRIPE account without user sets status to in sync and sends business process event but no email'() {
        given:
        mockPspSellerAccount.getPaymentProvider() >> PaymentProvider.STRIPE
        mockPspSellerAccount.getAccountId() >> STRIPE_ACCOUNT_ID
        mockPspSellerAccount.getUser() >> null
        def givenEvent = new PspAccountExportedEvent(CompanyExportedData
                .builder(mockCompany, CompanyExportResult.SUCCESS)
                .pspSellerAccount(mockPspSellerAccount)
                .errors(List.of())
                .build())

        when:
        pspAccountExportedListener.onEvent(givenEvent)

        then:
        1 * mockPspSellerAccount.setBillingSystemStatus(BillingSystemStatus.IN_SYNC)
        1 * modelService.save(mockPspSellerAccount)
        1 * businessProcessUtil.triggerBusinessProcessEvent(PROCESS_NAME, STRIPE_ACCOUNT_ID, EVENT_NAME, EVENT_CHOICE_SUCCESS)
        0 * onboardingEmailTemplateHandler._
        0 * cisEmailService._
    }

    @Test
    def 'event for DPG account sets status to in sync and doesnt send business process event'() {
        given:
        mockPspSellerAccount.getPaymentProvider() >> PaymentProvider.DPG
        mockPspSellerAccount.getAccountId() >> DPG_ACCOUNT_ID
        mockPspSellerAccount.getUser() >> mockDeveloper
        def givenEvent = new PspAccountExportedEvent(CompanyExportedData
                .builder(mockCompany, CompanyExportResult.SUCCESS)
                .pspSellerAccount(mockPspSellerAccount)
                .errors(List.of())
                .build())
        def givenMailData = Mock(EmailDto)

        when:
        pspAccountExportedListener.onEvent(givenEvent)

        then:
        1 * mockPspSellerAccount.setBillingSystemStatus(BillingSystemStatus.IN_SYNC)
        1 * modelService.save(mockPspSellerAccount)
        0 * businessProcessUtil.triggerBusinessProcessEvent(_, _, _, _)
        1 * onboardingEmailTemplateHandler.getEmailDto(mockPspSellerAccount) >> givenMailData
        1 * cisEmailService.sendEmail(mockDeveloper, givenMailData)
    }

    @Test
    def 'event for DPG account without user sets status to in sync and doesnt send business process event or email'() {
        given:
        mockPspSellerAccount.getPaymentProvider() >> PaymentProvider.DPG
        mockPspSellerAccount.getAccountId() >> DPG_ACCOUNT_ID
        mockPspSellerAccount.getUser() >> null
        def givenEvent = new PspAccountExportedEvent(CompanyExportedData
                .builder(mockCompany, CompanyExportResult.SUCCESS)
                .pspSellerAccount(mockPspSellerAccount)
                .errors(List.of())
                .build())

        when:
        pspAccountExportedListener.onEvent(givenEvent)

        then:
        1 * mockPspSellerAccount.setBillingSystemStatus(BillingSystemStatus.IN_SYNC)
        1 * modelService.save(mockPspSellerAccount)
        0 * businessProcessUtil.triggerBusinessProcessEvent(_, _, _, _)
        0 * onboardingEmailTemplateHandler._
        0 * cisEmailService._
    }

    @Test
    @Unroll
    def 'event contains errors, psp account for PSP=#givenPaymentProvider is not updated, alert is logged and no BP event is sent'() {
        given:
        mockPspSellerAccount.getPaymentProvider() >> givenPaymentProvider
        mockPspSellerAccount.getAccountId() >> 'it really doesnt matter here'
        def givenEvent = new PspAccountExportedEvent(CompanyExportedData
                .builder(mockCompany, CompanyExportResult.SUCCESS)
                .pspSellerAccount(mockPspSellerAccount)
                .errors(List.of('I am a big bad error'))
                .build())


        when:
        pspAccountExportedListener.onEvent(givenEvent)
        def actualLogs = testLogListener.getLog()

        then:
        0 * mockPspSellerAccount.setBillingSystemStatus(_)
        0 * businessProcessUtil.triggerBusinessProcessEvent(_, _, _, _)
        0 * modelService.save(mockPspSellerAccount)
        actualLogs.size() == 2

        expect:
        assert actualLogs.any { it.getLevel() == Level.ERROR }

        where:
        givenPaymentProvider << [PaymentProvider.DPG, PaymentProvider.STRIPE]
    }

    @Test
    @Unroll
    def 'event status is FAILURE, psp account for PSP=#givenPaymentProvider is not updated, alert is logged and no BP event is sent'() {
        given:
        mockPspSellerAccount.getPaymentProvider() >> givenPaymentProvider
        mockPspSellerAccount.getAccountId() >> 'it really doesnt matter here'
        def givenEvent = new PspAccountExportedEvent(CompanyExportedData
                .builder(mockCompany, CompanyExportResult.FAILURE)
                .pspSellerAccount(mockPspSellerAccount)
                .errors(List.of())
                .build())


        when:
        pspAccountExportedListener.onEvent(givenEvent)
        def actualLogs = testLogListener.getLog()

        then:
        0 * mockPspSellerAccount.setBillingSystemStatus(_)
        0 * businessProcessUtil.triggerBusinessProcessEvent(_, _, _, _)
        0 * modelService.save(mockPspSellerAccount)
        actualLogs.size() == 2

        expect:
        assert actualLogs.any { it.getLevel() == Level.ERROR }

        where:
        givenPaymentProvider << [PaymentProvider.DPG, PaymentProvider.STRIPE]
    }

    static getExpectedBpEventName(String accountId) {
        PROCESS_NAME + "_${accountId}_" + EVENT_NAME
    }
}
