package com.sast.cis.onboarding.facade

import com.sast.cis.core.config.keycloak.SiteUmpAdapterConfigResolutionService
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.enums.PayoutStatus
import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.factory.CisPriceDataFactory
import com.sast.cis.core.model.DeveloperModel
import com.sast.cis.core.model.DeveloperPayoutModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.customer.developer.DeveloperCompanyService
import com.sast.cis.core.service.customer.developer.DeveloperService
import com.sast.cis.payment.dpg.enums.DpgSellerAccountStatus
import com.sast.cis.test.utils.LoginUtil
import com.sast.cis.test.utils.UmpWireMockRule
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import generated.com.sast.cis.core.model.DeveloperPayoutBuilder
import generated.com.sast.cis.payment.dpg.model.DpgSellerAccountBuilder
import org.junit.Rule
import org.junit.Test
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.client.RestTemplate

import javax.annotation.Resource
import java.time.Instant

import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_DEVELOPER_A1_SSOID
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_DEVELOPER_A1_UID
import static com.sast.cis.test.utils.UmpWireMockRule.UMP_BASE_URL_KEY

@IntegrationTest
class PayoutFacadeImplISpec extends ServicelayerTransactionalSpockSpecification {
    private static final Date PAST_PAYOUT_DATE = Date.from(Instant.EPOCH)
    private static final Long COMPLETED_PAYOUT_AMOUNT = 20000L
    private static final String DPG_ACCOUNT_ID = 'Dpg Account ID'

    @Resource
    private PayoutFacadeImpl payoutFacadeImpl

    @Resource
    private DeveloperCompanyService developerCompanyService

    @Resource
    private DeveloperService developerService

    @Resource
    private UserService userService

    @Resource
    protected ConfigurationService configurationService

    @Resource
    private RestTemplate cisRestTemplate

    @Resource
    private ModelService modelService

    @Resource
    private CisPriceDataFactory cisPriceDataFactory

    @Resource
    private SiteUmpAdapterConfigResolutionService siteUmpAdapterConfigResolutionService

    @Rule
    public UmpWireMockRule umpWireMockRule = new UmpWireMockRule()

    DeveloperModel developer
    IoTCompanyModel company
    DeveloperPayoutModel dpgPayout

    def setup() {
        LoginUtil loginUtil = new LoginUtil(siteUmpAdapterConfigResolutionService, cisRestTemplate)
        SecurityContextHolder.getContext().setAuthentication(loginUtil.generateKeycloakAuthenticationObject())

        developer = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID)
        userService.setCurrentUser(developer)
        company = developer.getCompany()
        configurationService.getConfiguration().setProperty(UMP_BASE_URL_KEY, umpWireMockRule.getUmpUrl())
        umpWireMockRule.prepareGetUserDataResponse(SAMPLE_DATA_DEVELOPER_A1_SSOID, company.getUid())

        def dpgSellerAccount = DpgSellerAccountBuilder.generate()
                .withPaymentProvider(PaymentProvider.DPG)
                .withStatus(PspSellerAccountStatus.ONBOARDING)
                .withDpgStatus(DpgSellerAccountStatus.IN_APPROVAL)
                .withAccountId(DPG_ACCOUNT_ID)
                .withCompany(company)
                .buildIntegrationInstance()
        modelService.save(dpgSellerAccount)
        modelService.refresh(company)

        dpgPayout = DeveloperPayoutBuilder.generate()
                .withCompany(company)
                .withStatus(PayoutStatus.PAID)
                .withCurrency(company.country.currency)
                .withAmount(COMPLETED_PAYOUT_AMOUNT)
                .withPayoutDate(PAST_PAYOUT_DATE)
                .withPayoutId('SomePayoutId')
                .withPaymentProvider(PaymentProvider.DPG)
                .buildIntegrationInstance()
        modelService.save(dpgPayout)
    }

    @Test
    def 'getPayoutPageData only returns account data and zero amount future payout if there is no actual payout information'() {
        given:
        modelService.remove(dpgPayout)

        when:
        def actualPayoutData = payoutFacadeImpl.getPayoutPageData()

        then:
        with(actualPayoutData.getDpgPayoutPageData()) {
            pendingPayout.isEmpty()
            completedPayouts.empty
        }
    }
}
