package com.sast.cis.onboarding.process;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.config.BaseStoreConfigService;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.enums.BillingSystemStatus;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.enums.PspSellerAccountStatus;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.PspSellerAccountModel;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.onboarding.model.ExportPSPAccountProcessModel;
import com.sast.cis.test.utils.TestDataConstants;
import com.sast.cis.test.utils.UmpWireMockRule;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import de.hybris.platform.processengine.model.BusinessProcessModel;
import de.hybris.platform.processengine.model.ProcessTaskModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.user.UserService;
import generated.com.sast.cis.onboarding.model.ExportPSPAccountProcessBuilder;
import generated.com.sast.cis.payment.boschtransfer.model.BoschSellerAccountBuilder;
import generated.de.hybris.platform.processengine.model.ProcessTaskBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;

import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.EXPORT_PSP_ACCOUNT_PROCESS;
import static com.sast.cis.core.enums.PaymentProvider.BOSCH_TRANSFER;
import static com.sast.cis.core.enums.PspSellerAccountStatus.ACTIVE;
import static com.sast.cis.core.enums.PspSellerAccountStatus.ONBOARDING;
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_DEVELOPER_A1_UID;
import static com.sast.cis.test.utils.UmpWireMockRule.UMP_BASE_URL_KEY;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class ExportPSPAccountActionITest extends ServicelayerTransactionalTest {
    private static final String ACCOUNT_ID = "accountId";

    @Resource
    private ConfigurationService configurationService;

    @Resource
    private UserService userService;

    @Resource
    private DeveloperService developerService;

    @Resource
    private ModelService modelService;

    @Resource
    private ExportPSPAccountAction exportPSPAccountAction;

    @Resource
    private FlexibleSearchService flexibleSearchService;
    @Resource
    private BaseStoreConfigService baseStoreConfigService;

    @Rule
    public UmpWireMockRule umpRule = new UmpWireMockRule();

    private DeveloperModel developer;

    @Before
    public void setUp() {
        String configPrefixForBaseStore = baseStoreConfigService.getConfigPrefixForBaseStore(BaseStoreEnum.AZENA);
        String key = String.format(UMP_BASE_URL_KEY, configPrefixForBaseStore);
        configurationService.getConfiguration().setProperty(key, umpRule.getUmpUrl());
        developer = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);
        IoTCompanyModel company = developer.getCompany();
        company.setBillingSystemStatus(BillingSystemStatus.NEW);
        modelService.save(company);
        userService.setCurrentUser(developer);
        umpRule.prepareGetCompanyDataResponse(developer.getCompany().getUid(), TestDataConstants.AZENA_MARKETPLACE_ID);

    }

    private void cleanup() {
        modelService.removeAll(developer.getCompany().getPspSellerAccounts());
        modelService.removeAll(fetchExportPSPBusinessProcess());
    }

    @Test
    public void exportPSPAccount() {

        ExportPSPAccountProcessModel exportPSPAccountProcess = createExportPSPAccountProcess(ACTIVE, BOSCH_TRANSFER);

        assertThat(exportPSPAccountProcess.getPspSellerAccount().getBillingSystemStatus()).isEqualTo(BillingSystemStatus.NEW);
        AbstractSimpleDecisionAction.Transition transition = exportPSPAccountAction.executeAction(exportPSPAccountProcess);

        List<BusinessProcessModel> businessProcessModels = fetchExportPSPBusinessProcess();
        assertThat(businessProcessModels).hasSize(1);
        ExportPSPAccountProcessModel exportPSPProcessModel = (ExportPSPAccountProcessModel) businessProcessModels.get(0);
        assertThat(exportPSPProcessModel.getPspSellerAccount()).isNotNull();
        assertThat(exportPSPProcessModel.getPspSellerAccount().getCompany().getPspSellerAccounts().size()).isEqualTo(1);
        assertPspSellerAccount(exportPSPProcessModel.getPspSellerAccount(), BOSCH_TRANSFER);
        assertThat(transition).isEqualTo(AbstractSimpleDecisionAction.Transition.OK);
        cleanup();
    }

    @Test
    public void brim_exportPSPAccount() {
        developer.getCompany().setBillingSystemStatus(BillingSystemStatus.NEW);
        modelService.save(developer.getCompany());
        umpRule.prepareGetCompanyDataResponse(developer.getCompany().getUid(), TestDataConstants.AZENA_MARKETPLACE_ID);

        ExportPSPAccountProcessModel exportPSPAccountProcess = createExportPSPAccountProcess(ACTIVE, BOSCH_TRANSFER);
        assertThat(exportPSPAccountProcess.getPspSellerAccount().getBillingSystemStatus()).isEqualTo(BillingSystemStatus.NEW);

        AbstractSimpleDecisionAction.Transition transition = exportPSPAccountAction.executeAction(exportPSPAccountProcess);

        List<BusinessProcessModel> businessProcessModels = fetchExportPSPBusinessProcess();
        assertThat(businessProcessModels).hasSize(1);
        ExportPSPAccountProcessModel exportPSPProcessModel = (ExportPSPAccountProcessModel) businessProcessModels.get(0);
        assertThat(exportPSPProcessModel.getPspSellerAccount()).isNotNull();
        assertPspSellerAccount(exportPSPProcessModel.getPspSellerAccount(), BOSCH_TRANSFER);
        assertThat(transition).isEqualTo(AbstractSimpleDecisionAction.Transition.OK);
        cleanup();
    }
    
    @Test
    public void exportPSPAccountInProgressNoRetry() {
        ExportPSPAccountProcessModel exportPSPAccountProcess = createExportPSPAccountProcess(ONBOARDING, BOSCH_TRANSFER);
        AbstractSimpleDecisionAction.Transition transition = exportPSPAccountAction.executeAction(exportPSPAccountProcess);

        List<BusinessProcessModel> businessProcessModels = fetchExportPSPBusinessProcess();
        assertThat(businessProcessModels).hasSize(1);
        ExportPSPAccountProcessModel exportPSPProcessModel = (ExportPSPAccountProcessModel) businessProcessModels.get(0);
        assertThat(exportPSPProcessModel.getPspSellerAccount()).isNotNull();
        assertPspSellerAccount(exportPSPProcessModel.getPspSellerAccount(), BOSCH_TRANSFER);
        assertThat(exportPSPAccountProcess.getPspSellerAccount().getBillingSystemStatus()).isEqualTo(BillingSystemStatus.NEW);
        assertThat(transition).isEqualTo(AbstractSimpleDecisionAction.Transition.NOK);

        cleanup();
    }

    @Test
    public void exportPSPAccountUnExpectedExceptionNoRetry() {
        ExportPSPAccountProcessModel exportPSPAccountProcess = createExportPSPAccountProcess(ACTIVE, BOSCH_TRANSFER);
        exportPSPAccountProcess.getPspSellerAccount().setCompany(null);
        AbstractSimpleDecisionAction.Transition transition = exportPSPAccountAction.executeAction(exportPSPAccountProcess);
        assertThat(transition).isEqualTo(AbstractSimpleDecisionAction.Transition.NOK);
        cleanup();
    }

    private List<BusinessProcessModel> fetchExportPSPBusinessProcess() {
        BusinessProcessModel sampleProcess = new BusinessProcessModel();
        sampleProcess.setProcessDefinitionName(EXPORT_PSP_ACCOUNT_PROCESS.getValue());
        return flexibleSearchService.getModelsByExample(sampleProcess);
    }

    private PspSellerAccountModel createPspSellerAccount(PspSellerAccountStatus status, PaymentProvider paymentProvider) {
        IoTCompanyModel company = developer.getCompany();
        PspSellerAccountModel pspSellerAccount = BoschSellerAccountBuilder.generate()
            .withAccountId(ACCOUNT_ID)
            .withPaymentProvider(paymentProvider)
            .withStatus(status)
            .withCompany(company)
            .buildIntegrationInstance();

        company.setPspSellerAccounts(ImmutableList.of(pspSellerAccount));
        modelService.saveAll(company, pspSellerAccount);
        return pspSellerAccount;
    }

    private ExportPSPAccountProcessModel createExportPSPAccountProcess(PspSellerAccountStatus status, PaymentProvider paymentProvider) {
        ProcessTaskModel task = ProcessTaskBuilder.generate()
            .withAction("exportPSPAccountAction")
            .withRunnerBean("someBean").buildIntegrationInstance();

        ExportPSPAccountProcessModel process = ExportPSPAccountProcessBuilder.generate()
            .withCode("code")
            .withProcessDefinitionName(EXPORT_PSP_ACCOUNT_PROCESS.getValue())
            .withPspSellerAccount(createPspSellerAccount(status, paymentProvider))
            .withCurrentTasks(Collections.singleton(task))
            .buildIntegrationInstance();

        task.setProcess(process);
        modelService.save(process);
        return process;
    }

    private void assertPspSellerAccount(PspSellerAccountModel pspSellerAccount, PaymentProvider paymentProvider) {
        assertThat(pspSellerAccount.getPaymentProvider()).isEqualTo(paymentProvider);
        assertThat(pspSellerAccount.getAccountId()).isEqualTo(ACCOUNT_ID);
    }
}
