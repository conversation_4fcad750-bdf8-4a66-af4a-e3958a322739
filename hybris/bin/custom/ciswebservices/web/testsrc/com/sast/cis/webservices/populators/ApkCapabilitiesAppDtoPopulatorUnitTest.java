package com.sast.cis.webservices.populators;

import com.sast.cis.core.model.ApkMediaModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.model.DeviceCapabilityModel;
import com.sast.cis.core.service.AppVersionService;
import com.sast.cis.webservices.dto.AppDto;
import com.sast.cis.webservices.dto.DeviceCapabilityDto;
import com.sast.cis.webservices.utils.LocalizedValue;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ApkCapabilitiesAppDtoPopulatorUnitTest {
    private static final LocalizedValue DUMMY_NAME_EN = new LocalizedValue().withValue("Web Server").withLocale("en");
    private static final LocalizedValue DUMMY_NAME_DE = new LocalizedValue().withValue("Webserver").withLocale("de");
    private static final String DUMMY_CAPABILITY = "com.securityandsafetythings.capabilities.webserver";

    @Mock
    private AppModel mockApp;

    @Mock
    private AppVersionModel mockVersion;

    @Mock
    private ApkMediaModel mockApk;

    @Mock
    private DeviceCapabilityModel mockCapability;

    @Mock
    private AppVersionService appVersionService;

    @Mock
    private LocalizedValueConverter localizedValueConverter;

    @InjectMocks
    private ApkCapabilitiesAppDtoPopulator apkCapabilitiesAppDtoPopulator;

    @Before
    public void setUp() {
        when(appVersionService.getLatestVersion(mockApp)).thenReturn(Optional.of(mockVersion));
        when(mockVersion.getApk()).thenReturn(mockApk);
        when(mockApk.getDeviceCapabilities()).thenReturn(Set.of(mockCapability));
        when(mockCapability.getCode()).thenReturn(DUMMY_CAPABILITY);

        when(localizedValueConverter.createLocalizedValues(any())).thenReturn(List.of(DUMMY_NAME_DE, DUMMY_NAME_EN));

    }

    @Test
    public void shouldPopulateCapabilities() {
        AppDto appDto = new AppDto();

        apkCapabilitiesAppDtoPopulator.populate(mockApp, appDto);

        assertThat(appDto.getRequiredCapabilities()).flatExtracting(DeviceCapabilityDto::getName).containsExactly(DUMMY_CAPABILITY);
        assertThat(appDto.getRequiredCapabilities()).flatExtracting(DeviceCapabilityDto::getDisplayName)
            .containsExactly(DUMMY_NAME_DE, DUMMY_NAME_EN);
    }

    @Test
    public void shouldHandleApkWithoutCapabilities() {
        when(mockApk.getDeviceCapabilities()).thenReturn(Set.of());
        AppDto appDto = new AppDto();

        apkCapabilitiesAppDtoPopulator.populate(mockApp, appDto);

        assertThat(appDto.getRequiredCapabilities()).isEmpty();
    }

    @Test
    public void shouldHandleNullCapabilities() {
        when(mockApk.getDeviceCapabilities()).thenReturn(null);
        AppDto appDto = new AppDto();

        apkCapabilitiesAppDtoPopulator.populate(mockApp, appDto);

        assertThat(appDto.getRequiredCapabilities()).isEmpty();
    }

    @Test
    public void shouldHandleNullApk() {
        when(mockVersion.getApk()).thenReturn(null);
        AppDto appDto = new AppDto();

        apkCapabilitiesAppDtoPopulator.populate(mockApp, appDto);

        assertThat(appDto.getRequiredCapabilities()).isNull();
    }

    @Test
    public void shouldHandleAppWithoutVersion() {
        when(appVersionService.getLatestVersion(mockApp)).thenReturn(Optional.empty());
        AppDto appDto = new AppDto();

        apkCapabilitiesAppDtoPopulator.populate(mockApp, appDto);

        assertThat(appDto.getRequiredCapabilities()).isNull();
    }
}