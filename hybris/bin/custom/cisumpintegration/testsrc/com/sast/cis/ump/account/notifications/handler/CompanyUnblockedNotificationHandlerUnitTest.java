package com.sast.cis.ump.account.notifications.handler;

import com.sast.cis.core.service.ObjectMapperService;
import com.sast.cis.ump.account.notifications.NotificationType;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.verify;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CompanyUnblockedNotificationHandlerUnitTest {

    @Mock
    private ObjectMapperService objectMapperService;

    @Mock
    private NotificationHandlerDispatcher dispatcher;

    @Test
    public void testHandlerRegisteredWithCompanyUnblockedNotificationType() {
        var handler = new CompanyUnblockedNotificationHandler(objectMapperService, dispatcher);
        verify(dispatcher).registerHandler(NotificationType.COMPANY_UNBLOCKED, handler);
    }
}