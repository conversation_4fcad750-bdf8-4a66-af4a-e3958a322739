package com.sast.cis.ump.account.notifications.validation;

import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.ump.account.notifications.dto.UserCreationData;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class UserCreationDataValidatorUnitTest {

    @Mock
    private IotCompanyService iotCompanyService;

    @InjectMocks
    private UserCreationDataValidator userCreationDataValidator;

    private final String companyId = "companyId";

    @Before
    public void setUp() {
        when(iotCompanyService.companyExists(companyId)).thenReturn(true);
    }

    @Test
    public void givenValidUserCreationData_whenValidate_thenSucceed() {
        var userCreationData = UserCreationData.builder().companyId(companyId).build();

        userCreationDataValidator.validateUserCreationData(userCreationData);
    }

    @Test
    public void givenValidUserCreationDataWithUnknownCompanyId_whenValidate_thenSucceed() {
        var unknownCompanyId = "unknown";
        var userName = "userName";
        var userCreationData = UserCreationData.builder().userName(userName).companyId(unknownCompanyId).build();

        assertThatThrownBy(() -> userCreationDataValidator.validateUserCreationData(userCreationData))
            .isInstanceOf(UserCreationDataValidationException.class)
            .hasMessage("User with id '%s' cannot be created. Company with id='%s' not found", userName, unknownCompanyId);
    }
}
