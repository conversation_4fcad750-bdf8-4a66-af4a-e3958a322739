package com.sast.cis.ump.account.notifications.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sast.cis.aa.core.data.UmpCustomerGroupData;
import com.sast.cis.core.data.UmpAddressData;
import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.data.UmpCompanyType;
import com.sast.cis.core.data.UmpDistributorData;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;

import java.math.BigDecimal;
import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
public class CompanyUpdateData {

    private final UmpCompanyData companyData;

    @JsonCreator
    public CompanyUpdateData(
        @JsonProperty(value = "companyId", required = true) final String companyId,
        @JsonProperty(value = "companyName", required = true) final String companyName,
        @JsonProperty(value = "country", required = true) final String companyCountry,

        @JsonProperty(value = "taxId") final String taxId,
        @JsonProperty(value = "bpmdId") final String bpmdId,
        @JsonProperty(value = "creditLimit") final BigDecimal creditLimit,
        @JsonProperty(value = "friendlyName") final String friendlyName,
        @JsonProperty(value = "companyTypes") final Set<UmpCompanyType> companyTypes,
        @JsonProperty(value = "companyStatus") final String companyStatus,
        @JsonProperty(value = "billingAddress") final UmpAddressData billingAddress,
        @JsonProperty(value = "businessAddress") final UmpAddressData businessAddress,
        @JsonProperty(value = "manualAppApprovalEnabled") final Boolean isManualAppApprovalEnabled,
        @JsonProperty(value = "ownAppsPurchaseEnabled") final Boolean ownAppsPurchaseEnabled,
        @JsonProperty(value = "costCenter") final String costCenter,
        @JsonProperty(value = "managed") final boolean isManaged,

        @JsonProperty(value = "externalCustomerId") final String externalCustomerId,
        @JsonProperty(value = "distributor") final UmpDistributorData distributor,
        @JsonProperty(value = "customerGroup") final UmpCustomerGroupData customerGroup,
        @JsonProperty(value = "communicationLanguage") final String communicationLanguage,
        @JsonProperty(value = "licensingEmail") final String licensingEmail,
        @JsonProperty(value = "operationalStage") final String operationalStage,
        @JsonProperty(value = "imported") final Boolean imported,
        @JsonProperty(value = "companyEmail") final String companyEmail
    ) {
        this.companyData = new UmpCompanyData()
            .withCompanyId(companyId)
            .withCompanyName(companyName)
            .withFriendlyName(friendlyName)
            .withCompanyCountry(companyCountry)
            .withCompanyStatus(companyStatus)
            .withBusinessAddress(businessAddress)
            .withBillingAddress(billingAddress)
            .withCompanyTypes(companyTypes)
            .withCreditLimit(creditLimit)
            .withTaxId(taxId)
            .withBpmdId(bpmdId)
            .withIsManualAppApprovalEnabled(BooleanUtils.toBooleanDefaultIfNull(isManualAppApprovalEnabled, true))
            .withIsOwnAppsPurchaseEnabled(BooleanUtils.toBooleanDefaultIfNull(ownAppsPurchaseEnabled, false))
            .withCostCenter(costCenter)
            .withIsManaged(isManaged)
            .withExternalCustomerId(externalCustomerId)
            .withDistributor(distributor)
            .withCustomerGroup(customerGroup)
            .withCommunicationLanguage(communicationLanguage)
            .withLicensingEmail(licensingEmail)
            .withOperationalStage(operationalStage)
            .withImported(imported)
            .withCompanyEmail(companyEmail);
    }
}
