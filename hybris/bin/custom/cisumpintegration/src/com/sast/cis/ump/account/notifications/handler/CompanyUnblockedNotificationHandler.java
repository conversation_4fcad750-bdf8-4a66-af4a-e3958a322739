package com.sast.cis.ump.account.notifications.handler;

import com.sast.cis.core.service.ObjectMapperService;
import com.sast.cis.ump.account.notifications.NotificationType;
import com.sast.cis.ump.account.notifications.dto.CompanyUnblockedData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CompanyUnblockedNotificationHandler implements NotificationHandler {

    private final ObjectMapperService objectMapperService;

    public CompanyUnblockedNotificationHandler(
        final ObjectMapperService objectMapperService,
        final NotificationHandlerDispatcher notificationHandlerDispatcher) {

        this.objectMapperService = objectMapperService;
        notificationHandlerDispatcher.registerHandler(NotificationType.COMPANY_UNBLOCKED, this);
    }

    @Override
    public void handle(final NotificationPayload notificationPayload) {
        final CompanyUnblockedData companyUnblockedData = readCompanyUnblockedData(notificationPayload.getData());
        LOG.info("Notification for unblocking company with id '{}' received", companyUnblockedData.getCompanyId());
    }

    private CompanyUnblockedData readCompanyUnblockedData(final String payload) {
        return objectMapperService.toObject(payload, CompanyUnblockedData.class);
    }
}
