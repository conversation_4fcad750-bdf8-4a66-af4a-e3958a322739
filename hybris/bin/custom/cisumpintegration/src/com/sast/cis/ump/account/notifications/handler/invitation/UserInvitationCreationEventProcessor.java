package com.sast.cis.ump.account.notifications.handler.invitation;

import com.sast.cis.ump.account.notifications.dto.UserCreationData;
import com.sast.cis.ump.account.notifications.dto.UserInvitationCreationData;
import com.sast.cis.ump.account.notifications.mapper.UserInvitationMapper;
import com.sast.cis.ump.account.notifications.service.UserCreationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserInvitationCreationEventProcessor {

    private final UserCreationService userCreationService;
    private final UserInvitationMapper userInvitationMapper;

    public void process(UserInvitationCreationData userInvitationCreationData) {
        UserCreationData userCreationData = userInvitationMapper.map(userInvitationCreationData);
        userCreationService.createOrUpdateUser(userCreationData);
    }
}
