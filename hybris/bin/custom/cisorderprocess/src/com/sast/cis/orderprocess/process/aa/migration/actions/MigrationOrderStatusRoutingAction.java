package com.sast.cis.orderprocess.process.aa.migration.actions;

import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel;
import com.sast.cis.orderprocess.process.aa.migration.MigrationOrderStatusRoutingTransition;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.processengine.action.AbstractAction;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

import static java.util.Optional.ofNullable;

@Component
@Slf4j
public class MigrationOrderStatusRoutingAction extends AbstractAction<OrderMigrationBusinessProcessModel> {

    @Override
    public String execute(@NonNull final OrderMigrationBusinessProcessModel businessProcess) {

        return determineRoutingTransition(businessProcess).name();
    }

    @Override
    public Set<String> getTransitions() {
        return MigrationOrderStatusRoutingTransition.getStringValues();
    }

    private MigrationOrderStatusRoutingTransition determineRoutingTransition(final OrderMigrationBusinessProcessModel businessProcess) {
        final MigrationOrderDraftModel migrationOrderDraft = businessProcess.getMigrationOrderDraft();
        final OrderModel migrationOrder = ofNullable(migrationOrderDraft.getResultingOrder())
            .orElseThrow(() -> new IllegalStateException("No migration order for draft %s".formatted(migrationOrderDraft.getCode())));
        final OrderStatus status = migrationOrder.getStatus();
        return MigrationOrderStatusRoutingTransition.forOrderStatus(status);
    }
}
