package com.sast.cis.orderprocess.process.aa.migration.actions;

import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.orderprocess.process.aa.migration.MigrationOrderBillingTransition;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.processengine.action.AbstractAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Set;

import static com.sast.cis.core.enums.LicenseType.SUBSCRIPTION;

@Component
@Slf4j
public class MigrationOrderBillingRoutingAction extends AbstractAction<OrderMigrationBusinessProcessModel> {

    @Override
    public String execute(OrderMigrationBusinessProcessModel orderMigrationBusinessProcess) {
        return determineRoutingTransition(orderMigrationBusinessProcess).name();
    }

    @Override
    public Set<String> getTransitions() {
        return MigrationOrderBillingTransition.getStringValues();
    }

    private MigrationOrderBillingTransition determineRoutingTransition(final OrderMigrationBusinessProcessModel orderMigrationBusinessProcess) {
        final MigrationOrderDraftModel migrationOrderDraft = orderMigrationBusinessProcess.getMigrationOrderDraft();
        final OrderModel migrationOrder = migrationOrderDraft.getResultingOrder();

        if (migrationOrder == null || migrationOrder.getEntries() == null || migrationOrder.getEntries().isEmpty()) {
            LOG.error("ALERT Migration Order has null or empty entries");
            return MigrationOrderBillingTransition.ERROR;
        }

        // Check if order is billable (active subscription products)
        final boolean isBillable = isOrderDraftBillable(migrationOrderDraft);

        if (isBillable) {
            LOG.info("Routing billable order {} through BRIM export path", migrationOrder.getCode());
            return MigrationOrderBillingTransition.BILLABLE;
        } else {
            LOG.info("Routing non-billable order {} directly to LMP export", migrationOrder.getCode());
            return MigrationOrderBillingTransition.NON_BILLABLE;
        }
    }

    /**
     * Determines if an order needs to be billed through BRIM
     * @param migrationOrderDraft The order draft
     * @return true if the order contains active subscription products
     */
    private boolean isOrderDraftBillable(final MigrationOrderDraftModel migrationOrderDraft) {
        // If products are inactive, they're not billable
        if (migrationOrderDraft.isInactiveProductMigration()) {
            return false;
        }

        // Only active subscription products are billable
        final OrderModel order = migrationOrderDraft.getResultingOrder();
        return order.getEntries().stream()
            .map(entry -> ((AppLicenseModel) entry.getProduct()).getLicenseType())
            .anyMatch(SUBSCRIPTION::equals);
    }
}
