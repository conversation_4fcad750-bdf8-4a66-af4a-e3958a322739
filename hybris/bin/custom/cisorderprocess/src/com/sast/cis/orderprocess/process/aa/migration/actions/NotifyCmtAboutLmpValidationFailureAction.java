package com.sast.cis.orderprocess.process.aa.migration.actions;

import com.sast.cis.aa.core.model.ContractMigrationProcessModel;
import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.OrderMigrationBusinessProcessModel;
import com.sast.cis.aa.core.model.ProcessResultDetailsModel;
import com.sast.cis.core.cmtintegration.CmtNotificationService;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
@RequiredArgsConstructor
@Slf4j
public class NotifyCmtAboutLmpValidationFailureAction extends AbstractSimpleDecisionAction<OrderMigrationBusinessProcessModel> {

    private final CmtNotificationService cmtNotificationService;

    @Override
    public Transition executeAction(final OrderMigrationBusinessProcessModel migrationBusinessProcess) {
        final ProcessResultDetailsModel processResultDetails = migrationBusinessProcess.getResultDetails();
        final String lmpValidationMessage = getLmpValidationMessage(processResultDetails);
        if (isBlank(lmpValidationMessage)) {
            LOG.error("No LMP Validation message found for Order Migration BP with code: {}", migrationBusinessProcess.getCode());
            return Transition.NOK;
        }

        final MigrationOrderDraftModel migrationOrderDraft = migrationBusinessProcess.getMigrationOrderDraft();
        if (isInvalidMigrationOrderDraft(migrationOrderDraft)) {
            LOG.error("MigrationOrderDraft or ContractMigrationProcess is missing in OrderMigrationBusinessProcessModel with code: {}",
                migrationBusinessProcess.getCode());
            return Transition.NOK;
        }
        final ContractMigrationProcessModel contractMigrationProcess = migrationOrderDraft.getContractMigrationProcess();

        try {
            cmtNotificationService.notifyAboutLmpValidationError(contractMigrationProcess, lmpValidationMessage);
            return Transition.OK;
        } catch (final Exception e) {
            LOG.error("Failed to notify CMT about LMP Validation error for BP with code: {}", migrationBusinessProcess.getCode(), e);
            return Transition.NOK;
        }
    }

    private String getLmpValidationMessage(final ProcessResultDetailsModel processResultDetails) {
        return ofNullable(processResultDetails)
            .map(ProcessResultDetailsModel::getLmpValidationMessage)
            .orElse(EMPTY);
    }

    private boolean isInvalidMigrationOrderDraft(MigrationOrderDraftModel migrationOrderDraft) {
        return migrationOrderDraft == null || migrationOrderDraft.getContractMigrationProcess() == null;
    }
}
