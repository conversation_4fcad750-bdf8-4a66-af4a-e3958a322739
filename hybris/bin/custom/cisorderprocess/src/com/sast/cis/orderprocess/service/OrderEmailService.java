package com.sast.cis.orderprocess.service;

import com.sast.cis.aa.core.model.SellerContactModel;
import com.sast.cis.aa.core.service.AaSellerContactService;
import com.sast.cis.core.config.SpringProfileConfig;
import com.sast.cis.core.config.WebsiteUrlProvider;
import com.sast.cis.core.constants.Environment;
import com.sast.cis.core.data.UmpUserData;
import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.*;
import com.sast.cis.core.security.service.SecuredMediaService;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.core.service.order.CisOrderService;
import com.sast.cis.core.service.singlesignon.UserDataQueryService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.cis.email2.dto.*;
import com.sast.cis.email2.dto.aa.*;
import com.sast.cis.email2.service.Attachment;
import com.sast.cis.email2.service.CisEmailService;
import com.sast.cis.email2.service.EmailLanguageHelper;
import com.sast.cis.email2.service.SendEmailData;
import de.hybris.platform.core.model.ItemModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderEmailService {
    private static final String CAMERAS_URL = "shop.header.cameras.url";
    private static final String MATTHIAS_SERVICEDESK_PROPERTY = "azena.support.emailid";

    private static final Locale DEFAULT_LOCALE = Locale.ENGLISH;
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    private static final String SECOND_EMAIL_FEATURE_TOGGLE = "FEATURE_SECONDARY_EMAIL_FOR_FINANCE";

    @Resource
    private CisEmailService cisEmailService;

    @Resource
    private ConfigurationService configurationService;

    @Resource
    private IntegratorService integratorService;

    @Resource
    private CisOrderService cisOrderService;

    @Resource
    private UserDataQueryService userDataQueryService;

    @Resource
    private SpringProfileConfig springProfileConfig;

    @Resource
    private SecuredMediaService securedMediaService;

    @Resource
    private AaSellerContactService aaSellerContactService;

    @Resource
    private WebsiteUrlProvider websiteUrlProvider;

    @Resource
    private FeatureToggleService featureToggleService;

    public void sendOrderSuccessEmail(OrderModel order) {
        IntegratorModel integrator = getIntegrator(order);
        String integratorPortalUrl = configurationService.getConfiguration().getString(CAMERAS_URL);
        String orderDetailsUrl = websiteUrlProvider.getOrderDetails(order).toString();

        List<OrderItem> orderItems = getOrderItems(order);

        OrderSuccess orderSuccess = new OrderSuccess(
                order.getCode(),
                orderDetailsUrl,
                integratorPortalUrl,
                orderItems,
                integrator.getName(),
                order.getPaymentInfo(),
                order.getStore().getUid(),
                EmailLanguageHelper.determineEmailLanguage(order.getCompany()),
                getSellerContact(order)
        );

        cisEmailService.sendEmail(getOrderSuccessEmailType(orderItems), integrator, orderSuccess);
    }

    public List<OrderItem> getOrderItems(OrderModel order) {
        return order.getEntries()
                .stream()
                .map(item -> {
                    AppLicenseModel product = (AppLicenseModel) item.getProduct();
                    return new OrderItem(
                            product.getBaseProduct().getName(DEFAULT_LOCALE),
                            product.getLicenseType().getCode(),
                            item.getQuantity(), null);
                })
                .toList();
    }

    public void sendInvoiceFetchedEmail(OrderModel order) {
        InvoiceModel latestInvoice = getLatestInvoice(order).orElseThrow();

        InvoiceDownload invoiceDownload = InvoiceDownload.builder()
                .orderCode(order.getCode())
                .customerName(getIntegrator(order).getName())
                .orderDate(order.getDate())
                .orderOverviewUrl(websiteUrlProvider.getOrderOverview(order.getStore()))
                .orderDetailUrl(websiteUrlProvider.getOrderDetails(order))
                .invoiceDownloadUrl(websiteUrlProvider.getInvoiceDownload(latestInvoice))
                .storeUid(order.getStore().getUid())
                .languageIsocode(EmailLanguageHelper.determineEmailLanguage(order.getCompany()))
                .build();

        List<String> invoiceRecipients = determineDocumentRecipients(order);
        SendEmailData sendEmailData = SendEmailData.builder()
                .emailType(EmailType.INVOICE_DOWNLOAD)
                .messageContext(invoiceDownload)
                .to(invoiceRecipients)
                .language(EmailLanguageHelper.determineEmailLanguage(order.getCompany()))
                .build();
        cisEmailService.sendEmail(sendEmailData, getInvoiceAttachment(latestInvoice).orElse(null));
    }

    public void sendCreditNoteEmail(OrderModel order, InvoiceCreditNoteModel creditNoteInvoice) {
        IntegratorModel integrator = getIntegrator(order);
        CreditNote creditNote = new CreditNote(
                integrator.getDisplayName(),
                order.getCode(),
                integrator.getEmailAddress(),
                websiteUrlProvider.getOrderDetails(order).toString(),
                order.getStore() == null ? "" : order.getStore().getUid(),
                EmailLanguageHelper.determineEmailLanguage(order.getCompany())
        );

        List<String> creditNoteRecipients = determineDocumentRecipients(order);
        SendEmailData sendEmailData = SendEmailData.builder()
                .emailType(EmailType.CREDIT_NOTE)
                .messageContext(creditNote)
                .to(creditNoteRecipients)
                .language(EmailLanguageHelper.determineEmailLanguage(order.getCompany()))
                .build();
        cisEmailService.sendEmail(sendEmailData, getCreditNoteInvoiceAttachment(creditNoteInvoice).orElse(null));
    }

    public void sendSepaCreditInvoiceReadyEmail(OrderModel order) {
        IntegratorModel integrator = getIntegrator(order);
        String orderDetailsUrl = websiteUrlProvider.getOrderDetails(order).toString();

        SepaCreditInvoiceNotification invoiceNotification = new SepaCreditInvoiceNotification(
                order.getCode(),
                integrator.getName(),
                getOrderItems(order),
                orderDetailsUrl,
                cisOrderService.getDueDate(order),
                order.getStore().getUid(),
                EmailLanguageHelper.determineEmailLanguage(order.getCompany())
        );

        List<String> invoiceRecipients = determineDocumentRecipients(order);
        SendEmailData sendEmailData = SendEmailData.builder()
                .emailType(EmailType.SEPA_CREDIT_INVOICE_READY)
                .messageContext(invoiceNotification)
                .to(invoiceRecipients)
                .language(EmailLanguageHelper.determineEmailLanguage(order.getCompany()))
                .build();
        cisEmailService.sendEmail(sendEmailData, getLatestInvoice(order).flatMap(this::getInvoiceAttachment).orElse(null));
    }

    public void sendNotifyDeveloperEmail(OrderModel orderModel) {
        Map<String, List<OrderItem>> companyOrderItems = new HashMap<>();
        Map<String, IoTCompanyModel> companyMap = new HashMap<>();
        splitItemsByCompany(orderModel, companyOrderItems, companyMap);
        OrderSuccessNotifyDeveloperData data = getNotifyDeveloperData(orderModel);
        for (Map.Entry<String, List<OrderItem>> entry : companyOrderItems.entrySet()) {
            sendToEachCompany(companyMap.get(entry.getKey()), entry.getValue(), data);
        }
    }

    public void sendNotifyCustomerSupportEmail(OrderModel orderModel) {
        OrderSuccessNotifyCustomerSupportData data = getNotifyCustomerSupportData(orderModel);
        cisEmailService.sendEmail(EmailType.ORDER_SUCCESS_NOTIFY_CUSTOMER_SUPPORT, data);
    }

    private void splitItemsByCompany(OrderModel orderModel, Map<String, List<OrderItem>> companyOrderItems,
                                     Map<String, IoTCompanyModel> companyMap) {
        for (AbstractOrderEntryModel entry : orderModel.getEntries()) {
            AppLicenseModel product = (AppLicenseModel) entry.getProduct();
            AppModel appModel = (AppModel) product.getBaseProduct();
            IoTCompanyModel company = appModel.getCompany();
            companyMap.put(company.getUid(), company);
            if (companyOrderItems.containsKey(company.getUid())) {
                companyOrderItems.get(company.getUid()).add(getOrderItemByEntry(entry));
            } else {
                List<OrderItem> orderItems = new ArrayList<>();
                orderItems.add(getOrderItemByEntry(entry));
                companyOrderItems.put(company.getUid(), orderItems);
            }
        }
    }

    private void sendToEachCompany(IoTCompanyModel company, List<OrderItem> items, OrderSuccessNotifyDeveloperData data) {
        data.setOrderItems(items);
        data.setLanguage(EmailLanguageHelper.determineEmailLanguage(company));
        Set<UmpUserData> managers = userDataQueryService.requestCompanyManagers(company);
        for (UmpUserData manager : managers) {
            data.setRecipientName(manager.getFirstName() + " " + manager.getLastName());
            data.setRecipient(manager.getEmail());
            cisEmailService.sendEmail(EmailType.ORDER_SUCCESS_NOTIFY_DEVELOPER, data);
        }
    }

    private OrderItem getOrderItemByEntry(AbstractOrderEntryModel entry) {
        AppLicenseModel product = (AppLicenseModel) entry.getProduct();
        return new OrderItem(
                product.getBaseProduct().getName(DEFAULT_LOCALE),
                product.getLicenseType().getCode(),
                entry.getQuantity(), null);
    }

    private OrderSuccessNotifyDeveloperData getNotifyDeveloperData(OrderModel orderModel) {
        OrderSuccessNotifyDeveloperData data = new OrderSuccessNotifyDeveloperData();
        UserModel purchaser = orderModel.getUser();
        IoTCompanyModel company = orderModel.getCompany();
        data.setOrderNumber(orderModel.getCode());
        data.setPurchaserCompany(company.getName());
        data.setPurchaserCountry(company.getCountry().getName());
        data.setPurchaserName(purchaser.getName());
        IntegratorModel integrator = getIntegrator(orderModel);
        data.setPurchaserEmail(integrator.getEmailAddress());
        data.setPurchaserName(integrator.getName());
        data.setBaseStoreUid(orderModel.getStore().getUid());
        return data;
    }

    private OrderSuccessNotifyCustomerSupportData getNotifyCustomerSupportData(OrderModel orderModel) {
        OrderSuccessNotifyCustomerSupportData data = new OrderSuccessNotifyCustomerSupportData();
        Environment environment = springProfileConfig.getEnvironment();
        if (environment == null) {
            LOG.error("ALERT The environment is null for the order(code={})", orderModel.getCode());
            throw new IllegalStateException("Environment is null");
        }

        IoTCompanyModel company = orderModel.getCompany();
        if (company == null) {
            throw new IllegalStateException("Buyer Company can't be null");
        }

        Buyer buyer = new Buyer();
        buyer.setCompanyId(company.getUid());
        buyer.setCompanyName(company.getName());
        buyer.setBpmdId(company.getBpmdId());
        buyer.setExternalCustomerId(company.getAaExternalCustomerId());
        if (orderModel.getUser() instanceof IntegratorModel integrator) {
            buyer.setEmailAddress(integrator.getEmailAddress());
        }

        List<AaOrderItem> orderItems = new ArrayList<>();
        orderModel.getEntries().forEach(entry -> orderItems.add(getAaOrderItemByEntry(entry)));

        data.setRecipient(getServicedeskEmailId());
        data.setEnvironment(environment.getName());
        data.setCountry(company.getCountry().getIsocode().toUpperCase());
        data.setOrderItems(orderItems);
        data.setOrderNumber(orderModel.getCode());
        data.setBuyer(buyer);
        data.setInvoiceNote1(orderModel.getFirstInvoiceNote());
        data.setInvoiceNote2(orderModel.getSecondInvoiceNote());

        var distributor = new Distributor();
        if (orderModel.getAaDistributorCompany() != null) {
            var storedDistributor = orderModel.getAaDistributorCompany();
            distributor.setDistributorId(storedDistributor.getUmpId());
            distributor.setDistributorName(storedDistributor.getCompanyName());
        }
        data.setDistributor(distributor);
        return data;
    }

    private AaSellerContact getSellerContact(OrderModel orderModel) {
        SellerContactModel sellerContact = aaSellerContactService.getSellerContactForOrder(orderModel).orElse(null);
        if (sellerContact == null) {
            LOG.error("Cannot determine the SellerContact for the given order(ordernumber={})", orderModel.getCode());
            return null;
        }

        AaSellerContact aaSellerContact = new AaSellerContact();
        aaSellerContact.setName(sellerContact.getName());
        aaSellerContact.setEmail(sellerContact.getEmail());
        aaSellerContact.setPhone(sellerContact.getPhone());
        return aaSellerContact;
    }

    private String getServicedeskEmailId() {
        return configurationService.getConfiguration().getString(MATTHIAS_SERVICEDESK_PROPERTY);
    }

    private AaOrderItem getAaOrderItemByEntry(AbstractOrderEntryModel entry) {
        AppLicenseModel product = (AppLicenseModel) entry.getProduct();
        AaOrderItem aaOrderItem = new AaOrderItem();
        aaOrderItem.setProductCode(product.getCode());
        aaOrderItem.setProductName(product.getBaseProduct().getName(DEFAULT_LOCALE));
        aaOrderItem.setQuantity(entry.getQuantity());
        aaOrderItem.setExternalProductId(product.getSellerProductId());
        aaOrderItem.setLicenseType(product.getLicenseType().getCode());

        List<AaSubscription> subscriptions = CollectionUtils.emptyIfNull(entry.getBuyerContracts())
            .stream()
            .map(buyerContractModel -> new AaSubscription(buyerContractModel.getCode(), dateFormat(buyerContractModel.getStartDate())))
            .collect(Collectors.toList());

        aaOrderItem.setSubscriptions(subscriptions);

        return aaOrderItem;
    }

    private String dateFormat(Date sourceDate) {
        TimeZone timeZone = TimeZone.getTimeZone(ZoneOffset.UTC);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_TIME_FORMAT);
        dateFormat.setTimeZone(timeZone);
        return dateFormat.format(sourceDate);
    }

    private EmailType getOrderSuccessEmailType(List<OrderItem> orderItems) {
        boolean onlyEvaluationLicenses = orderItems.stream()
                .allMatch(item -> item.getLicenseType().equals(LicenseType.EVALUATION.getCode()));

        return onlyEvaluationLicenses ? EmailType.ORDER_SUCCESS_EVAL_ONLY : EmailType.ORDER_SUCCESS;
    }

    private IntegratorModel getIntegrator(OrderModel order) {
        if (order.getUser() == null) {
            throw new IllegalStateException(String.format("Order with code=%s has no user", order.getCode()));
        }
        return integratorService.checkAndCast(order.getUser());
    }

    private Optional<InvoiceModel> getLatestInvoice(OrderModel order) {
        return order.getInvoices().stream()
                .max(Comparator.comparing(ItemModel::getCreationtime));
    }

    private Optional<Attachment> getInvoiceAttachment(InvoiceModel invoice) {
        return getAttachment(invoice.getDocument(), invoice.getExternalId(), invoice.getCode(), invoice.getInvoiceDate());
    }

    private Optional<Attachment> getCreditNoteInvoiceAttachment(InvoiceCreditNoteModel invoice) {
        return getAttachment(invoice.getDocument(), invoice.getExternalId(), invoice.getCode(), invoice.getIssuanceDate());
    }

    private Optional<Attachment> getAttachment(MediaModel document, String invoiceId, String invoiceCode, Date date) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            InputStreamResource mediaStream = securedMediaService.getStream(document)
                    .orElseThrow();

            byte[] invoiceDocumentBytes = mediaStream.getInputStream().readAllBytes();
            var attachment = Attachment.builder()
                    .data(invoiceDocumentBytes)
                    .filename(String.format("%s-%s.pdf", dateFormat.format(date), invoiceId))
                    .build();
            return Optional.of(attachment);
        } catch (NoSuchElementException e) {
            LOG.warn("No data stream for the invoice {} found", invoiceCode);
        } catch (IOException e) {
            LOG.warn("Can't get input stream for the invoice {} media data: {}",
                    invoiceCode, e.getMessage());
        }
        return Optional.empty();
    }

    private List<String> determineDocumentRecipients(OrderModel order) {
        // This is a crude deduplication of email addresses. Of course it wont accurately catch all
        // email addresses which are equal, but that'd be a non-trivial problem to solve
        Set<String> recipients = new HashSet<>();
        Optional.ofNullable(getIntegrator(order).getEmailAddress()).ifPresent(recipients::add);
        if (featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true)) {
            Optional.ofNullable(order.getCompany())
                    .map(IoTCompanyModel::getCompanyEmail)
                    .ifPresent(recipients::add);
        }
        return List.copyOf(recipients);
    }
}
