package com.sast.cis.orderprocess.converter.aa;

import com.sast.cis.core.dto.portal.*;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.customer.InternalCustomerUidTranslationService;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class OrderModelToAaOrderConfirmationDtoPopulator implements Populator<OrderModel, AaOrderConfirmationDto> {
    private final InternalCustomerUidTranslationService<IntegratorModel> internalIntegratorUidTranslationService;
    private final IotCompanyService iotCompanyService;
    private final Converter<AbstractOrderEntryModel, AaOrderEntryDto> abstractOrderEntryModelToAaOrderEntryDtoConverter;

    @Override
    public void populate(OrderModel order, AaOrderConfirmationDto target) {
        IntegratorModel integrator = getIntegratorUserFromOrder(order);
        assertAllProductsAreValidLicenses(order);
        fillOrderConfirmationDto(order, target, integrator);
    }

    private IntegratorModel getIntegratorUserFromOrder(OrderModel order) {
        UserModel user = order.getUser();
        if (!(user instanceof IntegratorModel)) {
            String orderCode = order.getCode();
            LOG.error("User pk={} of order code={} is not an integrator user.", user.getPk(), orderCode);
            throw new IllegalArgumentException("Order " + orderCode + " does not belong to an integrator user.");
        }

        return (IntegratorModel) user;
    }

    private void assertAllProductsAreValidLicenses(OrderModel order) {
        order.getEntries().forEach(entry -> {
            if (!(entry.getProduct() instanceof AppLicenseModel)) {
                LOG.error("Product with code={} is not of type AppLicense!", entry.getProduct());
                throw new IllegalArgumentException("Order contains product(s) that are not licenses.");
            }
        });
    }

    private void fillOrderConfirmationDto(OrderModel order, AaOrderConfirmationDto aaOrderConfirmationDto, IntegratorModel integrator) {
        IoTCompanyModel developerCompany = iotCompanyService.getDeveloperCompanyForOrderItems(order);
        AaDistributorCompanyModel aaDistributor = order.getAaDistributorCompany();

        aaOrderConfirmationDto.withCreationTimestamp(order.getCreationtime())
            .withBuyer(getBuyerDto(integrator))
            .withSeller(getSellerDto(developerCompany))
            .withDistributorCompany(getDistributorCompanyDto(aaDistributor))
            .withOrderId(order.getCode())
            .withOrderStatus(order.getStatus().getCode())
            .withOrderCurrency(order.getCurrency().getIsocode())
            .withOrderEntries(abstractOrderEntryModelToAaOrderEntryDtoConverter.convertAll(order.getEntries()));
    }

    private BuyerDto getBuyerDto(IntegratorModel integrator) {
        return new BuyerDto()
            .withUserId(internalIntegratorUidTranslationService.translateToSsoId(integrator))
            .withCompanyId(integrator.getCompany().getUid());
    }

    private SellerDto getSellerDto(IoTCompanyModel developerCompany) {
        return new SellerDto()
            .withCompanyId(developerCompany.getUid());
    }

    private DistributorCompanyDto getDistributorCompanyDto(AaDistributorCompanyModel aaDistributor) {
        if (aaDistributor == null) return null;
        return new DistributorCompanyDto()
                .withCompanyId(aaDistributor.getUmpId())
                .withCompanyName(aaDistributor.getCompanyName());
    }
}
