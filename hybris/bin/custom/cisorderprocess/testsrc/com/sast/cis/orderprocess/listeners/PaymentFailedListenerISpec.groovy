package com.sast.cis.orderprocess.listeners

import com.amazonaws.services.sns.AmazonSNS
import com.amazonaws.services.sqs.AmazonSQS
import com.sast.cis.core.enums.Feature
import com.sast.cis.core.enums.InvoiceStatus
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.event.invoice.PaymentFailedMailEvent
import com.sast.cis.core.exceptions.CompanyNotFoundException
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.InvoiceModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.SepaCreditTransferPaymentInfoModel
import com.sast.cis.core.service.ObjectMapperService
import com.sast.cis.core.service.customer.integrator.IntegratorService
import com.sast.cis.email2.service.aws.SnsSqsHelper
import com.sast.cis.email2.service.dto.SQSEmailMessage
import com.sast.cis.test.utils.FeatureToggleRule
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.enums.OrderStatus
import de.hybris.platform.core.enums.PaymentStatus
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.core.model.order.payment.PaymentInfoModel
import de.hybris.platform.servicelayer.ServicelayerSpockSpecification
import de.hybris.platform.servicelayer.event.EventService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.testframework.RunListeners
import de.hybris.platform.testframework.runlistener.ItemCreationListener
import generated.com.sast.cis.core.model.InvoiceBuilder
import generated.com.sast.cis.core.model.SepaCreditTransferPaymentInfoBuilder
import org.junit.Rule
import org.junit.Test

import javax.annotation.Resource

import static com.sast.cis.test.utils.TestDataConstants.*

@IntegrationTest
@RunListeners([ItemCreationListener])

class PaymentFailedListenerISpec extends ServicelayerSpockSpecification {

    private final static String SQS_QUEUE_NAME = 'email-service-send-email-queue'
    private static final String SELLER_UID = DEMO_COMPANY_UID
    private static final String INVOICE_ID = "9876543210"
    private static final String INVOICE_CODE = "00002"

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    private String sqsEmailClientQueueUrl
    private SnsSqsHelper sqsHelper
    private IntegratorModel integrator
    private IoTCompanyModel sellerCompany
    private SepaCreditTransferPaymentInfoModel sepaInfo
    private OrderModel sampleOrder
    InvoiceModel invoice

    @Rule
    public FeatureToggleRule featureToggleRule = new FeatureToggleRule()

    @Resource
    IntegratorService integratorService

    @Resource
    AmazonSQS amazonSQS

    @Resource
    AmazonSNS amazonSNS

    @Resource
    ModelService modelService

    @Resource
    private UserService userService

    @Resource
    ObjectMapperService objectMapperService

    @Resource
    EventService eventService


    void setup() {
        featureToggleRule.enable(Feature.FEATURE_EMAIL_CLIENT_ENABLED)
        sqsHelper = new SnsSqsHelper(amazonSQS, amazonSNS, objectMapperService)
        sqsEmailClientQueueUrl = sqsHelper.getQueueURI(SQS_QUEUE_NAME)

        integrator = integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_UID)
        userService.setCurrentUser(userService.getUserForUID(DEMO_COMPANY_DEVELOPER_UID))
        sellerCompany = sampleDataCreator.getIotCompanyForUid(SELLER_UID)
                .orElseThrow(() -> new CompanyNotFoundException("SellerCompany not found", SELLER_UID))
        modelService.saveAll(sellerCompany)

        sepaInfo = createSepaInfo(integrator)
        sampleOrder = createOrder(sepaInfo)
        invoice = createInvoice(sampleOrder)

        sqsHelper.purgeQueue(sqsEmailClientQueueUrl)

    }

    void cleanup() {
        sqsHelper.purgeQueue(sqsEmailClientQueueUrl)
    }

    @Test
    void 'Receiving failed payment Notification triggers an email'() {
        when:
        eventService.publishEvent(new PaymentFailedMailEvent(invoice))
        SQSEmailMessage emailMessage = sqsHelper.retrieveSqsEmailMessage(sqsEmailClientQueueUrl)

        then:
        emailMessage != null
    }


    private SepaCreditTransferPaymentInfoModel createSepaInfo(IntegratorModel integrator) {
        var info = SepaCreditTransferPaymentInfoBuilder.generate()
                .withUser(integrator)
                .withCode("amazingSepaInfo")
                .withPaymentProvider(PaymentProvider.DPG)
                .buildIntegrationInstance()
        modelService.save(info)
        return info
    }

    private OrderModel createOrder(PaymentInfoModel paymentInfo) {
        var order = sampleDataCreator.createOrder(integrator, OrderStatus.COMPLETED)
        order.setPaymentInfo(paymentInfo)
        order.setPaymentStatus(PaymentStatus.PENDING)
        modelService.save(order)
        return order
    }

    private InvoiceModel createInvoice(OrderModel order) {
        final InvoiceModel invoice = InvoiceBuilder.generate()
                .withExternalId(INVOICE_ID)
                .withCode(INVOICE_CODE)
                .withStatus(InvoiceStatus.PENDING)
                .withOrder(Set.of(order))
                .buildIntegrationInstance()
        modelService.save(invoice)
        return invoice


    }
}