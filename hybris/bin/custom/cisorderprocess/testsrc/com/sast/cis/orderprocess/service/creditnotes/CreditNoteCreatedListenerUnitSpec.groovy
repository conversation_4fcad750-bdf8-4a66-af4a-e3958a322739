package com.sast.cis.orderprocess.service.creditnotes

import com.sast.cis.core.billingintegration.dto.CreditNoteCreatedData
import com.sast.cis.core.billingintegration.events.CreditNoteCreatedEvent
import com.sast.cis.core.dao.invoice.InvoiceDao
import com.sast.cis.core.invoice.service.status.InvoiceStatusTransitionDelegate
import com.sast.cis.core.invoice.service.type.CreditNoteTypeResolver
import com.sast.cis.core.model.InvoiceCreditNoteModel
import com.sast.cis.core.model.InvoiceModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.FeatureToggleService
import com.sast.cis.orderprocess.service.OrderEmailService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.media.MediaModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.time.TimeService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

import java.time.Instant

import static com.sast.cis.core.enums.Feature.FEATURE_EMAIL_CREDIT_NOTE_ENABLED

@UnitTest
class CreditNoteCreatedListenerUnitSpec extends JUnitPlatformSpecification {

    InvoiceDao invoiceDao = Mock(InvoiceDao)
    TimeService timeService = Mock(TimeService)
    FeatureToggleService featureToggleService = Mock(FeatureToggleService)
    ModelService modelService = Mock(ModelService)
    CreditNoteTypeResolver creditNoteTypeResolver = Mock(CreditNoteTypeResolver)
    InvoiceStatusTransitionDelegate invoiceStatusTransitionService = Mock(InvoiceStatusTransitionDelegate)
    OrderEmailService orderEmailService = Mock(OrderEmailService)

    CreditNoteCreatedListener creditNoteCreatedListener

    InvoiceCreditNoteModel creditNote = Mock(InvoiceCreditNoteModel)
    InvoiceModel originalInvoice = Mock(InvoiceModel)
    CreditNoteCreatedData creditNoteCreatedData = Mock(CreditNoteCreatedData)
    MediaModel creditNoteMedia = Mock(MediaModel)
    CreditNoteCreatedEvent creditNoteCreatedEvent = Mock(CreditNoteCreatedEvent)
    OrderModel order = Mock(OrderModel)
    IoTCompanyModel company = Mock(IoTCompanyModel)

    String originalInvoiceId = "O123455"
    String creditNoteId = "O123456"
    BigDecimal invoiceNetAmount = BigDecimal.valueOf(100.00d)
    BigDecimal invoiceTaxAmount = BigDecimal.valueOf(19.00d)
    BigDecimal invoiceGrossAmount = BigDecimal.valueOf(119.00d)

    def setup() {
        creditNoteCreatedListener = new CreditNoteCreatedListener(
                modelService, orderEmailService, invoiceDao, timeService,
                featureToggleService, creditNoteTypeResolver, invoiceStatusTransitionService
        )

        creditNoteCreatedEvent.getCreditNoteCreatedData() >> creditNoteCreatedData
        creditNoteCreatedEvent.getCreditNoteMedia() >> creditNoteMedia
        creditNoteCreatedData.getInvoiceId() >> creditNoteId
        creditNoteCreatedData.getOriginalInvoice() >> originalInvoiceId
        creditNoteCreatedData.getTaxAmount() >> invoiceTaxAmount
        creditNoteCreatedData.getGrossAmount() >> invoiceGrossAmount
        creditNoteCreatedData.getNetAmount() >> invoiceNetAmount

        originalInvoice.getOrder() >> Set.of(order)
        order.getCompany() >> company

        modelService.create(InvoiceCreditNoteModel.class) >> creditNote

        invoiceDao.getByExternalIdOrThrow(originalInvoiceId) >> originalInvoice
        featureToggleService.isEnabledOrDefault(FEATURE_EMAIL_CREDIT_NOTE_ENABLED, false) >> true
        timeService.getCurrentTime() >> Date.from(Instant.EPOCH)
    }

    @Test
    def 'when process event then create credit note and associate with original invoice'() {
        when:
        creditNoteCreatedListener.onEvent(creditNoteCreatedEvent)

        then:
        1 * creditNote.setExternalId(creditNoteId)
        1 * creditNote.setOriginalInvoice(originalInvoice)
        1 * creditNote.setTaxAmount(invoiceTaxAmount)
        1 * creditNote.setGrossAmount(invoiceGrossAmount)
        1 * creditNote.setNetAmount(invoiceNetAmount)
        1 * modelService.saveAll(creditNote, creditNoteMedia)
        1 * creditNoteMedia.setPermittedPrincipals(Collections.singleton(company))
        1 * invoiceStatusTransitionService.updateInvoiceStatusOnCreditNoteCreation(originalInvoice)
        1 * orderEmailService.sendCreditNoteEmail(order, creditNote)
    }

    @Test
    def 'credit note invoice email feature toggle is disabled'() {
        when:
        creditNoteCreatedListener.onEvent(creditNoteCreatedEvent)

        then:
        featureToggleService.isEnabledOrDefault(FEATURE_EMAIL_CREDIT_NOTE_ENABLED, false) >> false
        0 * orderEmailService.sendCreditNoteEmail(order, creditNote)
    }
}
