package com.sast.cis.orderprocess.converter.aa

import com.sast.cis.core.dto.portal.AaOrderConfirmationDto
import com.sast.cis.core.model.BuyerContractModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.servicelayer.dto.converter.Converter
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class AaOrderReconfigurationDtoConverterUnitSpec extends JUnitPlatformSpecification {

    private Converter<OrderModel, AaOrderConfirmationDto> orderModelToAaOrderConfirmationDtoConverter = Mock()
    private AaOrderReconfigurationDtoConverter converter

    private OrderModel reconfiguredOrder = Mock()
    private BuyerContractModel reconfiguredContract = Mock()

    private AbstractOrderEntryModel masterEntry = Mock()
    private BuyerContractModel masterContract = Mock()
    private AbstractOrderEntryModel thlEntry = Mock()
    private BuyerContractModel thlContract = Mock()

    private AaOrderConfirmationDto orderConfirmationDto = Mock()

    private final String reconfiguredContractCode = "reconfiguredContractCode"
    private final String masterContractCode = "masterContractCode"
    private final String thlContractCode = "thlContractCode"

    void setup() {
        converter = new AaOrderReconfigurationDtoConverter(orderModelToAaOrderConfirmationDtoConverter)

        reconfiguredContract.getCode() >> reconfiguredContractCode
        reconfiguredOrder.getEntries() >> [masterEntry, thlEntry]

        masterEntry.getBuyerContracts() >> [masterContract]
        masterContract.getCode() >> masterContractCode

        thlEntry.getBuyerContracts() >> [thlContract]
        thlContract.getCode() >> thlContractCode

        orderModelToAaOrderConfirmationDtoConverter.convert(reconfiguredOrder) >> orderConfirmationDto
    }

    def "should convert to AaOrderReconfigurationDto"() {
        when:
        def result = converter.convertToOrderReconfigurationDto(reconfiguredOrder, reconfiguredContract)

        then:
        result
        result.getOrder() == orderConfirmationDto
        result.getReconfiguredContracts() == [(reconfiguredContractCode): [masterContractCode, thlContractCode]]
    }
}
