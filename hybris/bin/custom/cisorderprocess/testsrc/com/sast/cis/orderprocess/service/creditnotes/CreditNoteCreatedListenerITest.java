package com.sast.cis.orderprocess.service.creditnotes;

import com.sast.cis.core.billingintegration.dto.CreditNoteCreatedData;
import com.sast.cis.core.billingintegration.events.CreditNoteCreatedEvent;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.dao.invoice.InvoiceDao;
import com.sast.cis.core.enums.InvoiceStatus;
import com.sast.cis.core.invoice.service.status.InvoiceStatusTransitionDelegate;
import com.sast.cis.core.invoice.service.type.CreditNoteTypeResolver;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.InvoiceCreditNoteModel;
import com.sast.cis.core.model.InvoiceModel;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.orderprocess.service.OrderEmailService;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.catalog.CatalogVersionService;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.event.EventService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.time.TimeService;
import generated.com.sast.cis.core.model.InvoiceBuilder;
import generated.de.hybris.platform.core.model.media.MediaBuilder;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.MediaType;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Set;
import java.util.UUID;

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG;
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_INTEGRATOR_UID;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class CreditNoteCreatedListenerITest extends ServicelayerTransactionalTest {

    @Resource
    private ModelService modelService;

    @Resource
    private OrderEmailService orderEmailService;

    @Resource
    private IntegratorService integratorService;

    @Resource
    private CatalogVersionService catalogVersionService;

    @Resource
    private TimeService timeService;

    @Resource
    private FeatureToggleService featureToggleService;

    @Resource
    private EventService eventService;

    @Resource
    private InvoiceDao invoiceDao;

    @Resource
    private CreditNoteTypeResolver creditNoteTypeResolver;

    @Resource
    private InvoiceStatusTransitionDelegate invoiceStatusTransitionDelegate;

    private CreditNoteCreatedListener creditNoteCreatedListener;
    private InvoiceModel invoice;
    private MediaModel creditNoteMedia;
    private OrderModel order;

    private final BigDecimal invoiceNetAmount = BigDecimal.valueOf(100.00d);
    private final BigDecimal invoiceTaxAmount = BigDecimal.valueOf(19.00d);
    private final BigDecimal invoiceGrossAmount = BigDecimal.valueOf(119.00d);

    @Before
    public void setUp() throws Exception {

        creditNoteCreatedListener = new CreditNoteCreatedListener(
                modelService,
                orderEmailService,
                invoiceDao,
                timeService,
                featureToggleService,
                creditNoteTypeResolver,
                invoiceStatusTransitionDelegate
        );
        eventService.registerEventListener(creditNoteCreatedListener);

        final CatalogVersionModel stagedCatalogVersion = catalogVersionService
                .getCatalogVersion(CIS_PRODUCT_CATALOG, CatalogVersion.STAGED.getVersionName());
        final IntegratorModel integrator =
                integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_UID);

        order = OrderBuilder.generate()
                .withCurrency(integrator.getSessionCurrency())
                .withCompany(integrator.getCompany())
                .withUser(integrator)
                .withDate(Date.from(Instant.now()))
                .withCode(UUID.randomUUID().toString())
                .buildIntegrationInstance();

        final MediaModel invoiceMedia = MediaBuilder.generate()
                .withCode("InvoiceMedia")
                .withMime(MediaType.APPLICATION_PDF_VALUE)
                .withRealFileName("invoiceMedia")
                .withCatalogVersion(stagedCatalogVersion)
                .buildIntegrationInstance();

        invoice = InvoiceBuilder.generate()
                .withExternalId(UUID.randomUUID().toString())
                .withStatus(InvoiceStatus.PENDING)
                .withOrder(Set.of(order))
                .withDocument(invoiceMedia)
                .withGrossAmount(invoiceGrossAmount)
                .withTaxAmount(invoiceTaxAmount)
                .withNetAmount(invoiceNetAmount)
                .buildIntegrationInstance();

        creditNoteMedia = MediaBuilder.generate()
                .withCode("CreditNoteMedia")
                .withMime(MediaType.APPLICATION_PDF_VALUE)
                .withRealFileName("creditNoteMedia")
                .withCatalogVersion(stagedCatalogVersion)
                .buildIntegrationInstance();

        modelService.saveAll(order, invoiceMedia, invoice, creditNoteMedia);
    }

    @After
    public void cleanup() {
        eventService.unregisterEventListener(creditNoteCreatedListener);
    }

    @Test
    public void onEvent_addCreditNoteToInvoice() {
        final CreditNoteCreatedData creditNoteCreatedData = new CreditNoteCreatedData();
        creditNoteCreatedData.setInvoiceId("credit_note_ext_id_1");
        creditNoteCreatedData.setInvoiceDate(LocalDate.now());
        creditNoteCreatedData.setNetAmount(invoiceNetAmount);
        creditNoteCreatedData.setTaxAmount(invoiceTaxAmount);
        creditNoteCreatedData.setGrossAmount(invoiceGrossAmount);
        creditNoteCreatedData.setInvoiceDate(LocalDate.ofInstant(Instant.now(), ZoneId.of("UTC")));
        creditNoteCreatedData.setOriginalInvoice(invoice.getExternalId());

        creditNoteCreatedListener.onEvent(new CreditNoteCreatedEvent(creditNoteCreatedData, creditNoteMedia));

        modelService.refresh(invoice);
        assertThat(invoice.getStatus()).isEqualTo(InvoiceStatus.REVERSED);
        final Set<InvoiceCreditNoteModel> creditNotes = invoice.getCreditNotes();
        assertThat(creditNotes).hasSize(1);
        final InvoiceCreditNoteModel creditNote = creditNotes.iterator().next();
        assertThat(creditNote.getOriginalInvoice()).isEqualTo(invoice);
        assertThat(creditNote.getNetAmount()).isEqualTo(invoiceNetAmount);
        assertThat(creditNote.getTaxAmount()).isEqualTo(invoiceTaxAmount);
        assertThat(creditNote.getGrossAmount()).isEqualTo(invoiceGrossAmount);
        final MediaModel creditNoteMedia = creditNote.getDocument();
        assertThat(creditNoteMedia).isNotNull();
        assertThat(creditNoteMedia.getPermittedPrincipals()).containsExactly(order.getCompany());
    }
}
