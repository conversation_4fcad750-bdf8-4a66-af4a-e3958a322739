package com.sast.cis.orderprocess.service;

import com.sast.cis.aa.core.model.SellerContactModel;
import com.sast.cis.aa.core.service.AaSellerContactService;
import com.sast.cis.core.config.SpringProfileConfig;
import com.sast.cis.core.config.WebsiteUrlProvider;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.constants.Environment;
import com.sast.cis.core.data.UmpUserData;
import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.*;
import com.sast.cis.core.security.service.SecuredMediaService;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.core.service.order.CisOrderService;
import com.sast.cis.core.service.singlesignon.UserDataQueryService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.cis.email2.dto.InvoiceDownload;
import com.sast.cis.email2.dto.OrderSuccess;
import com.sast.cis.email2.dto.OrderSuccessNotifyDeveloperData;
import com.sast.cis.email2.dto.SepaCreditInvoiceNotification;
import com.sast.cis.email2.dto.aa.CreditNote;
import com.sast.cis.email2.dto.aa.OrderSuccessNotifyCustomerSupportData;
import com.sast.cis.email2.service.Attachment;
import com.sast.cis.email2.service.CisEmailService;
import com.sast.cis.email2.service.SendEmailData;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import de.hybris.platform.store.BaseStoreModel;
import generated.com.sast.cis.core.model.*;
import generated.de.hybris.platform.core.model.c2l.CountryBuilder;
import generated.de.hybris.platform.core.model.c2l.LanguageBuilder;
import generated.de.hybris.platform.core.model.media.MediaBuilder;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import generated.de.hybris.platform.core.model.order.OrderEntryBuilder;
import generated.de.hybris.platform.store.BaseStoreBuilder;
import lombok.SneakyThrows;
import org.apache.commons.configuration.Configuration;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;
import org.springframework.core.io.InputStreamResource;

import java.io.InputStream;
import java.net.URI;
import java.time.LocalDate;
import java.util.*;

import static java.time.Month.APRIL;
import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(DataProviderRunner.class)
public class OrderEmailServiceUnitTest {
    private static final String CAMERAS_URL_KEY = "shop.header.cameras.url";
    private static final String CAMERAS_URL_VALUE = "http://magnificent-camera-url.test";
    private static final String STORE_URL_KEY = "website.iotstore.https";
    private static final String STORE_URL_VALUE = "http://excellent-store-url.test";
    private static final String ORDER_CODE = "Order12354";
    private static final Date ORDER_DATE = new Date();
    private static final String PRODUCT_CODE = "A_00000302";
    private static final String PURCHASER_FIST_NAME = "Tim";
    private static final String PURCHASER_LAST_NAME = "Installer";
    private static final String PURCHASER_COUNTRY_NAME = "Germany";
    private static final String PURCHASER_COUNTRY_ISO_CODE = "de";
    private static final String PURCHASER_EMAIL = "<EMAIL>";
    private static final String PURCHASER_COMPANY_NAME = "ABC";
    private static final String PURCHASER_COMPANY_EMAIL = "<EMAIL>";
    private static final String PURCHASER_COMPANY_LANGUAGE = "de";
    private static final String MANAGER_EMAIL = "<EMAIL>";
    private static final String MANAGER_FIST_NAME = "Admin";
    private static final String MANAGER_LAST_NAME = "Developer";
    private static final String CUSTOMER_SUPPORT_EMAILID_PROP = "azena.support.emailid";
    private static final String CUSTOMER_SUPPORT_EMAILID = "<EMAIL>";
    public static final String BPMD_ID = "testBPMD_Id";
    public static final String EXTERNAL_PRODUCT_ID = "testSellerProduct_Id";
    public static final String BRIM_ID = "testBRIM_Id";
    private static final Date NOW = Date.from(LocalDate.of(2011, APRIL, 1).atTime(13, 14, 15).toInstant(UTC));
    private static final String OLD_INVOICE_ID = "00112233";
    private static final String NEW_INVOICE_ID = "00112244";
    private static final Date OLD_INVOICE_DATE = Date.from(NOW.toInstant().minusSeconds(100_000L));
    private static final Date NEW_INVOICE_DATE = Date.from(NOW.toInstant().minusSeconds(10L));
    private static final byte[] INVOICE_DATA = new byte[10];
    private static final Attachment EXPECTED_ATTACHMENT = Attachment.builder().filename("20110401-00112244.pdf").data(INVOICE_DATA).build();
    private static final String SELLER_CONTACT_NAME = "SellerContactName";
    private static final String SELLER_CONTACT_EMAIL = "SellerContactEmail";
    private static final String SELLER_CONTACT_PHONE = "SellerContactPhone";
    private static final String NEW_INVOICE_CODE = "newInvoiceCodeFoo";

    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private CisEmailService cisEmailService;

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private Configuration mockConfiguration;

    @Mock
    private IntegratorModel mockIntegrator;

    @Mock
    private IntegratorService integratorService;

    @Mock
    private CisOrderService cisOrderService;

    @Mock
    private UserDataQueryService userDataQueryService;

    @Mock
    private SpringProfileConfig springProfileConfig;

    @Mock
    private SecuredMediaService securedMediaService;

    @Mock
    private AaSellerContactService aaSellerContactService;

    @Mock
    private WebsiteUrlProvider websiteUrlProvider;

    @Mock
    private FeatureToggleService featureToggleService;

    @Captor
    private ArgumentCaptor<OrderSuccess> orderSuccessCaptor;

    @Captor
    private ArgumentCaptor<OrderSuccessNotifyDeveloperData> orderSuccessNotifyDeveloperCaptor;

    @Captor
    private ArgumentCaptor<OrderSuccessNotifyCustomerSupportData> orderSuccessNotifyAaServiceDeskDataCaptor;

    @Captor
    private ArgumentCaptor<SendEmailData> sendEmailDataCaptor;

    @InjectMocks
    private OrderEmailService orderEmailService;

    @Mock
    private InputStreamResource mockInputStreamResource;

    @Mock
    private InputStream mockInputStream;

    @Mock
    private SellerContactModel sellerContact;

    @Mock
    private CountryModel country;

    private OrderModel mockOrder;
    private Set<InvoiceModel> orderInvoices;
    private InvoiceModel newerInvoice;
    private InvoiceCreditNoteModel invoiceCreditNoteModel;


    @Before
    public void setUp() throws Exception {
        MediaModel oldInvoiceModel = MediaBuilder.generate().buildMockInstance();
        MediaModel newerInvoiceModel = MediaBuilder.generate().buildMockInstance();

        InvoiceModel olderInvoice = InvoiceBuilder.generate()
                .withInvoiceDate(OLD_INVOICE_DATE)
                .withExternalId(OLD_INVOICE_ID)
                .withDocument(oldInvoiceModel)
                .withCreationtime(OLD_INVOICE_DATE)
                .buildMockInstance();
        newerInvoice = InvoiceBuilder.generate()
                .withInvoiceDate(NEW_INVOICE_DATE)
                .withExternalId(NEW_INVOICE_ID)
                .withDocument(newerInvoiceModel)
                .withCreationtime(NEW_INVOICE_DATE)
                .buildMockInstance();
        invoiceCreditNoteModel = InvoiceCreditNoteBuilder.generate()
                .withIssuanceDate(NEW_INVOICE_DATE)
                .withExternalId(NEW_INVOICE_ID)
                .withDocument(MediaBuilder.generate().buildMockInstance())
                .withCreationtime(NEW_INVOICE_DATE)
                .buildMockInstance();
        orderInvoices = Set.of(olderInvoice, newerInvoice);
        when(securedMediaService.getStream(oldInvoiceModel)).thenReturn(Optional.of(mockInputStreamResource));
        when(securedMediaService.getStream(newerInvoiceModel)).thenReturn(Optional.of(mockInputStreamResource));
        when(securedMediaService.getStream(invoiceCreditNoteModel.getDocument())).thenReturn(Optional.of(mockInputStreamResource));
        when(mockInputStreamResource.getInputStream()).thenReturn(mockInputStream);
        when(mockInputStream.readAllBytes()).thenReturn(INVOICE_DATA);
        when(aaSellerContactService.getSellerContactForOrder(any(OrderModel.class))).thenReturn(Optional.of(sellerContact));
        when(sellerContact.getCountry()).thenReturn(country);
        when(sellerContact.getName()).thenReturn(SELLER_CONTACT_NAME);
        when(sellerContact.getEmail()).thenReturn(SELLER_CONTACT_EMAIL);
        when(sellerContact.getPhone()).thenReturn(SELLER_CONTACT_PHONE);
        when(featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true))
                .thenReturn(Boolean.FALSE);
    }

    @DataProvider
    public static Object[][] orderItemsToEmailTypeData() {
        return new Object[][]{
                {List.of(), EmailType.ORDER_SUCCESS_EVAL_ONLY},
                {List.of(createOrderItem(LicenseType.FULL)), EmailType.ORDER_SUCCESS},
                {List.of(createOrderItem(LicenseType.EVALUATION)), EmailType.ORDER_SUCCESS_EVAL_ONLY},
                {List.of(createOrderItem(LicenseType.EVALUATION), createOrderItem(LicenseType.FULL)),
                        EmailType.ORDER_SUCCESS}
        };
    }

    private static OrderEntryModel createOrderItem(LicenseType licenseType) {
        IoTCompanyModel seller = IoTCompanyBuilder.generate().withUid("1123222").withName("seller").buildMockInstance();

        return OrderEntryBuilder.generate()
                .withProduct(AppLicenseBuilder.generate()
                        .withBaseProduct(AppBuilder.generate()
                                .withName("asdf")
                                .withCompany(seller)
                                .buildMockInstance())
                        .withSellerProductId(EXTERNAL_PRODUCT_ID)
                        .withLicenseType(licenseType)
                        .withCode(PRODUCT_CODE)
                        .buildMockInstance())
                .withQuantity(3L)
                .buildMockInstance();
    }

    private OrderEntryModel createOrderItemWithSubscription(LicenseType licenseType) {
        IoTCompanyModel seller = IoTCompanyBuilder.generate().withUid("1123222").withName("seller").buildMockInstance();
        SubscriptionContractModel subscriptionContractModel = new SubscriptionContractModel();
        subscriptionContractModel.setCode(BRIM_ID);
        subscriptionContractModel.setStartDate(NOW);

        return OrderEntryBuilder.generate()
            .withProduct(AppLicenseBuilder.generate()
                .withBaseProduct(AppBuilder.generate()
                    .withName("asdf").withCompany(seller)
                    .buildMockInstance())
                .withLicenseType(licenseType)
                .withSellerProductId(EXTERNAL_PRODUCT_ID)
                .withCode(PRODUCT_CODE)
                .buildMockInstance())
            .withQuantity(3L)
            .withBuyerContracts(List.of(subscriptionContractModel))
            .buildMockInstance();
    }

    private static IoTCompanyModel createPurchaserCompany() {
        CountryModel countryModel = CountryBuilder.generate()
                .withName(PURCHASER_COUNTRY_NAME)
                .withIsocode(PURCHASER_COUNTRY_ISO_CODE)
                .buildMockInstance();
        return IoTCompanyBuilder.generate()
                .withName(PURCHASER_COMPANY_NAME)
                .withCountry(countryModel)
                .withCommunicationLanguage(LanguageBuilder.generate().withIsocode(PURCHASER_COMPANY_LANGUAGE).buildInstance())
                .withCompanyEmail(PURCHASER_COMPANY_EMAIL)
                .buildMockInstance();
    }

    @Before
    public void prepareMocks() throws Exception {
        when(configurationService.getConfiguration()).thenReturn(mockConfiguration);
        when(mockConfiguration.getString(CAMERAS_URL_KEY)).thenReturn(CAMERAS_URL_VALUE);
        when(mockConfiguration.getString(STORE_URL_KEY)).thenReturn(STORE_URL_VALUE);
        when(integratorService.checkAndCast(mockIntegrator)).thenReturn(mockIntegrator);
        when(mockIntegrator.getName()).thenReturn(PURCHASER_FIST_NAME + " " + PURCHASER_LAST_NAME);
        when(mockIntegrator.getEmailAddress()).thenReturn(PURCHASER_EMAIL);
        when(mockIntegrator.getDisplayName()).thenReturn(PURCHASER_FIST_NAME + " " + PURCHASER_LAST_NAME);
        when(cisOrderService.getDueDate(any())).thenReturn(DateUtils.addDays(new Date(), 30));
        IoTCompanyModel integratorCompany = createPurchaserCompany();
        BaseStoreModel baseStore = BaseStoreBuilder.generate().withUid(BaseStoreEnum.AZENA.getBaseStoreUid()).buildMockInstance();
        when(integratorCompany.getStore()).thenReturn(baseStore);
        when(mockIntegrator.getCompany()).thenReturn(integratorCompany);


        mockOrder = OrderBuilder.generate()
                .withCode(ORDER_CODE)
                .withUser(mockIntegrator)
                .withDate(ORDER_DATE)
                .withStore(baseStore)
                .withCompany(integratorCompany)
                .buildMockInstance();
        when(springProfileConfig.getEnvironment()).thenReturn(Environment.DEV);
        when(integratorCompany.getBpmdId()).thenReturn(BPMD_ID);
        when(mockConfiguration.getString(CUSTOMER_SUPPORT_EMAILID_PROP)).thenReturn(CUSTOMER_SUPPORT_EMAILID);
        when(websiteUrlProvider.getOrderOverview(baseStore)).thenReturn(new URI(STORE_URL_VALUE + "/my-account/orders/"));
        when(websiteUrlProvider.getOrderDetails(mockOrder)).thenReturn(new URI(STORE_URL_VALUE + "/my-account/orders/" + ORDER_CODE));
        when(websiteUrlProvider.getInvoiceDownload(newerInvoice)).thenReturn(new URI(STORE_URL_VALUE + "/invoices/" + NEW_INVOICE_CODE));
    }

    @Test
    public void testSendSuccessEmail() {
        List<AbstractOrderEntryModel> mockOrderEntries = List.of(createOrderItem(LicenseType.FULL));
        when(mockOrder.getEntries()).thenReturn(mockOrderEntries);
        orderEmailService.sendOrderSuccessEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(eq(EmailType.ORDER_SUCCESS), eq(mockIntegrator), orderSuccessCaptor.capture());
        OrderSuccess actualOrderSuccess = orderSuccessCaptor.getValue();
        assertThat(actualOrderSuccess.getOrderCode()).isEqualTo(ORDER_CODE);
        assertThat(actualOrderSuccess.getOrderItems()).hasSameSizeAs(mockOrderEntries);
        assertThat(actualOrderSuccess.getOrderCode()).isEqualTo(mockOrder.getCode());
        assertThat(actualOrderSuccess.getIntegratorPortalUrl())
                .isEqualTo(CAMERAS_URL_VALUE);
        assertThat(actualOrderSuccess.getOrderDetailsUrl())
                .contains(STORE_URL_VALUE + "/my-account/orders/" + mockOrder.getCode());
        assertThat(actualOrderSuccess.getAaSellerContact().getName()).isEqualTo(SELLER_CONTACT_NAME);
        assertThat(actualOrderSuccess.getAaSellerContact().getEmail()).isEqualTo(SELLER_CONTACT_EMAIL);
        assertThat(actualOrderSuccess.getAaSellerContact().getPhone()).isEqualTo(SELLER_CONTACT_PHONE);
    }

    @Test
    public void testSendSuccessEmail_with_no_seller_contact() {
        List<AbstractOrderEntryModel> mockOrderEntries = List.of(createOrderItem(LicenseType.FULL));
        when(aaSellerContactService.getSellerContactForOrder(any(OrderModel.class))).thenReturn(Optional.empty());
        when(mockOrder.getEntries()).thenReturn(mockOrderEntries);
        orderEmailService.sendOrderSuccessEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(eq(EmailType.ORDER_SUCCESS), eq(mockIntegrator), orderSuccessCaptor.capture());
        OrderSuccess actualOrderSuccess = orderSuccessCaptor.getValue();
        assertThat(actualOrderSuccess.getOrderCode()).isEqualTo(ORDER_CODE);
        assertThat(actualOrderSuccess.getOrderItems()).hasSameSizeAs(mockOrderEntries);
        assertThat(actualOrderSuccess.getOrderCode()).isEqualTo(mockOrder.getCode());
        assertThat(actualOrderSuccess.getIntegratorPortalUrl())
                .isEqualTo(CAMERAS_URL_VALUE);
        assertThat(actualOrderSuccess.getOrderDetailsUrl())
                .contains(STORE_URL_VALUE + "/my-account/orders/" + mockOrder.getCode());
        assertThat(actualOrderSuccess.getAaSellerContact()).isNull();
    }

    @Test
    @UseDataProvider("orderItemsToEmailTypeData")
    public void testSendSuccessEmailOnlyEvalLicenses(List<AbstractOrderEntryModel> givenOrderItems, EmailType expectedEmailType) {
        when(mockOrder.getEntries()).thenReturn(givenOrderItems);
        orderEmailService.sendOrderSuccessEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(eq(expectedEmailType), eq(mockIntegrator), any(OrderSuccess.class));
    }

    @Test
    @SneakyThrows
    public void sendInvoice() {
        when(mockOrder.getInvoices()).thenReturn(orderInvoices);

        InvoiceDownload expectedInvoiceDownload = InvoiceDownload.builder()
                .orderCode(ORDER_CODE)
                .customerName(PURCHASER_FIST_NAME + " " + PURCHASER_LAST_NAME)
                .orderDate(ORDER_DATE)
                .orderOverviewUrl(new URI(STORE_URL_VALUE + "/my-account/orders/"))
                .orderDetailUrl(new URI(STORE_URL_VALUE + "/my-account/orders/" + ORDER_CODE))
                .invoiceDownloadUrl(new URI(STORE_URL_VALUE + "/invoices/" + NEW_INVOICE_CODE))
                .storeUid("iotstore")
                .languageIsocode("de")
                .build();

        orderEmailService.sendInvoiceFetchedEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.INVOICE_DOWNLOAD);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(InvoiceDownload.class).isEqualTo(expectedInvoiceDownload);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void sendInvoice_alsoSendsToCompanyEmail() {
        when(featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true))
                .thenReturn(Boolean.TRUE);
        when(mockOrder.getInvoices()).thenReturn(orderInvoices);
        orderEmailService.sendInvoiceFetchedEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.INVOICE_DOWNLOAD);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL, PURCHASER_COMPANY_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(InvoiceDownload.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void sendInvoice_handlesEmptyCompanyEmailGracefully() {
        when(featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true))
                .thenReturn(Boolean.TRUE);
        when(mockOrder.getInvoices()).thenReturn(orderInvoices);
        when(mockOrder.getCompany().getCompanyEmail()).thenReturn(null);

        orderEmailService.sendInvoiceFetchedEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.INVOICE_DOWNLOAD);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(InvoiceDownload.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void test_sendSepaCreditInvoiceReadyNotification_sepaCreditTransferOrder_sendsEmailWithoutInvoice() {
        when(mockOrder.getInvoices()).thenReturn(Set.of());
        orderEmailService.sendSepaCreditInvoiceReadyEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), isNull());
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.SEPA_CREDIT_INVOICE_READY);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(SepaCreditInvoiceNotification.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void test_sendSepaCreditInvoiceReadyNotification_sepaCreditTransferOrder_sendsEmailWithInvoice() {
        when(mockOrder.getInvoices()).thenReturn(orderInvoices);
        orderEmailService.sendSepaCreditInvoiceReadyEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.SEPA_CREDIT_INVOICE_READY);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(SepaCreditInvoiceNotification.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void test_sendSepaCreditInvoiceReadyNotification_sepaCreditTransferOrder_alsoSendsToCompanyEmail() {
        when(featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true))
                .thenReturn(Boolean.TRUE);
        when(mockOrder.getInvoices()).thenReturn(orderInvoices);
        orderEmailService.sendSepaCreditInvoiceReadyEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.SEPA_CREDIT_INVOICE_READY);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL, PURCHASER_COMPANY_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(SepaCreditInvoiceNotification.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void test_sendSepaCreditInvoiceReadyNotification_sepaCreditTransferOrder_handlesEmptyCompanyEmailGracefully() {
        when(featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true))
                .thenReturn(Boolean.TRUE);
        when(mockOrder.getInvoices()).thenReturn(orderInvoices);
        when(mockOrder.getCompany().getCompanyEmail()).thenReturn(null);
        orderEmailService.sendSepaCreditInvoiceReadyEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.SEPA_CREDIT_INVOICE_READY);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(SepaCreditInvoiceNotification.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void test_sendNotifyDeveloperEmail() {
        List<AbstractOrderEntryModel> mockOrderEntries = List.of(createOrderItem(LicenseType.FULL));
        when(mockOrder.getEntries()).thenReturn(mockOrderEntries);
        Set<UmpUserData> managers = createManagers();
        when(userDataQueryService.requestCompanyManagers(any())).thenReturn(managers);
        orderEmailService.sendNotifyDeveloperEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(eq(EmailType.ORDER_SUCCESS_NOTIFY_DEVELOPER), orderSuccessNotifyDeveloperCaptor.capture());
        OrderSuccessNotifyDeveloperData notifyDeveloperData = orderSuccessNotifyDeveloperCaptor.getValue();
        assertThat(notifyDeveloperData.getOrderNumber()).isEqualTo(ORDER_CODE);
        assertThat(notifyDeveloperData.getOrderItems()).hasSameSizeAs(mockOrderEntries);
        assertThat(notifyDeveloperData.getPurchaserEmail()).isEqualTo(PURCHASER_EMAIL);
        assertThat(notifyDeveloperData.getPurchaserName()).isEqualTo(PURCHASER_FIST_NAME + " " + PURCHASER_LAST_NAME);
        assertThat(notifyDeveloperData.getPurchaserCompany()).isEqualTo(PURCHASER_COMPANY_NAME);
        assertThat(notifyDeveloperData.getPurchaserCountry()).isEqualTo(PURCHASER_COUNTRY_NAME);
        assertThat(notifyDeveloperData.getRecipient()).isEqualTo(MANAGER_EMAIL);
        assertThat(notifyDeveloperData.getRecipientName()).isEqualTo(MANAGER_FIST_NAME + " " + MANAGER_LAST_NAME);
    }

    @Test
    public void test_sendNotifyAaServiceDeskEmail_withSubscription() {
        OrderEntryModel orderEntry = createOrderItemWithSubscription(LicenseType.SUBSCRIPTION);
        List<AbstractOrderEntryModel> mockOrderEntries = List.of(orderEntry);
        when(mockOrder.getEntries()).thenReturn(mockOrderEntries);
        orderEmailService.sendNotifyCustomerSupportEmail(mockOrder);
        verify(cisEmailService, times(1))
                .sendEmail(eq(EmailType.ORDER_SUCCESS_NOTIFY_CUSTOMER_SUPPORT), orderSuccessNotifyAaServiceDeskDataCaptor.capture());
        OrderSuccessNotifyCustomerSupportData notifyAaServiceDeskData = orderSuccessNotifyAaServiceDeskDataCaptor.getValue();
        assertThat(notifyAaServiceDeskData.getOrderItems()).hasSameSizeAs(mockOrderEntries);
        assertBasicAaServiceDeskData(notifyAaServiceDeskData);
        assertThat(notifyAaServiceDeskData.getOrderItems().get(0).getSubscriptions().get(0).getContractId()).isEqualTo(BRIM_ID);
        assertThat(notifyAaServiceDeskData.getDistributor().getDistributorId()).isNull();
        assertThat(notifyAaServiceDeskData.getDistributor().getDistributorName()).isNull();
    }

    @Test
    public void test_sendNotifyAaServiceDeskEmail_withDistributor() {
        var mockDistributor = AaDistributorCompanyBuilder.generate()
                .withCompanyName("Some Car Parts Distributor")
                .withUmpId("ump distri ID")
                .buildMockInstance();
        var orderCompany = mockOrder.getCompany();
        when(mockOrder.getAaDistributorCompany()).thenReturn(mockDistributor);
        OrderEntryModel orderEntry = createOrderItemWithSubscription(LicenseType.FULL);
        List<AbstractOrderEntryModel> mockOrderEntries = List.of(orderEntry);
        when(mockOrder.getEntries()).thenReturn(mockOrderEntries);
        orderEmailService.sendNotifyCustomerSupportEmail(mockOrder);
        verify(cisEmailService, times(1))
                .sendEmail(eq(EmailType.ORDER_SUCCESS_NOTIFY_CUSTOMER_SUPPORT), orderSuccessNotifyAaServiceDeskDataCaptor.capture());
        OrderSuccessNotifyCustomerSupportData notifyAaServiceDeskData = orderSuccessNotifyAaServiceDeskDataCaptor.getValue();
        assertThat(notifyAaServiceDeskData.getOrderItems()).hasSameSizeAs(mockOrderEntries);
        assertBasicAaServiceDeskData(notifyAaServiceDeskData);
        assertThat(notifyAaServiceDeskData.getOrderItems().get(0).getSubscriptions().get(0).getContractId()).isEqualTo(BRIM_ID);
        assertThat(notifyAaServiceDeskData.getDistributor().getDistributorId()).isEqualTo("ump distri ID");
        assertThat(notifyAaServiceDeskData.getDistributor().getDistributorName()).isEqualTo("Some Car Parts Distributor");
    }

    @Test
    public void test_sendNotifyAaServiceDeskEmail_withoutSubscription() {
        OrderEntryModel orderEntry = createOrderItem(LicenseType.FULL);
        List<AbstractOrderEntryModel> mockOrderEntries = List.of(orderEntry);
        when(mockOrder.getEntries()).thenReturn(mockOrderEntries);
        orderEmailService.sendNotifyCustomerSupportEmail(mockOrder);

        verify(cisEmailService, times(1))
                .sendEmail(eq(EmailType.ORDER_SUCCESS_NOTIFY_CUSTOMER_SUPPORT), orderSuccessNotifyAaServiceDeskDataCaptor.capture());
        OrderSuccessNotifyCustomerSupportData notifyAaServiceDeskData = orderSuccessNotifyAaServiceDeskDataCaptor.getValue();
        assertThat(notifyAaServiceDeskData.getOrderItems()).hasSameSizeAs(mockOrderEntries);
        assertBasicAaServiceDeskData(notifyAaServiceDeskData);
        assertThat(notifyAaServiceDeskData.getOrderItems().get(0).getSubscriptions()).isEmpty();
    }

    @Test(expected = IllegalStateException.class)
    public void test_sendNotifyAaServiceDeskEmail_no_enviroment_throws_exception() throws IllegalStateException {
        OrderEntryModel orderEntry = createOrderItem(LicenseType.FULL);
        List<AbstractOrderEntryModel> mockOrderEntries = List.of(orderEntry);
        when(mockOrder.getEntries()).thenReturn(mockOrderEntries);
        when(springProfileConfig.getEnvironment()).thenReturn(null);
        orderEmailService.sendNotifyCustomerSupportEmail(mockOrder);

    }

    @Test
    public void sendCreditNoteEmail_happyPath() {
        orderEmailService.sendCreditNoteEmail(mockOrder, invoiceCreditNoteModel);

        CreditNote expectedCreditNote = CreditNote.builder()
                .customerName(PURCHASER_FIST_NAME + " " + PURCHASER_LAST_NAME)
                .orderNumber(ORDER_CODE)
                .recipient(PURCHASER_EMAIL)
                .orderDetailsUrl(STORE_URL_VALUE + "/my-account/orders/" + ORDER_CODE)
                .storeUid(BaseStoreEnum.AZENA.getBaseStoreUid())
                .language("de")
                .build();

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.CREDIT_NOTE);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(CreditNote.class).isEqualTo(expectedCreditNote);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }


    @Test
    public void sendCreditNoteEmail_alsoSendsToCompanyEmail() {
        when(featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true))
                .thenReturn(Boolean.TRUE);

        orderEmailService.sendCreditNoteEmail(mockOrder, invoiceCreditNoteModel);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.CREDIT_NOTE);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL, PURCHASER_COMPANY_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(CreditNote.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    @Test
    public void sendCreditNoteEmail_handlesEmptyCompanyEmailGracefully() {
        when(featureToggleService.isEnabledOrDefault(Feature.FEATURE_SECONDARY_EMAIL_FOR_FINANCE, true))
                .thenReturn(Boolean.TRUE);
        when(mockOrder.getCompany().getCompanyEmail()).thenReturn(null);


        orderEmailService.sendCreditNoteEmail(mockOrder, invoiceCreditNoteModel);

        verify(cisEmailService, times(1))
                .sendEmail(sendEmailDataCaptor.capture(), eq(EXPECTED_ATTACHMENT));
        assertThat(sendEmailDataCaptor.getValue()).isNotNull().satisfies(sendEmailData -> {
            assertThat(sendEmailData.getEmailType()).isEqualTo(EmailType.CREDIT_NOTE);
            assertThat(sendEmailData.getTo()).containsExactlyInAnyOrder(PURCHASER_EMAIL);
            assertThat(sendEmailData.getMessageContext()).isInstanceOf(CreditNote.class);
            assertThat(sendEmailData.getLanguage()).isEqualTo(PURCHASER_COMPANY_LANGUAGE);
        });
    }

    private void assertBasicAaServiceDeskData(OrderSuccessNotifyCustomerSupportData notifyAaServiceDeskData) {
        assertThat(notifyAaServiceDeskData.getOrderNumber()).isEqualTo(ORDER_CODE);
        assertThat(notifyAaServiceDeskData.getBuyer().getCompanyName()).isEqualTo(PURCHASER_COMPANY_NAME);
        assertThat(notifyAaServiceDeskData.getCountry()).isEqualTo(PURCHASER_COUNTRY_ISO_CODE.toUpperCase());
        assertThat(notifyAaServiceDeskData.getRecipient()).isEqualTo(CUSTOMER_SUPPORT_EMAILID);
        assertThat(notifyAaServiceDeskData.getOrderItems().get(0).getExternalProductId()).isEqualTo(EXTERNAL_PRODUCT_ID);
        assertThat(notifyAaServiceDeskData.getOrderItems().get(0).getProductCode()).isEqualTo(PRODUCT_CODE);
    }

    private Set<UmpUserData> createManagers() {
        Set<UmpUserData> managers = new HashSet<>();
        UmpUserData userData = new UmpUserData();
        userData.setEmail(MANAGER_EMAIL);
        userData.setFirstName(MANAGER_FIST_NAME);
        userData.setLastName(MANAGER_LAST_NAME);
        managers.add(userData);
        return managers;
    }
}
