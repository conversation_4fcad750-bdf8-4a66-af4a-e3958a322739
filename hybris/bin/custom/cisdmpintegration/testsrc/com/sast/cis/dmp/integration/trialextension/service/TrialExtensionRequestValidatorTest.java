package com.sast.cis.dmp.integration.trialextension.service;

import com.sast.cis.dmp.integration.trialextension.dto.TrialExtensionApplicationData;
import com.sast.cis.dmp.integration.trialextension.dto.TrialExtensionRequestMessageData;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;

import java.time.format.DateTimeParseException;
import java.util.Date;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@UnitTest
public class TrialExtensionRequestValidatorTest {

    private static final String DUMMY_VALUE = "123";
    private static final String PERIOD_3_YEARS = "P3Y";
    private static final int NO_OF_CAMERAS = 5;
    private TrialExtensionRequestValidator validator;

    @Before
    public void setUp() {
        validator = new TrialExtensionRequestValidator();
    }

    @Test
    public void allDataValid() {
        assertThat(validator.validate(
            TrialExtensionRequestMessageData.builder().requestId(DUMMY_VALUE).companyAccountId(DUMMY_VALUE).creationTimestamp(new Date())
                .duration(PERIOD_3_YEARS)
                .application(TrialExtensionApplicationData.builder().applicationId(DUMMY_VALUE).versionCode(0L).versionName("1.0.0").build())
                .noOfCameras(5).messageToSeller("TEST").userId(DUMMY_VALUE).build())).isTrue();
    }

    @Test
    public void noUserIdAndMessageValid() {
        assertThat(validator.validate(
            TrialExtensionRequestMessageData.builder().requestId(DUMMY_VALUE).companyAccountId(DUMMY_VALUE).creationTimestamp(new Date())
                .duration(PERIOD_3_YEARS)
                .application(TrialExtensionApplicationData.builder().applicationId(DUMMY_VALUE).versionCode(0L).versionName("1.0.0").build())
                .noOfCameras(NO_OF_CAMERAS).build())).isTrue();
    }

    @Test
    public void throwExceptionDueToMissingRequestId() {
        assertThatThrownBy(() -> validator.validate(
            TrialExtensionRequestMessageData.builder().companyAccountId(DUMMY_VALUE).creationTimestamp(new Date()).duration(PERIOD_3_YEARS)
                .noOfCameras(5).build()))
            .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void throwExceptionDueToMalFormedDuration() {
        assertThatThrownBy(() -> validator.validate(
            TrialExtensionRequestMessageData.builder().requestId(DUMMY_VALUE).companyAccountId(DUMMY_VALUE).creationTimestamp(new Date())
                .application(TrialExtensionApplicationData.builder().applicationId(DUMMY_VALUE).versionCode(0L).versionName("1.0.0").build())
                .duration("P3YGGG").noOfCameras(5).build()))
            .isInstanceOf(DateTimeParseException.class);
    }
}
