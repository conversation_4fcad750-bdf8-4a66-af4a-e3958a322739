package com.sast.cis.dmp.integration.trialextension.service;

import com.sast.cis.core.model.*;
import com.sast.cis.core.service.AppService;
import com.sast.cis.core.service.AppVersionService;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.dmp.integration.exceptions.DmpBusinessException;
import com.sast.cis.dmp.integration.trialextension.dto.TrialExtensionRequestMessageData;
import com.sast.cis.trialextension.enums.TrialExtensionRequestStatus;
import com.sast.cis.trialextension.model.TrialExtensionRequestModel;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.sast.cis.core.dao.CatalogVersion.ONLINE;

@Slf4j
@Component
@RequiredArgsConstructor
public class TrialExtensionRequestReversePopulator implements Populator<TrialExtensionRequestMessageData, TrialExtensionRequestModel> {

    private final IotCompanyService iotCompanyService;
    private final AppService appService;
    private final AppVersionService appVersionService;

    @Override public void populate(TrialExtensionRequestMessageData requestData, TrialExtensionRequestModel model)
        throws ConversionException {
        IoTCompanyModel integratorCompany = getIntegratorCompany(requestData);
        AppVersionModel appVersion = getAppVersion(requestData);

        model.setAppVersion(appVersion);
        model.setRequestUserUid(requestData.getUserId());
        model.setRequestId(requestData.getRequestId());
        model.setIntegratorCompany(integratorCompany);
        model.setNoOfCameras(requestData.getNoOfCameras());
        model.setPeriod(requestData.getDuration());
        model.setMessageToSeller(requestData.getMessageToSeller());
        model.setRequestCreationTime(requestData.getCreationTimestamp());
        model.setStatus(TrialExtensionRequestStatus.REQUESTED);
    }

    private IoTCompanyModel getIntegratorCompany(TrialExtensionRequestMessageData requestData) {
        return iotCompanyService.getCompanyByUid(requestData.getCompanyAccountId())
            .orElseThrow(() ->new DmpBusinessException(String.format("Company '%s' is unknown", requestData.getCompanyAccountId())));
    }

    private AppVersionModel getAppVersion(TrialExtensionRequestMessageData requestData) {
        final String packageName = requestData.getApplication().getApplicationId();
        final Long versionCode = requestData.getApplication().getVersionCode();

        LOG.debug("searching for app with package {} and version code {}", packageName, versionCode);

        Optional<AppModel> app = appService.getAppForPackage(packageName);

        if (app.isPresent()) {
            Optional<AppVersionModel> appVersion = appVersionService
                .getVersionForCodeAndCatalogVersion(app.get().getCode(), versionCode, ONLINE);
            if (appVersion.isPresent()) {
                return appVersion.get();
            }
        }

        throw new DmpBusinessException(String.format("App '%s' with version code '%s' is unknown", packageName, versionCode));
    }

}
