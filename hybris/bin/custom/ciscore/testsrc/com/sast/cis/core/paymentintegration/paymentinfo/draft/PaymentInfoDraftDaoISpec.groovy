package com.sast.cis.core.paymentintegration.paymentinfo.draft

import com.sast.cis.core.enums.PaymentInfoDraftCreationStatus
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.PaymentInfoDraftModel
import com.sast.cis.core.service.customer.integrator.IntegratorService
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import generated.com.sast.cis.core.model.PaymentInfoDraftBuilder
import org.junit.Test

import javax.annotation.Resource

import static com.sast.cis.core.enums.PaymentInfoDraftCreationStatus.FAILED
import static com.sast.cis.core.enums.PaymentInfoDraftCreationStatus.SUCCEEDED
import static com.sast.cis.core.enums.PaymentMethodType.SEPA_DIRECTDEBIT
import static com.sast.cis.core.enums.PaymentProvider.PGW
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_INTEGRATOR_UID
import static com.sast.cis.test.utils.TestDataConstants.DEMO_COMPANY_INTEGRATOR_UID

@IntegrationTest
class PaymentInfoDraftDaoISpec extends ServicelayerTransactionalSpockSpecification {

    @Resource
    private ModelService modelService

    @Resource
    private IntegratorService integratorService

    @Resource
    private PaymentInfoDraftDao paymentInfoDraftDao

    private PaymentInfoDraftModel paymentInfoDraft
    private IntegratorModel integrator

    private final String paymentInfoDraftCode = 'pi_draft_code'
    private final PaymentInfoDraftCreationStatus paymentInfoDraftStatus = SUCCEEDED

    def setup() {
        integrator = integratorService.getIntegratorByInternalUserId(AA_AUSTRIA1_COMPANY_INTEGRATOR_UID)

        paymentInfoDraft = PaymentInfoDraftBuilder.generate()
                .withCode(paymentInfoDraftCode)
                .withIntegrator(integrator)
                .withCreationStatus(paymentInfoDraftStatus)
                .withPaymentMethodType(SEPA_DIRECTDEBIT)
                .withPaymentProvider(PGW)
                .buildIntegrationInstance()

        modelService.save(paymentInfoDraft)
    }

    @Test
    void 'findByCode returns existing payment info'() {
        when:
        def result = paymentInfoDraftDao.findByCode(paymentInfoDraftCode)

        then:
        result == Optional.of(paymentInfoDraft)
    }

    @Test
    void 'findByCode returns empty for unknown code'() {
        when:
        def result = paymentInfoDraftDao.findByCode("unknownCode")

        then:
        result == Optional.empty()
    }

    @Test
    void 'findByIntegratorAndCreationStatus returns existing payment info'() {
        when:
        def result = paymentInfoDraftDao.findByIntegratorAndCreationStatus(integrator, paymentInfoDraftStatus)

        then:
        result == [paymentInfoDraft]
    }

    @Test
    void 'given no payment info with status when findByIntegratorAndCreationStatus then return empty'() {
        when:
        def result = paymentInfoDraftDao.findByIntegratorAndCreationStatus(integrator, FAILED)

        then:
        result.isEmpty()
    }

    @Test
    void 'given no payment info for integrator when findByIntegratorAndCreationStatus then return empty'() {
        given:
        def otherIntegrator = integratorService.getIntegratorByInternalUserId(DEMO_COMPANY_INTEGRATOR_UID)

        when:
        def result = paymentInfoDraftDao.findByIntegratorAndCreationStatus(otherIntegrator, paymentInfoDraftStatus)

        then:
        result.isEmpty()
    }
}
