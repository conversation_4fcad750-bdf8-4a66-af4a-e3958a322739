package com.sast.cis.core.config.keycloak;

import com.sast.cis.core.constants.BaseStoreEnum;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import org.junit.Test;
import org.keycloak.representations.adapters.config.AdapterConfig;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class SiteUmpAdapterConfigResolutionServiceITest extends ServicelayerTransactionalTest {

    @Resource
    private SiteUmpAdapterConfigResolutionService siteUmpAdapterConfigResolutionService;

    @Test
    public void givenAAStore_whenGetConfigForBaseStore_thenReturnAAStoreConfig() {
        final AdapterConfig configForBaseStore = siteUmpAdapterConfigResolutionService.getConfigForBaseStore(BaseStoreEnum.AA);

        assertThat(configForBaseStore).isNotNull();
        assertThat(configForBaseStore.getRealm()).isEqualTo("baam");
        assertThat(configForBaseStore.getResource()).isEqualTo("iotstore-shop");
        assertThat(configForBaseStore.getPrincipalAttribute()).isEqualTo("preferred_username");
        assertThat(configForBaseStore.getAuthServerUrl()).isEqualTo("http://localhost:8080/auth");
        assertThat(configForBaseStore.getTruststore()).isNull();
        assertThat(configForBaseStore.getTruststorePassword()).isNull();
    }

    @Test
    public void givenAzenaStore_whenGetConfigForBaseStore_thenReturnAzenaStoreConfig() {
        final AdapterConfig configForBaseStore = siteUmpAdapterConfigResolutionService.getConfigForBaseStore(BaseStoreEnum.AZENA);

        assertThat(configForBaseStore).isNotNull();
        assertThat(configForBaseStore.getRealm()).isEqualTo("sast");
        assertThat(configForBaseStore.getResource()).isEqualTo("iotstore-shop");
        assertThat(configForBaseStore.getPrincipalAttribute()).isEqualTo("preferred_username");
        assertThat(configForBaseStore.getAuthServerUrl()).isEqualTo("http://localhost:8080/auth");
        assertThat(configForBaseStore.getTruststore()).isNull();
        assertThat(configForBaseStore.getTruststorePassword()).isNull();
    }
}
