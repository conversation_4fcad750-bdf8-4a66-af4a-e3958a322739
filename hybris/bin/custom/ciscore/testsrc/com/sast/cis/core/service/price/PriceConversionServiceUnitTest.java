package com.sast.cis.core.service.price;

import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.PriceLimitModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.util.PriceUtil;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commerceservices.i18n.CommerceCommonI18NService;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.com.sast.cis.core.model.PriceLimitBuilder;
import generated.de.hybris.platform.core.model.c2l.CountryBuilder;
import generated.de.hybris.platform.core.model.c2l.CurrencyBuilder;
import org.assertj.core.data.Offset;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyDouble;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(DataProviderRunner.class)
public class PriceConversionServiceUnitTest {
    private static final CurrencyModel USD_CURRENCY = newCurrency("USD", 2);
    private static final CurrencyModel EUR_CURRENCY = newCurrency("EUR", 2);
    private static final CurrencyModel MADE_UP_CURRENCY = newCurrency("NEW", 4);

    private static final Offset<Double> FLOAT_ROUNDING_ERROR_OFFSET = Offset.offset(Math.pow(10, -8));

    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private CommerceCommonI18NService commerceCommonI18NService;

    @Mock
    private PriceUtil priceUtil;

    @Mock
    private PriceLimitService priceLimitService;

    @InjectMocks
    private PriceConversionService priceConversionService;

    @Test
    public void getConvertedPrices_convertsToAllCurrencies() {
        Double priceToConvert = 5.12d;
        setUp(priceToConvert);
        Map<String, String> convertedPrices = priceConversionService.getConvertedPrices(priceToConvert);

        assertThat(convertedPrices.get("USD")).isEqualTo("5,00");
        assertThat(convertedPrices.get("EUR")).isEqualTo("5,00");
        assertThat(convertedPrices.get("NEW")).isEqualTo("5,0000");
    }

    private void setUp(Double priceToConvert) {
        List<CurrencyModel> currencies = Arrays.asList(USD_CURRENCY, EUR_CURRENCY, MADE_UP_CURRENCY);

        CountryModel country = CountryBuilder.generate().withCurrency(USD_CURRENCY).buildMockInstance();

        IoTCompanyModel company = IoTCompanyBuilder.generate().withCountry(country).buildMockInstance();
        when(iotCompanyService.getCurrentCompanyOrThrow()).thenReturn(company);
        when(commerceCommonI18NService.getCurrentLocale()).thenReturn(Locale.GERMAN);
        when(commerceCommonI18NService.getAllCurrencies()).thenReturn(currencies);
        when(priceUtil.getConvertedPrice(any(), any(), anyDouble())).thenReturn((double) Math.round(priceToConvert));
        PriceLimitModel priceLimit = PriceLimitBuilder.generate()
            .withUpperLimit(200000)
            .buildMockInstance();
        when(priceLimitService.getPriceLimit(any())).thenReturn(priceLimit);
        when(priceUtil.formatAmount(2, 5d)).thenReturn("5,00");
        when(priceUtil.formatAmount(4, 5d)).thenReturn("5,0000");
    }

    @Test
    public void getConvertedPrices_returnsHyphenForPriceOverLimit() {
        Double priceToConvert = 200001d;
        setUp(priceToConvert);

        Map<String, String> convertedPrices = priceConversionService.getConvertedPrices(priceToConvert);

        assertThat(convertedPrices.get("USD")).isEqualTo("-");
        assertThat(convertedPrices.get("EUR")).isEqualTo("-");
        assertThat(convertedPrices.get("NEW")).isEqualTo("-");
    }

    @DataProvider
    public static Object[][] convertFromSubcurrencyAmount() {
        return new Object[][] {
            { USD_CURRENCY, 432, 4.32 },
            { EUR_CURRENCY, -200, -2.00 },
            { MADE_UP_CURRENCY, 43210, 4.3210 }
        };
    }

    @Test
    @UseDataProvider
    public void convertFromSubcurrencyAmount(CurrencyModel givenCurrency, int givenSubcurrencyAmount, double expectedAmount) {
        double actualAmount = priceConversionService.convertFromSubcurrencyAmount(givenCurrency, givenSubcurrencyAmount);
        assertThat(actualAmount).isCloseTo(expectedAmount, FLOAT_ROUNDING_ERROR_OFFSET);
    }

    @DataProvider
    public static Object[][] convertToSubcurrencyAmount() {
        return new Object[][] {
            { USD_CURRENCY, 1.236, 124 },
            { USD_CURRENCY, -1.234, -123 },
            { MADE_UP_CURRENCY, 1.234, 12340 }
        };
    }

    @Test
    @UseDataProvider
    public void convertToSubcurrencyAmount(CurrencyModel givenCurrency, double givenCurrencyAmount, int expectedSubcurrencyAmount) {
        int actualSubcurrencyAmount = priceConversionService.convertToSubcurrencyAmount(givenCurrency, givenCurrencyAmount);

        assertThat(actualSubcurrencyAmount).isEqualTo(expectedSubcurrencyAmount);
    }

    private static CurrencyModel newCurrency(String isoCode, int digits) {
        return CurrencyBuilder.generate().withIsocode(isoCode).withActive(true).withDigits(digits).buildMockInstance();
    }
}
