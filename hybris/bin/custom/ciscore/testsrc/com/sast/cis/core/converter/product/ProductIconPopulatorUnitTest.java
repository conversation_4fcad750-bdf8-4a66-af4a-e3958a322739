package com.sast.cis.core.converter.product;

import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.ImageFormatMapping;
import de.hybris.platform.commercefacades.product.data.ImageData;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.media.MediaContainerModel;
import de.hybris.platform.core.model.media.MediaFormatModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.media.MediaContainerService;
import de.hybris.platform.servicelayer.media.MediaService;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppLicenseBuilder;
import generated.de.hybris.platform.core.model.media.MediaBuilder;
import generated.de.hybris.platform.core.model.media.MediaContainerBuilder;
import generated.de.hybris.platform.core.model.media.MediaFormatBuilder;
import generated.de.hybris.platform.core.model.product.ProductBuilder;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProductIconPopulatorUnitTest {

    private static final String MEDIA_FORMAT = "icon-normal";
    private static final String MEDIA_FORMAT_QUALIFIER = "16x16";

    @Mock
    private MediaService mediaService;

    @Mock
    private MediaContainerService mediaContainerService;

    @Mock
    private ImageFormatMapping imageFormatMapping;

    @Mock
    private Converter<MediaModel, ImageData> imageConverter;

    @Mock
    private ModelService modelService;

    @InjectMocks
    private ProductIconPopulator<ProductModel, ProductData> populator;

    @Mock
    private ImageData iconImage;

    private List<String> imageFormats;

    @Before
    public void setUp() {
        imageFormats = new ArrayList<>();
        imageFormats.add(MEDIA_FORMAT);

        populator.setImageFormatMapping(imageFormatMapping);
        populator.setImageFormats(imageFormats);
    }

    @Test
    public void populate_withApp_correctlyPopulated() {
        MediaModel iconMedia = MediaBuilder.generate().buildMockInstance();
        MediaFormatModel iconFormat = MediaFormatBuilder.generate().buildMockInstance();

        MediaContainerModel iconContainer = MediaContainerBuilder.generate().buildMockInstance();
        AppModel app = AppBuilder.generate().withIcon(iconContainer).buildMockInstance();

        when(mediaService.getFormat(MEDIA_FORMAT_QUALIFIER)).thenReturn(iconFormat);
        when(mediaContainerService.getMediaForFormat(iconContainer, iconFormat)).thenReturn(iconMedia);
        when(imageConverter.convert(iconMedia)).thenReturn(iconImage);
        when(imageFormatMapping.getMediaFormatQualifierForImageFormat(MEDIA_FORMAT)).thenReturn(MEDIA_FORMAT_QUALIFIER);

        final ProductData result = new ProductData();
        populator.populate(app, result);

        verify(modelService, never()).getAttributeValue(app, ProductModel.PICTURE);
        Assert.assertEquals(1, result.getImages().size());
        Assert.assertTrue(result.getImages().contains(iconImage));
    }

    @Test
    public void populate_withAppLicense_correctlyPopulated() {
        MediaModel iconMedia = MediaBuilder.generate().buildMockInstance();
        MediaFormatModel iconFormat = MediaFormatBuilder.generate().buildMockInstance();

        MediaContainerModel iconContainer = MediaContainerBuilder.generate().buildMockInstance();
        AppLicenseModel appLicense = AppLicenseBuilder.generate().withIcon(iconContainer).buildMockInstance();

        when(mediaService.getFormat(MEDIA_FORMAT_QUALIFIER)).thenReturn(iconFormat);
        when(mediaContainerService.getMediaForFormat(iconContainer, iconFormat)).thenReturn(iconMedia);
        when(imageConverter.convert(iconMedia)).thenReturn(iconImage);
        when(imageFormatMapping.getMediaFormatQualifierForImageFormat(MEDIA_FORMAT)).thenReturn(MEDIA_FORMAT_QUALIFIER);

        final ProductData result = new ProductData();
        populator.populate(appLicense, result);

        verify(modelService, never()).getAttributeValue(appLicense, ProductModel.PICTURE);
        Assert.assertEquals(1, result.getImages().size());
        Assert.assertTrue(result.getImages().contains(iconImage));
    }

    @Test
    public void populate_withUnknownProduct_correctlyPopulated() {
        MediaModel iconMedia = MediaBuilder.generate().buildMockInstance();
        MediaFormatModel iconFormat = MediaFormatBuilder.generate().buildMockInstance();

        MediaContainerModel iconContainer = MediaContainerBuilder.generate().buildMockInstance();
        ProductModel product = ProductBuilder.generate().buildMockInstance();

        when(mediaService.getFormat(MEDIA_FORMAT_QUALIFIER)).thenReturn(iconFormat);
        when(mediaContainerService.getMediaForFormat(iconContainer, iconFormat)).thenReturn(iconMedia);
        when(imageConverter.convert(iconMedia)).thenReturn(iconImage);
        when(imageFormatMapping.getMediaFormatQualifierForImageFormat(MEDIA_FORMAT)).thenReturn(MEDIA_FORMAT_QUALIFIER);
        when(iconMedia.getMediaContainer()).thenReturn(iconContainer);
        when(modelService.getAttributeValue(product, ProductModel.PICTURE)).thenReturn(iconMedia);

        final ProductData result = new ProductData();
        populator.populate(product, result);

        verify(modelService, times(1)).getAttributeValue(product, ProductModel.PICTURE);
        Assert.assertEquals(1, result.getImages().size());
        Assert.assertTrue(result.getImages().contains(iconImage));
    }
}
