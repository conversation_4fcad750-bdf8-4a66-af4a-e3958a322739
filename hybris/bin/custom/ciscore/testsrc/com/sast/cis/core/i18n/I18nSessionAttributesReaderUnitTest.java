package com.sast.cis.core.i18n;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.storesession.StoreSessionFacade;
import de.hybris.platform.commercefacades.storesession.data.LanguageData;
import de.hybris.platform.servicelayer.session.SessionService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.sast.cis.core.i18n.StoreI18nConstants.SELECTED_COUNTRY_SESSION_ATTR_KEY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class I18nSessionAttributesReaderUnitTest {

    @Mock
    private SessionService defaultSessionService;

    @Mock
    private StoreSessionFacade defaultStoreSessionFacade;

    @InjectMocks
    private I18nSessionAttributesReader i18nSessionAttributesReader;

    private final String selectedCountry = "DE";
    private final LanguageData currentLanguage = new LanguageData().withIsocode("en");

    @Before
    public void setup() {
        when(defaultSessionService.getAttribute(SELECTED_COUNTRY_SESSION_ATTR_KEY)).thenReturn(selectedCountry);
        when(defaultStoreSessionFacade.getCurrentLanguage()).thenReturn(currentLanguage);
    }

    @Test
    public void whenGetUserSelectedCountry_thenGetSelectedCountrySessionAttribute() {
        assertThat(i18nSessionAttributesReader.getUserSelectedCountry()).isEqualTo(selectedCountry);
    }

    @Test
    public void whenGetCurrentLanguage_thenGetCurrentLanguageFromSession() {
        assertThat(i18nSessionAttributesReader.getCurrentLanguage()).isEqualTo(currentLanguage);
    }
}
