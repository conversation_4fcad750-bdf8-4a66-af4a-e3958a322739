package com.sast.cis.core.converter;

import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.enums.CompanyApprovalStatus;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.singlesignon.BillingAddressService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.user.AddressModel;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.core.model.c2l.CountryBuilder;
import generated.de.hybris.platform.core.model.user.AddressBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.sast.cis.core.enums.CompanyApprovalStatus.APPROVED_COMMERCIAL;
import static com.sast.cis.core.enums.CompanyApprovalStatus.UNAPPROVED;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class IotCompanySsoDataToModelPopulatorUnitTest {

    private static final String COMPANY_ID = "companyId";
    private static final String COMPANY_NAME = "company name";
    private static final String COMPANY_COUNTRY_ISO_CODE = "DE";
    private static final String MARKETPLACE_ID = "iotstore";
    private static final String BPMD_ID = "iAmABpmdID";
    private static final boolean IMPORTED = false;

    @Mock
    private CommonI18NService commonI18NService;

    @Mock
    private BillingAddressService billingAddressService;

    @Mock
    private BaseStoreService baseStoreService;

    @InjectMocks
    private IotCompanySsoDataToModelPopulator iotCompanySsoDataToModelPopulator;

    @Mock
    private BaseStoreModel baseStoreModel;

    @Before
    public void setUp() {
        when(baseStoreService.getBaseStoreForUid(MARKETPLACE_ID)).thenReturn(baseStoreModel);
    }

    @Test
    public void populate_WithCommerciallyApprovedCompany_correctlyPopulated() {
        UmpCompanyData companyData = createDefaultUmpCompanyData(APPROVED_COMMERCIAL);
        CountryModel country = createDefaultCountry();
        AddressModel billingAddress = createDefaultBillingAddress();

        IoTCompanyModel actualResult = IoTCompanyBuilder.generate().buildMockInstance();

        when(commonI18NService.getCountry(COMPANY_COUNTRY_ISO_CODE)).thenReturn(country);
        when(billingAddressService.createAddress(companyData, actualResult)).thenReturn(billingAddress);

        iotCompanySsoDataToModelPopulator.populate(companyData, actualResult);

        verifyCommon(actualResult);
        verify(actualResult).setCountry(country);
        verify(actualResult).setApprovalStatus(APPROVED_COMMERCIAL);
        verify(actualResult).setBillingAddress(billingAddress);
        verify(actualResult).setBpmdId(BPMD_ID);
        verify(actualResult).setAaImported(IMPORTED);
    }

    @Test
    public void populate_WithUnapprovedCompany_correctlyPopulated() {
        UmpCompanyData companyData = createDefaultUmpCompanyData(UNAPPROVED);
        CountryModel country = createDefaultCountry();
        AddressModel billingAddress = createDefaultBillingAddress();

        IoTCompanyModel actualResult = IoTCompanyBuilder.generate().buildMockInstance();

        when(commonI18NService.getCountry(COMPANY_COUNTRY_ISO_CODE)).thenReturn(country);
        when(billingAddressService.createAddress(companyData, actualResult)).thenReturn(billingAddress);

        iotCompanySsoDataToModelPopulator.populate(companyData, actualResult);

        verifyCommon(actualResult);
        verify(actualResult).setCountry(country);
        verify(actualResult).setApprovalStatus(UNAPPROVED);
        verify(actualResult).setBillingAddress(billingAddress);
        verify(actualResult).setAaImported(IMPORTED);
    }

    @Test
    public void populate_WithCompanyWithoutCountryAndBillingAddress_companyAndAddressAreNotPopulated() {
        UmpCompanyData companyData = createDefaultUmpCompanyData(APPROVED_COMMERCIAL).withCompanyCountry(null);

        IoTCompanyModel actualResult = IoTCompanyBuilder.generate().buildMockInstance();

        when(commonI18NService.getCountry(null)).thenReturn(null);
        when(billingAddressService.createAddress(companyData, actualResult)).thenReturn(null);

        iotCompanySsoDataToModelPopulator.populate(companyData, actualResult);

        verifyCommon(actualResult);
        verify(actualResult).setCountry(null);
        verify(actualResult).setApprovalStatus(APPROVED_COMMERCIAL);
        verify(actualResult).setBillingAddress(null);
    }

    private void verifyCommon(IoTCompanyModel actualResult) {
        verify(actualResult).setUid(COMPANY_ID);
        verify(actualResult).setName(COMPANY_NAME);
        verify(actualResult).setStore(baseStoreModel);

    }

    private UmpCompanyData createDefaultUmpCompanyData(CompanyApprovalStatus approvedCommercial) {
        return new UmpCompanyData()
            .withCompanyId(COMPANY_ID)
            .withCompanyName(COMPANY_NAME)
            .withCompanyCountry(COMPANY_COUNTRY_ISO_CODE)
            .withMarketplaceId(MARKETPLACE_ID)
            .withBpmdId(BPMD_ID)
            .withImported(IMPORTED)
            .withCompanyStatus(approvedCommercial.getCode());
    }

    private CountryModel createDefaultCountry() {
        return CountryBuilder.generate().withIsocode(COMPANY_COUNTRY_ISO_CODE).buildMockInstance();
    }

    private AddressModel createDefaultBillingAddress() {
        return AddressBuilder.generate().buildMockInstance();
    }

}
