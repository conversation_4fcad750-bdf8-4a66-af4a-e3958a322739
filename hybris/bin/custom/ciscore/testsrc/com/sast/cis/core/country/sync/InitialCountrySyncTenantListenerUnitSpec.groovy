package com.sast.cis.core.country.sync

import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.Tenant
import de.hybris.platform.cronjob.enums.CronJobStatus
import de.hybris.platform.cronjob.model.CronJobModel
import de.hybris.platform.servicelayer.cronjob.CronJobService
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

@UnitTest
class InitialCountrySyncTenantListenerUnitSpec extends JUnitPlatformSpecification {
    private static final String COUNTRY_SYNC_CODE = 'countrySyncCronJob'
    private static final String MASTER = 'master'

    private CronJobService cronJobService = Mock()

    private InitialCountrySyncTenantListener initialCountrySyncTenantListener

    private Tenant mockTenant = Mock()
    private CronJobModel mockJob = Mock(CronJobModel)

    def setup() {
        initialCountrySyncTenantListener = new InitialCountrySyncTenantListener(cronJobService)
        cronJobService.getCronJob(COUNTRY_SYNC_CODE) >> mockJob
    }

    @Test
    @Unroll
    def 'nothing is done for tenant #givenTenantName'() {
        when:
        initialCountrySyncTenantListener.afterTenantStartUp(mockTenant)

        then:
        mockTenant.getTenantID() >> givenTenantName
        0 * cronJobService._

        where:
        givenTenantName << ['junit', 'foobarbaz']
    }

    @Test
    @Unroll
    def 'job is executed for master tenant if job has status #givenStatus'() {
        when:
        initialCountrySyncTenantListener.afterTenantStartUp(mockTenant)

        then:
        mockTenant.getTenantID() >> MASTER
        mockJob.getStatus() >> givenStatus
        1 * cronJobService.performCronJob(mockJob, true)

        where:
        givenStatus << whitelistedStates
    }

    @Test
    @Unroll
    def 'job is not executed for master tenant if job has status #givenStatus'() {
        when:
        initialCountrySyncTenantListener.afterTenantStartUp(mockTenant)

        then:
        mockTenant.getTenantID() >> MASTER
        mockJob.getStatus() >> givenStatus
        0 * cronJobService.performCronJob(mockJob, true)

        where:
        givenStatus << nonWhitelistedStates
    }

    private static getWhitelistedStates() {
        [CronJobStatus.UNKNOWN]
    }

    private static getNonWhitelistedStates() {
        def states = CronJobStatus.findAll()
        states.removeAll(whitelistedStates)
        states
    }
}
