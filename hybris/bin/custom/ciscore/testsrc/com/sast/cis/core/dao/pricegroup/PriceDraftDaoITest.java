package com.sast.cis.core.dao.pricegroup;

import com.sast.cis.core.model.PriceDraftModel;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.europe1.enums.UserPriceGroup;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.com.sast.cis.core.model.PriceDraftBuilder;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@Slf4j
@IntegrationTest
public class PriceDraftDaoITest extends ServicelayerTransactionalTest {
    private static final String APP_LICENSE_CODE = "appLicenseCode";
    private static final String USER_GROUP = "KA0001";
    private static final String BRIM_ID = "appLicenseCode".toUpperCase();

    @Resource
    private ModelService modelService;

    @Resource
    private PriceDraftDao priceDraftDao;

    @Resource
    private CommonI18NService commonI18NService;

    private UserPriceGroup userPriceGroup;

    private CurrencyModel eur;
    private CurrencyModel usd;
    private CurrencyModel gbp;

    private PriceDraftModel priceDraft1;
    private PriceDraftModel priceDraft2;
    private PriceDraftModel priceDraft3;

    @Before
    public void setup() {
        userPriceGroup = UserPriceGroup.valueOf("aastore_" + USER_GROUP);
        eur = commonI18NService.getCurrency("EUR");
        usd = commonI18NService.getCurrency("USD");
        gbp = commonI18NService.getCurrency("GBP");
        priceDraft1 = PriceDraftBuilder.generate()
            .withAppLicenseCode(APP_LICENSE_CODE)
            .withAmount(7d)
            .withCurrency(eur)
            .withBillingPriceCode(USER_GROUP + "_" + BRIM_ID + "_" + eur.getIsocode())
            .withUserPriceGroup(userPriceGroup)
            .withValidFrom(new Date())
            .buildIntegrationInstance();
        priceDraft2 = PriceDraftBuilder.generate()
            .withAppLicenseCode(APP_LICENSE_CODE)
            .withAmount(14d)
            .withCurrency(usd)
            .withBillingPriceCode(USER_GROUP + "_" + BRIM_ID + "_" + usd.getIsocode())
            .withUserPriceGroup(userPriceGroup)
            .withValidFrom(new Date())
            .buildIntegrationInstance();
        priceDraft3 = PriceDraftBuilder.generate()
            .withAppLicenseCode(APP_LICENSE_CODE)
            .withAmount(21d)
            .withCurrency(gbp)
            .withBillingPriceCode(USER_GROUP + "_" + BRIM_ID + "_" + gbp.getIsocode())
            .withUserPriceGroup(userPriceGroup)
            .withValidFrom(new Date())
            .buildIntegrationInstance();

        modelService.saveAll(eur, usd, userPriceGroup, priceDraft1, priceDraft2, priceDraft3);
    }

    @Test
    public void getPriceDraft_successfully_returns() {
        String billingPriceCode = USER_GROUP + "_" + BRIM_ID + "_" + gbp.getIsocode();
        Optional<PriceDraftModel> priceDraft = priceDraftDao.getPriceDraft(billingPriceCode);
        assertThat(priceDraft).isPresent();
        assertThat(priceDraft.get().getBillingPriceCode()).isEqualTo(billingPriceCode);
        assertThat(priceDraft.get().getCurrency()).isEqualTo(gbp);
    }
}
