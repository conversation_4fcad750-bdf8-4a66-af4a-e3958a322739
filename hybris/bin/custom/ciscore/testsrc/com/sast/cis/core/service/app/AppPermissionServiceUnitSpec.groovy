package com.sast.cis.core.service.app

import com.sast.cis.core.constants.BaseStoreEnum
import com.sast.cis.core.customergroup.AnonymousUserCustomerGroupProvider
import com.sast.cis.core.data.ErrorMessageData
import com.sast.cis.core.enums.StoreAvailabilityMode
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.AppLicenseService
import com.sast.cis.core.service.customer.integrator.IntegratorService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.store.BaseStoreModel
import de.hybris.platform.store.services.BaseStoreService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

@UnitTest
class AppPermissionServiceUnitSpec extends JUnitPlatformSpecification {
    UserService userService = Mock(UserService)
    IntegratorService integratorService = Mock(IntegratorService)
    AppLicenseService appLicenseService = Mock(AppLicenseService)
    BaseStoreService baseStoreService = Mock(BaseStoreService)
    AnonymousUserCustomerGroupProvider anonymousUserCustomerGroupProvider = Mock(AnonymousUserCustomerGroupProvider)
    BaseStoreModel baseStoreModel = Mock(BaseStoreModel)
    AppPermissionService appPermissionService

    AppModel app = Mock(AppModel)
    def appCompany = Mock(IoTCompanyModel)
    def integrator = Mock(IntegratorModel)

    def setup() {
        appPermissionService = new AppPermissionService(userService, integratorService, appLicenseService, baseStoreService, anonymousUserCustomerGroupProvider)
        app.getCompany() >> appCompany
        appCompany.getUid() >> "test"
        integratorService.getIntegratorByUser(_) >> integrator
        baseStoreModel.getUid() >> BaseStoreEnum.AZENA.getBaseStoreUid()
        baseStoreService.getCurrentBaseStore() >> baseStoreModel
    }

    def "app model is null throws exception"() {
        when:
        appPermissionService.verifyAppCanBeViewedByIntegrator(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Unroll
    def "Unrestricted apps have no validation errors"() {
        given:
        app.getStoreAvailabilityMode() >> mode

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        Set.of() == errors

        where:
        mode                              | _
        StoreAvailabilityMode.PUBLIC      | _
        StoreAvailabilityMode.UNAVAILABLE | _
    }

    def "anonymous user have no access to a restricted app"() {
        given:
        app.getStoreAvailabilityMode() >> StoreAvailabilityMode.RESTRICTED_BUYER
        userService.isAnonymousUser(_) >> true

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        !errors.isEmpty()
    }

    @Unroll
    def "user not in buyers list no access to a restricted app"() {
        given:
        app.getStoreAvailabilityMode() >> StoreAvailabilityMode.RESTRICTED_BUYER
        app.getPermittedBuyerCompanies() >> companies
        integrator.getCompany() >> Mock(IoTCompanyModel)
        userService.isAnonymousUser(_) >> false
        integratorService.getIntegratorByUser(_) >> integrator

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        !errors.isEmpty()

        where:
        companies               | _
        null                    | _
        []                      | _
        [Mock(IoTCompanyModel)] | _
    }

    def "user in buyers list can access a restricted app"() {
        given:
        def buyer = Mock(IoTCompanyModel)
        app.getStoreAvailabilityMode() >> StoreAvailabilityMode.RESTRICTED_BUYER
        app.getPermittedBuyerCompanies() >> [buyer]
        integrator.getCompany() >> buyer
        userService.isAnonymousUser(_) >> false
        integratorService.getIntegratorByUser(_) >> integrator

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        errors.isEmpty()
    }

    def "user in can see his own companies app"() {
        given:
        app.getStoreAvailabilityMode() >> StoreAvailabilityMode.RESTRICTED_BUYER
        app.getCompany() >> appCompany
        integrator.getCompany() >> appCompany
        userService.isAnonymousUser(_) >> false
        integratorService.getIntegratorByUser(_) >> integrator

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        errors.isEmpty()
    }

    @Unroll
    def "anonymous user can have access to group restricted apps"() {
        given:
        app.getStoreAvailabilityMode() >> StoreAvailabilityMode.RESTRICTED_BUYER_GROUP
        appLicenseService.getUserGroupUidSetFromApp(app) >> appUserGroups
        userService.isAnonymousUser(_) >> true
        anonymousUserCustomerGroupProvider.getCustomerGroupUidForBaseStore(baseStoreModel) >> anonymousUserGroup

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        baseStoreModel.getUid() >> BaseStoreEnum.AA.baseStoreUid
        errors.isEmpty() == expectedResult

        where:
        appUserGroups    | anonymousUserGroup    | expectedResult
        Set.of("ABC000") | Optional.of("ABC000") | true
        Set.of("ABC000") | Optional.of("DEF000") | false
        Set.of("ABC000") | Optional.empty()      | false
        Set.of()         | Optional.of("DEF000") | false
        Set.of()         | Optional.empty()      | false
    }

    def "AA user belonging to the same customer group as the user group within the app can see it in a restricted_buyer_group mode"() {
        given:
        app.getStoreAvailabilityMode() >> StoreAvailabilityMode.RESTRICTED_BUYER_GROUP
        integrator.getCompany() >> appCompany
        appLicenseService.isAppUserGroupsContainsIntegratorCustomerGroup(app, appCompany) >> true

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        baseStoreModel.getUid() >> BaseStoreEnum.AA.baseStoreUid
        errors.isEmpty()
    }
}
