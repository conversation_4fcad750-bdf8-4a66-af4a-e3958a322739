package com.sast.cis.core.job.maintenance;

import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.cronjob.model.CronJobHistoryModel;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.time.TimeService;
import generated.de.hybris.platform.cronjob.model.CronJobBuilder;
import generated.de.hybris.platform.cronjob.model.CronJobHistoryBuilder;
import org.junit.After;
import org.junit.Test;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

import static java.time.Month.FEBRUARY;
import static java.time.Month.JANUARY;
import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class RemoveOldCronJobHistoryEntriesJobPerformableITest extends ServicelayerTransactionalTest {

    private static final Date NOW = Date.from(LocalDate.of(2019, FEBRUARY, 15).atStartOfDay().toInstant(UTC));
    private static final Date HISTORY_ENTRY_TO_CLEAN = Date.from(LocalDate.of(2019, JANUARY, 1).atStartOfDay().toInstant(UTC));
    private static final Date HISTORY_ENTRY_TO_KEEP = Date.from(LocalDate.of(2019, FEBRUARY, 14).atStartOfDay().toInstant(UTC));
    private static final String JOB_CODE_ENTRY_TO_CLEAN = "someJobCode1";
    private static final String CRON_JOB_CODE_ENTRY_TO_CLEAN = "someCronJobCode1";
    private static final String JOB_CODE_ENTRY_TO_KEEP = "someJobCode2";
    private static final String CRON_JOB_CODE_ENTRY_TO_KEEP = "someCronJobCode2";

    @Resource
    private TimeService timeService;

    @Resource
    private ModelService modelService;

    @Resource
    private FlexibleSearchService flexibleSearchService;

    @Resource
    private RemoveOldCronJobHistoryEntriesJobPerformable removeOldCronJobHistoryEntriesJobPerformable;

    @After
    public void cleanup() {
        timeService.resetTimeOffset();
    }

    @Test
    public void oldAndNewHistoryEntryExists_correctlyCleaned() {
        timeService.setCurrentTime(NOW);

        CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("removeOldCronJobHistoryEntriesCronJob")
            .buildMockInstance();

        CronJobHistoryModel historyEntryToDelete = CronJobHistoryBuilder.generate()
            .withStartTime(HISTORY_ENTRY_TO_CLEAN)
            .withCreationtime(HISTORY_ENTRY_TO_CLEAN)
            .withJobCode(JOB_CODE_ENTRY_TO_CLEAN)
            .withCronJobCode(CRON_JOB_CODE_ENTRY_TO_CLEAN)
            .buildIntegrationInstance();

        CronJobHistoryModel historyEntryToKeep = CronJobHistoryBuilder.generate()
            .withStartTime(HISTORY_ENTRY_TO_KEEP)
            .withCreationtime(HISTORY_ENTRY_TO_KEEP)
            .withJobCode(JOB_CODE_ENTRY_TO_KEEP)
            .withCronJobCode(CRON_JOB_CODE_ENTRY_TO_KEEP)
            .buildIntegrationInstance();

        modelService.saveAll(historyEntryToDelete, historyEntryToKeep);

        removeOldCronJobHistoryEntriesJobPerformable.perform(cronJob);

        assertHistoryEntryToDelete();
        assertHistoryEntryToKeep(historyEntryToKeep);
    }

    private void assertHistoryEntryToDelete() {
        CronJobHistoryModel cronJobHistoryModel = new CronJobHistoryModel();
        cronJobHistoryModel.setJobCode(JOB_CODE_ENTRY_TO_CLEAN);
        cronJobHistoryModel.setCronJobCode(CRON_JOB_CODE_ENTRY_TO_CLEAN);
        List<CronJobHistoryModel> historyModels = flexibleSearchService.getModelsByExample(cronJobHistoryModel);
        assertThat(historyModels).isEmpty();
    }

    private void assertHistoryEntryToKeep(CronJobHistoryModel historyEntry) {
        CronJobHistoryModel cronJobHistoryModel = new CronJobHistoryModel();
        cronJobHistoryModel.setJobCode(JOB_CODE_ENTRY_TO_KEEP);
        cronJobHistoryModel.setCronJobCode(CRON_JOB_CODE_ENTRY_TO_KEEP);
        List<CronJobHistoryModel> historyModels = flexibleSearchService.getModelsByExample(cronJobHistoryModel);
        assertThat(historyModels).hasSize(1);
        assertThat(historyModels).contains(historyEntry);
    }
}
