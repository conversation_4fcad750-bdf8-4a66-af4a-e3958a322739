package com.sast.cis.core.resolver;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.category.model.CategoryModel;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.solrfacetsearch.config.IndexConfig;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class DirectParentCategoriesSourceTest {

    private static final String TEST_CATS_QUALIFIER = "supercategories";

    @Mock
    private ModelService modelService;

    @InjectMocks
    private DirectParentCategoriesSource directParentCategoriesSource;

    @Mock
    private IndexedProperty indexedProperty;

    @Mock
    private IndexConfig indexConfig;

    @Mock
    private ProductModel baseProduct;

    @Mock(lenient = true)
    private CategoryModel rootCategory;

    @Mock(lenient = true)
    private CategoryModel category1;

    @Mock(lenient = true)
    private CategoryModel category2;

    @Mock(lenient = true)
    private CategoryModel category21;

    @Before
    public void setup() {
        directParentCategoriesSource.setCategoriesQualifier(TEST_CATS_QUALIFIER);

        when(category1.getSupercategories()).thenReturn(List.of(rootCategory));
        when(category1.getAllSupercategories()).thenReturn(List.of(rootCategory));
        when(category2.getSupercategories()).thenReturn(List.of(rootCategory));
        when(category2.getAllSupercategories()).thenReturn(List.of(rootCategory));
        when(category21.getSupercategories()).thenReturn(List.of(category2));
        when(category21.getAllSupercategories()).thenReturn(List.of(category2));

        when(modelService.getAttributeValue(baseProduct, TEST_CATS_QUALIFIER)).thenReturn(List.of(category1, category21));
    }

    @Test
    public void givenProductWithParentCategories_whenGetCategoriesForConfigAndProperty_thenReturnDirectParents() {
        var result = directParentCategoriesSource.getCategoriesForConfigAndProperty(indexConfig, indexedProperty, baseProduct);

        assertThat(result).containsExactlyInAnyOrder(category1, category21);
    }

    @Test
    public void givenProductWithNoParentCategories_whenGetCategoriesForConfigAndProperty_thenReturnEmpty() {
        given(modelService.getAttributeValue(baseProduct, TEST_CATS_QUALIFIER)).willReturn(List.of());

        var result = directParentCategoriesSource.getCategoriesForConfigAndProperty(indexConfig, indexedProperty, baseProduct);

        assertThat(result).isEmpty();
    }

}
