package com.sast.cis.core.converter.payment


import com.sast.cis.core.data.SepaCreditTransferPaymentInfoData
import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.model.SepaCreditTransferPaymentInfoModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.user.CustomerModel
import de.hybris.platform.servicelayer.dto.converter.ConversionException
import generated.com.sast.cis.core.model.SepaCreditTransferPaymentInfoBuilder
import generated.de.hybris.platform.core.model.user.CustomerBuilder
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class SepaCreditTransferPaymentInfoPopulatorUnitSpec extends JUnitPlatformSpecification {
    private SepaCreditTransferPaymentInfoPopulator sepaCreditTransferPaymentInfoPopulator

    private CustomerModel customer = CustomerBuilder.generate()
            .buildInstance()

    private SepaCreditTransferPaymentInfoModel sepaModel = SepaCreditTransferPaymentInfoBuilder.generate()
            .withSaved(true)
            .withPaymentProvider(PaymentProvider.DPG)
            .withUser(customer)
            .buildInstance()

    def setup() {
        sepaCreditTransferPaymentInfoPopulator = new SepaCreditTransferPaymentInfoPopulator()
        customer.setDefaultPaymentInfo(sepaModel)
    }

    @Test
    def 'populate throws exception if source is null'() {
        given:
        def sepaInfoData = new SepaCreditTransferPaymentInfoData()

        when:
        sepaCreditTransferPaymentInfoPopulator.populate(null, sepaInfoData)

        then:
        thrown(ConversionException)
    }

    @Test
    def 'populate throws exception if target is null'() {
        when:
        sepaCreditTransferPaymentInfoPopulator.populate(sepaModel, null)

        then:
        thrown(ConversionException)
    }

    @Test
    def 'populate converts from given model'() {
        given:
        def sepaInfoData = new SepaCreditTransferPaymentInfoData()

        when:
        sepaCreditTransferPaymentInfoPopulator.populate(sepaModel, sepaInfoData)

        then:
        with(sepaInfoData) {
            verifyAll {
                id == sepaModel.pk.toString()
                saved
                defaultPaymentInfo
                paymentProvider == PaymentProvider.DPG
                paymentMethod == PaymentMethodType.SEPA_CREDIT
                enabled
            }
        }
    }

    @Test
    def 'populate sets default false if info is not default of user'() {
        given:
        def sepaInfoData = new SepaCreditTransferPaymentInfoData()
        customer.setDefaultPaymentInfo(null)

        when:
        sepaCreditTransferPaymentInfoPopulator.populate(sepaModel, sepaInfoData)

        then:
        with(sepaInfoData) {
            verifyAll {
                id == sepaModel.pk.toString()
                saved
                !defaultPaymentInfo
                paymentProvider == PaymentProvider.DPG
                paymentMethod == PaymentMethodType.SEPA_CREDIT
                enabled
            }
        }
    }
}
