package com.sast.cis.core.facade.apk;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.constants.devcon.DevconInputField;
import com.sast.cis.core.data.ApkData;
import com.sast.cis.core.data.ApkMetaData;
import com.sast.cis.core.data.AppVersionMetaData;
import com.sast.cis.core.data.ErrorMessageData;
import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.model.ApkMediaModel;
import com.sast.cis.core.model.AppDraftModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.AppVersionDraftModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.service.ProductContainerService;
import com.sast.cis.core.service.TranslationService;
import com.sast.cis.core.service.apk.ApkPersistenceService;
import com.sast.cis.core.validator.apk.ApkMetaDataValidator;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.keygenerator.KeyGenerator;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.com.sast.cis.core.model.ApkMediaBuilder;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppDraftBuilder;
import generated.com.sast.cis.core.model.AppVersionDraftBuilder;
import generated.com.sast.cis.core.model.PermissionBuilder;
import generated.com.sast.cis.core.model.ProductContainerBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static com.sast.cis.core.constants.devcon.DevconErrorCode.BACKEND_ERROR;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ApkFacadeUnitTest {

    private static final String CONTAINER_CODE = "testContainerCode";
    private static final String VERSION_DRAFT_CODE = "versionDraftCode";
    private final ApkData apkData = new ApkData().withParsedApk(new ApkMetaData()).withProductContainerCode(CONTAINER_CODE);

    @Mock
    private ModelService modelService;

    @Mock
    private ProductContainerService productContainerService;

    @Mock
    private ApkMetaDataValidator apkMetaDataValidator;

    @Mock
    private Converter<MultipartFile, ApkMetaData> apkMetaDataConverter;

    @Mock
    private Converter<AppVersionDraftModel, AppVersionMetaData> appVersionDraftMetaDataConverter;

    @Mock
    private ApkPersistenceService apkPersistenceService;

    @Mock
    private TranslationService translationService;

    @Mock(name = "appVersionDraftCodeGenerator")
    private KeyGenerator keyGenerator;

    @Mock
    private ErrorMessageService errorMessageService;

    @InjectMocks
    private ApkFacade apkFacade;

    @Captor
    private ArgumentCaptor<DevconErrorCode> errorCodeCaptor;

    private Set<ErrorMessageData> validationResult;

    @Before
    public void setUpMocks() {
        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(BACKEND_ERROR);
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class))).thenReturn(devconErrorMessage);
        when(translationService.translate(devconErrorMessage)).thenReturn(new ErrorMessageData().withMessage(BACKEND_ERROR.getValue()));
        validationResult = new HashSet<>();
    }

    @Test
    public void parseApk_apkMetaDataPresent_converterInvokedAndApkMetaDataReturned() throws Exception {
        byte[] apkFile = new byte[20];
        testParseApkNullHandling(apkFile, true);
    }

    @Test
    public void parseApk_apkMetaDataAbsent_converterNotInvoked() throws Exception {
        testParseApkNullHandling(null, false);
    }

    private void testParseApkNullHandling(byte[] apkFile, boolean apkMetaDataExpected) throws Exception {
        ApkMetaData apkMetaData = new ApkMetaData();
        when(apkMetaDataConverter.convert(any())).thenReturn(apkMetaData);

        ApkMetaData parsedApk = apkFacade.parseApk(apkFile);
        assertThat(parsedApk).isEqualTo(apkMetaDataExpected ? apkMetaData : null);
        verify(apkMetaDataConverter, times(apkMetaDataExpected ? 1 : 0)).convert(any());
    }

    @Test
    public void createAppVersionDraft_apkIsProvided_appVersionDraftIsCreated() {
        String productContainerCode = "pc_test";
        String packageName = "my.package.name";
        String permission = "eavesdrop";
        long versionCode = 5L;
        String versionName = "1.2.3-RELEASE";
        String draftCode = "uniqueKey";

        ApkMediaModel apk = ApkMediaBuilder.generate()
            .withPackageName(packageName)
            .withVersionCode(versionCode)
            .withVersionName(versionName)
            .withPermissions(ImmutableList.of(PermissionBuilder.generate().withTechnicalName(permission).buildMockInstance()))
            .buildMockInstance();
        when(apkPersistenceService.persistApkDraft(apkData.getParsedApk(), draftCode)).thenReturn(apk);
        mockProductContainer(productContainerCode, draftCode);

        apkData.getParsedApk()
            .withVersionCode(versionCode)
            .withPackageName(packageName)
            .withPermissions(Collections.singletonList(permission))
            .withVersionName(versionName);
        apkData.withProductContainerCode(productContainerCode);
        apkFacade.createAppVersionDraft(apkData);

        verify(apkPersistenceService).persistApkDraft(apkData.getParsedApk(), draftCode);
        verify(modelService).save(any(AppVersionDraftModel.class));
    }

    private void mockProductContainer(String productContainerCode, String draftCode) {
        AppVersionDraftModel appVersionDraft = AppVersionDraftBuilder.generate().buildInstance(); // no mock
        when(modelService.create(AppVersionDraftModel.class)).thenReturn(appVersionDraft);
        when(keyGenerator.generate()).thenReturn(draftCode);
        ProductContainerModel productContainer = ProductContainerBuilder.generate().buildMockInstance();
        when(productContainerService.getProductContainerForCode(productContainerCode)).thenReturn(Optional.of(productContainer));
    }

    @Test
    public void validateForDraft_apkDataWithoutParsedApk_failsEarlyWithApkParseError() {
        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(DevconErrorCode.APK_PARSE_ERROR)
            .withField(DevconInputField.APK);
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class), any(DevconInputField.class)))
            .thenReturn(devconErrorMessage);
        when(translationService.translate(devconErrorMessage)).thenReturn(new ErrorMessageData()
            .withMessage(DevconErrorCode.APK_PARSE_ERROR.getValue())
            .withInputField(DevconInputField.APK.getValue())
        );

        apkData.setParsedApk(null);

        apkFacade.getContainerWithBasicValidation(apkData, validationResult);

        verify(errorMessageService).createErrorMessage(any(), any());
        verifyNoMoreInteractions(apkMetaDataValidator);
        assertThat(validationResult).hasSize(1);
        assertThat(validationResult).contains(new ErrorMessageData()
            .withMessage(DevconErrorCode.APK_PARSE_ERROR.getValue())
            .withInputField(DevconInputField.APK.getValue()));
    }

    @Test
    public void validateForDraft_productContainerNotFound_returnProductContainerError() {
        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(DevconErrorCode.PRODUCTCONTAINER_NOT_FOUND);
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class)))
            .thenReturn(devconErrorMessage);
        when(translationService.translate(devconErrorMessage))
            .thenReturn(new ErrorMessageData().withMessage(DevconErrorCode.PRODUCTCONTAINER_NOT_FOUND.getValue()));
        when(productContainerService.getProductContainerForCode(CONTAINER_CODE)).thenReturn(Optional.empty());

        ProductContainerModel productContainer = apkFacade.getContainerWithBasicValidation(apkData, validationResult);

        assertThat(productContainer).isNull();
        assertThat(validationResult).hasSize(1);
        assertThat(validationResult).contains(new ErrorMessageData().withMessage(DevconErrorCode.PRODUCTCONTAINER_NOT_FOUND.getValue()));
    }

    @Test
    public void validateForDraft_productContainerCodeInApkDataNull_returnBackendError() {
        apkData.setProductContainerCode(null);
        ProductContainerModel productContainer = apkFacade.getContainerWithBasicValidation(apkData, validationResult);

        assertThat(productContainer).isNull();
        assertThat(validationResult).hasSize(1);
        assertThat(validationResult).contains(new ErrorMessageData().withMessage(BACKEND_ERROR.getValue()));
    }

    @Test
    public void validateForDraft_productContainerHasApp_returnBackendError() {
        AppModel app = AppBuilder.generate().buildMockInstance();
        ProductContainerModel productContainer = ProductContainerBuilder.generate().withApp(app).buildMockInstance();
        when(productContainerService.getProductContainerForCode(CONTAINER_CODE)).thenReturn(Optional.of(productContainer));

        Set<ErrorMessageData> validationResult = apkFacade.validateForDraft(apkData, productContainer);

        assertThat(validationResult).hasSize(1);
        assertThat(validationResult).contains(new ErrorMessageData().withMessage(BACKEND_ERROR.getValue()));
    }

    @Test
    public void validateForDraft_appVersionDraftAlreadyPresent_returnDraftPresentError() {
        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(DevconErrorCode.DRAFT_PRESENT);
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class)))
            .thenReturn(devconErrorMessage);
        when(translationService.translate(devconErrorMessage))
            .thenReturn(new ErrorMessageData().withMessage(DevconErrorCode.DRAFT_PRESENT.getValue()));
        AppVersionDraftModel appVersionDraft = AppVersionDraftBuilder.generate().buildMockInstance();
        AppDraftModel appDraft = AppDraftBuilder.generate()
            .buildMockInstance();
        ProductContainerModel productContainer = ProductContainerBuilder.generate()
            .withAppVersionDraft(appVersionDraft)
            .withAppDraft(appDraft)
            .buildMockInstance();
        when(productContainerService.getProductContainerForCode(CONTAINER_CODE)).thenReturn(Optional.of(productContainer));

        Set<ErrorMessageData> validationResult = apkFacade.validateForDraft(apkData, productContainer);

        assertThat(validationResult).hasSize(1);
        assertThat(validationResult).contains(new ErrorMessageData().withMessage(DevconErrorCode.DRAFT_PRESENT.getValue()));
    }

    @Test
    public void validateForDraft_noError_returnEmptyErrorMap() {
        ProductContainerModel productContainer = ProductContainerBuilder.generate().buildMockInstance();
        when(productContainerService.getProductContainerForCode(CONTAINER_CODE)).thenReturn(Optional.of(productContainer));
        when(apkMetaDataValidator.validateApkForDraft(any())).thenReturn(new HashSet<>());

        Set<ErrorMessageData> validationResult = apkFacade.validateForDraft(apkData, productContainer);

        verify(apkMetaDataValidator).validateApkForDraft(any());
        verifyNoMoreInteractions(apkMetaDataValidator);
        verifyNoMoreInteractions(productContainerService);

        assertThat(validationResult).isEmpty();
    }

    @Test
    public void validateForApp_productContainerHasAppDraft_returnBackendError() {
        AppDraftModel appDraft = AppDraftBuilder.generate().buildMockInstance();
        ProductContainerModel productContainer = ProductContainerBuilder.generate().withAppDraft(appDraft).buildMockInstance();
        when(productContainerService.getProductContainerForCode(CONTAINER_CODE)).thenReturn(Optional.of(productContainer));

        Set<ErrorMessageData> validationResult = apkFacade.validateForApp(apkData, productContainer);

        assertThat(validationResult).hasSize(1);
        assertThat(validationResult).contains(new ErrorMessageData().withMessage(BACKEND_ERROR.getValue()));
    }

    @Test
    public void validateForApp_productContainerHasNoApp_returnNoAppError() {
        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(DevconErrorCode.APP_NOT_FOUND);
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class)))
            .thenReturn(devconErrorMessage);
        when(translationService.translate(devconErrorMessage))
            .thenReturn(new ErrorMessageData().withMessage(DevconErrorCode.APP_NOT_FOUND.getValue()));
        ProductContainerModel productContainer = ProductContainerBuilder.generate().buildMockInstance();
        when(productContainerService.getProductContainerForCode(CONTAINER_CODE)).thenReturn(Optional.of(productContainer));

        Set<ErrorMessageData> validationResult = apkFacade.validateForApp(apkData, productContainer);

        assertThat(validationResult).hasSize(1);
        assertThat(validationResult).contains(new ErrorMessageData().withMessage(DevconErrorCode.APP_NOT_FOUND.getValue()));
    }
}
