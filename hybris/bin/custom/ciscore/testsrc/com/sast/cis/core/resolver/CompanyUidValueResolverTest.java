package com.sast.cis.core.resolver;

import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import de.hybris.platform.solrfacetsearch.indexer.IndexerBatchContext;
import de.hybris.platform.solrfacetsearch.indexer.spi.InputDocument;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collection;
import java.util.List;

import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CompanyUidValueResolverTest {

    private final CompanyUidValueResolver resolver = new CompanyUidValueResolver();

    @Mock
    private InputDocument inputDocument;

    @Mock
    private IndexerBatchContext indexerBatchContext;

    @Mock
    private IndexedProperty indexedProperty;

    @Mock
    private AppModel app;

    @Mock
    private IoTCompanyModel ioTCompany;

    private Collection<IndexedProperty> collection;

    private final String companyUid = "uid";

    @Before
    public void setUp() {
        when(app.getCompany()).thenReturn(ioTCompany);

        when(ioTCompany.getUid()).thenReturn(companyUid);

        collection = List.of(indexedProperty);
    }

    @Test
    public void givenAppNull_whenResolve_thenDoNotAddField() throws Exception {
        resolver.resolve(inputDocument, indexerBatchContext, collection, null);

        verifyZeroInteractions(inputDocument);
    }

    @Test
    public void givenAppHasNoCompany_whenResolve_thenDoNotAddField() throws Exception {
        when(app.getCompany()).thenReturn(null);

        resolver.resolve(inputDocument, indexerBatchContext, collection, app);

        verifyZeroInteractions(inputDocument);
    }

    @Test
    public void givenAppWithCompany_whenResolve_thenAddFieldWithCompanyId() throws Exception {
        resolver.resolve(inputDocument, indexerBatchContext, collection, app);

        verify(inputDocument).addField(indexedProperty, companyUid);
    }

}