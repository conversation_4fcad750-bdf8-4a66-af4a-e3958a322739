package com.sast.cis.core.validator.storecontent;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sast.cis.core.constants.CiscoreConstants;
import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.constants.devcon.DevconInputField;
import com.sast.cis.core.dao.IndustryDao;
import com.sast.cis.core.data.EulaData;
import com.sast.cis.core.data.IndustryData;
import com.sast.cis.core.data.PdfData;
import com.sast.cis.core.data.StoreContentData;
import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.enums.EulaType;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.model.UseCaseModel;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.service.HtmlSanitizingService;
import com.sast.cis.core.service.ProductContainerService;
import com.sast.cis.core.service.UseCaseService;
import com.sast.cis.core.validator.IsocodeMapValidator;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.commerceservices.i18n.CommerceCommonI18NService;
import de.hybris.platform.core.PK;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import de.hybris.platform.storelocator.map.Map;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.ProductContainerBuilder;
import generated.com.sast.cis.core.model.UseCaseBuilder;
import generated.de.hybris.platform.core.model.c2l.LanguageBuilder;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.util.HashMap;
import java.util.Optional;
import java.util.Set;

import static com.sast.cis.test.utils.TestDataConstants.PRODUCT_CONTAINER.APP_NAME;
import static com.sast.cis.test.utils.TestDataConstants.basicStoreContent;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(DataProviderRunner.class)
public class AppStoreContentValidatorUnitTest {
    private static final LanguageModel DEFAULT_LANGUAGE = LanguageBuilder.generate().withIsocode("en").buildMockInstance();

    @Rule
    public MockitoRule mockito = MockitoJUnit.rule();

    @Mock
    private CommerceCommonI18NService commerceCommonI18NService;

    @Mock
    private IsocodeMapValidator isocodeMapValidator;

    @Mock
    private ProductContainerService productContainerService;

    @Mock
    private ConfigurationService configurationService;

    @Mock
    private Configuration configuration;

    @Mock
    private ErrorMessageService errorMessageService;

    @Mock
    private IndustryDao industryDao;

    @Mock
    private UseCaseService useCaseService;

    @Mock
    private AppNameValidator appNameValidator;

    @Mock
    private AppVideoUrlValidator appVideoUrlValidator;

    @Mock
    private HtmlSanitizingService htmlSanitizingService;

    private AppStoreContentValidator validator;

    @Before
    public void prepareMocks() {
        ProductContainerModel productContainer = createProductContainer();

        when(commerceCommonI18NService.getDefaultLanguage()).thenReturn(DEFAULT_LANGUAGE);
        when(isocodeMapValidator.isValidMapOfLanguageIsocodes(anyMapOf(String.class, String.class))).thenReturn(true);
        when(configuration.getInt(eq(CiscoreConstants.EMAIL_LENGTH), anyInt())).thenReturn(255);
        when(configuration.getInt(eq(CiscoreConstants.NAME_LENGTH), anyInt())).thenReturn(30);
        when(configuration.getInt(eq(CiscoreConstants.DESCRIPTION_LENGTH), anyInt())).thenReturn(2000);
        when(configuration.getInt(eq(CiscoreConstants.SUMMARY_LENGTH), anyInt())).thenReturn(200);
        when(configuration.getInt(eq(CiscoreConstants.URL_LENGTH), anyInt())).thenReturn(255);
        when(configuration.getInt(eq(CiscoreConstants.PHONE_LENGTH), anyInt())).thenReturn(255);
        when(configuration.getInt(eq(CiscoreConstants.NUMBER_OF_PDF_DOCUMENTS), anyInt())).thenReturn(15);
        when(configurationService.getConfiguration()).thenReturn(configuration);
        when(productContainerService.getProductContainerForCode(any())).thenReturn(Optional.of(productContainer));
        UseCaseModel useCaseModel = UseCaseBuilder.generate().withName("Other").withEnabled(true).buildMockInstance();
        when(useCaseModel.getPk()).thenReturn(PK.fromLong(1L));
        when(useCaseService.getAllEnabledUseCases()).thenReturn(ImmutableList.of(useCaseModel));
        when(htmlSanitizingService.sanitizeInput(anyString())).thenAnswer(answer -> answer.getArguments()[0]);
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class)))
            .thenAnswer(answer -> new DevconErrorMessage().withCode(answer.getArgument(0, DevconErrorCode.class)));
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class), any(DevconInputField.class)))
            .thenAnswer(answer -> new DevconErrorMessage()
                .withCode(answer.getArgument(0, DevconErrorCode.class))
                .withField(answer.getArgument(1, DevconInputField.class)));

        validator = new AppStoreContentValidator(null,
            configurationService,
            errorMessageService,
            appNameValidator,
            commerceCommonI18NService,
            isocodeMapValidator,
            productContainerService,
            industryDao,
            useCaseService,
            htmlSanitizingService,
            appVideoUrlValidator);
    }

    private ProductContainerModel createProductContainer() {
        AppModel app = AppBuilder.generate().withApprovalStatus(APPROVED).buildMockInstance();
        return ProductContainerBuilder.generate()
            .withApp(app)
            .buildMockInstance();
    }

    @Test
    public void validate_noAppFound_fails() {
        ProductContainerModel productContainerWithoutApp = ProductContainerBuilder.generate().buildMockInstance();
        when(productContainerService.getProductContainerForCode(any())).thenReturn(Optional.of(productContainerWithoutApp));

        assertThat(validator.validate(basicStoreContent()))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.APP_NOT_FOUND));
    }

    @Test
    public void validate_failOnInvalidDescriptionMap() {
        HashMap<String, String> descriptionMap = new HashMap<>() {{
            put("en", APP_NAME);
            put("abc", "noSuchLanguage");
        }};
        StoreContentData newStoreContentData = basicStoreContent().withDescriptionByIsocode(descriptionMap);
        when(isocodeMapValidator.isValidMapOfLanguageIsocodes(descriptionMap)).thenReturn(false);
        assertThat(validator.validate(newStoreContentData))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.INVALID_LANGUAGE_DATA));
    }

    @Test
    public void validate_failOnMissingPrivacyPolicy() {
        assertThat(validator.validate(basicStoreContent().withPrivacyPolicyUrl("")))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.URL_EMPTY).withField(DevconInputField.PRIVACY_POLICY_URL));
    }

    @Test
    public void validate_failOnMissingTermsOfUse() {
        assertThat(validator.validate(basicStoreContent().withEula(new EulaData().withType(EulaType.CUSTOM))))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.URL_EMPTY).withField(DevconInputField.TERMS_OF_USE_URL));
    }

    @Test
    public void validate_failOnMissingEmail() {
        assertThat(validator.validate(basicStoreContent().withEmailAddress("")))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.EMAIL_EMPTY).withField(DevconInputField.EMAIL_KEY));
    }

    @Test
    public void failOnDuplicateDisplayNames() {
        PdfData pdf1 = new PdfData().withCode("1").withDisplayName("name");
        PdfData pdf2 = new PdfData().withCode("2").withDisplayName("name");

        StoreContentData storeContentData = basicStoreContent().withDocumentationFiles(ImmutableList.of(pdf1, pdf2));
        assertThat(validator.validate(storeContentData))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.DOCUMENTS_NAMES_NOT_UNIQUE).withField(DevconInputField.DOCUMENTS));
    }

    @Test
    public void validateTextFields_givenAppWithPendingApproval_fails() {
        Optional<ProductContainerModel> productContainer = Optional.of(ProductContainerBuilder.generate()
            .withApp(AppBuilder.generate().withApprovalStatus(ArticleApprovalStatus.CHECK).buildMockInstance()).buildMockInstance());
        when(productContainerService.getProductContainerForCode(any())).thenReturn(productContainer);
        assertThat(validator.validate(basicStoreContent()))
            .contains(new DevconErrorMessage().withCode(DevconErrorCode.APP_VERSION_IN_APPROVAL));
    }

    @Test
    public void validate_shouldReturnValidationErrorForUnsupportedIndustry() {
        IndustryData industry = new IndustryData().withId(123L).withName("test");
        StoreContentData storeContentData = basicStoreContent().withIndustries(ImmutableList.of(industry));

        Set<DevconErrorMessage> response = validator.validate(storeContentData);

        assertThat(response).hasSize(1);
        DevconErrorMessage expectedError = new DevconErrorMessage()
            .withCode(DevconErrorCode.STORE_CONTENT_INDUSTRIES_NOT_SUPPORTED)
            .withField(DevconInputField.INDUSTRY)
            .withAdditionalProperty("test");
        assertThat(response).contains(expectedError);
    }

    @Test
    public void validate_shouldReturnValidationErrorWhenIndustryIsNotSpecified() {
        StoreContentData storeContent = basicStoreContent()
            .withIndustries(null);
        Set<DevconErrorMessage> response = validator.validate(storeContent);

        assertThat(response).isEmpty();
    }
}
