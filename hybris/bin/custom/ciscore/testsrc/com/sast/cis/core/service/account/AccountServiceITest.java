package com.sast.cis.core.service.account;

import com.sast.cis.core.BaseIntegrationTest;
import com.sast.cis.core.model.AccountDeletionModel;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IoTCompanyModel;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import generated.com.sast.cis.core.model.DeveloperBuilder;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class AccountServiceITest extends BaseIntegrationTest {

    @Resource
    private AccountService accountService;

    @Resource
    private FlexibleSearchService flexibleSearchService;

    @Test
    public void givenCompany_whenDeactivateCompany_thenCreateAccountDeletion() {
        final IoTCompanyModel company = createTestCompany();

        assertThat(findAccountDeletions(company.getUid())).isEmpty();

        accountService.deactivateCompany(company.getUid());

        assertThat(company.getDeactivationDate()).isNotNull();
        assertThat(findAccountDeletions(company.getUid())).isNotEmpty();
    }

    @Test
    public void givenDeveloper_whenDeactivateUser_thenCreateAccountDeletion() {
        final String developerUid = UUID.randomUUID().toString();
        final String developerInternalId = developerUid + "@devcon";
        final DeveloperModel developer = createTestDeveloper(developerInternalId);

        assertThat(findAccountDeletions(developerInternalId)).isEmpty();

        accountService.deactivateUser(developerUid);

        assertThat(developer.getDeactivationDate()).isNotNull();
        assertThat(findAccountDeletions(developerInternalId)).isNotEmpty();
    }

    private List<AccountDeletionModel> findAccountDeletions(final String accountId) {
        final AccountDeletionModel accountDeletion = modelService.create(AccountDeletionModel.class);
        accountDeletion.setUid(accountId);
        return flexibleSearchService.getModelsByExample(accountDeletion);
    }

    private DeveloperModel createTestDeveloper(final String developerId) {
        final DeveloperModel developer = DeveloperBuilder.generate()
            .withUid(developerId)
            .withName("NAME_" + developerId)
            .withCompany(createTestCompany())
            .buildIntegrationInstance();
        modelService.save(developer);
        return developer;
    }

    private IoTCompanyModel createTestCompany() {
        return sampleDataCreator.createIotCompany();
    }
}
