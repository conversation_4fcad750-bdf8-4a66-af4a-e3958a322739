package com.sast.cis.core.search.context;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.model.classification.ClassAttributeAssignmentModel;
import de.hybris.platform.catalog.model.classification.ClassificationAttributeValueModel;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import de.hybris.platform.solrfacetsearch.config.IndexedType;
import de.hybris.platform.solrfacetsearch.search.BoostField;
import de.hybris.platform.solrfacetsearch.search.SearchQuery;
import de.hybris.platform.solrfacetsearch.search.context.FacetSearchContext;
import generated.de.hybris.platform.catalog.model.classification.ClassAttributeAssignmentBuilder;
import generated.de.hybris.platform.catalog.model.classification.ClassificationAttributeValueBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static de.hybris.platform.solrfacetsearch.search.BoostField.BoostType.MULTIPLICATIVE;
import static de.hybris.platform.solrfacetsearch.search.SearchQuery.QueryOperator.EQUAL_TO;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class SolrClassificationAttributeValueTermBoostListenerUnitTest {

    private static final String INDEX_FIELD = "indexField";
    private static final String ATTRIBUTE_VALUE = "AttributeValue";
    private static final float TERM_BOOST = 500F;
    private final BoostField EXPECTED_BOOST_FIELD = new BoostField(INDEX_FIELD, EQUAL_TO, ATTRIBUTE_VALUE, TERM_BOOST, MULTIPLICATIVE);
    @Captor
    ArgumentCaptor<BoostField> bootFieldCaptor;
    private List<String> indexFieldsToBoost = new ArrayList<>();
    private SolrClassificationAttributeValueTermBoostListener solrTermBoostListener;
    @Mock
    private CommonI18NService commonI18NService;
    @Mock
    private FacetSearchContext facetSearchContext;
    @Mock
    private SearchQuery searchQuery;
    @Mock
    private IndexedType indexedType;
    @Mock
    private Map<String, IndexedProperty> indexedProperties;

    @Before
    public void setup() {
        ClassificationAttributeValueModel attributeValue = ClassificationAttributeValueBuilder.generate().buildMockInstance();
        when(attributeValue.getName(any())).thenReturn(ATTRIBUTE_VALUE);
        when(attributeValue.getTermBoost()).thenReturn(TERM_BOOST);

        ClassAttributeAssignmentModel classAttributeAssignment = ClassAttributeAssignmentBuilder.generate()
            .withAttributeValues(List.of(attributeValue))
            .buildMockInstance();

        IndexedProperty indexedProperty = new IndexedProperty();
        indexedProperty.setName(INDEX_FIELD);
        indexedProperty.setClassAttributeAssignment(classAttributeAssignment);
        when(indexedProperties.get(any())).thenReturn(indexedProperty);
        solrTermBoostListener = new SolrClassificationAttributeValueTermBoostListener();
        solrTermBoostListener.setCommonI18NService(commonI18NService);
        solrTermBoostListener.setIndexFieldsToBoost(indexFieldsToBoost);
        when(facetSearchContext.getIndexedType()).thenReturn(indexedType);
        when(facetSearchContext.getSearchQuery()).thenReturn(searchQuery);
        when(indexedType.getIndexedProperties()).thenReturn(indexedProperties);
        when(commonI18NService.getLocaleForIsoCode(any())).thenReturn(Locale.ENGLISH);
    }

    @Test
    public void beforeSearch_emptyIndexFieldsToBoost_doesNothing() {
        when(indexedProperties.get(any())).thenReturn(null);
        solrTermBoostListener.beforeSearch(facetSearchContext);
        verifyZeroInteractions(facetSearchContext);
    }

    @Test
    public void beforeSearch_configuredIndexFieldsToBoost_addsBoostFieldToSolrQuery() {
        solrTermBoostListener.setIndexFieldsToBoost(List.of(INDEX_FIELD));
        solrTermBoostListener.beforeSearch(facetSearchContext);
        verify(searchQuery, times(1)).addBoost(bootFieldCaptor.capture());
        assertThat(bootFieldCaptor.getValue()).isEqualToComparingFieldByField(EXPECTED_BOOST_FIELD);
    }

}
