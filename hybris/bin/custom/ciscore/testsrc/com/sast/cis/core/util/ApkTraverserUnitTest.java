package com.sast.cis.core.util;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.service.apk.Manifests;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;

import java.io.IOException;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
@RunWith(DataProviderRunner.class)
public class ApkTraverserUnitTest {

    @DataProvider
    public static Object[][] definesActivities() {
        return new Object[][] {
            { Manifests.WITHOUT_ACTIVITY, false },
            { Manifests.WITH_ACTIVITY, true } };
    }

    @Test
    @UseDataProvider
    public void definesActivities(String manifest, boolean expected) throws ParserConfigurationException, SAXException, IOException {
        ApkTraverser apkTraverser = new ApkTraverser(manifest);
        assertThat(apkTraverser.definesActivities()).isEqualTo(expected);
    }

    @DataProvider
    public static Object[][] hasStartHookService() {
        return new Object[][] {
            { Manifests.WITH_SERVICE, true },
            { Manifests.WITH_ACTIVITY, false }
        };
    }

    @Test
    @UseDataProvider
    public void hasStartHookService(String manifest, boolean expected) throws ParserConfigurationException, SAXException, IOException {
        ApkTraverser apkTraverser = new ApkTraverser(manifest);
        assertThat(apkTraverser.hasStartHookService()).isEqualTo(expected);
    }

    @DataProvider
    public static Object[][] getSdkAddons() {
        return new Object[][] {
            { Manifests.WITH_NO_SDK_VERSION, ImmutableList.of() },
            { Manifests.WITH_ACTIVITY, ImmutableList.of(new SdkAddonTag("com.securityandsafetythings.apis.v1", true)) },
            { Manifests.WITH_MULTIPLE_SDK_VERSIONS, ImmutableList.of(new SdkAddonTag("com.securityandsafetythings.apis.v1", true),
                new SdkAddonTag("com.securityandsafetythings.apis.v1", true)) },
            { Manifests.WITH_WRONG_SDK_VERSION, ImmutableList.of(new SdkAddonTag("io.somewrongsdk.apis.v1", true)) },
            { Manifests.WITH_CORRECT_AND_WRONG_SDK_VERSION_ATTRIBUTE_FALSE,
                ImmutableList.of(new SdkAddonTag("io.sast.apis.v1", true), new SdkAddonTag("io.somewrongsdk.apis.v1", false)) }
        };
    }

    @Test
    @UseDataProvider
    public void getSdkAddons(String manifest, List<SdkAddonTag> expected) throws ParserConfigurationException, SAXException, IOException {
        ApkTraverser apkTraverser = new ApkTraverser(manifest);
        assertThat(apkTraverser.getSdkAddonTags()).containsExactlyElementsOf(expected);
    }

    @DataProvider
    public static Object[][] numberOfMinAndroidApiVersions() {
        return new Object[][] {
            { Manifests.WITH_NO_MIN_ANDROID_API_VERSION, 0 },
            { Manifests.WITH_ACTIVITY, 1 },
            { Manifests.WITH_MORE_THAN_ONE_ANDROID_API_VERSIONS, 2 },
        };
    }

    @Test
    @UseDataProvider
    public void numberOfMinAndroidApiVersions(String manifest, int expected)
        throws ParserConfigurationException, SAXException, IOException {
        ApkTraverser apkTraverser = new ApkTraverser(manifest);
        assertThat(apkTraverser.getNumberOfMinAndroidApiVersions()).isEqualTo(expected);
    }

}