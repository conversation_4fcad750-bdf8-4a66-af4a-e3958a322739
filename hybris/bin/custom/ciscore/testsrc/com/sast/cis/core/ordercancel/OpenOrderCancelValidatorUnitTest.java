package com.sast.cis.core.ordercancel;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@UnitTest
public class OpenOrderCancelValidatorUnitTest {

    @Mock
    private OrderModel order;

    @InjectMocks
    private OpenOrderCancelValidator openOrderCancelValidator;

    @Before
    public void setUp() {
        when(order.getCode()).thenReturn("OPEN_ORDER_123");
        when(order.getStatus()).thenReturn(OrderStatus.OPEN);
    }

    @Test
    public void givenOrderWithOpenStatusWhenValidateForCancellationThenReturnTrue() {

        assertThat(openOrderCancelValidator.validateForCancellation(order)).isTrue();
    }

    @Test
    public void givenOrderWithNonOpenStatusWhenValidateForCancellationThenReturnFalse() {
        when(order.getStatus()).thenReturn(OrderStatus.COMPLETED);

        assertThat(openOrderCancelValidator.validateForCancellation(order)).isFalse();
    }
}
