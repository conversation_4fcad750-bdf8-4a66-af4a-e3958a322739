package com.sast.cis.core.service.customer;

import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IntegratorModel;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import de.hybris.bootstrap.annotations.UnitTest;
import generated.com.sast.cis.core.model.IntegratorBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
@RunWith(DataProviderRunner.class)
public class InternalIntegratorUidTranslationServiceUnitTest {

    @InjectMocks
    private InternalIntegratorUidTranslationService internalIntegratorUidTranslationService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @DataProvider(value = {"", " "})
    @Test(expected = IllegalArgumentException.class)
    public void testMissingCombinationOfIdAndName_throws(String id) {
        internalIntegratorUidTranslationService.translateToInternalUid(id);
    }

    @Test
    public void testSuccess() {
        String id = "<EMAIL>";
        String expectedInternalUid = "<EMAIL>@shop";
        String internalUid = internalIntegratorUidTranslationService.translateToInternalUid(id);
        assertThat(internalUid).isEqualTo(expectedInternalUid);
    }

    @Test
    public void test_translateToSsoId() {
        String givenInternalUid = "<EMAIL>@shop";
        String expectedSsoId = "<EMAIL>";
        IntegratorModel mockIntegrator = IntegratorBuilder.generate()
            .withUid(givenInternalUid)
            .buildMockInstance();
        String actualSsoId = internalIntegratorUidTranslationService.translateToSsoId(mockIntegrator);
        assertThat(actualSsoId).isEqualTo(expectedSsoId);
    }
}
