package com.sast.cis.core.order.hook;

import com.sast.cis.core.order.split.SplitOrderService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commerceservices.service.data.CommerceCheckoutParameter;
import de.hybris.platform.commerceservices.service.data.CommerceOrderResult;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.order.OrderService;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@UnitTest
public class CisCommercePlaceOrderMethodHookUnitTest {

    @InjectMocks
    private CisCommercePlaceOrderMethodHook cisCommercePlaceOrderMethodHook;

    @Mock
    private SplitOrderService splitOrderService;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderModel aOrderModel;

    @Mock
    private CommerceCheckoutParameter commerceCheckoutParameter;

    @Mock
    private CommerceOrderResult commerceOrderResult;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void beforeSubmitOrder_hasEntriesToSplit_splitOrderSubmitted() {
        OrderModel orderModel = OrderBuilder.generate().buildMockInstance();
        when(splitOrderService.splitIfNeeded(aOrderModel)).thenReturn(List.of(orderModel));
        when(commerceOrderResult.getOrder()).thenReturn(aOrderModel);

        cisCommercePlaceOrderMethodHook.beforeSubmitOrder(commerceCheckoutParameter, commerceOrderResult);

        verify(orderService, times(0)).submitOrder(aOrderModel);
        verify(orderService, times(1)).submitOrder(orderModel);
    }

    @Test
    public void beforeSubmitOrder_noEntriesToSplit_NoOrderSubmitted() {
        when(splitOrderService.splitIfNeeded(aOrderModel)).thenReturn(Collections.emptyList());
        when(commerceOrderResult.getOrder()).thenReturn(aOrderModel);

        cisCommercePlaceOrderMethodHook.beforeSubmitOrder(commerceCheckoutParameter, commerceOrderResult);

        verifyZeroInteractions(orderService);
    }
}