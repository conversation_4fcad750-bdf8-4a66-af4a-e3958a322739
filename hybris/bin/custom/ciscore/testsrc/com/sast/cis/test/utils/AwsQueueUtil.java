package com.sast.cis.test.utils;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.GetQueueUrlResult;
import com.amazonaws.services.sqs.model.PurgeQueueRequest;

public class AwsQueueUtil {

    public static AmazonSQS getClient() {
        return AmazonSQSClientBuilder.standard().withCredentials(getCredentialsProvider())
            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration("http://localhost:4566", "eu-central-1"))
            .build();
    }

    public static void purge(AmazonSQS client) {
        GetQueueUrlResult queueUrl = client.getQueueUrl("IotStore-successful_orders");
        client.purgeQueue(new PurgeQueueRequest(queueUrl.getQueueUrl()));
    }

    private static AWSCredentialsProvider getCredentialsProvider() {
        return new AWSStaticCredentialsProvider(new BasicAWSCredentials("foo", "bar"));
    }
}
