package com.sast.cis.core.validator;

import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.data.UmpUserData;
import com.sast.cis.core.exceptions.IncompleteCompanyDataException;
import com.sast.cis.core.exceptions.IncompleteUserDataException;
import com.sast.cis.core.exceptions.InvalidCountryException;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import generated.de.hybris.platform.core.model.c2l.CountryBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class UmpDataValidatorUnitTest {

    @InjectMocks
    private UmpDataValidator umpDataValidator;

    @Mock
    private CommonI18NService commonI18NService;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Before
    public void setUp() throws Exception {
        CountryModel country = CountryBuilder.generate().withIsocode("SAMPLE").withActive(true).buildMockInstance();
        when(commonI18NService.getCountry(anyString())).thenReturn(country);
    }

    @Test
    public void validateUserData_happyCase() {
        UmpUserData umpUserData = new UmpUserData().withFirstName("Tester").withLastName("Test");

        umpDataValidator.validateUserData(umpUserData);
        //no exception thrown
    }

    @Test
    public void validateUserData_onlyFirstNameIsValid() {
        UmpUserData umpUserData = new UmpUserData().withFirstName("Tester");

        umpDataValidator.validateUserData(umpUserData);
        //no exception thrown
    }

    @Test
    public void validateUserData_onlyLastNameIsValid() {
        UmpUserData umpUserData = new UmpUserData().withLastName("Tester");

        umpDataValidator.validateUserData(umpUserData);
        //no exception thrown
    }

    @Test
    public void validateUserData_completelyEmptyNameIsInvalid() {
        UmpUserData umpUserData = new UmpUserData();
        expectedException.expect(IncompleteUserDataException.class);

        umpDataValidator.validateUserData(umpUserData);
    }

    @Test
    public void validateCompanyData_nameIsSetAndCountryActive_valid() {
        UmpCompanyData umpCompanyData = new UmpCompanyData().withCompanyName("TestCompany").withCompanyCountry("Testland");

        umpDataValidator.validateCompanyData(umpCompanyData);
        //no exception thrown
    }

    @Test
    public void validateCompanyData_nameIsNotSet_invalid() {
        UmpCompanyData umpCompanyData = new UmpCompanyData().withCompanyCountry("Testland");
        expectedException.expect(IncompleteCompanyDataException.class);

        umpDataValidator.validateCompanyData(umpCompanyData);
    }

    @Test
    public void validateCompanyData_countryIsInactive_invalid() {
        CountryModel country = CountryBuilder.generate().withIsocode("SAMPLE").withActive(false).buildMockInstance();
        when(commonI18NService.getCountry(anyString())).thenReturn(country);

        UmpCompanyData umpCompanyData = new UmpCompanyData().withCompanyName("TestCompany").withCompanyCountry("Testland");
        expectedException.expect(InvalidCountryException.class);

        umpDataValidator.validateCompanyData(umpCompanyData);
    }

    @Test
    public void validateCompanyData_countryIsEmpty_invalid() {
        UmpCompanyData umpCompanyData = new UmpCompanyData().withCompanyName("TestCompany");
        expectedException.expect(InvalidCountryException.class);

        umpDataValidator.validateCompanyData(umpCompanyData);
    }

    @Test
    public void validateUserNameFormat_noUuid_invalid() {
        UmpUserData umpUserData = new UmpUserData().withUserName("Not a UUID");
        expectedException.expect(IncompleteUserDataException.class);

        umpDataValidator.validateUserNameFormat(umpUserData);
    }

    @Test
    public void validateUserNameFormat_uuid_valid() {
        UmpUserData umpUserData = new UmpUserData().withUserName(UUID.randomUUID().toString());

        umpDataValidator.validateUserNameFormat(umpUserData);
        //no exception thrown
    }
}