package com.sast.cis.core.service.apk;

import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import org.junit.Test;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class ApkSignatureServiceITest extends ServicelayerTransactionalTest {

    @Resource
    private ApkSignatureService apkSignatureService;

    @Test
    public void givenBlockedCN_whenIsSignerCertSubjectCNameBlocked_thenReturnTrue() {
        final String blockedCN = "Android Debug";

        assertThat(apkSignatureService.isSignerCertSubjectCNameBlocked(blockedCN)).isTrue();
    }

    @Test
    public void givenBlockedCNWithDifferentCase_whenIsSignerCertSubjectCNameBlocked_thenReturnTrue() {
        final String blockedCN = "Android DEBUG";

        assertThat(apkSignatureService.isSignerCertSubjectCNameBlocked(blockedCN)).isTrue();
    }

    @Test
    public void givenNonBlockedCN_whenIsSignerCertSubjectCNameBlocked_thenReturnFalse() {
        final String blockedCN = "Android Prod";

        assertThat(apkSignatureService.isSignerCertSubjectCNameBlocked(blockedCN)).isFalse();
    }
}