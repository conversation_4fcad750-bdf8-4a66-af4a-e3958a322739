package com.sast.cis.core.paymentintegration

import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.paymentintegration.impl.CartPaymentServiceStrategy
import com.sast.cis.core.service.company.IotCompanyService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

@UnitTest
class CartPaymentServiceStrategyUnitSpec extends JUnitPlatformSpecification {
    PaymentMethodStrategy paymentMethodStrategy = Mock(PaymentMethodStrategy)
    PaymentService zeroPaymentService = Mock(PaymentService)
    PaymentService boschTransferPaymentService = Mock(PaymentService)
    PaymentService dpgPaymentService = Mock(PaymentService)
    PaymentService stripePaymentService = Mock(PaymentService)

    PaymentServiceStrategy paymentServiceStrategy

    AppLicenseModel mockLicense = Mock(AppLicenseModel)
    IoTCompanyModel mockBuyerCompany = Mock(IoTCompanyModel)

    void setup() {
        zeroPaymentService.getPaymentProvider() >> PaymentProvider.ZERO
        boschTransferPaymentService.getPaymentProvider() >> PaymentProvider.BOSCH_TRANSFER
        dpgPaymentService.getPaymentProvider() >> PaymentProvider.DPG
        stripePaymentService.getPaymentProvider() >> PaymentProvider.STRIPE

        paymentServiceStrategy = new CartPaymentServiceStrategy(paymentMethodStrategy)
        paymentServiceStrategy.setPaymentServices(
                List.of(zeroPaymentService, boschTransferPaymentService, dpgPaymentService, stripePaymentService))
    }

    @Test
    def 'return zero payment service if cart value is 0'() {
        given:
        CartModel cart = Mock(CartModel)

        IoTCompanyModel sellerCompany = getSellerMock()
        IoTCompanyModel buyerCompany = getBuyerMock()
        cart.getCompany() >> buyerCompany

        IotCompanyService companyService = Mock(IotCompanyService)
        companyService.getDeveloperCompanyForOrderItems(cart) >> sellerCompany

        PaymentService zeroPaymentService = Mock(PaymentService)
        zeroPaymentService.supportsPurchase(PaymentMethodType.ZERO, buyerCompany, Set.of()) >> true
        PaymentService dpgPaymentService = Mock(PaymentService)
        dpgPaymentService.supportsPurchase(PaymentMethodType.ZERO, buyerCompany, Set.of()) >> false
        PaymentMethodStrategy paymentMethodStrategy = Mock(PaymentMethodStrategy)
        paymentMethodStrategy.getPaymentMethods(cart) >> Set.of(PaymentMethodType.ZERO)


        paymentServiceStrategy = new CartPaymentServiceStrategy(paymentMethodStrategy)
        paymentServiceStrategy.setPaymentServices(List.of(zeroPaymentService, dpgPaymentService))

        expect:
        paymentServiceStrategy.getPaymentServices(cart) == Map.of(PaymentMethodType.ZERO, zeroPaymentService)
    }

    @Test
    def 'a license can be paid with all payment methods, prefer DPG on conflict'() {
        given:
        paymentMethodStrategy.getPaymentMethods(mockLicense, mockBuyerCompany) >> Set.of(PaymentMethodType.ACH_INTERNATIONAL, PaymentMethodType.SEPA_CREDIT, PaymentMethodType.CREDIT_CARD)
        dpgPaymentService.supportsPurchase(PaymentMethodType.ACH_INTERNATIONAL, mockBuyerCompany, Set.of(mockLicense)) >> true
        dpgPaymentService.supportsPurchase(PaymentMethodType.SEPA_CREDIT, mockBuyerCompany, Set.of(mockLicense)) >> true
        stripePaymentService.supportsPurchase(PaymentMethodType.SEPA_CREDIT, mockBuyerCompany, Set.of(mockLicense)) >> true
        stripePaymentService.supportsPurchase(PaymentMethodType.CREDIT_CARD, mockBuyerCompany, Set.of(mockLicense)) >> true

        when:
        def actualServices = paymentServiceStrategy.getPaymentServices(mockLicense, mockBuyerCompany)

        then:
        actualServices == [
                (PaymentMethodType.ACH_INTERNATIONAL): dpgPaymentService,
                (PaymentMethodType.SEPA_CREDIT)      : dpgPaymentService,
                (PaymentMethodType.CREDIT_CARD)      : stripePaymentService
        ]
    }

    @Test
    def 'payment method is not supported for the given company combination by any PSP, return empty map'() {
        given:
        paymentMethodStrategy.getPaymentMethods(mockLicense, mockBuyerCompany) >> Set.of(PaymentMethodType.SEPA_CREDIT)
        dpgPaymentService.supportsPurchase(PaymentMethodType.ACH_INTERNATIONAL, mockBuyerCompany, Set.of(mockLicense)) >> true
        dpgPaymentService.supportsPurchase(PaymentMethodType.SEPA_CREDIT, mockBuyerCompany, Set.of(mockLicense)) >> false
        stripePaymentService.supportsPurchase(PaymentMethodType.SEPA_CREDIT, mockBuyerCompany, Set.of(mockLicense)) >> false
        stripePaymentService.supportsPurchase(PaymentMethodType.CREDIT_CARD, mockBuyerCompany, Set.of(mockLicense)) >> true

        when:
        def actualServices = paymentServiceStrategy.getPaymentServices(mockLicense, mockBuyerCompany)

        then:
        actualServices == [] as HashMap
    }

    @Test
    def 'only one payment method is supported by the PSPs, only return that payment method'() {
        given:
        paymentMethodStrategy.getPaymentMethods(mockLicense, mockBuyerCompany) >> Set.of(
                PaymentMethodType.ZERO, PaymentMethodType.SEPA_CREDIT, PaymentMethodType.ACH_INTERNATIONAL, PaymentMethodType.CREDIT_CARD)
        zeroPaymentService.supportsPurchase(PaymentMethodType.ZERO, mockBuyerCompany, Set.of(mockLicense)) >> true

        when:
        def actualServices = paymentServiceStrategy.getPaymentServices(mockLicense, mockBuyerCompany)

        then:
        actualServices == [(PaymentMethodType.ZERO): zeroPaymentService]
    }

    @Test
    def 'given license is null, exception is thrown'() {
        when:
        paymentServiceStrategy.getPaymentServices(null, mockBuyerCompany)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    def 'given buyer company is null, exception is thrown'() {
        when:
        paymentServiceStrategy.getPaymentServices(mockLicense, null)

        then:
        thrown(IllegalArgumentException)
    }

    private def getSellerMock() {
        IoTCompanyModel seller = Mock(IoTCompanyModel)
        return seller
    }

    private def getBuyerMock() {
        IoTCompanyModel buyer = Mock(IoTCompanyModel)
        return buyer
    }
}
