package com.sast.cis.core.service.customer.developer;

import com.sast.cis.core.exceptions.user.UserNotDeveloperException;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.service.customer.InternalDeveloperUidTranslationService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commerceservices.impersonation.ImpersonationService;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.user.UserService;
import generated.com.sast.cis.core.model.DeveloperBuilder;
import generated.de.hybris.platform.core.model.user.UserBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class DeveloperServiceUnitTest {
    @Mock
    private UserService userService;

    @Mock
    private ImpersonationService impersonationService;

    @Mock
    private FlexibleSearchService flexibleSearchService;

    @Mock
    private InternalDeveloperUidTranslationService internalDeveloperUidTranslationService;

    @InjectMocks
    private DeveloperService developerService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void testCheckAndCast() {
        DeveloperModel givenDeveloper = DeveloperBuilder.generate().buildMockInstance();
        DeveloperModel actualDeveloper = developerService.checkAndCast(givenDeveloper);

        assertThat(actualDeveloper).isSameAs(givenDeveloper);
    }

    @Test
    public void testCheckAndCastThrowsForNullValue() {
        assertThatThrownBy(() -> developerService.checkAndCast(null))
            .isInstanceOf(IllegalArgumentException.class);
    }

    @Test
    public void testCheckAndCastThrowsForWrongType() {
        UserModel givenUser = UserBuilder.generate()
            .withUid("CDEF12345")
            .buildMockInstance();
        assertThatThrownBy(() -> developerService.checkAndCast(givenUser))
            .isInstanceOf(UserNotDeveloperException.class)
            .hasMessageContaining(givenUser.getUid());
    }
}