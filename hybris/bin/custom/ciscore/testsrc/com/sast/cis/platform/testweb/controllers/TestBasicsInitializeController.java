package com.sast.cis.platform.testweb.controllers;

import de.hybris.platform.core.Initialization;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.concurrent.Callable;

import static com.sast.cis.platform.TestBasicsInitializer.installTestBasicsForJunitTenant;

@Controller
public class TestBasicsInitializeController {
    private static final Logger LOG = LoggerFactory.getLogger(TestBasicsInitializeController.class);

    public static final String OPERATION_NAME = "Initialization of testBasics.csv";

    @PostMapping("/initializewithtestbasics")
    public String initializeAndPrepareTestBasics(RedirectAttributes model) {
        return performJUnitOperation("initialization", Initialization::initializeTestSystem, model);
    }

    private String performJUnitOperation(String action, Callable<Boolean> callable, RedirectAttributes model) {
        LOG.info("Performing " + action + " JUnit tenant and preparing test basics.");

        try {
            model.addFlashAttribute("msg", callable.call()
                    ? "Successfully performed " + action + " for JUnit tenant."
                    : "Error during " + action + " of JUnit tenant. Check logs!");
        } catch (Exception e) {
            model.addFlashAttribute("msg", "Exception during " + action);
            model.addFlashAttribute("stackTrace", e.getStackTrace());
        }

        try {
            installTestBasicsForJunitTenant();
        } catch (Exception e) {
            model.addFlashAttribute("msg", "JUnit " + action + " succeded, but exception was thrown during test basics installation.");
            model.addFlashAttribute("stackTrace", e.getStackTrace());
        }

        return "redirect:/";
    }

    @PostMapping("/updatewithtestbasics")
    public String updateAndPrepareTestBasics(RedirectAttributes model) {
        return performJUnitOperation("update", Initialization::updateTestSystem, model);
    }
}
