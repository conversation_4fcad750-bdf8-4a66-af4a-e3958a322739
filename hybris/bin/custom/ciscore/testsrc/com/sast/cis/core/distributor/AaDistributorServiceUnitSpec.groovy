package com.sast.cis.core.distributor

import com.sast.cis.core.data.UmpDistributorData
import com.sast.cis.core.model.AaDistributorCompanyModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.company.IotCompanyService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.servicelayer.exceptions.ModelSavingException
import de.hybris.platform.servicelayer.interceptor.InterceptorException
import de.hybris.platform.servicelayer.interceptor.impl.UniqueAttributesInterceptor
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

@UnitTest
class AaDistributorServiceUnitSpec extends JUnitPlatformSpecification {
    private static final String DISTRIBUTOR_ID = 'foobarbaz1234'
    private static final String DISTRIBUTOR_NAME = 'Super duper company name GmbH'
    private static final String DISTRIBUTOR_EXTERNAL_ID = '25965721'
    private static final String CUSTOMIZATION_ID = 'IDW000'

    private ModelService modelService = Mock()
    private AaDistributorCompanyDao aaDistributorCompanyDao = Mock()

    private IotCompanyService iotCompanyService = Mock()

    private AaDistributorService aaDistributorService
    private AaDistributorCompanyModel newDistributor = Mock()
    private AaDistributorCompanyModel existingDistributor = Mock()
    private IoTCompanyModel company = Mock()
    private CountryModel country = Mock()
    private UserGroupModel userGroup = Mock()

    def setup() {
        aaDistributorService = new AaDistributorService(modelService, aaDistributorCompanyDao, iotCompanyService)

        modelService.create(AaDistributorCompanyModel) >> newDistributor
        aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> existingDistributor

        country.getAaDistributorCompanies() >> Set.of(getDistributorData())
        company.getCountry() >> country
        company.getAaCustomerGroup() >> userGroup
        userGroup.getUid() >> CUSTOMIZATION_ID

        iotCompanyService.getCurrentCompanyOrThrow() >> company

    }

    @Test
    void 'createOrGetDistributor creates new distributor if it doesnt exist'() {
        when:
        def actualDistributor = aaDistributorService.createOrGetDistributor(distributorData)

        then:
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.empty()
        1 * newDistributor.setUmpId(DISTRIBUTOR_ID)
        1 * newDistributor.setCompanyName(DISTRIBUTOR_NAME)
        1 * newDistributor.setAaExternalId(DISTRIBUTOR_EXTERNAL_ID)
        1 * modelService.save(newDistributor)
        actualDistributor == newDistributor
    }

    @Test
    void 'createOrGetDistributor gets existing distributor if it was created in the meantime and DB constraint was violated'() {
        given:
        def givenException = new ModelSavingException('lala')

        when:
        def actualDistributor = aaDistributorService.createOrGetDistributor(distributorData)

        then:
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.empty()
        1 * modelService.save(newDistributor) >> { throw givenException }
        1 * modelService.isUniqueConstraintErrorAsRootCause(givenException) >> true
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.of(existingDistributor)
        actualDistributor == existingDistributor
    }

    @Test
    void 'createOrGetDistributor gets existing distributor if it was created in the meantime and interceptor constraint was violated'() {
        given:
        def interceptorException = new InterceptorException('foo', Mock(UniqueAttributesInterceptor))
        def givenException = new ModelSavingException('lala', interceptorException)

        when:
        def actualDistributor = aaDistributorService.createOrGetDistributor(distributorData)

        then:
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.empty()
        1 * modelService.save(newDistributor) >> { throw givenException }
        1 * modelService.isUniqueConstraintErrorAsRootCause(givenException) >> false
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.of(existingDistributor)
        actualDistributor == existingDistributor
    }

    @Test
    void 'createOrGetDistributor throws if exception thrown was not caused by a unique constraint violation'() {
        given:
        def givenException = new ModelSavingException('lala')

        when:
        aaDistributorService.createOrGetDistributor(distributorData)

        then:
        thrown(ModelSavingException)
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.empty()
        1 * modelService.save(newDistributor) >> { throw givenException }
        1 * modelService.isUniqueConstraintErrorAsRootCause(givenException) >> false
    }

    @Test
    void 'createOrGetDistributor throws IllegalStateException if the saving error indicates unique constraint violation but distributor cannot be retrieved'() {
        given:
        def givenException = new ModelSavingException('lala')

        when:
        aaDistributorService.createOrGetDistributor(distributorData)

        then:
        thrown(IllegalStateException)
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.empty()
        1 * modelService.save(newDistributor) >> { throw givenException }
        1 * modelService.isUniqueConstraintErrorAsRootCause(givenException) >> true
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.empty()
    }

    @Test
    void 'createOrUpdateDistributor creates new distributor if it doesnt exist'() {
        when:
        def actualDistributor = aaDistributorService.createOrUpdateDistributor(distributorData)

        then:
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.empty()
        1 * newDistributor.setUmpId(DISTRIBUTOR_ID)
        // 2 invocations because we create then update
        2 * newDistributor.setCompanyName(DISTRIBUTOR_NAME)
        2 * newDistributor.setAaExternalId(DISTRIBUTOR_EXTERNAL_ID)
        2 * modelService.save(newDistributor)
        actualDistributor == newDistributor
    }

    @Test
    void 'createOrUpdateDistributor updates distributor if it already exist'() {
        given:
        def newName = 'New name'
        def newExternalId = 'New external id'
        def existingWithNewData = new UmpDistributorData()
                .withId(DISTRIBUTOR_ID)
                .withName(newName)
                .withExternalCustomerId(newExternalId)

        when:
        def actualDistributor = aaDistributorService.createOrUpdateDistributor(existingWithNewData)

        then:
        1 * aaDistributorCompanyDao.byUmpDistributorId(DISTRIBUTOR_ID) >> Optional.of(existingDistributor)
        0 * existingDistributor.setUmpId(_)
        1 * existingDistributor.setCompanyName(newName)
        1 * existingDistributor.setAaExternalId(newExternalId)
        1 * modelService.save(existingDistributor)
        actualDistributor == existingDistributor
    }

    @Test
    void 'getDistributorsForBuyerCompany returns distributors for the current users country based on the valid AA customergroup for direct sales'() {
        when:
        def actualDistributors = aaDistributorService.getDistributorsForBuyerCompany()

        then:
        actualDistributors.size() == 1
        actualDistributors.get(0).getId() == getDistributorData().getId()
        actualDistributors.get(0).getName() == getDistributorData().getName()
    }

    @Test
    void 'getDistributorsForBuyerCompany returns no distributors for the current users country based for the invalid AA customergroup'() {
        when:
        def actualDistributors = aaDistributorService.getDistributorsForBuyerCompany()

        then:
        userGroup.getUid() >> "InvalidId"
        actualDistributors.size() == 0
    }

    @Test
    void 'getDistributorsForBuyerCompany returns no distributors for the current users with no aa customer group'() {
        when:
        def actualDistributors = aaDistributorService.getDistributorsForBuyerCompany()

        then:
        company.getAaCustomerGroup() >> null
        actualDistributors.size() == 0
    }

    @Test
    void 'getDistributorsForBuyerCompany with no current company throws exception'() {
        given:
        def givenException = new IllegalStateException()

        when:
        aaDistributorService.getDistributorsForBuyerCompany()

        then:
        thrown(IllegalStateException)
        iotCompanyService.getCurrentCompanyOrThrow() >> { throw givenException }
    }

    private getDistributorData() {
        new UmpDistributorData()
                .withId(DISTRIBUTOR_ID)
                .withName(DISTRIBUTOR_NAME)
                .withExternalCustomerId(DISTRIBUTOR_EXTERNAL_ID)
    }
}
