package com.sast.cis.core.service;

import com.sast.cis.core.factory.HtmlSanitizerFactory;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
@RunWith(DataProviderRunner.class)
public class HtmlSanitizingServiceUnitTest {

    private final HtmlSanitizingService htmlSanitizingService = new HtmlSanitizingService(HtmlSanitizerFactory.create());


    @DataProvider
    public static Object[][] allowedHtmlElements() {
        return new Object[][] {
            { "<a href=\"https://azena.com\">link text</a>" },
            { "<u>underlined text</u>" },
        };
    }

    @Test
    @UseDataProvider("allowedHtmlElements")
    public void givenHtmlWithAllowedElements_whenSanitize_thenDoNotRemove(final String htmlInput) {
        final String result = htmlSanitizingService.sanitizeInput(htmlInput);
        assertThat(result).isEqualTo(htmlInput);
    }

    @DataProvider
    public static Object[][] disallowedHtmlElements() {
        return new Object[][] {
            { "<script>alert();</script>" },
            { "<style></style>" },
        };
    }

    @Test
    @UseDataProvider("disallowedHtmlElements")
    public void givenHtmlWithDisallowedElements_whenSanitize_thenSantitze(final String htmlInput) {
        final String result = htmlSanitizingService.sanitizeInput(htmlInput);
        assertThat(result).isEmpty();
    }
}