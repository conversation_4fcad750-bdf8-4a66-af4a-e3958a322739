package com.sast.cis.core.service.media.devcon;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.data.ClientImageData;
import com.sast.cis.core.data.PdfData;
import com.sast.cis.core.data.StoreContentData;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.PdfMediaModel;
import com.sast.cis.core.model.StoreContentDraftModel;
import com.sast.cis.core.service.ProductCatalogIdentifierService;
import com.sast.cis.core.service.media.CisMediaContainerService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.CatalogVersionService;
import de.hybris.platform.catalog.model.CatalogModel;
import de.hybris.platform.core.model.media.MediaContainerModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.PdfMediaBuilder;
import generated.com.sast.cis.core.model.StoreContentDraftBuilder;
import generated.de.hybris.platform.core.model.media.MediaBuilder;
import generated.de.hybris.platform.core.model.media.MediaContainerBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collection;

import static com.google.common.collect.Lists.newArrayList;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class MediaUpdateServiceUnitTest {

    @Mock
    private ModelService modelService;

    @Mock
    private CisMediaContainerService cisMediaContainerService;

    @Mock
    private FlexibleSearchService flexibleSearchService;

    @Mock
    private CatalogVersionService catalogVersionService;

    @Spy
    private PdfFileNameService pdfFileNameService;

    @InjectMocks
    private MediaUpdateService mediaUpdateService;

    private MediaContainerModel icon;

    @Mock
    private ProductCatalogIdentifierService productCatalogIdentifierService;
    @Mock
    private CatalogModel catalog;

    @Before
    public void setup() {
        when(catalog.getId()).thenReturn("catalogId");
        when(productCatalogIdentifierService.getSessionProductCatalog()).thenReturn(catalog);
        MediaModel media1 = MediaBuilder.generate().buildMockInstance();
        MediaModel media2 = MediaBuilder.generate().buildMockInstance();
        icon = MediaContainerBuilder.generate().withMedias(ImmutableList.of(media1, media2)).buildMockInstance();
        when(media1.getMediaContainer()).thenReturn(icon);
        when(cisMediaContainerService.getStagedMediaContainerForQualifier(any())).thenReturn(icon);
        when(cisMediaContainerService.getStagedMediaContainer(any())).thenReturn(icon);

    }

    @Test
    public void updateDraftIcon_noExistingIcon_doNothingOnNullIconUpload() {
        mediaUpdateService.updateDraftIcon(new StoreContentData(), StoreContentDraftBuilder.generate().buildMockInstance());
        verifyZeroInteractions(modelService);
    }

    @Test
    public void updateDraftIcon_noExistingIcon_doNothingOnEmptyIconUpload() {
        mediaUpdateService
            .updateDraftIcon(new StoreContentData().withIcon(ImmutableList.of()), StoreContentDraftBuilder.generate().buildMockInstance());
        verifyZeroInteractions(modelService);
    }

    @Test
    public void updateDraftIcon_nullUpload_deleteExistingIconAndMedias() {
        MediaModel media1 = MediaBuilder.generate().buildMockInstance();
        MediaModel media2 = MediaBuilder.generate().buildMockInstance();
        MediaContainerModel icon = MediaContainerBuilder.generate().withMedias(ImmutableList.of(media1, media2)).buildMockInstance();
        StoreContentDraftModel storeContentDraft = StoreContentDraftBuilder.generate().withIcon(icon).buildMockInstance();

        mediaUpdateService.updateDraftIcon(new StoreContentData(), storeContentDraft);

        verify(storeContentDraft).setIcon(null);
        deleteExistingIcon(media1, media2, icon);
    }

    @Test
    public void updateDraftIcon_uploadWithIcon_saveNewIcon() {

        StoreContentDraftModel storeContentDraft = StoreContentDraftBuilder.generate().buildMockInstance();

        when(cisMediaContainerService.getStagedMediaContainerForQualifier(any())).thenReturn(icon);

        ClientImageData iconData = new ClientImageData().withQualifier("qualifier");

        mediaUpdateService.updateDraftIcon(new StoreContentData().withIcon(ImmutableList.of(iconData)),
            storeContentDraft);

        saveNewIcon(icon.getMedias(), icon, storeContentDraft);
    }

    @Test
    public void updateDraftIcon_uploadedIconIsExistingOne_doNothing() {
        StoreContentDraftModel storeContentDraft = StoreContentDraftBuilder.generate().withIcon(icon).buildMockInstance();

        when(cisMediaContainerService.getStagedMediaContainerForQualifier(any())).thenReturn(icon);

        ClientImageData iconData = new ClientImageData().withQualifier("qualifier");

        mediaUpdateService.updateDraftIcon(new StoreContentData().withIcon(ImmutableList.of(iconData)),
            storeContentDraft);

        verifyZeroInteractions(modelService);
    }

    @Test
    public void updateDraftIcon_uploadedIconIsNotExistingOne_deleteOldIconAndSaveNewOne() {
        MediaModel oldMedia1 = MediaBuilder.generate().buildMockInstance();
        MediaModel oldMedia2 = MediaBuilder.generate().buildMockInstance();
        MediaContainerModel oldIcon = MediaContainerBuilder.generate().withMedias(ImmutableList.of(oldMedia1, oldMedia2))
            .buildMockInstance();
        when(oldMedia1.getMediaContainer()).thenReturn(oldIcon);

        MediaModel newMedia1 = MediaBuilder.generate().buildMockInstance();
        MediaContainerModel newIcon = MediaContainerBuilder.generate().withMedias(ImmutableList.of(newMedia1))
            .buildMockInstance();
        when(newMedia1.getMediaContainer()).thenReturn(newIcon);

        StoreContentDraftModel storeContentDraft = StoreContentDraftBuilder.generate().withIcon(oldIcon).buildMockInstance();

        when(cisMediaContainerService.getStagedMediaContainerForQualifier(any())).thenReturn(newIcon);

        ClientImageData iconData = new ClientImageData().withQualifier("qualifier");

        mediaUpdateService.updateDraftIcon(new StoreContentData().withIcon(ImmutableList.of(iconData)),
            storeContentDraft);

        deleteExistingIcon(oldMedia1, oldMedia2, oldIcon);
        saveNewIcon(newIcon.getMedias(), newIcon, storeContentDraft);
    }

    private void deleteExistingIcon(MediaModel media1, MediaModel media2, MediaContainerModel icon) {
        verify(modelService).removeAll(ImmutableList.of(media1, media2));
        verify(modelService).remove(icon);
    }

    private void saveNewIcon(Collection<MediaModel> medias, MediaContainerModel icon, StoreContentDraftModel storeContentDraft) {
        medias.forEach(media -> verify(media).setUsed(true));
        verify(storeContentDraft).setIcon(eq(icon));
        verify(modelService).saveAll(medias);
    }

    @Test
    public void updateAppIcon_noExistingIcon_doNothingOnNullIconUpload() {
        mediaUpdateService.updateAppIcon(new StoreContentData(), AppBuilder.generate().buildMockInstance());
        verifyZeroInteractions(modelService);
    }

    @Test
    public void updateAppIcon_noExistingIcon_doNothingOnEmptyIconUpload() {
        mediaUpdateService
            .updateAppIcon(new StoreContentData().withIcon(ImmutableList.of()), AppBuilder.generate().buildMockInstance());
        verifyZeroInteractions(modelService);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void updateAppDocuments_addsNewAndRemovesDeleted() {
        PdfMediaModel toBeDeletedPdfDocument = PdfMediaBuilder.generate().buildMockInstance();
        PdfMediaModel newPdfDocument = PdfMediaBuilder.generate().withCode("newPdfDocument").buildMockInstance();
        PdfMediaModel keptPdfDocument = PdfMediaBuilder.generate().withCode("keptPdfDocument").buildMockInstance();

        PdfData newPdfData = new PdfData().withCode("newPdfDocument").withDisplayName("new pdf document");
        PdfData keptPdfData = new PdfData().withCode("keptPdfDocument").withDisplayName("kept pdf document");

        AppModel app = AppBuilder.generate().withDocumentationFiles(ImmutableList.of(toBeDeletedPdfDocument, keptPdfDocument))
            .buildMockInstance();
        StoreContentData storeContentData = new StoreContentData().withDocumentationFiles(ImmutableList.of(newPdfData, keptPdfData));
        when(flexibleSearchService.getModelsByExample(any())).thenReturn(newArrayList(newPdfDocument), newArrayList(keptPdfDocument));

        mediaUpdateService.updateAppDocuments(storeContentData, app);
        verify(toBeDeletedPdfDocument).setUsed(false);
        verify(newPdfDocument).setUsed(true);
        verify(keptPdfDocument).setUsed(true);
        verify(modelService).saveAll(newArrayList(toBeDeletedPdfDocument));
        verify(modelService).saveAll(newArrayList(newPdfDocument, keptPdfDocument));
    }

    @SuppressWarnings("unchecked")
    @Test
    public void updateDraftDocuments_addsNewAndRemovesDeleted() {
        PdfMediaModel oldPdfDocument = PdfMediaBuilder.generate().buildMockInstance();
        PdfMediaModel newPdfDocument = PdfMediaBuilder.generate().withCode("newPdfDocument").buildMockInstance();
        PdfMediaModel keptPdfDocument = PdfMediaBuilder.generate().withCode("keptPdfDocument").buildMockInstance();

        PdfData newPdfData = new PdfData().withCode("newPdfDocument").withDisplayName("new pdf document");
        PdfData keptPdfData = new PdfData().withCode("keptPdfDocument").withDisplayName("kept pdf document");

        StoreContentDraftModel draft = StoreContentDraftBuilder.generate()
            .withDocumentationFiles(ImmutableList.of(oldPdfDocument, keptPdfDocument)).buildMockInstance();
        StoreContentData storeContentData = new StoreContentData().withDocumentationFiles(ImmutableList.of(newPdfData, keptPdfData));
        when(flexibleSearchService.getModelsByExample(any())).thenReturn(newArrayList(newPdfDocument), newArrayList(keptPdfDocument));

        mediaUpdateService.updateDraftDocuments(storeContentData, draft);
        verify(modelService).remove(oldPdfDocument);
        verify(newPdfDocument).setUsed(true);
        verify(keptPdfDocument).setUsed(true);
        verify(modelService).save(newPdfDocument);
        verify(modelService).save(keptPdfDocument);
    }

    @Test
    public void getPdfMediaModel_returnsCorrectFileName() {
        PdfData newPdfDataNoExt = new PdfData().withDisplayName("file");
        PdfData newPdfDataLowerExt = new PdfData().withDisplayName("file.pdf");
        PdfData newPdfDataUpperExt = new PdfData().withDisplayName("file.PdF");

        PdfMediaModel pdfMediaModelNoExt = PdfMediaBuilder.generate().buildMockInstance();
        when(flexibleSearchService.getModelsByExample(any())).thenReturn(newArrayList(pdfMediaModelNoExt));
        pdfMediaModelNoExt = mediaUpdateService.getPdfMediaWithUpdatedFileName(newPdfDataNoExt);
        verify(pdfMediaModelNoExt).setRealFileName("file.pdf");

        PdfMediaModel pdfMediaModelLowerExt = PdfMediaBuilder.generate().buildMockInstance();
        when(flexibleSearchService.getModelsByExample(any())).thenReturn(newArrayList(pdfMediaModelLowerExt));
        pdfMediaModelLowerExt = mediaUpdateService.getPdfMediaWithUpdatedFileName(newPdfDataLowerExt);
        verify(pdfMediaModelLowerExt).setRealFileName("file.pdf");

        PdfMediaModel pdfMediaModelUpperExt = PdfMediaBuilder.generate().buildMockInstance();
        when(flexibleSearchService.getModelsByExample(any())).thenReturn(newArrayList(pdfMediaModelUpperExt));
        pdfMediaModelUpperExt = mediaUpdateService.getPdfMediaWithUpdatedFileName(newPdfDataUpperExt);
        verify(pdfMediaModelUpperExt).setRealFileName("file.PdF");
    }
}
