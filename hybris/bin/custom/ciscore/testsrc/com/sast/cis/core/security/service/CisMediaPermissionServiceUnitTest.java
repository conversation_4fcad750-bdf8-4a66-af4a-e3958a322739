package com.sast.cis.core.security.service;

import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.core.model.security.PrincipalModel;
import generated.com.sast.cis.core.model.IntegratorBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CisMediaPermissionServiceUnitTest {

    @InjectMocks
    private CisMediaPermissionService permissionService;

    private IoTCompanyModel company = IoTCompanyBuilder.generate().buildMockInstance();
    private IntegratorModel integrator = IntegratorBuilder.generate().withCompany(company).buildMockInstance();

    @Test
    public void getCorrectPrincipal() {
        PrincipalModel principal = permissionService.getCorrectPrincipal(new MediaModel(), integrator);
        assertThat(principal).isEqualTo(integrator);
    }
}
