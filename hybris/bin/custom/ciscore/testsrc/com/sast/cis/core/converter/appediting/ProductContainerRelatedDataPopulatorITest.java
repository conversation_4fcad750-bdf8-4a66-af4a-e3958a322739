package com.sast.cis.core.converter.appediting;

import com.sast.cis.core.constants.CiscoreConstants;
import com.sast.cis.core.data.ProductContainerRelatedData;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.type.SearchRestrictionModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.type.TypeService;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.site.BaseSiteService;
import generated.de.hybris.platform.core.model.type.SearchRestrictionBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import javax.annotation.Resource;

import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.test.utils.SampleDataCreator.APP_CODE_PREFIX;
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_DEVELOPER_A1_UID;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class ProductContainerRelatedDataPopulatorITest extends ServicelayerTransactionalTest {

    private static final String PRODUCT_CONTAINER_TITLE = "TestTitle";
    public static final String APP_CODE = APP_CODE_PREFIX + PRODUCT_CONTAINER_TITLE;
    private static final String SEARCH_RESTRICTION_CODE = "searchRestrictionTestCode";

    @Resource
    private ProductContainerRelatedDataPopulator productContainerRelatedDataPopulator;

    @Resource
    private UserService userService;

    @Resource
    private DeveloperService developerService;

    @Resource
    private BaseSiteService baseSiteService;

    @Resource
    private ModelService modelService;

    @Resource
    private TypeService typeService;
    @Rule
    public SessionCatalogRule sessionCatalogRule = SessionCatalogRule.stagedCatalog();

    private DeveloperModel currentDeveloper;
    private ProductContainerModel productContainer;
    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    @Before
    public void setUp() {
        currentDeveloper = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);
        userService.setCurrentUser(currentDeveloper);
        baseSiteService.setCurrentBaseSite(CiscoreConstants.DEVELOPER_CONSOLE_BASE_STORE_UID, false);

        SearchRestrictionModel searchRestriction = SearchRestrictionBuilder.generate()
            .withCode(SEARCH_RESTRICTION_CODE)
            .withPrincipal(userService.getUserGroupForUID("integratorgroup"))
            .withGenerate(true)
            .withQuery("{item:catalogVersion} IN (?session.catalogversions)")
            .withRestrictedType(typeService.getComposedTypeForClass(AppModel.class))
            .buildIntegrationInstance();
        modelService.save(searchRestriction);
    }

    @Test
    public void test_populateFromApp_shopLinkIsFilledCorrectly() {
        productContainer = sampleDataCreator
            .createProductContainerWithApp(PRODUCT_CONTAINER_TITLE, currentDeveloper, "sample.package.name", ONLINE);

        ProductContainerRelatedData productContainerRelatedData = new ProductContainerRelatedData();
        productContainerRelatedDataPopulator.populate(productContainer, productContainerRelatedData);

        assertThat(productContainerRelatedData.getAppShopUrl()).isEqualTo(
            "https://store.dev.local:9002/shop/p/" + APP_CODE);

    }

    @Test
    public void test_populateFromApp_appIsNotInStore_noShopLinkIsSet() {
        productContainer = sampleDataCreator
            .createProductContainerWithApp(PRODUCT_CONTAINER_TITLE, currentDeveloper, "sample.package.name", STAGED);

        ProductContainerRelatedData productContainerRelatedData = new ProductContainerRelatedData();
        productContainerRelatedDataPopulator.populate(productContainer, productContainerRelatedData);

        assertThat(productContainerRelatedData.getAppShopUrl()).isEmpty();
    }
}
