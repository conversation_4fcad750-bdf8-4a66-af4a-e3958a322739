package com.sast.cis.core.facade.cart

import com.sast.cis.core.constants.BaseStoreEnum
import com.sast.cis.core.data.PlaceOrderData
import com.sast.cis.core.distributor.AaDistributorNotFoundException
import com.sast.cis.core.distributor.AaDistributorService
import com.sast.cis.core.model.AaDistributorCompanyModel
import com.sast.cis.core.model.IoTCompanyModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.store.BaseStoreModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

@UnitTest
class AaDistributorExtensionHandlerUnitSpec extends JUnitPlatformSpecification {

    private static String DEFAULT_DISTRIBUTOR_ID = 'default_aa_distributor_id_000'
    private static String NEW_DISTRIBUTOR_ID = 'new_aa_distributor_id_000'
    private static String NON_EXISTENT_DISTRIBUTOR_ID = 'non_existent_distributor_id'


    private AaDistributorExtensionHandler aaDistributorExtensionHandler

    private CartModel sessionCart = Mock()
    private IoTCompanyModel buyerCompany = Mock()
    private PlaceOrderData placeOrderData = Mock()
    private AaDistributorService aaDistributorService = Mock()
    private AaDistributorCompanyModel defaultAaDistributorCompanyModel = Mock()
    private AaDistributorCompanyModel newAaDistributorCompanyModel = Mock()
    private BaseStoreModel baseStore = Mock()

    void setup() {
        defaultAaDistributorCompanyModel.getUmpId() >> DEFAULT_DISTRIBUTOR_ID
        newAaDistributorCompanyModel.getUmpId() >> NEW_DISTRIBUTOR_ID
        sessionCart.getCompany() >> buyerCompany
        sessionCart.getStore() >> baseStore
        baseStore.getUid() >> BaseStoreEnum.AA.baseStoreUid
        buyerCompany.getDefaultAaDistributor() >> defaultAaDistributorCompanyModel
        aaDistributorExtensionHandler = new AaDistributorExtensionHandler(aaDistributorService)
        aaDistributorService.getDistributorsForBuyerCompany() >> [defaultAaDistributorCompanyModel, newAaDistributorCompanyModel]
    }


    @Test
    void 'new distributor saved for the cart'() {
        given:
        placeOrderData.getDistributorID() >> NEW_DISTRIBUTOR_ID
        def aaDistributorExtensionHandler = new AaDistributorExtensionHandler(aaDistributorService)

        when:
        aaDistributorExtensionHandler.extendSessionCart(sessionCart, placeOrderData)

        then:
        aaDistributorService.findAaDistributorByID(NEW_DISTRIBUTOR_ID) >> Optional.of(newAaDistributorCompanyModel)
        1 * sessionCart.setAaDistributorCompany(newAaDistributorCompanyModel)
    }

    @Test
    void 'throws exception if placeOrderData aaDistributor is not found'() {
        given:
        placeOrderData.getDistributorID() >> null

        when:
        aaDistributorExtensionHandler.extendSessionCart(sessionCart, placeOrderData)

        then:
        aaDistributorService.findAaDistributorByID(_) >> Optional.empty()
        thrown(AaDistributorNotFoundException)

    }

    @Test
    void 'throws exception if placeOrderData aaDistributor not in backend'() {
        given:
        placeOrderData.getDistributorID() >> NON_EXISTENT_DISTRIBUTOR_ID


        when:
        aaDistributorExtensionHandler.extendSessionCart(sessionCart, placeOrderData)

        then:
        aaDistributorService.findAaDistributorByID(NON_EXISTENT_DISTRIBUTOR_ID) >> Optional.empty()
        thrown(AaDistributorNotFoundException)
    }

    @Test
    void 'ignoring setting the distributor if not AA store'() {
        given:
        placeOrderData.getDistributorID() >> NEW_DISTRIBUTOR_ID


        when:
        aaDistributorExtensionHandler.extendSessionCart(sessionCart, placeOrderData)

        then:
        baseStore.getUid() >> BaseStoreEnum.AZENA.baseStoreUid
        0 * sessionCart.setAaDistributorCompany(_)
    }

    @Test
    void 'ignoring setting the distributor if buyercompany doesnot have distributors '() {
        given:
        placeOrderData.getDistributorID() >> NEW_DISTRIBUTOR_ID
        aaDistributorService.getDistributorsForBuyerCompany() >> []


        when:
        aaDistributorExtensionHandler.extendSessionCart(sessionCart, placeOrderData)

        then:
        baseStore.getUid() >> BaseStoreEnum.AZENA.baseStoreUid
        0 * sessionCart.setAaDistributorCompany(_)
    }

}
