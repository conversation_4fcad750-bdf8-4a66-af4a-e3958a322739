package com.sast.cis.core.bdreports.service;

import com.sast.cis.core.bdreports.service.strategies.ExportStrategy;
import com.sast.cis.core.dao.AppDao;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.ByteArrayOutputStream;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class BDReportServiceUnitTest {

    private BDReportService bdReportService;

    @Mock
    private AppDao appDao;
    @Mock
    private ExportStrategy exportStrategy;

    @Before
    public void setup() {
        bdReportService = new BDReportService(appDao, List.of(exportStrategy));
        when(exportStrategy.export(any())).thenReturn("dummy");
        when(exportStrategy.getFilename()).thenReturn("dummy.csv");
        when(exportStrategy.getHeader()).thenReturn("header");
        when(appDao.getAppsForCatalogVersion(any(), any())).thenReturn(List.of());

    }

    @Test
    public void generateBusinessDeveloperReports_delegatesToStrategy_returnsValueReturnedByStategy() {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        bdReportService.generateBusinessDeveloperReports(byteArrayOutputStream, "catalog");
        verify(exportStrategy, times(1)).export(any());
    }

}
