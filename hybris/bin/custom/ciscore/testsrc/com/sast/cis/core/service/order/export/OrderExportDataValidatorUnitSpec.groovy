package com.sast.cis.core.service.order.export

import com.sast.cis.core.billingintegration.dto.OrderExportData
import com.sast.cis.core.billingintegration.dto.OrderExportResult
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class OrderExportDataValidatorUnitSpec extends JUnitPlatformSpecification {

    private OrderExportDataValidator orderExportDataValidator = new OrderExportDataValidator()

    def "when validate null then fail"() {
        when:
        orderExportDataValidator.validateOrderExportData(null)

        then:
        def e = thrown(IllegalArgumentException)
        e.message == "orderExportData is marked non-null but is null"
    }

    def "given export data with no order id when validate then fail"() {
        given:
        def orderExportData = OrderExportData.builder().build()

        when:
        orderExportDataValidator.validateOrderExportData(orderExportData)

        then:
        def e = thrown(InvalidOrderExportDataException)
        e.message.contains("OrderId cannot be null or empty")
    }

    def "given export data with no order status when validate then fail"() {
        given:
        def orderExportData = OrderExportData.builder()
                .id("testId")
                .build()

        when:
        orderExportDataValidator.validateOrderExportData(orderExportData)

        then:
        def e = thrown(InvalidOrderExportDataException)
        e.message.contains("OrderStatus cannot be null or empty")
    }

    def "given COMPLETED export data with errors when validate then fail"() {
        given:
        def orderExportData = OrderExportData.builder()
                .id("testId")
                .orderExportResult(OrderExportResult.COMPLETED)
                .errors(List.of("error1", "error2"))
                .build()

        when:
        orderExportDataValidator.validateOrderExportData(orderExportData)

        then:
        def e = thrown(InvalidOrderExportDataException)
        e.message.contains("Completed export data contains errors")
    }
}
