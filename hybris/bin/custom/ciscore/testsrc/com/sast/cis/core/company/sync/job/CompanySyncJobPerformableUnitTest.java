package com.sast.cis.core.company.sync.job;

import com.sast.cis.core.exceptions.CompanyNotFoundException;
import com.sast.cis.core.model.CompanySyncCronJobModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.company.sync.IotCompanySyncService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import de.hybris.platform.store.BaseStoreModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CompanySyncJobPerformableUnitTest {
    @Mock
    private IotCompanySyncService iotCompanySyncService;
    @Mock
    private IotCompanyService iotCompanyService;

    @InjectMocks
    private CompanySyncJobPerformable companySyncJobPerformable;

    @Mock
    private CompanySyncCronJobModel cronJobModel;
    @Mock
    private IoTCompanyModel company;
    @Mock
    private BaseStoreModel baseStore;

    private final String COMPANY_ID = "that-is-company-id";

    @Before
    public void setup() {
        when(company.getStore()).thenReturn(baseStore);
        when(iotCompanyService.getAllCompanies()).thenReturn(List.of(company));
    }

    @Test
    public void perform_companyNotAssociatedToAnyMarketplace_syncNotPerformed() {
        when(company.getStore()).thenReturn(null);
        PerformResult perform = companySyncJobPerformable.perform(cronJobModel);

        assertThat(perform.getResult()).isEqualTo(CronJobResult.SUCCESS);
        verifyZeroInteractions(iotCompanySyncService);
    }
    @Test
    public void perform_companyAssociatedToAnyMarketplace_syncPerformed() {
        PerformResult perform = companySyncJobPerformable.perform(cronJobModel);

        assertThat(perform.getResult()).isEqualTo(CronJobResult.SUCCESS);
        verify(iotCompanySyncService,times(1)).syncCompany(company);
    }

    @Test
    public void perform_onException_resultsInFailureRun() {
        doThrow(new RuntimeException("Simulation Exception")).when(iotCompanySyncService).syncCompany(company);
        PerformResult perform = companySyncJobPerformable.perform(cronJobModel);

        assertThat(perform.getResult()).isEqualTo(CronJobResult.FAILURE);
    }

    @Test
    public void perform_onCompanyNotFoundException_shouldSucceed() {
        doThrow(new CompanyNotFoundException("Company not found at UMP", COMPANY_ID)).when(iotCompanySyncService).syncCompany(company);
        PerformResult perform = companySyncJobPerformable.perform(cronJobModel);

        assertThat(perform.getResult()).isEqualTo(CronJobResult.SUCCESS);
    }
}
