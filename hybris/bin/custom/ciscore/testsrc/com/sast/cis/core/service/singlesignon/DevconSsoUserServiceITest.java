package com.sast.cis.core.service.singlesignon;

import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.test.utils.TestDataConstants;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.user.UserModel;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Optional;

import static com.sast.cis.test.utils.TestDataConstants.*;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class DevconSsoUserServiceITest extends BaseSsoServiceITest {
    @Resource
    private DevconSsoUserService devconSsoUserService;

    @Test
    public void getOrCreateUserByToken_existingUserForExistingCompany_returnsExistingUser() {
        umpWireMockRule.prepareGetCompanyDataResponse(SAMPLE_DATA_COMPANY_A_UID, TestDataConstants.AZENA_MARKETPLACE_ID);
        SsoUserDetails givenTokenDetails = getTokenUserDetails(SAMPLE_DATA_DEVELOPER_A1_SSOID, SAMPLE_DATA_COMPANY_A_UID);
        UserModel existingUser = userService.getUserForUID(SAMPLE_DATA_DEVELOPER_A1_UID);

        DeveloperModel actualUser = devconSsoUserService.getOrCreateUserBySsoUserDetails(givenTokenDetails);

        verifyTokenDataInUsers(actualUser, givenTokenDetails);
        assertThat(actualUser).isEqualTo(existingUser);
    }

    @Test
    public void createUser_newUserForExistingCompany_createsUserForExistingCompany() {
        umpWireMockRule.prepareGetCompanyDataResponse(SAMPLE_DATA_COMPANY_A_UID, TestDataConstants.AZENA_MARKETPLACE_ID);
        SsoUserDetails givenTokenDetails = getTokenUserDetails(SSO_ACCOUNT_ID, SAMPLE_DATA_COMPANY_A_UID);
        IoTCompanyModel existingCompany = iotCompanyService.getCompanyByUid(SAMPLE_DATA_COMPANY_A_UID).orElseThrow();

        DeveloperModel developer = devconSsoUserService.getOrCreateUserBySsoUserDetails(givenTokenDetails);

        verifyTokenDataInUsers(developer, givenTokenDetails);
        assertThat(developer.getCompany()).isEqualTo(existingCompany);
    }

    @Test
    public void createUser_createsUserAndCompany_createsUserAndCompany() {
        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, TestDataConstants.AZENA_MARKETPLACE_ID);
        SsoUserDetails givenTokenDetails = getTokenUserDetails(SSO_ACCOUNT_ID, COMPANY_ID);
        DeveloperModel developer = devconSsoUserService.getOrCreateUserBySsoUserDetails(givenTokenDetails);

        verifyTokenDataInUsers(developer, givenTokenDetails);
        Optional<IoTCompanyModel> actualCompany = iotCompanyService.getCompanyByUid(COMPANY_ID);
        assertThat(actualCompany).isPresent();
    }
}
