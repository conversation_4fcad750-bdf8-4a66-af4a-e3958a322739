package com.sast.cis.core.service.app

import com.sast.cis.core.dao.CatalogVersion
import com.sast.cis.core.data.ErrorMessageData
import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.enums.StoreAvailabilityMode
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.service.customer.integrator.IntegratorService
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.catalog.enums.ArticleApprovalStatus
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.core.model.user.UserModel
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import de.hybris.platform.servicelayer.user.UserService
import de.hybris.platform.site.BaseSiteService
import de.hybris.platform.store.services.BaseStoreService
import org.junit.Test
import spock.lang.Shared
import spock.lang.Unroll

import javax.annotation.Resource

import static com.sast.cis.core.constants.BaseStoreEnum.AA
import static com.sast.cis.core.constants.BaseStoreEnum.AZENA
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_INTEGRATOR_A1_UID
import static com.sast.cis.test.utils.TestDataConstants.SAMPLE_DATA_INTEGRATOR_B1_UID

@IntegrationTest
class AppPermissionServiceISpec extends ServicelayerTransactionalSpockSpecification {

    @Resource
    private UserService userService

    @Resource
    private IntegratorService integratorService

    @Resource
    private AppPermissionService appPermissionService

    @Resource
    private ModelService modelService

    @Resource
    private BaseStoreService baseStoreService

    @Resource
    private BaseSiteService baseSiteService

    @Resource
    private FlexibleSearchService flexibleSearchService

    @Shared
    private IntegratorModel integrator

    @Shared
    private IntegratorModel notAllowedBuyer

    @Shared
    private UserModel anonymous

    private AppModel app

    private AppLicenseModel appLicense

    private SampleDataCreator sampleDataCreator = new SampleDataCreator()

    def setup() {
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(AZENA.getBaseStoreUid()), true);
        integrator = integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_A1_UID)
        notAllowedBuyer = integratorService.getIntegratorByInternalUserId(SAMPLE_DATA_INTEGRATOR_B1_UID)
        anonymous = userService.getAnonymousUser()

        app = sampleDataCreator.createApp("privateApp", "com.sast.private", CatalogVersion.ONLINE)
        app.setStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
        modelService.save(app)

        appLicense = sampleDataCreator.createAppLicense(app.getCode() + "_sub", app, ArticleApprovalStatus.APPROVED, LicenseType.SUBSCRIPTION)
        appLicense.setUserGroups(Set.of())
        modelService.save(appLicense)
    }

    @Unroll
    @Test
    def "Unrestricted apps have no validation errors for logged in users"() {
        given:
        app.setStoreAvailabilityMode(mode)
        modelService.save(app)
        userService.setCurrentUser(integrator)

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        Set.of() == errors

        where:
        mode                              | _
        StoreAvailabilityMode.PUBLIC      | _
        StoreAvailabilityMode.UNAVAILABLE | _
    }

    @Unroll
    @Test
    def "Unrestricted apps have no validation errors for anonymous in users"() {
        given:
        app.setStoreAvailabilityMode(mode)
        modelService.save(app)
        userService.setCurrentUser(anonymous)

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        Set.of() == errors

        where:
        mode                              | _
        StoreAvailabilityMode.PUBLIC      | _
        StoreAvailabilityMode.UNAVAILABLE | _
    }

    @Test
    def "anonymous users have no access to a restricted app"() {
        given:
        app.setStoreAvailabilityMode(StoreAvailabilityMode.RESTRICTED_BUYER)
        app.setPermittedBuyerCompanies(null)
        modelService.save(app)
        userService.setCurrentUser(anonymous)

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        !errors.isEmpty()
    }

    @Test
    @Unroll
    def "anonymous users have access to group restricted apps for independent workshops"() {
        given:
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(AA.getBaseStoreUid()), true);
        app.setStoreAvailabilityMode(StoreAvailabilityMode.RESTRICTED_BUYER_GROUP)
        modelService.save(app)
        appLicense.setUserGroups(Set.of(findUserGroup(appUserGroup)))
        modelService.save(appLicense)
        userService.setCurrentUser(anonymous)

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        errors.isEmpty() == expectedResult

        where:
        appUserGroup | expectedResult
        "IDW000"     | true
        "KA0001"     | false
    }

    @Test
    def "logged in users not in permitted buyer have no access to a restricted app"() {
        given:
        app.setStoreAvailabilityMode(StoreAvailabilityMode.RESTRICTED_BUYER)
        app.setPermittedBuyerCompanies(Set.of(integrator.getCompany()))
        modelService.save(app)
        userService.setCurrentUser(notAllowedBuyer)

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        !errors.isEmpty()
    }


    @Test
    def "logged in users in buyers list have access to a restricted app"() {
        given:
        app.setStoreAvailabilityMode(StoreAvailabilityMode.RESTRICTED_BUYER)
        app.setPermittedBuyerCompanies(Set.of(integrator.getCompany()))
        modelService.save(app)
        userService.setCurrentUser(integrator)

        when:
        Set<ErrorMessageData> errors = appPermissionService.verifyAppCanBeViewedByIntegrator(app)

        then:
        errors.isEmpty()
    }

    UserGroupModel findUserGroup(String uid) {
        def runtime = new UserGroupModel();
        runtime.setUid(uid);
        flexibleSearchService.getModelByExample(runtime);
    }
}
