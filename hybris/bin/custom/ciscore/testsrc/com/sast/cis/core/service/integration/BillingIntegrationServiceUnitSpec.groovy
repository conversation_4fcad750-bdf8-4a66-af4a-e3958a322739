package com.sast.cis.core.service.integration

import com.sast.cis.core.billingintegration.request.ExportMode
import com.sast.cis.core.billingintegration.request.ProductExport
import com.sast.cis.core.billingintegration.request.ProductExportFactory
import com.sast.cis.core.enums.BillingSystemStatus
import com.sast.cis.core.exceptions.InvalidStateException
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.PendingProductInfoModel
import com.sast.cis.core.util.LocalePopulatingService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.model.ModelService
import generated.com.sast.cis.core.model.AppLicenseBuilder
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class BillingIntegrationServiceUnitSpec extends JUnitPlatformSpecification {

    public static final String NAME = "testName"
    ProductExportFactory productExportFactory
    ModelService modelService
    LocalePopulatingService localePopulatingService
    BillingIntegrationService billingIntegrationService
    AppModel app
    ProductExport productExport

    def setup() {
        productExportFactory = Mock(ProductExportFactory.class)
        modelService = Mock(ModelService.class)
        localePopulatingService = Mock(LocalePopulatingService.class)
        app = Mock(AppModel.class)
        productExport = Mock(ProductExport.class)
        billingIntegrationService = new BillingIntegrationService(productExportFactory, modelService, localePopulatingService)
    }

    @Test
    def 'if billing system status is update pending then throw an exception'() {
        when:
        def appLicense = getLicenseWithBillingStatus(BillingSystemStatus.UPDATE_PENDING)
        billingIntegrationService.updateBaseProductAndVariants(app, appLicense)

        then: 'The billing status is invalid'
        thrown(InvalidStateException.class)
    }

    @Test
    def 'if billing system status is create pending then throw an exception'() {
        when:
        def appLicense = getLicenseWithBillingStatus(BillingSystemStatus.CREATE_PENDING)
        billingIntegrationService.updateBaseProductAndVariants(app, appLicense)

        then: 'The billing status is invalid'
        thrown(InvalidStateException.class)
    }

    @Test
    def 'if billing system status is new then throw an exception'() {
        when:
        def appLicense = getLicenseWithBillingStatus(BillingSystemStatus.NEW)
        billingIntegrationService.updateBaseProductAndVariants(app, appLicense)

        then: 'The billing status is invalid'
        thrown(InvalidStateException.class)
    }

    @Test
    def 'if billing system status is rejected then throw an exception'() {
        when:
        def appLicense = getLicenseWithBillingStatus(BillingSystemStatus.REJECTED)
        billingIntegrationService.updateBaseProductAndVariants(app, appLicense)

        then: 'The billing status is invalid'
        thrown(InvalidStateException.class)
    }

    @Test
    def 'update base product and variants when billing system status is inSync'() {
        given:
        def appLicense = getLicenseWithBillingStatus(BillingSystemStatus.IN_SYNC)
        def pendingProductInfo = Mock(PendingProductInfoModel.class)
        pendingProductInfo.setName(NAME, Locale.ENGLISH)
        app.setName(NAME, Locale.ENGLISH)
        appLicense.setPendingProductInfo(pendingProductInfo)
        appLicense.setBillingSystemStatus(BillingSystemStatus.UPDATE_PENDING)

        when:
        localePopulatingService.generateLocaleMapIgnoringNulls(_) >> Map.of(Locale.ENGLISH, "testApp")
        productExportFactory.getProductExport(ExportMode.SYNC) >> productExport
        productExport.updateProduct(appLicense)
        modelService.saveAll(pendingProductInfo, appLicense)
        billingIntegrationService.updateBaseProductAndVariants(app, appLicense)

        then:
        1 * modelService.saveAll(pendingProductInfo, appLicense)
        2 * productExport.updateProduct(appLicense)
    }

    def getLicenseWithBillingStatus(BillingSystemStatus status) {
        AppLicenseBuilder.generate().withBillingSystemStatus(status).buildMockInstance()
    }
}
