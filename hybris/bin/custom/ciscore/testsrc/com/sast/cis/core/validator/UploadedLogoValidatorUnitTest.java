package com.sast.cis.core.validator;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.constants.devcon.DevconInputField;
import com.sast.cis.core.data.ClientImageData;
import com.sast.cis.core.data.StoreContentData;
import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.service.ProductContainerService;
import com.sast.cis.core.service.media.CisMediaContainerService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.media.MediaContainerModel;
import generated.com.sast.cis.core.model.ProductContainerBuilder;
import generated.de.hybris.platform.core.model.media.MediaContainerBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Optional;
import java.util.Set;

import static com.sast.cis.test.utils.TestDataConstants.basicStoreContent;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class UploadedLogoValidatorUnitTest {

    private static final StoreContentData BASIC_STORE_CONTENT = basicStoreContent();
    private static final int LOGO_NUMBER_TOO_MANY = 2;

    @InjectMocks
    private UploadedLogoValidator uploadedLogoValidator;

    @Mock
    private ProductContainerService productContainerService;

    @Mock
    private CisMediaContainerService cisMediaContainerService;

    @Mock
    private ErrorMessageService errorMessageService;

    @Before
    public void setUp() {
        ProductContainerModel productContainer = ProductContainerBuilder.generate().buildMockInstance();

        MediaContainerModel mediaContainerModel = MediaContainerBuilder.generate()
            .withOwner(productContainer)
            .buildMockInstance();

        when(productContainerService.getProductContainerForCode(any())).thenReturn(Optional.of(productContainer));
        when(cisMediaContainerService.getStagedMediaContainerForQualifier(any())).thenReturn(mediaContainerModel);
        when(errorMessageService.createErrorMessage(any(DevconErrorCode.class), any(DevconInputField.class)))
            .thenAnswer(answer -> new DevconErrorMessage()
                .withCode(answer.getArgument(0, DevconErrorCode.class))
                .withField(answer.getArgument(1, DevconInputField.class)));
    }

    @Test
    public void validate_validStoreContent_emptyErrorMap() {
        StoreContentData storeContentData = BASIC_STORE_CONTENT.withIcon(ImmutableList.of());

        Set<DevconErrorMessage> validationErrors = uploadedLogoValidator.validate(storeContentData, false);
        assertThat(validationErrors).isEmpty();
    }

    @Test
    public void validate_withoutNeededLogo_withCorrectMap() {
        StoreContentData storeContentData = BASIC_STORE_CONTENT.withIcon(ImmutableList.of());

        Set<DevconErrorMessage> validationErrors = uploadedLogoValidator.validate(storeContentData, true);
        assertThat(validationErrors)
            .isEqualTo(ImmutableSet.of(new DevconErrorMessage().withCode(DevconErrorCode.ICON_EMPTY).withField(DevconInputField.ICON)));
    }

    @Test
    public void validate_tooManyLogos_correctMessageInErrorMap() {
        ClientImageData sampleImage = new ClientImageData()
            .withQualifier("testQualifier");
        StoreContentData storeContentData = BASIC_STORE_CONTENT.withIcon(new ArrayList<>(Collections.nCopies(
            LOGO_NUMBER_TOO_MANY, sampleImage)));

        Set<DevconErrorMessage> validationErrors = uploadedLogoValidator.validate(storeContentData, false);
        assertThat(validationErrors).isEqualTo(ImmutableSet.of(new DevconErrorMessage()
            .withCode(DevconErrorCode.ICON_MAX_NUMBER_EXCEEDED)
            .withField(DevconInputField.ICON)));
    }

    @Test
    public void validate_wrongOwner_correctMessageInErrorMap() {
        ClientImageData sampleImage = new ClientImageData()
            .withQualifier("testQualifier");
        StoreContentData storeContentData = BASIC_STORE_CONTENT.withIcon(ImmutableList.of(sampleImage));

        setupMockReturningWrongProductContainer();

        Set<DevconErrorMessage> validationErrors = uploadedLogoValidator.validate(storeContentData, false);
        assertThat(validationErrors).isEqualTo(ImmutableSet.of(new DevconErrorMessage()
            .withCode(DevconErrorCode.ICON_OWNER_NOT_MATCHING)
            .withField(DevconInputField.ICON)));
    }

    private void setupMockReturningWrongProductContainer() {
        ProductContainerModel otherContainer = ProductContainerBuilder.generate().buildMockInstance();
        when(productContainerService.getProductContainerForCode(any())).thenReturn(Optional.of(otherContainer));
    }
}
