package com.sast.cis.core.service.company.sync;

import com.sast.cis.core.BaseIntegrationTest;
import com.sast.cis.core.config.BaseStoreConfigService;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.data.UmpAddressData;
import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.enums.CompanyApprovalStatus;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.core.service.singlesignon.BillingAddressService;
import com.sast.cis.test.utils.Country;
import com.sast.cis.test.utils.UmpWireMockRule;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.core.model.user.AddressModel;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.core.model.user.AddressBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import javax.annotation.Resource;
import java.math.BigDecimal;

import static com.sast.cis.test.utils.TestDataConstants.AZENA_MARKETPLACE_ID;
import static com.sast.cis.test.utils.UmpWireMockRule.UMP_BASE_URL_KEY;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class IotCompanySyncServiceITest extends BaseIntegrationTest {
    @Rule
    public UmpWireMockRule umpWireMockRule = new UmpWireMockRule();

    @Resource
    private IotCompanySyncService iotCompanySyncService;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Resource
    private DeveloperService developerService;

    @Resource
    private BaseStoreService baseStoreService;

    @Resource
    private BaseStoreConfigService baseStoreConfigService;
    @Resource
    private BillingAddressService billingAddressService;

    @Resource
    private CommonI18NService commonI18NService;

    private static final String COMPANY_ID = "123456";
    private static final String UMP_COMPANY_NAME = "New Great name";
    private static final String UMP_BILLING_ADDRESS_STREET = "New Ump Street";
    private static final String UMP_BILLING_ADDRESS_CITY = "New Ump City";
    private static final String COMPANY_FRIENDLY_NAME = "Great friendly name";
    private static final String ORIGINAL_COMPANY_NAME = "Company name";
    private static final String COST_CENTER_ID = "123456";
    private static final String COMPANY_EMAIL = "<EMAIL>";
    private IoTCompanyModel company;
    private BaseStoreModel baseStore;

    private LanguageModel english;
    private LanguageModel german;

    @Before
    public void setup() {
        baseStore = baseStoreService.getBaseStoreForUid(AZENA_MARKETPLACE_ID);
        String configPrefixForBaseStore = baseStoreConfigService.getConfigPrefixForBaseStore(BaseStoreEnum.AZENA);
        String key = String.format(UMP_BASE_URL_KEY, configPrefixForBaseStore);
        configurationService.getConfiguration().setProperty(key, umpWireMockRule.getUmpUrl());

        english = commonI18NService.getLanguage("en");
        german = commonI18NService.getLanguage("de");
    }

    @Test
    public void shouldSyncRelevantFields() {
        prepareCompanyData(CompanyApprovalStatus.UNAPPROVED);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, AZENA_MARKETPLACE_ID);
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(false);
        companyData.setCostCenter(COST_CENTER_ID);

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getBillingAddress().getCompany()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getBrimCostCenterId()).isEqualTo(COST_CENTER_ID);
        assertThat(company.isManualAppApprovalEnabled()).isFalse();
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
    }

    @Test
    public void shouldSyncWithoutBillingAddress() {
        prepareCompanyWithoutBillingAddress(CompanyApprovalStatus.UNAPPROVED);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, AZENA_MARKETPLACE_ID);
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(true);
        companyData.setBillingAddress(null);

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getBillingAddress()).isNull();
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
    }

    @Test
    public void shouldSyncWithBillingAddressWhenNotPresentInitially() {
        prepareCompanyWithoutBillingAddress(CompanyApprovalStatus.UNAPPROVED);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, AZENA_MARKETPLACE_ID);
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(true);
        companyData.setBillingAddress(new UmpAddressData()
                .withCity(UMP_BILLING_ADDRESS_CITY)
                .withStreet(UMP_BILLING_ADDRESS_STREET));

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getBillingAddress().getStreetname()).isEqualTo(UMP_BILLING_ADDRESS_STREET);
        assertThat(company.getBillingAddress().getTown()).isEqualTo(UMP_BILLING_ADDRESS_CITY);
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
    }

    @Test
    public void shouldSyncWithBillingAddressWhenExists() {
        prepareCompanyWithoutBillingAddress(CompanyApprovalStatus.UNAPPROVED);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, AZENA_MARKETPLACE_ID);
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(true);
        company.setBillingAddress(billingAddressService.createAddress(companyData, company));

        companyData.setBillingAddress(new UmpAddressData()
                .withCity(UMP_BILLING_ADDRESS_CITY)
                .withStreet(UMP_BILLING_ADDRESS_STREET));

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getBillingAddress().getStreetname()).isEqualTo(UMP_BILLING_ADDRESS_STREET);
        assertThat(company.getBillingAddress().getTown()).isEqualTo(UMP_BILLING_ADDRESS_CITY);
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
    }

    @Test
    public void shouldSyncWithIsManagedFlag() {
        prepareCompanyWithoutBillingAddress(CompanyApprovalStatus.UNAPPROVED);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, BaseStoreEnum.AA.getBaseStoreUid());
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(true);
        companyData.setIsManaged(true);

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
        assertThat(company.isIsManaged()).isTrue();
    }

    @Test
    public void shouldSetCommunicationLanguage() {
        prepareCompanyWithoutBillingAddress(CompanyApprovalStatus.UNAPPROVED);
        company.setCommunicationLanguage(null);
        modelService.save(company);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, BaseStoreEnum.AA.getBaseStoreUid());
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(true);
        companyData.setCommunicationLanguage("en");

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
        assertThat(company.getCommunicationLanguage()).isNotNull().isEqualTo(english);
    }

    @Test
    public void shouldRemoveCommunicationLanguage() {
        prepareCompanyWithoutBillingAddress(CompanyApprovalStatus.UNAPPROVED);
        company.setCommunicationLanguage(german);
        modelService.save(company);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, BaseStoreEnum.AA.getBaseStoreUid());
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(true);
        companyData.setCommunicationLanguage(null);

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
        assertThat(company.getCommunicationLanguage()).isNull();
    }

    @Test
    public void shouldChangeCommunicationLanguage() {
        prepareCompanyWithoutBillingAddress(CompanyApprovalStatus.UNAPPROVED);
        company.setCommunicationLanguage(english);
        modelService.save(company);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, BaseStoreEnum.AA.getBaseStoreUid());
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(true);
        companyData.setCommunicationLanguage("de");

        umpWireMockRule.prepareGetCompanyDataResponse(COMPANY_ID, umpWireMockRule.buildResponseDefinition(companyData));

        iotCompanySyncService.syncCompany(company);

        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
        assertThat(company.getCommunicationLanguage()).isNotNull().isEqualTo(german);
    }

    @Test
    public void shouldUpdateRelevantFields() {
        prepareCompanyData(CompanyApprovalStatus.UNAPPROVED);

        UmpCompanyData companyData = umpWireMockRule.buildUmpCompanyData(COMPANY_ID, AZENA_MARKETPLACE_ID);
        companyData.setCompanyName(UMP_COMPANY_NAME);
        companyData.setFriendlyName(COMPANY_FRIENDLY_NAME);
        companyData.setCompanyStatus(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL.getCode());
        companyData.setCreditLimit(BigDecimal.TEN);
        companyData.setIsManualAppApprovalEnabled(false);
        companyData.setCostCenter(COST_CENTER_ID);
        companyData.setCompanyEmail(COMPANY_EMAIL);

        iotCompanySyncService.updateOrCreateCompany(companyData);
        modelService.refresh(company);

        assertThat(company.getApprovalStatus()).isEqualTo(CompanyApprovalStatus.APPROVED_NON_COMMERCIAL);
        assertThat(company.getName()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getFriendlyName()).isEqualTo(COMPANY_FRIENDLY_NAME);
        assertThat(company.getBillingAddress().getCompany()).isEqualTo(UMP_COMPANY_NAME);
        assertThat(company.getBrimCostCenterId()).isEqualTo(COST_CENTER_ID);
        assertThat(company.isManualAppApprovalEnabled()).isFalse();
        assertThat(company.isIsManaged()).isFalse();
        assertThat(company.getStore().getUid()).isEqualTo(AZENA_MARKETPLACE_ID);
        assertThat(company.getCompanyEmail()).isEqualTo(COMPANY_EMAIL);
    }

    private void prepareCompanyData(CompanyApprovalStatus status) {

        prepareCompanyWithoutBillingAddress(status);

        AddressModel address = defaultAddress(company);

        company.setBillingAddress(address);
        company.setContactAddress(address);

        modelService.saveAll(address, company);
        modelService.refresh(address);
    }

    private AddressModel defaultAddress(IoTCompanyModel company) {
        return AddressBuilder.generate()
            .withCompany(ORIGINAL_COMPANY_NAME)
            .withOwner(company)
            .buildIntegrationInstance();
    }

    private void prepareCompanyWithoutBillingAddress(CompanyApprovalStatus status) {

        company = IoTCompanyBuilder.generate()
            .withName(ORIGINAL_COMPANY_NAME)
            .withCountry(sampleDataCreator.getCountry(Country.GERMANY))
            .withUid(COMPANY_ID)
            .withStore(baseStore)
            .withBillingAddress(null)
            .buildIntegrationInstance();

        AddressModel address = defaultAddress(company);

        company.setApprovalStatus(status);
        company.setContactAddress(address);
        modelService.saveAll(company, address);
    }
}
