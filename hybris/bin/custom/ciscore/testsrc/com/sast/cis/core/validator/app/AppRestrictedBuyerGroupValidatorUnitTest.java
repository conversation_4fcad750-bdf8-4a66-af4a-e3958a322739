package com.sast.cis.core.validator.app;

import com.sast.cis.core.customergroup.AnonymousUserCustomerGroupProvider;
import com.sast.cis.core.enums.StoreAvailabilityMode;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.company.IotCompanyService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.user.UserGroupModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class AppRestrictedBuyerGroupValidatorUnitTest {

    @Mock
    private UserService userService;

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private AppLicenseService appLicenseService;

    @Mock
    private BaseStoreService baseStoreService;

    @Mock
    private AnonymousUserCustomerGroupProvider anonymousUserCustomerGroupProvider;

    @InjectMocks
    private AppRestrictedBuyerGroupValidator appRestrictedBuyerGroupValidator;

    @Mock
    private AppModel appModel;

    @Mock
    private UserModel userModel;

    @Mock
    private IoTCompanyModel ioTCompanyModel;

    @Mock
    private UserGroupModel userGroupModel;

    @Mock
    private BaseStoreModel baseStoreModel;

    private final String userGroupUid = "TEST_USER_GROUP";
    private final String wrongUserGroupUid = "WRONG_USER_GROUP";

    @Before
    public void setUp() {
        when(userService.getCurrentUser()).thenReturn(userModel);
        when(iotCompanyService.getCurrentCompany()).thenReturn(Optional.of(ioTCompanyModel));
        when(appLicenseService.getUserGroupUidSetFromApp(appModel)).thenReturn(Set.of(userGroupUid));
        when(baseStoreService.getCurrentBaseStore()).thenReturn(baseStoreModel);

        when(userGroupModel.getUid()).thenReturn(userGroupUid);
    }

    @Test
    public void givenAppIsPublic_whenIsAvailableForCurrentUser_thenReturnsTrue() {
        when(appModel.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.PUBLIC);

        assertThat(appRestrictedBuyerGroupValidator.isAvailableForCurrentUser(appModel)).isTrue();
    }

    @Test
    public void givenAuthenticatedUserWithoutCustomerGroupAndAppIsRestricted_whenIsAvailableForCurrentUser_thenReturnsFalse() {
        when(appModel.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.RESTRICTED_BUYER_GROUP);

        assertThat(appRestrictedBuyerGroupValidator.isAvailableForCurrentUser(appModel)).isFalse();
    }

    @Test
    public void givenAuthenticatedUserWithCustomerGroupAndAppIsRestricted_whenIsAvailableForCurrentUser_thenReturnsTrue() {
        when(appModel.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.RESTRICTED_BUYER_GROUP);
        when(ioTCompanyModel.getAaCustomerGroup()).thenReturn(userGroupModel);

        assertThat(appRestrictedBuyerGroupValidator.isAvailableForCurrentUser(appModel)).isTrue();
    }

    @Test
    public void givenAuthenticatedUserWithWrongCustomerGroupAndAppIsRestricted_whenIsAvailableForCurrentUser_thenReturnsFalse() {
        when(appModel.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.RESTRICTED_BUYER_GROUP);
        when(ioTCompanyModel.getAaCustomerGroup()).thenReturn(userGroupModel);
        when(userGroupModel.getUid()).thenReturn(wrongUserGroupUid);

        assertThat(appRestrictedBuyerGroupValidator.isAvailableForCurrentUser(appModel)).isFalse();
    }

    @Test
    public void givenAnonymousUserWithoutCustomerGroupAndAppIsRestricted_whenIsAvailableForCurrentUser_thenReturnsFalse() {
        when(userService.isAnonymousUser(userModel)).thenReturn(true);
        when(appModel.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.RESTRICTED_BUYER_GROUP);
        when(anonymousUserCustomerGroupProvider.getCustomerGroupUidForBaseStore(baseStoreModel)).thenReturn(Optional.empty());

        assertThat(appRestrictedBuyerGroupValidator.isAvailableForCurrentUser(appModel)).isFalse();
    }

    @Test
    public void givenAnonymousUserWithCustomerGroupAndAppIsRestricted_whenIsAvailableForCurrentUser_thenReturnsTrue() {
        when(userService.isAnonymousUser(userModel)).thenReturn(true);
        when(appModel.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.RESTRICTED_BUYER_GROUP);
        when(anonymousUserCustomerGroupProvider.getCustomerGroupUidForBaseStore(baseStoreModel)).thenReturn(Optional.of(userGroupUid));

        assertThat(appRestrictedBuyerGroupValidator.isAvailableForCurrentUser(appModel)).isTrue();
    }

    @Test
    public void givenAnonymousUserWithWrongCustomerGroupAndAppIsRestricted_whenIsAvailableForCurrentUser_thenReturnsFalse() {
        when(userService.isAnonymousUser(userModel)).thenReturn(true);
        when(appModel.getStoreAvailabilityMode()).thenReturn(StoreAvailabilityMode.RESTRICTED_BUYER_GROUP);
        when(anonymousUserCustomerGroupProvider.getCustomerGroupUidForBaseStore(baseStoreModel)).thenReturn(
            Optional.of(wrongUserGroupUid));

        assertThat(appRestrictedBuyerGroupValidator.isAvailableForCurrentUser(appModel)).isFalse();
    }
}
