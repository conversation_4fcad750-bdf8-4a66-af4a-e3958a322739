package com.sast.cis.core.validator.license.activation.rules

import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.LicenseActivationModel
import com.sast.cis.core.validator.license.activation.LicenseActivationValidationError
import de.hybris.bootstrap.annotations.UnitTest
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

import static com.sast.cis.core.enums.LicenseType.*

@UnitTest
class ValidLicenseTypeRuleUnitSpec extends JUnitPlatformSpecification {

    LicenseActivationModel licenseActivation = Mock(LicenseActivationModel)
    AppLicenseModel appLicense = Mock(AppLicenseModel)

    ValidLicenseTypeRule validLicenseTypeRule

    def setup() {
        validLicenseTypeRule = new ValidLicenseTypeRule()

        licenseActivation.getAppLicense() >> appLicense
    }

    @Test
    @Unroll
    def 'given license is #givenLicenseType, when validate then validation succeeds'() {
        when:
        def validationResult = validLicenseTypeRule.validate(licenseActivation)

        then:
        appLicense.getLicenseType() >> givenLicenseType
        validationResult == [] as Set

        where:
        givenLicenseType << [TOOL]
    }

    @Test
    @Unroll
    def 'given license is #givenLicenseType, when validate then validation fails'() {
        when:
        def validationResult = validLicenseTypeRule.validate(licenseActivation)

        then:
        appLicense.getLicenseType() >> givenLicenseType
        validationResult == [LicenseActivationValidationError.LICENSE_TYPE_NOT_SUPPORTED] as Set

        where:
        givenLicenseType << [EVALUATION, FULL, SUBSCRIPTION]
    }
}
