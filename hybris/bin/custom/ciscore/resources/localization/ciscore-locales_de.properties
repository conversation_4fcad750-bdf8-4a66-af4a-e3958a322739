
type.AppLicense.name=App-Liz<PERSON>z
type.AppLicense.description=Ein<PERSON> Lizenz fÃ¼r eine App
type.AppLicense.licenseType.name=Liz<PERSON>ztyp
type.AppLicense.licenseType.description=Der verwendete Lizenztyp
type.AppLicense.billingSystemStatus.name=Status des Abrechnungssystems
type.AppLicense.billingSystemStatus.description=Synchronisationsstatus des Abrechnungssystems
type.AppLicense.groupPriceUpdateStatus.name=Status der Gruppenpreisaktualisierung
type.AppLicense.groupPriceUpdateStatus.description=Synchronisationsstatus der Gruppenpreisaktualisierung
type.AppLicense.currentlyValidPrices.name=Aktuelle Preise
type.AppLicense.currentlyValidPrices.description=Aktuell aktive Preise
type.AppLicense.icon.name=Icon
type.AppLicense.icon.description=Icon (vom Basisprodukt)
type.AppLicense.enabled.name=Aktiv
type.AppLicense.enabled.description=Ist der Lizenztyp aktuell aktiv
type.AppLicense.purchasableInBillingSystem.name=Erwerbbar im Billingsystem
type.AppLicense.purchasableInBillingSystem.description=App-Lizenz ist im Billingsystem erwerbbar
type.AppLicense.volumeDiscounts.name=Volumenrabatte
type.AppLicense.volumeDiscounts.description=Volumenrabatte basierend auf der Mindestmenge
type.AppLicense.futurePrices.name=ZukÃ¼nftige Preise
type.AppLicense.futurePrices.description=Ausstehende Preise, die in Zukunft verwendet werden
type.AppLicense.userGroups.name=App-Lizenz Benutzergruppen
type.AppLicense.userGroups.description=Die Benutzergruppen, die die Lizenz erworben haben
type.AppVersion.name=App-Version
type.AppVersion.description=Eine Version einer App
type.AppVersion.code.name=Code
type.AppVersion.code.description=Code der App-Version
type.AppVersion.apk.name=APK der App-Version
type.AppVersion.apk.description=Die BinÃ¤rdatei fÃ¼r das Deployment der App
type.AppVersion.app.name=App
type.AppVersion.app.description=Die dazugehÃ¶rige App
type.AppVersion.changelog.name=App-Versions-Changelog
type.AppVersion.changelog.description=Das Changelog einer App-Version
type.AppVersion.rejectionReason.name=ZurÃ¼ckweisungsgrund
type.AppVersion.rejectionReason.description=Grund fÃ¼r die ZurÃ¼ckweisung der Version
type.AppVersion.approvalStatus.name=Genehmigungsstatus
type.AppVersion.approvalStatus.description=Der Genehmigungsstatus der Version
type.AppVersion.catalogVersion.name=Katalogversion
type.AppVersion.catalogVersion.description=Katalogversion, welche die App-Version enthÃ¤lt
type.AppVersion.exportRegulationAcknowledged.name=Exportrichtlinien zur Kenntnis genommen
type.AppVersion.exportRegulationAcknowledged.description=Exportrichtlinien zur Kenntnis genommen vor dem APK Upload
type.AppVersion.dualUse.name=Dual Use
type.AppVersion.dualUse.description=Kennzeichnung der App-Version als "Dual Use"
type.AppVersion.eccn.name=ECCN
type.AppVersion.eccn.description=Export Control Classification Number fÃ¼r diese App-Version
type.AppVersion.submittedBy.name=Eingereicht von
type.AppVersion.submittedBy.description=Der User, der die App-Version eingereicht hat

type.ApkSignature.code.name=Code
type.ApkSignature.code.description=Code der Apk-Signatur
type.ApkSignature.name=Apk-Signatur
type.ApkSignature.description=EnthÃ¤lt die Liste der validen Signaturen fÃ¼r das APK
type.ApkSignature.certificateSha256.name=APK SHA-256 Digest
type.ApkSignature.certificateSha256.description=SHA-256 des verwendeten Zertifikats
type.ApkSignature.signatureVersion.name=Signatur-Version
type.ApkSignature.signatureVersion.description=Versionsnummer der Signature
type.ApkSignature.validTo.name=GÃ¼ltig bis
type.ApkSignature.validTo.description=Der Zeitpunkt, bis zu dem die Signatur gÃ¼ltig ist
type.ApkSignature.subjectCName.name=Inhabers CN
type.ApkSignature.subjectCName.description=Inhabers Common Name

type.IoTCustomer.name=IoT-Kunde
type.IoTCustomer.description=Kunde des Ãkosystems
type.IoTCustomer.company.name=IoT-Firma
type.IoTCustomer.company.description=Die Firma, fÃ¼r die ein Entwickler/Integrator arbeitet

type.Developer.name=Developer
type.Developer.description=Developer
type.Developer.integrator.name=Integrator
type.Developer.integrator.description=Der zu diesem Benutzerkonto gehÃ¶rige Integrator
type.Developer.apps.name=Apps
type.Developer.apps.description=Die Apps des Entwicklers

type.Device.name=Device
type.Device.description=Ein IoT GerÃ¤t
type.Device.appVersions.name=App-Versionen
type.Device.appVersions.description=App-Version fÃ¼r das GerÃ¤t
type.Device.deviceID.name=Device ID
type.Device.deviceID.description=Die ID des GerÃ¤tes im Remote Portal
type.Device.name.name=Device Name
type.Device.name.description=Der Name des GerÃ¤tes
type.Device.versions.name=Kompatible Versionen
type.Device.versions.description=Kompatible Versionen der App fÃ¼r dieses GerÃ¤t

type.Integrator.name=Integrator
type.Integrator.description=Ein System-Integrator
type.Integrator.developer.name=Developer
type.Integrator.developer.description=Der zu diesem Benutzerkonto gehÃ¶rige Developer

type.App.name=App
type.App.description=Eine App
type.App.company.name=Company
type.App.company.description=Company des Produktes (App)
type.App.packageName.name=Paketname
type.App.packageName.description=Der eindeutige Name des Androidpakets
type.App.emailAddress.name=Support-E-Mail-Adresse
type.App.emailAddress.description=E-Mail-Adresse des Supports der App
type.App.supportPhoneNumber.name=Telefonnummer des Kundendienstes
type.App.supportPhoneNumber.description=Telefonnummer des Kundendienstes
type.App.privacyPolicyUrl.name=URL der DatenschutzerklÃ¤rung
type.App.privacyPolicyUrl.description=URL, die auf die DatenschutzerklÃ¤rung zeigt
type.App.supportPageUrl.name=URL der Hilfsseite
type.App.supportPageUrl.description=URL, die auf die Hilfsseite zeigt
type.App.termsOfUseUrl.name=URL der Nutzungsbedingungen
type.App.termsOfUseUrl.description=URL, die auf die Nutzungsbedingungen zeigt
type.App.productWebsiteUrl.name=App- oder Firmenwebsite
type.App.productWebsiteUrl.description=Website der App oder der Firma
type.App.installCount.name=Installationen
type.App.installCount.description=Anzahl der KÃ¤ufe und Installationen dieser App
type.App.specifiedPrice.name=Spezifizierter Preis
type.App.specifiedPrice.description=Der durch den Entwickler festgelegte Grundpreis
type.App.subscriptionPrice.name=Angegebener Abonnementpreis
type.App.subscriptionPrice.description=Der durch den Entwickler festgelegte Abonnementpreis
type.App.versions.name=App-Versionen
type.App.versions.description=Versionen der App
type.App.enabledInStore.name=VerfÃ¼gbar im Webshop
type.App.enabledInStore.description=App is verfÃ¼gbar im Webshop
type.App.masterEnabled.name=System aktiviert
type.App.masterEnabled.description=App auf Systemebene aktiviert
type.App.owningCompanies.name=Firmen
type.App.owningCompanies.description=Firmen, die die App gekauft haben
type.App.icon.name=Icon
type.App.icon.description=Das Icon der App
type.App.documentationFiles.name=Dokumentationsdateien
type.App.documentationFiles.description=Detaillierte Beschreibung der App
type.App.latestVersion.name=Neueste Version
type.App.latestVersion.description=Die neueste Version der App
type.App.submittedBy.name=Eingereicht von
type.App.submittedBy.description=Der User, der die App eingereicht hat
type.App.useCases.name=Nutzungsszenarien
type.App.useCases.description=Vorgeschlagene Nutzungsszenarien fÃ¼r diese App
type.App.permittedBuyerCompanies.name=Erlaubte KÃ¤ufer
type.App.permittedBuyerCompanies.description=Firmen, fÃ¼r welche die App im RESTRICTED_BUYER-VerfÃ¼gbarkeitsmodus zur VerfÃ¼gung steht
type.App.storeAvailabilityMode.name=VerfÃ¼gbarkeitsmodus
type.App.storeAvailabilityMode.description=VerfÃ¼gbarkeitsmodus dieser App
type.App.newCountryAddingAllowed.name=Neues Land hinzufÃ¼gen erlaubt
type.App.newCountryAddingAllowed.description=Zustimmung zum HinzufÃ¼gen eines neuen Ãkosystemlandes zu App-fÃ¤higen LÃ¤ndern
type.App.appIntegrations.name=App-Integrationen
type.App.appIntegrations.description=Liste von Integrationen mit externen Systemen
type.App.eula.name=EULA
type.App.eula.description=End User License Agreement
type.App.eulaContainers.name=Landesspezifische EULAs
type.App.eulaContainers.description=Landesspezifische End User License Agreements
type.App.privateOfferRequest.name=Privat Angebotsanfrage
type.App.privateOfferRequest.description=Privat Angebotsanfrage

type.StandardAppIntegration.name=Standard-App-Integration
type.StandardAppIntegration.description=Standard-Integration einer App mit externen Systemen
type.StandardAppIntegration.name.name=Name der Integration
type.StandardAppIntegration.name.description=Der Name der Standard-App-Integration
type.StandardAppIntegration.description.name=Beschreibung der Integration
type.StandardAppIntegration.description.description=Eine Beschreibung der Standard-App-Integration, die im Frontend angezeigt wird
type.StandardAppIntegration.enabled.name=Integration freigegeben
type.StandardAppIntegration.enabled.description=Ob die Integration im Store gezeigt wird
type.StandardAppIntegration.code.name=App-Integration-Code
type.StandardAppIntegration.code.description=Eindeutige ID fÃ¼r die Standard-App-Integration
type.StandardAppIntegration.displayName.name=Ãffentlicher Name der App-Integration
type.StandardAppIntegration.displayName.description=Im Store angezeigter Name der App-Integration
type.StandardAppIntegration.integrationType.name=Typ der Integration
type.StandardAppIntegration.integrationType.description=Externer Standard, Gateway, usw
type.StandardAppIntegration.externalDescription.name=Ãffentliche Beschreibung der App-Integration
type.StandardAppIntegration.externalDescription.description=Im Store angezeigte Beschreibung der App-Integration
type.StandardAppIntegration.order.name=Reihenfolge der App-Integration
type.StandardAppIntegration.order.description=Die Reihenfolge der App-Integration in die Liste

type.AbstractOrder.company.name=Firma
type.AbstractOrder.company.description=IoT-Firma, mit der diese AbstractOrder verknÃ¼pft ist
type.AbstractOrder.requestOrderTime.name=Bestellungsanforderungszeit
type.AbstractOrder.requestOrderTime.description=Zeit, zu der die Simualtion der Bestellung beim Billing-Backend angefordert wurde
type.AbstractOrder.eulaAcceptance.name=EULA-Zustimmung
type.AbstractOrder.eulaAcceptance.description=EULA-Zustimmung
type.AbstractOrder.activationMode.name=Aktivierungsmodus
type.AbstractOrder.activationMode.description=Der Lizenz aktivierenmodus der Bestellung

type.PaymentInfo.paymentProvider.name=Zahlungsanbieter
type.PaymentInfo.paymentProvider.description=Der Zahlungsanbieter, mit dem die Zahlung durchgefÃ¼hrt werden soll.
type.PaymentInfo.companyScope.name=Unternehmensbereich
type.PaymentInfo.companyScope.description=Gibt an, ob das Mandat für das Unternehmen oder den Benutzer gilt.

type.PaymentProvider.name=Zahlungsanbieter
type.PaymentProvider.description=Ein Zahlungsanbieter.

type.StripeCreditCardPaymentInfo.name=Stripe-Bezahlinformation
type.StripeCreditCardPaymentInfo.description=Informationen zur Bezahlung Ã¼ber Stripe
type.StripeCreditCardPaymentInfo.cardSummary.name=Kreditkartennummer (Ende)
type.StripeCreditCardPaymentInfo.cardSummary.description=Die letzten 4 Ziffern der Kreditkartennummer
type.StripeCreditCardPaymentInfo.subVariant.name=Kreditkartensubtyp
type.StripeCreditCardPaymentInfo.subVariant.description=Subtyp der Kreditkarte
type.StripeCreditCardPaymentInfo.stripeCcOwnerId.name=Kunden-Id
type.StripeCreditCardPaymentInfo.stripeCcOwnerId.description=Id des bei Stripe hinterlegten Kreditkartenbesitzers
type.StripeCreditCardPaymentInfo.paymentMethodId.name=Zahlungsmethoden-Id
type.StripeCreditCardPaymentInfo.paymentMethodId.description=Id der bei Stripe hinterlegten Zahlungsmethode
type.StripeCreditCardPaymentInfo.reusable.name=Hinterlegt
type.StripeCreditCardPaymentInfo.reusable.description=Kreditkartendaten sind beim Zahlungsanbieter hinterlegt

type.SepaCreditTransferPaymentInfo.name=Stripe SEPA Ãberweisung Zahlungsinformationen
type.SepaCreditTransferPaymentInfo.description=Informationen zur SEPA Ãberweisung per Stripe
type.SepaCreditTransferPaymentInfo.bankAccountIdentifier.name=Bankkontokennung
type.SepaCreditTransferPaymentInfo.bankAccountIdentifier.description=Bankkontokennung
type.SepaCreditTransferPaymentInfo.bankIdentifier.name=Bankkennung
type.SepaCreditTransferPaymentInfo.bankIdentifier.description=Bankkennung
type.SepaCreditTransferPaymentInfo.bankName.name=Bank Name
type.SepaCreditTransferPaymentInfo.bankName.description=Name der Bank
type.SepaCreditTransferPaymentInfo.accountHolder.name=Kontoinhaber
type.SepaCreditTransferPaymentInfo.accountHolder.description=Kontoinhaber
type.SepaCreditTransferPaymentInfo.sourceId.name=Die Quell-ID ist die Referenzinformation fÃ¼r die SepaCreditTransfer-Daten, die im externen Zahlungsanbieter gespeichert sind
type.SepaCreditTransferPaymentInfo.sourceId.description=Die Quell-ID ist die Referenzinformation fÃ¼r die SepaCreditTransfer-Daten, die im externen Zahlungsanbieter gespeichert sind

type.ZeroPaymentInfo.name=Zahlungsfreie Bezahlinformation
type.ZeroPaymentInfo.description=Bezahlinformationen, welche genutzt werden wenn der Gesampreis gleich null ist.

type.InvoiceBySellerPaymentInfo.name=Zahlungsinformationen Fakturierung durch VerkÃ¤ufer
type.InvoiceBySellerPaymentInfo.description=Zahlungsinformation bei Fakturierung durch VerkÃ¤ufer. Der Marktplatz stellt keine Rechnung an den KÃ¤ufer

type.AchInternationalCreditTransferPaymentInfo.name=Zahlungsinformationen ACH International
type.AchInternationalCreditTransferPaymentInfo.description=Zahlungsinformationen fÃ¼r ACH-Ãberweisungen von auÃerhalb der USA

type.DirectDebitPaymentInfo.name=Lastschrift
type.DirectDebitPaymentInfo.description=Reprï¿½sentiert Lastschriftermï¿½chtigungen

type.SepaMandatePaymentInfo.name=SEPA-Mandat
type.SepaMandatePaymentInfo.description=Payment Info fï¿½r SEPA-Lastschriftmandate
type.SepaMandatePaymentInfo.mandateReference.name=Mandatsreferenz
type.SepaMandatePaymentInfo.mandateReference.description=Identifiziert ein SEPA-Mandat in Verbindung mit einer Glï¿½ubiger-Identifikationsnummer eindeutig
type.SepaMandatePaymentInfo.IBAN.name=IBAN
type.SepaMandatePaymentInfo.IBAN.description=IBAN des zu belastenden Bankkontos
type.SepaMandatePaymentInfo.accountHolderName.name=Kontoinhaber
type.SepaMandatePaymentInfo.accountHolderName.description=Kontoinhaber des zu belastenden bankkontos
type.SepaMandatePaymentInfo.dateOfSignature.name=Tag der Unterzeichnung
type.SepaMandatePaymentInfo.dateOfSignature.description=Tag der Unterzeichnung des Mandates durch den Kontoinhaber

type.IoTCompany.name=IoT-Firma
type.IoTCompany.description=Firma im IoT-Ãkosystem
type.IoTCompany.friendlyName.name=Freundlicher Firmenname
type.IoTCompany.friendlyName.description=Freundlicher Firmenname Beschreibung
type.IoTCompany.companyAccountId.name=Firmen-ID
type.IoTCompany.companyAccountId.description=ID der Firma
type.IoTCompany.developers.name=Entwickler
type.IoTCompany.developers.description=Entwickler bei der Firma
type.IoTCompany.integrators.name=Integrator
type.IoTCompany.integrators.description=Integrator bei der Firma
type.IoTCompany.employees.name=Benutzer
type.IoTCompany.employees.description=Benutzerkonten der Firma
type.IoTCompany.apps.name=Apps
type.IoTCompany.apps.description=Apps der Firma
type.IoTCompany.paymentInfos.name=Zahlungsinformationen
type.IoTCompany.paymentInfos.description=Zahlungsinformationen
type.IoTCompany.defaultPaymentInfo.name=Standardzahlungsinformationen
type.IoTCompany.defaultPaymentInfo.description=Zahlungsinformationen, die als Standard verwendet wird
type.IoTCompany.appReviews.name=App-Bewertungen
type.IoTCompany.appReviews.description=Von der Firma vorgenommene oder offene App-Bewertungen
type.IoTCompany.productContainers.name=Produkt-Container
type.IoTCompany.productContainers.description=Produkt-Container, die von der Firma verwaltet werden
type.IoTCompany.abstractOrders.name=AbstractOrders
type.IoTCompany.abstractOrders.description=Alle mit der Firma verknÃ¼pften Bestellungen
type.IoTCompany.country.name=Land der Firma
type.IoTCompany.country.description=Land fÃ¼r Auszahlungen an diese Firma
type.IoTCompany.deactivationDate.name=Deaktivierungsdatum
type.IoTCompany.deactivationDate.description=Das Deaktivierungsdatum fÃ¼r diese Firma
type.IoTCompany.active.name=Aktiv
type.IoTCompany.active.description=Ist die Firma nicht deaktiviert?
type.IoTCompany.purchasedApps.name=Gekaufte Apps
type.IoTCompany.purchasedApps.description=Von der Firma gekaufte Apps
type.IoTCompany.stripeCustomerId.name=Kunden-Id
type.IoTCompany.stripeCustomerId.description=Kunden-Id bei Stripe
type.IoTCompany.stripeConnectAccounts.name=Stripe-Connect Konten
type.IoTCompany.stripeConnectAccounts.description=Stripe-Connect Konten, die zu der Firma gehÃ¶ren
type.IoTCompany.payouts.name=Auszahlungen
type.IoTCompany.payouts.description=An diese Firma erfolgte Auszahlungen
type.IoTCompany.billingSystemStatus.name=Billingsystem-Status
type.IoTCompany.billingSystemStatus.description=Status der synchronisation der Firma mit dem Billingsystem
type.IoTCompany.sepaEnabled.name=Sepa-Enabled
type.IoTCompany.sepaEnabled.description=Gibt an, ob die BankÃ¼berweisungsfunktion fÃ¼r das Unternehmen aktiviert ist
type.IoTCompany.privateAppDrafts.name=Private App-EntwÃ¼rfe
type.IoTCompany.privateAppDrafts.description=EntwÃ¼rfe fÃ¼r Apps, welche dieser Firma im RESTRICTED_BUYER-VerfÃ¼gbarkeitsmodus zur VerfÃ¼gung stehen werden
type.IoTCompany.privateApps.name=Private Apps
type.IoTCompany.privateApps.description=Apps, welche dieser Firma im RESTRICTED_BUYER-VerfÃ¼gbarkeitsmodus zur VerfÃ¼gung stehen
type.IoTCompany.pspSellerAccounts.name=PSP Seller Accounts
type.IoTCompany.pspSellerAccounts.description=PSP VerkÃ¤uferkonten, das diesem Unternehmen zugeordnet ist
type.IoTCompany.privateOfferRequest.name=Privat Angebotsanfrage
type.IoTCompany.privateOfferRequest.description=Privat Angebotsanfrage
type.IoTCompany.manualAppApprovalEnabled.name=Manuelle App-Genehmigung aktiviert
type.IoTCompany.manualAppApprovalEnabled.description=FÃ¼r das Unternehmen sind manuelle Genehmigungen erforderlich
type.IoTCompany.ownAppsPurchaseEnabled.name=Kauf eigener Apps aktiviert
type.IoTCompany.ownAppsPurchaseEnabled.description=Wenn aktiviert, kÃ¶nnen Integratoren eigene Unternehmens-Apps kaufen.
type.IoTCompany.store.name=Store
type.IoTCompany.store.description=GeschÃ¤ft des Unternehmens, fungiert als Bereich fÃ¼r das Unternehmen.
type.IoTCompany.bpmdId.name=BPMD Id
type.IoTCompany.bpmdId.description=Die BPMD-ID des Unternehmens, die mit UMP synchronisiert ist.
type.IoTCompany.billingEmail.name=Billing Email
type.IoTCompany.billingEmail.description=Die E-Mail-ID des Unternehmens, die fï¿½r die Abrechnung verwendet wird.
type.IoTCompany.creditLimit.name=Kreditlimit
type.IoTCompany.creditLimit.description=Das Kreditlimit des Unternehmens wird bei UMP festgelegt.
type.IoTCompany.isManaged.name=Das Unternehmen ist ein verwaltetes Konto.
type.IoTCompany.isManaged.description=Das Unternehmen ist ein verwaltetes Konto.
type.IoTCompany.communicationLanguage.name=Kommunikationssprache
type.IoTCompany.communicationLanguage.description=Kommunikationssprache dieser Firma (z.B. fÃ¼r Rechnungen und E-Mails)
type.IoTCompany.companyEmail.name=Firmen-E-Mail
type.IoTCompany.companyEmail.description=E-Mail-Addresse der Firma

type.UnusedPaymentRemovalCronJob.name=UnusedPaymentRemovalCronJob
type.UnusedPaymentRemovalCronJob.description=Cronjob zum LÃ¶schen von ungenutzten Zahlungsdaten
type.UnusedPaymentRemovalCronJob.age.name=Speicherzeit
type.UnusedPaymentRemovalCronJob.age.description=Speicherzeit fÃ¼r ungenutzte Zahlungsdaten

type.PaymentTransactionEntry.cartHash.name=WarenkorbprÃ¼fsumme
type.PaymentTransactionEntry.cartHash.description=Die PrÃ¼fsumme, die bei der Anforderung der Zahlungsautorisierung fÃ¼r den Warenkorb gebildet wurde

type.PaymentContext.name=Bezahlkontext
type.PaymentContext.description=ZusÃ¤tzliche informationen zur benutzten Bezahlmethode
type.PaymentContext.pspCustomerIdentifier.name=PSP-Kundenummer
type.PaymentContext.pspCustomerIdentifier.description=Kundennummer fÃ¼r den relevanten PSP

type.PaymentTransaction.type.name=Transaktionstyp
type.PaymentTransaction.type.description=Typ der beabsichtigten Transaktion
type.PaymentTransaction.paymentInstrument.name=Bezahlmethode
type.PaymentTransaction.paymentInstrument.description=Bezahlmethode dieser Transaktion

type.PaymentInstrument.name=Bezahlmethode
type.PaymentInstrument.description=ZusÃ¤tzliche Informationen zur Bezahlmethode einer Bezahltransaktion
type.PaymentInstrument.pspCustomerIdentifier.name=PSP-Kundennummer
type.PaymentInstrument.pspCustomerIdentifier.description=Kundennummer des Zahlers bei dem relevanten PSP

type.SepaTransferPaymentInstrument.name=SEPA-Ãberweisung
type.SepaTransferPaymentInstrument.description=ZusÃ¤tzliche Informationen zur SEPA-Ãberweisung
type.SepaTransferPaymentInstrument.bankName.name=Bank
type.SepaTransferPaymentInstrument.bankName.description=Name der Bank fÃ¼r Ãberweisung
type.SepaTransferPaymentInstrument.bic.name=BIC
type.SepaTransferPaymentInstrument.bic.description=Bank Identifier Code
type.SepaTransferPaymentInstrument.iban.name=IBAN
type.SepaTransferPaymentInstrument.iban.description=International Bank Account Number

type.AchTransferPaymentInstrument.name=ACH-Ãberweisung
type.AchTransferPaymentInstrument.description=ZusÃ¤tzliche Informationen zur ACH-Ãberweisung
type.AchTransferPaymentInstrument.accountNumber.name=Kontonummer
type.AchTransferPaymentInstrument.accountNumber.description=Kontonummer
type.AchTransferPaymentInstrument.routingNumber.name=Routing Number
type.AchTransferPaymentInstrument.routingNumber.description=Routing Number
type.AchTransferPaymentInstrument.bankName.name=Name der Bank
type.AchTransferPaymentInstrument.bankName.description=Name der Bank
type.AchTransferPaymentInstrument.bic.name=BIC
type.AchTransferPaymentInstrument.bic.description=Bank Identifier Code

type.Order.invoice.name=Rechnung
type.Order.invoice.description=Die Rechnung fÃ¼r die Bestellung
type.Order.invoices.name=Rechnungen
type.Order.invoices.description=Die Rechnungen fÃ¼r die Bestellung
type.Order.idempotencyKey.name=IdempotenzschlÃ¼ssel
type.Order.idempotencyKey.description=SchlÃ¼ssel, der zur Sicherstellung der Idempotenz an das Billing-Backend geschickt wird
type.Order.externalOrderId.name=Externe Bestellnummer
type.Order.externalOrderId.description=Externer code, der zur Identifizierung der Bestellung vom Billing-Backend vergeben wird
type.Order.developerPayout.name=Entwickler-Auszahlung
type.Order.developerPayout.description=Auszahlung an eine Entwicklerfirma, welche diese Bestellung beinhaltet
type.Order.selfBillingInvoices.name=Abrechnungsbelege
type.Order.selfBillingInvoices.description=Abrechnungsbelege, die dieser Bestellung zugeordnet sind
type.Order.mcfOrder.name=MindestprovisionsgebÃ¼hr angewendet?
type.Order.mcfOrder.description=FÃ¼r die Bestellung wird eine MindestprovisionsgebÃ¼hr erhoben.

type.Customer.emailAddress.name=E-Mail-Adresse
type.Customer.emailAddress.description=E-Mail-Adresse des Kunden

type.PaymentInfo.company.name=Firma
type.PaymentInfo.company.description=Firma fÃ¼r die die Bezahlinfo gÃ¼ltig ist

type.Permission.name=Berechtigung
type.Permission.description=Eine Berechtigung, die eine App benÃ¶tigen kann.
type.Permission.technicalName.name=Technischer Name
type.Permission.technicalName.description=Technischer Name der Berechtigung
type.Permission.name.name=Name
type.Permission.name.description=Der angezeigte Name der Berechtigung
type.Permission.icon.name=Symbol
type.Permission.icon.description=Das Symbol, welches die Berechtigung reprÃ¤sentiert
type.Permission.blacklisted.name=Blacklisted
type.Permission.blacklisted.description=Geblacklistete Permissions dÃ¼rfen in keinen Apks enthalten sein welche Ã¼ber die Developer Konsole hochgeladen werden

type.DeviceCapability.name=GerÃ¤te-FÃ¤higkeiten
type.DeviceCapability.description=GerÃ¤tespezifische FÃ¤higkeiten (z.B. Pan-Tilt-Zoom-FÃ¤higkeit)
type.DeviceCapability.displayName.name=Name
type.DeviceCapability.displayName.description=Der angezeigte Name der FÃ¤higkeit
type.DeviceCapability.code.name=Code
type.DeviceCapability.code.description=Code einer FÃ¤higkeit wie im uses-feature-Element des Apk-Manifests deklariert, z.B. com.securityandsafetythings.devicecapabilities.ptz

type.AppDraft.name=App-Entwurf
type.AppDraft.description=Entwurf fÃ¼r eine App
type.AppDraft.code.name=Code
type.AppDraft.code.description=Der eindeutige Code des App-Entwurfs
type.AppDraft.storeContentDraft.name=Webshop-Inhaltsentwurf
type.AppDraft.storeContentDraft.description=Entwurf fÃ¼r die im Webshop anzuzeigenden Metadaten der App
type.AppDraft.countriesAndPricesDraft.name=LÃ¤nder und Preise-Entwurf
type.AppDraft.countriesAndPricesDraft.description=Entwurf, in welchen LÃ¤ndern die App zu welchen Preisen verfÃ¼gbar ist
type.AppDraft.approvalStatus.name=Genehmigungsstatus
type.AppDraft.approvalStatus.description=Der Genehmigungsstatus des App-Entwurfs
type.AppDraft.rejectionReasons.name=ZurÃ¼ckweisungsgrÃ¼nde
type.AppDraft.rejectionReasons.description=GrÃ¼nde fÃ¼r die ZurÃ¼ckweisung des App-Drafts
type.AppDraft.submittedBy.name=Eingereicht von
type.AppDraft.submittedBy.description=Der User, der des App-Entwurfs eingereicht hat
type.AppDraft.permittedBuyerCompanies.name=Erlaubte KÃ¤ufer
type.AppDraft.permittedBuyerCompanies.description=Firmen, fÃ¼r welche die App im RESTRICTED_BUYER-VerfÃ¼gbarkeitsmodus zur VerfÃ¼gung steht
type.AppDraft.storeAvailabilityMode.name=VerfÃ¼gbarkeitsmodus
type.AppDraft.storeAvailabilityMode.description=VerfÃ¼gbarkeitsmodus dieser App
type.AppDraft.newCountryAddingAllowed.name=Neues Land hinzufÃ¼gen erlaubt
type.AppDraft.newCountryAddingAllowed.description=ZDraftContainer2AppAvailabilityDataPopulatorustimmung zum HinzufÃ¼gen eines neuen Ãkosystemlandes zu App-fÃ¤higen LÃ¤ndern

type.RejectionReason.name=ZurÃ¼ckweisungsgrund
type.RejectionReason.description=Grund fÃ¼r die ZurÃ¼ckweisung des App-Entwurfs
type.RejectionReason.rejectionCode.name=Code des ZurÃ¼ckweisungsgrundes
type.RejectionReason.rejectionCode.description=Interner Code fÃ¼r den Grund der ZurÃ¼ckweisung
type.RejectionReason.comment.name=Kommentar zum ZurÃ¼ckweisungsgrund
type.RejectionReason.comment.description=Kommentar, der den Grund der ZurÃ¼ckweisung erlÃ¤utert
type.RejectionReason.appDraft.name=App-Entwurf
type.RejectionReason.appDraft.description=App-Entwurf, fÃ¼r den der ZurÃ¼ckweisungsgrund zutrifft
type.RejectionReason.appVersionDraft.name=App-Version-Entwurf
type.RejectionReason.appVersionDraft.description=App-Version-Entwurf, fÃ¼r den der ZurÃ¼ckweisungsgrund zutrifft

type.StoreContentDraft.name=Webshop-Inhaltsentwurf
type.StoreContentDraft.description=Entwurf fÃ¼r die im Webshop anzuzeigenden Metadaten der App
type.StoreContentDraft.code.name=Code
type.StoreContentDraft.code.description=Der eindeutige Code des Webshop-Inhaltsentwurfs
type.StoreContentDraft.name.name=Name
type.StoreContentDraft.name.description=Name der App
type.StoreContentDraft.description.name=Beschreibung
type.StoreContentDraft.description.description=Beschreibung der App
type.StoreContentDraft.icon.name=Icon
type.StoreContentDraft.icon.description=Icon der App
type.StoreContentDraft.summary.name=Zusammenfassung
type.StoreContentDraft.summary.description=Zusammenfassung der App
type.StoreContentDraft.logo.name=Logo
type.StoreContentDraft.logo.description=Das Logo der App. Veraltet, sollte nicht genutzt werden
type.StoreContentDraft.screenshots.name=Bildschirmfotos
type.StoreContentDraft.screenshots.description=Bildschirmfotos der App
type.StoreContentDraft.documentationFiles.name=Dokumentationsdateien
type.StoreContentDraft.documentationFiles.description=Detaillierte Beschreibung der App
type.StoreContentDraft.emailAddress.name=E-Mail-Adresse
type.StoreContentDraft.emailAddress.description=E-Mail-Adresse fÃ¼r die Kontaktinformationen
type.StoreContentDraft.supportPhoneNumber.name=Kundendiensttelefon
type.StoreContentDraft.supportPhoneNumber.description=Telefonnummer des Kundendienstes
type.StoreContentDraft.privacyPolicyUrl.name=DatenschutzerklÃ¤rungs-URL
type.StoreContentDraft.privacyPolicyUrl.description=URL, die auf die DatenschutzerklÃ¤rung zeigt
type.StoreContentDraft.supportPageUrl.name=URL der Hilfsseite
type.StoreContentDraft.supportPageUrl.description=URL, die auf die Hilfsseite zeigt
type.StoreContentDraft.termsOfUseUrl.name=URL der Nutzungsbedingungen
type.StoreContentDraft.termsOfUseUrl.description=URL, die auf die Nutzungsbedingungen zeigt
type.StoreContentDraft.productWebsiteUrl.name=App- oder Firmenwebsite
type.StoreContentDraft.productWebsiteUrl.description=Website der App oder der Firma
type.StoreContentDraft.useCases.name=Anwendbare AnwendungsfÃ¤lle
type.StoreContentDraft.useCases.description=Autor angegeben Verwendet FÃ¤lle fÃ¼r die App
type.StoreContentDraft.eula.name=EULA
type.StoreContentDraft.eula.description=End User License Agreement

type.CountriesAndPricesDraft.name=LÃ¤nder und Preise-Entwurf
type.CountriesAndPricesDraft.description=Entwurf, in welchen LÃ¤ndern die App zu welchen Preisen verfÃ¼gbar ist
type.CountriesAndPricesDraft.code.name=Code
type.CountriesAndPricesDraft.code.description=Der eindeutige Code des LÃ¤nder und Preise-Entwurfs
type.CountriesAndPricesDraft.enabledInStore.name=VerfÃ¼gbar im Webshop
type.CountriesAndPricesDraft.enabledInStore.description=App soll im Webshop verfÃ¼gbar sein
type.CountriesAndPricesDraft.appLicenses.name=VerfÃ¼gbare Lizenzen
type.CountriesAndPricesDraft.appLicenses.description=Lizenzen, unter denen die App zu kaufen sein wird
type.CountriesAndPricesDraft.specifiedPrice.name=Spezifizierter Preis
type.CountriesAndPricesDraft.specifiedPrice.description=Der durch den Entwickler festgelegte Grundpreis in der WÃ¤hrung der Entwicklerfirma
type.CountriesAndPricesDraft.subscriptionPrice.name=Angegebener Abonnementpreis
type.CountriesAndPricesDraft.subscriptionPrice.description=Der durch den Entwickler festgelegte Abonnementpreis in der WÃ¤hrung der Entwicklerfirma

type.AppVersionDraft.name=App-Versions-Entwurf
type.AppVersionDraft.description=Entwurf einer App-Version
type.AppVersionDraft.code.name=Code
type.AppVersionDraft.code.description=Der eindeutige Code des App-Versions-Entwurfs
type.AppVersionDraft.apk.name=APK
type.AppVersionDraft.apk.description=Das APK der App-Version
type.AppVersionDraft.changelog.name=App Changelog
type.AppVersionDraft.changelog.description=Das Changelog der App
type.AppVersionDraft.packageName.name=Paketname der App
type.AppVersionDraft.packageName.description=Der eindeutige Name des Androidpakets der App
type.AppVersionDraft.versionName.name=Version
type.AppVersionDraft.versionName.description=Die Version der App
type.AppVersionDraft.versionCode.name=VersionsschlÃ¼ssel
type.AppVersionDraft.versionCode.description=Der VersionsschlÃ¼ssel der App
type.AppVersionDraft.permissions.name=Berechtigungen
type.AppVersionDraft.permissions.description=Berechtigungen, die die App-Version benÃ¶tigt
type.AppVersionDraft.approvalStatus.name=Genehmigungsstatus
type.AppVersionDraft.approvalStatus.description=Der Genehmigungsstatus des App-Entwurfs
type.AppVersionDraft.rejectionReasons.name=ZurÃ¼ckweisungsgrÃ¼nde
type.AppVersionDraft.rejectionReasons.description=GrÃ¼nde fÃ¼r die ZurÃ¼ckweisung des App-Version-Drafts
type.AppVersionDraft.exportRegulationAcknowledged.name=Exportrichtlinien zur Kenntnis genommen
type.AppVersionDraft.exportRegulationAcknowledged.description=Exportrichtlinien zur Kenntnis genommen vor dem APK Upload
type.AppVersionDraft.dualUse.name=Dual Use
type.AppVersionDraft.dualUse.description=Kennzeichnung der App-Version als "Dual Use"
type.AppVersionDraft.eccn.name=ECCN
type.AppVersionDraft.eccn.description=Export Control Classification Number fÃ¼r diese App-Version
type.AppVersionDraft.submittedBy.name=Eingereicht von
type.AppVersionDraft.submittedBy.description=Der User, der des App-Versions-Entwurf eingereicht hat


type.AppLicenseDraft.name=Name
type.AppLicenseDraft.description=Entwurf einer Instanz der App-Lizenz
type.AppLicenseDraft.licenseType.name=Lizenztyp
type.AppLicenseDraft.licenseType.description=UnterstÃ¼tzter Lizenztyp
type.AppLicenseDraft.enabled.name=Lizenz aktiviert
type.AppLicenseDraft.enabled.description=Aktivieren Sie die Lizenzsichtbarkeit
type.AppLicenseDraft.countriesAndPricesDraft.name=LÃ¤nder und Preise
type.AppLicenseDraft.countriesAndPricesDraft.description=LÃ¤nder und Lizenzpreise
type.AppLicenseDraft.volumeDiscounts.name=Volumenrabatte
type.AppLicenseDraft.volumeDiscounts.description=Volumenrabatte basierend auf der Mindestmenge

type.SupportedLicense.name=UnterstÃ¼tzte Lizenz
type.SupportedLicense.description=Systemweit unterstÃ¼tzte Lizenz
type.SupportedLicense.licenseType.name=Lizenztyp
type.SupportedLicense.licenseType.description=UnterstÃ¼tzter Lizenztyp
type.SupportedLicense.enabled.name=Lizenz aktiviert
type.SupportedLicense.enabled.description=Wenn deaktiviert, ist die Lizenz fÃ¼r keinen Benutzer sichtbar

type.ProductContainer.name=Produkt-Container
type.ProductContainer.description=Ein Produkt-Container, in dem ein App-Projekt verwaltet wird. Es enthÃ¤lt eine App und/oder einen App-Entwurf.
type.ProductContainer.code.name=Code
type.ProductContainer.code.description=Der eindeutige Code des Produkt-Containers
type.ProductContainer.app.name=App
type.ProductContainer.app.description=Die App, die im Produkt-Container verwaltet wird
type.ProductContainer.appDraft.name=App-Entwurf
type.ProductContainer.appDraft.description=Der App-Entwurf, der im Produkt-Container verwaltet wird
type.ProductContainer.appVersionDraft.name=App-Versions-Entwurf
type.ProductContainer.appVersionDraft.description=Entwurf einer App-Version
type.ProductContainer.title.name=Titel
type.ProductContainer.title.description=Titel des Produkt-Containers (Jetzt veraltet, siehe App-Name / StoreContentDraft-Name).
type.ProductContainer.company.name=Firma
type.ProductContainer.company.description=Firma, die den Produkt-Container verwaltet.
type.ProductContainer.createdBy.name=Erstellt von
type.ProductContainer.createdBy.description=Der Integrator, der den Produkt-Container erstellt hat
type.ProductContainer.draft.name=Entwurf
type.ProductContainer.draft.description=Zeigt an, dass der Produkt-Container sich im Entwurfsstadium befindet
type.ProductContainer.approved.name=Genehmigt
type.ProductContainer.approved.description=Zeigt an, dass der Produkt-Container eine genehmigte App enthÃ¤lt

type.Country.currency.name=WÃ¤hrung
type.Country.currency.description=WÃ¤hrung eines Landes
type.Country.apps.name=Apps
type.Country.apps.description=In diesem Land zum Verkauf angebotene Apps
type.Country.drafts.name=EntwÃ¼rfe
type.Country.drafts.description=Zum Verkauf in diesem Land angelegte EntwÃ¼rfe von Apps
type.Country.enabledForDevelopers.name=Aktiv fÃ¼r Entwickler
type.Country.enabledForDevelopers.description=Das Land wurde fÃ¼r App-Entwickler aktiviert
type.Country.enabledForIntegrators.name=Aktiv fÃ¼r Integratoren
type.Country.enabledForIntegrators.description=Das Land wurde fÃ¼r Integratoren aktiviert
type.Country.supportedPaymentProviders.name=UnterstÃ¼tzte Zahlungsdienstleister
type.Country.supportedPaymentProviders.description=Die liste der unterstÃ¼tzten Zahlungsdienstleister im Land
type.Country.supportedPaymentMethods.name=UnterstÃ¼tzte Zahlungsmethoden
type.Country.supportedPaymentMethods.description=Die liste der unterstÃ¼tzten Zahlungsmethoden im Land
type.Country.inEu.name=Innerhalb der eu
type.Country.inEu.description=Das land ist in der eu
type.Country.blockedCountriesCommercial.name=ISO-Codes der blockierte LÃ¤nder
type.Country.blockedCountriesCommercial.description=Unternehmen aus diesen LÃ¤ndern kÃ¶nnen keine kommerziellen Lizenzen erwerben, die von Unternehmen mit Sitz in diesem Land verkauft werden. Synchronisiert von UMP - Countries service.
type.Country.aaDistributorCompanies.name=AA-Distributoren
type.Country.aaDistributorCompanies.description=In diesem Land aktive AA-Distributoren

type.Media.used.name=Benutzt
type.Media.used.description=Aktiv an App oder ProductContainer angehÃ¤ngt

type.ApkMedia.name=APK
type.ApkMedia.description=Media Container, der alle APK-spezifischen Felder kapselt
type.ApkMedia.signatures.name=APK-Signaturen
type.ApkMedia.signatures.description=EnthÃ¤lt die List aller valider Signaturen fÃ¼r das APK
type.ApkMedia.versionName.name=Versionsname
type.ApkMedia.versionName.description=Der Versionsname des APKs (beschreibt die Version fÃ¼r den Benutzer)
type.ApkMedia.versionCode.name=VersionsschlÃ¼ssel
type.ApkMedia.versionCode.description=Der VersionsschlÃ¼ssel der App (identifiziert die Version eindeutig)
type.ApkMedia.permissions.name=Berechtigungen
type.ApkMedia.permissions.description=Berechtigungen, die das APK benÃ¶tigt
type.ApkMedia.packageName.name=Paketname
type.ApkMedia.packageName.description=Der eindeutige Name des Androidpakets
type.ApkMedia.sdkAddonVersion.name=SDK-Addon-Version
type.ApkMedia.sdkAddonVersion.description=Version des SAST SDK-Addon, die von diesem APK unterstÃ¼tzt wird
type.ApkMedia.minAndroidApiVersion.name=Niedrigste Android-API-Version
type.ApkMedia.minAndroidApiVersion.description=Niedrigste von dem SAST SDK-Addon unterstÃ¼tzte Version der Android-API
type.ApkMedia.deviceCapabilities.name=GerÃ¤te-FÃ¤higkeiten
type.ApkMedia.deviceCapabilities.description=BenÃ¶tigte gerÃ¤tespezifische FÃ¤higkeiten (z.B. Pan-Tilt-Zoom-FÃ¤higkeit)

type.PdfMedia.name=PDF
type.PdfMedia.description=Ein PDF-Dokument

type.CatalogUnawarePdfMedia.name=PDF
type.CatalogUnawarePdfMedia.description=Ein PDF-Dokument

type.PdfMedia.app.name=App
type.PdfMedia.app.description=App, zu der diese Datei gehÃ¶rt

type.PdfMedia.storeContentDraft.name=Webshop-Inhaltsentwurf
type.PdfMedia.storeContentDraft.description=Webshop-Inhaltsentwurf, zu dem diese Datei gehÃ¶rt

type.UnusedMediaRemovalCronJob.name=UnusedMediaRemovalCronJob
type.UnusedMediaRemovalCronJob.description=UnusedMediaRemovalCronJob
type.UnusedMediaRemovalCronJob.age.name=Alter
type.UnusedMediaRemovalCronJob.age.description=Alter eines unbenutzten Medienobjekts, bevor es gelÃ¶scht werden darf.

type.FeatureToggle.name=Feature-Toggle
type.FeatureToggle.description=Bestimmt den aktuellen Status eines spezifischen Features
type.FeatureToggle.code.name=Code
type.FeatureToggle.code.description=Der eindeutige Code des Feature-Toggles
type.FeatureToggle.enabled.name=Aktiv
type.FeatureToggle.enabled.description=Flag, um das Feature zu aktivieren oder deaktivieren

type.PurchasedAppsAssignmentCronJob.name=Cron Job Gekaufte Apps pro Firma.
type.PurchasedAppsAssignmentCronJob.description=Cron Job zur Ermittlung gekaufter Apps
type.PurchasedAppsAssignmentCronJob.days.name=Tage
type.PurchasedAppsAssignmentCronJob.days.description=Anzahl an Tagen in die Vergangenheit, die betrachtet werden sollen.

type.CustomerReview.showCompany.name=Anzeige der Firma
type.CustomerReview.showCompany.description=Flag, um herauszufinden, ob der Name der Firma in der Rezension genannt werden soll.
type.CustomerReview.showName.name=Anzeige des Namens
type.CustomerReview.showName.description=Flag, um herauszufinden, ob der Name in der Rezension genannt werden soll.

type.PriceLimit.name=PreisbeschrÃ¤nkung
type.PriceLimit.description=Definiert EinschrÃ¤nkungen fÃ¼r Preise
type.PriceLimit.code.name=Code
type.PriceLimit.code.description=Der ISO-Code der WÃ¤hrung oder der Code des Ausweicheintrages
type.PriceLimit.upperLimit.name=Preisobergrenze
type.PriceLimit.upperLimit.description=Der hÃ¶chste erlaubte Preis fÃ¼r eine App
type.PriceLimit.currency.name=WÃ¤hrung
type.PriceLimit.currency.description=Die WÃ¤hrung dieser PreisbeschrÃ¤nkung

type.Product.idempotencyKey.name=IdempotenzschlÃ¼ssel
type.Product.idempotencyKey.description=SchlÃ¼ssel, der zur Sicherstellung der Idempotenz an das Billing-Backend geschickt wird
type.Product.pendingProductInfo.name=Pending Product Info
type.Product.pendingProductInfo.description=Pending product info
type.Product.countryRestricted.name=LÃ¤ndereinschrÃ¤nkung
type.Product.countryRestricted.description=Flag, um anzugeben, ob das Produkt lÃ¤nderbeschrÃ¤nkt ist oder nicht.

type.StripeConnectAccount.name=Stripe-Connect-Konto
type.StripeConnectAccount.description=Konto bei Stripe-Connect, auf das Auszahlungen vorgenommen werden kÃ¶nnen.
type.StripeConnectAccount.email.name=E-Mail
type.StripeConnectAccount.email.description=E-Mail-Adresse, die bei RÃ¼ckfragen fÃ¼r die Einrichtung des Stripe-Connect-Kontos verwendet wird.
type.StripeConnectAccount.accountId.name=Konto-Identifikationsnummer
type.StripeConnectAccount.accountId.description=Identifikationsnummer des Stripe-Connect-Kontos
type.StripeConnectAccount.user.name=Benutzer
type.StripeConnectAccount.user.description=Benutzer, der die Einrichtung des Stripe-Connect-Kontos veranlasst hat.
type.StripeConnectAccount.accountManager.name=Konto-Manager
type.StripeConnectAccount.accountManager.description=Angestellter, der die Einrichtung des Stripe-Connect-Kontos von SAST-Seite unterstÃ¼tzt.
type.StripeConnectAccount.status.name=Status
type.StripeConnectAccount.status.description=Status der Einrichtung des Stripe-Connect-Kontos
type.StripeConnectAccount.submissionDate.name=Abgabetermin
type.StripeConnectAccount.submissionDate.description=Termin, zu dem das Stripe-Connect-Konto eingreicht wurde.
type.StripeConnectAccount.company.name=Firma
type.StripeConnectAccount.company.description=Die Firma, zu der das Stripe-Connect Konto gehÃ¶rt.
type.StripeConnectAccount.provideInformationInvoked.name=Informationen bereitgestellt
type.StripeConnectAccount.provideInformationInvoked.description=Gibt an ob der Registrierungs-Workflow schon einmal gestartet wurde
type.StripeConnectAccount.exported.name=Exported
type.StripeConnectAccount.exported.description=Wenn account nach pj-eco exportiert wurde

type.SdkAddon.name=SDK Addon KompatibilitÃ¤t
type.SdkAddon.description=KompatibilitÃ¤t des SAST SDK-Addons nach Version
type.SdkAddon.version.name=SDK-Addon Version
type.SdkAddon.version.description=Version des SAST SDK-Addons
type.SdkAddon.maxSupportedMinAndroidApiVersion.name=Maximal erlaubte minimale Android-API-Version
type.SdkAddon.maxSupportedMinAndroidApiVersion.description=Maximale Version der Android-API-Versin, die im Android-Manifest als minSdkVersion gleichzeitig mit dieser sdkAddonVersion gesetzt werden darf.
type.SdkAddon.minOsVersion.name=Erforderliche Mindestversion des Betriebssystems
type.SdkAddon.minOsVersion.description=Erforderliche Mindestversion des Betriebssystems
type.SdkAddon.maxOsVersion.name=Maximal erforderliche Betriebssystemversion
type.SdkAddon.maxOsVersion.description=Maximal erforderliche Betriebssystemversion
type.SdkAddon.unsupportedLegacyVersion.name=Nicht-unterstÃ¼tzte Legacy-Version
type.SdkAddon.unsupportedLegacyVersion.description=Veraltete Version, die nicht mehr unterstÃ¼tzt wird

type.ReservedPackageName.name=Reservierte Paketnamen
type.ReservedPackageName.description=Von einer Firma reservierte Paketnamen
type.ReservedPackageName.company.name=Firma
type.ReservedPackageName.company.description=Die Firma welche Apps mit diesem Paketnamen hochladen darf
type.ReservedPackageName.packageNamePrefix.name=Paketnamen-PrÃ¤fix
type.ReservedPackageName.packageNamePrefix.description=Der PrÃ¤fix fÃ¼r Paketnamen welcher beschrÃ¤nkt ist

type.AccountDeletion.name=KontolÃ¶schung
type.AccountDeletion.description=Zustand der KontolÃ¶schung
type.AccountDeletion.uid.name=Uid
type.AccountDeletion.uid.description=Uid des zu lÃ¶schenden Kontos
type.AccountDeletion.processed.name=Verarbeitet
type.AccountDeletion.processed.description=Markiert, ob eine KontolÃ¶schung verarbeitet wurde

type.NavigationItem.name=Navigation Items
type.NavigationItem.description=Konfiguration der Navigationslinks
type.NavigationItem.uid.name=Uid
type.NavigationItem.uid.description=Uid von Navigationselementen
type.NavigationItem.type.name=der Typ
type.NavigationItem.type.description=Die Art des Navigationselements: STORE, DEVCON oder GLOBAL
type.NavigationItem.group.name=Gruppe
type.NavigationItem.group.description=Die Gruppe des Items: HEADER, FOOTER ...
type.NavigationItem.text.name=der Text
type.NavigationItem.text.description=Der Text, den Sie im Link sehen
type.NavigationItem.url.name=URL
type.NavigationItem.url.description=Die Link-URL des Navigationselements
type.NavigationItem.target.name=Target
type.NavigationItem.target.description=Zielattribut des Link
type.NavigationItem.icon.name=Icon
type.NavigationItem.icon.description=Symbol, mit dem dieser Link angezeigt werden kann
type.NavigationItem.index.name=Index
type.NavigationItem.index.description=Index, der zum Sortieren von Elementen verwendet wird
type.NavigationItem.enabled.name=Aktiv
type.NavigationItem.enabled.description=Flag, um das Feature zu aktivieren oder deaktivieren
type.NavigationItem.description.name=NavigationItem Beschreibung
type.NavigationItem.description.description=Beschreibung der Beschreibung des NavigationItem
type.NavigationItem.itemCode.name=ItemCode
type.NavigationItem.itemCode.description=Der eindeutige Code des NavigationItem
type.NavigationItem.store.name=Store
type.NavigationItem.store.description=Der Base Store
type.NavigationItem.interpolatedUrl.name=URL-Interpolation
type.NavigationItem.interpolatedUrl.description=Definiert, ob das URL-Attribut mittels Variableninterpolation verarbeitet werden soll
type.NavigationItem.customAttributes.name=Spezifische Zusatzattribute
type.NavigationItem.customAttributes.description=Zusï¿½tzliche HTML-Attribute, welche diesem Navigation Item hinzugefï¿½gt werden sollen

type.NavigationItemAttribute.name=Navigation Item Attribute
type.NavigationItemAttribute.description=Zusï¿½tzliches attribut fï¿½r ein Navigation Item
type.NavigationItemAttribute.name.name=Name
type.NavigationItemAttribute.name.description=Name des HTML-Attributes
type.NavigationItemAttribute.value.name=Wert
type.NavigationItemAttribute.value.description=Wert des HTML-Attributes (optional). Leere bzw. null-Werte mï¿½ssen durch das UI als boolean-Attribute mit Wert true behandelt werden.
type.NavigationItemAttribute.navigationItem.name=Navigation Item
type.NavigationItemAttribute.navigationItem.description=Das zugehï¿½rige Navigation Item

type.Invoice.name=Die Rechnung
type.Invoice.description=Die Rechnung
type.Invoice.code.name=Code
type.Invoice.code.description=Interner Code der Rechnung
type.Invoice.externalId.name=Externe ID
type.Invoice.externalId.description=Die ID der Rechnung, die vom externen Abrechnungssystem angegeben wird
type.Invoice.displayName.name=Anzeigename
type.Invoice.displayName.description=Der Name der Rechnung, die dem Benutzer angezeigt wird
type.Invoice.document.name=Dokument
type.Invoice.document.description=Der eigentliche Rechnungsbeleg (normalerweise ein PDF)
type.Invoice.order.name=Bestellung
type.Invoice.order.description=Die Bestellung, fÃ¼r die die Rechnung ausgestellt wird
type.Invoice.netAmount.name=Nettobetrag
type.Invoice.netAmount.description=Nettobetrag der Rechnung
type.Invoice.taxAmount.name=Steuern
type.Invoice.taxAmount.description=Steuern wie auf der Rechnung ausgewiesen
type.Invoice.grossAmount.name=Bruttobetrag
type.Invoice.grossAmount.description=Bruttobetrag der Rechnung
type.Invoice.status.name=Status
type.Invoice.status.description=Status der Rechnung
type.Invoice.invoiceDate.name=Rechnungsdatum
type.Invoice.invoiceDate.description=Rechnungsdatum
type.Invoice.creditNotes.name=Gutschriften
type.Invoice.creditNotes.description=Gutschriften
type.Invoice.invoiceItems.name=Rechungspositionen
type.Invoice.invoiceItems.description=Positionen der Rechnung


type.InvoiceItem.name=Rechnungsposition
type.InvoiceItem.description=Rechnungsposition
type.InvoiceItem.originalInvoice.name=UrsprÃ¼ngliche Rechnung
type.InvoiceItem.originalInvoice.description=UrsprÃ¼ngliche Rechnung
type.InvoiceItem.positionId.name=Rechnungsposition ID
type.InvoiceItem.positionId.description=ID der Rechnungsposition
type.InvoiceItem.productId.name=Produkt Id
type.InvoiceItem.productId.description=ProduktsID der Rechnungsposition
type.InvoiceItem.quantity.name=Die Menge
type.InvoiceItem.quantity.description=Produktsmenge der Rechnungsposition
type.InvoiceItem.netPrice.name=Nettopreis
type.InvoiceItem.netPrice.description=Nettopreis der Rechnungsposition
type.InvoiceItem.currency.name=WÃ¤hrung
type.InvoiceItem.currency.description=die WÃ¤hrung fÃ¼r den Produktpreis der Rechnungsposition


type.SelfBillingInvoice.name=Abrechnungsbeleg
type.SelfBillingInvoice.description=Die Rechnung, die den Verkauf von Apps fÃ¼r das Entwicklerunternehmen darstellt
type.SelfBillingInvoice.code.name=Code
type.SelfBillingInvoice.code.description=Interner Code der Abrechnungsbeleg
type.SelfBillingInvoice.externalId.name=Externe ID
type.SelfBillingInvoice.externalId.description=Die ID der Abrechnungsbeleg, die vom externen Abrechnungssystem angegeben wird
type.SelfBillingInvoice.displayName.name=Anzeigename
type.SelfBillingInvoice.displayName.description=Der Name der Abrechnungsbeleg, die dem Benutzer angezeigt wird
type.SelfBillingInvoice.document.name=Dokument
type.SelfBillingInvoice.document.description=Der eigentliche Rechnungsbeleg (normalerweise ein PDF)
type.SelfBillingInvoice.order.name=Bestellung
type.SelfBillingInvoice.order.description=Die Bestellung, fÃ¼r die die Abrechnungsbeleg ausgestellt wird
type.SelfBillingInvoice.marketplaceShare.name=Nettoanteil des Marktplatzes
type.SelfBillingInvoice.marketplaceShare.description=Nettoanteil des Marktplatzes am Gesamtnettobetrag
type.SelfBillingInvoice.sellerShare.name=Nettoanteil des VerkÃ¤ufers
type.SelfBillingInvoice.sellerShare.description=Nettoanteil des VerkÃ¤ufers am Gesamtnettobetrag (dieser Wert ist Ã¼blicherweise negativ, da es sich um eine Gutschrift an den VerkÃ¤ufer handelt)
type.SelfBillingInvoice.marketplaceTaxAmount.name=Steueranteil des Marktplatzes
type.SelfBillingInvoice.marketplaceTaxAmount.description=Steueranteil des Marktplatzes an der VerkÃ¤uferprovision (dieser Wert ist Ã¼blicherweise negativ, da es sich um eine Gutschrift an den VerkÃ¤ufer handelt)
type.SelfBillingInvoice.creditNotes.name=Gutschriften
type.SelfBillingInvoice.creditNotes.description=FÃ¼r die Gutschrift ausgestellte Gutschriften
type.SelfBillingInvoice.status.name=Status
type.SelfBillingInvoice.status.description=Status der Rechnung
type.SelfBillingInvoice.invoiceDate.name=Rechnungsdatum
type.SelfBillingInvoice.invoiceDate.description=Rechnungsdatum
type.UserSpecificOfflineToken.name=Benutzerspezifisches Offline-Token
type.UserSpecificOfflineToken.description=Speichert Offline-Token des Benutzers
type.UserSpecificOfflineToken.userId.name=UserId
type.UserSpecificOfflineToken.userId.description=UMP UserId
type.UserSpecificOfflineToken.offlineToken.name=Offline Token
type.UserSpecificOfflineToken.offlineToken.description=Offline Token
type.UserGroup.appLicenses.name=Die Benutzergruppen App-Lizenzen
type.UserGroup.appLicenses.description=App-Lizenzen, die fÃ¼r Benutzergruppen verfÃ¼gbar sind
type.DeveloperPayout.name=Entwickler-Auszahlung
type.DeveloperPayout.description=Erfolgte Auszahlung an eine Entwicklerfirma
type.DeveloperPayout.amount.name=Betrag
type.DeveloperPayout.amount.description=Ausgezahlter Betrag (in der kleinsten WÃ¤hrungseinheit)
type.DeveloperPayout.currency.name=WÃ¤hrung
type.DeveloperPayout.currency.description=WÃ¤hrung dieser Auszahlung
type.DeveloperPayout.payoutDate.name=Auszahlungdatum
type.DeveloperPayout.payoutDate.description=Datum, zu welchem die Auszahlung erfolgte
type.DeveloperPayout.company.name=Firma
type.DeveloperPayout.company.description=Firma, an welche die Auszahlung erfolgte
type.DeveloperPayout.orders.name=Bestellunge0n
type.DeveloperPayout.orders.description=In dieser Auszahlung inkludierte Bestellungen
type.DeveloperPayout.payoutId.name=Entwickler-Auszahlung Id
type.DeveloperPayout.payoutId.description=Id des Auszahlungsobjekts auf Stripe Seite
type.DeveloperPayout.status.name=Status
type.DeveloperPayout.status.description=Status der Auszahlung auf Stripe Seite
type.DeveloperPayout.paymentProvider.name=Zahlungsdienstleister
type.DeveloperPayout.paymentProvider.description=Zahlungsdienstleister

type.UseCase.name=Anwendungsfall
type.UseCase.description=Verwendet FÃ¤lle der Apps im Application Store
type.UseCase.name.name=Name
type.UseCase.name.description=Name des Anwendungsfalls
type.UseCase.index.name=Index
type.UseCase.index.description=Index of this Use-case among the others
type.UseCase.enabled.name=Aktiv
type.UseCase.enabled.description=Flag, um das UseCase zu aktivieren oder deaktivieren
type.UseCase.storeContentDrafts.name=StoreContentDrafts
type.UseCase.storeContentDrafts.description=StoreContentDrafts assigned to this use-case
type.UseCase.apps.name=App
type.UseCase.apps.description=Apps assigned to this use-case

type.StoreContentDraft2UseCase.name=StoreContentDraft2UseCase
type.StoreContentDraft2UseCase.description=StoreContentDraft2UseCase-Beziehung
type.StoreContentDraft2UseCase.storeContentDrafts.name=storeContentDrafts
type.StoreContentDraft2UseCase.storeContentDrafts.description=storeContentDrafts-Zuordnung der AnwendungsfÃ¤lle
type.StoreContentDraft2UseCase.useCases.name=UseCases
type.StoreContentDraft2UseCase.useCases.description=useCases-Zuordnung der StoreContentDrafts

type.App2UseCase.name=App2UseCase
type.App2UseCase.description=Beziehung zwischen App und UseCase
type.App2UseCase.apps.name=apps
type.App2UseCase.apps.description=Apps, die dem Anwendungsfall zugeordnet sind
type.App2UseCase.useCases.name=useCases
type.App2UseCase.useCases.description=useCases, die der App zugeordnet sind

type.AppVideo.name=App-Video
type.AppVideo.description=Video einer App
type.AppVideo.code.name=Code
type.AppVideo.code.description=Code des App-Videos
type.AppVideo.type.name=Video-Dienstanbieter
type.AppVideo.type.description=Dienstanbieter dieses Videos
type.AppVideo.status.name=Status
type.AppVideo.status.description=Dienstanbieter-VerfÃ¼gbarkeit dieses Videos
type.AppVideo.lastStatusCheck.name=VerfÃ¼gbarkeitsprÃ¼fung
type.AppVideo.lastStatusCheck.description=Zeitpunkt der letzten VerfÃ¼gbarkeitsprÃ¼fung
type.AppVideo.source.name=Video-URL
type.AppVideo.source.description=UrsprÃ¼ngliche Video-URL
type.AppVideo.externalId.name=Video-ID
type.AppVideo.externalId.description=Dienstanbieter-ID dieses Videos

type.PriceDraft.name=Price Draft
type.PriceDraft.description=Price Draft der App-Lizenz
type.PriceDraft.currency.name=WÃ¤hrung
type.PriceDraft.currency.description=WÃ¤hrung des Price Draft
type.PriceDraft.amount.name=Menge
type.PriceDraft.amount.description=Menge des Price Draft
type.PriceDraft.minQuantity.name=Mindestanzahl
type.PriceDraft.minQuantity.description=Mindestanzahl, fÃ¼r welche der resultierende Preis gÃ¼ltig ist
type.PriceDraft.scaledPriceDiscount.name=Rabatt
type.PriceDraft.scaledPriceDiscount.description=Rabatt, welcher mit diesem Preis gegenÃ¼ber den Basispreis eingerÃ¤umt wird
type.PriceDraft.validFrom.name=GÃ¼ltig ab
type.PriceDraft.validFrom.description=PreisgÃ¼ltigkeit fÃ¼r die App
type.PriceDraft.userPriceGroup.name=Benutzerpreisgruppe
type.PriceDraft.userPriceGroup.description=Preise fÃ¼r bestimmte Kunden
type.PriceDraft.billingPriceCode.name=Rechnungspreiscode
type.PriceDraft.billingPriceCode.description=Preiscode fÃ¼r Price Draft

type.PriceRow.scaledPriceDiscount.name=Rabatt
type.PriceRow.scaledPriceDiscount.description=Rabatt, welcher mit diesem Preis gegenÃ¼ber den Basispreis eingerÃ¤umt wird
type.PriceRow.billingRowId.name=Abrechnungszeilen-ID
type.PriceRow.billingRowId.description=Abrechnungs-ID fÃ¼r Preis
type.PriceRow.billingPriceCode.name=Rechnungspreiscode
type.PriceRow.billingPriceCode.description=Preiscode fÃ¼r Preis

type.AppLicence2PriceDrafts.name=AppLicence2PriceDrafts
type.AppLicence2PriceDrafts.description=Beziehung zwischen AppLicence und PriceDrafts
type.AppLicence2PriceDrafts.appLicense.name=App License
type.AppLicence2PriceDrafts.appLicense.description=App License fÃ¼r die Price Drafts
type.AppLicence2PriceDrafts.priceDrafts.name=Price Drafts
type.AppLicence2PriceDrafts.priceDrafts.description=Price Drafts fÃ¼r die App License

type.PendingProductInfo.name=Pending Product Info
type.PendingProductInfo.description=PendingProductInfo enthÃ¤lt Informationen zu ausstehenden Produktattributen.
type.PendingProductInfo.name.name=Namensattribut
type.PendingProductInfo.name.description=Ausstehendes Namensattribut des Produkts
type.PendingProductInfo.prices.name=Preisattribute
type.PendingProductInfo.prices.description=Ausstehende Preisattribute des Produkts
type.PendingProductInfo.groupPriceDraft.name=Gruppenpreisentwurf
type.PendingProductInfo.groupPriceDraft.description=GruppenpreisentwÃ¼rfe sind die Sammlung der ausstehenden Gruppenpreise
type.PendingProductInfo.useLatestChargePlan.name=Neuesten Verrechnungsplan benutzen
type.PendingProductInfo.useLatestChargePlan.description=Weist BRIM an, den neuesten Verrechnungsplan dieses Produktes zu nutzen

type.VolumeDiscount.name=Volumenrabatte
type.VolumeDiscount.description=Volumenrabatte basierend auf der Mindestmenge fÃ¼r das Produkt
type.VolumeDiscount.appLicense.name=App-Lizenz
type.VolumeDiscount.appLicense.description=Der Volumenrabatte gilt fÃ¼r diese App-Lizenz
type.VolumeDiscount.minQuantity.name=Mindestmenge
type.VolumeDiscount.minQuantity.description=FÃ¼r diese Mindestmenge gilt der Volumenrabatte
type.VolumeDiscount.discount.name=Rabatt
type.VolumeDiscount.discount.description=Der Rabattprozentsatz auf den Grundpreis
type.VolumeDiscount.active.name=Aktiv
type.VolumeDiscount.active.description=Ist der Rabatt aktuell aktiv

type.VolumeDiscountDraft.name=VolumenrabatteDraft
type.VolumeDiscountDraft.description=Volumenrabatte basierend auf der Mindestmenge fÃ¼r das Drafts Produkt
type.VolumeDiscountDraft.appLicenseDraft.name=App-Lizenz
type.VolumeDiscountDraft.appLicenseDraft.description=Der Volumenrabatte gilt fÃ¼r diese App-Lizenz-Drafts
type.VolumeDiscountDraft.minQuantity.name=Mindestmenge
type.VolumeDiscountDraft.minQuantity.description=FÃ¼r diese Mindestmenge gilt der Volumenrabatte
type.VolumeDiscountDraft.discount.name=Rabatt
type.VolumeDiscountDraft.discount.description=Der Rabattprozentsatz auf den Grundpreis

type.Subscription.name=Abonnement
type.Subscription.description=Abonnement
type.Subscription.uuid.name=UUID
type.Subscription.uuid.description=UUID
type.Subscription.productCode.name=Produktcode
type.Subscription.productCode.description=Produktcode
type.Subscription.orderNumber.name=Bestellnummer
type.Subscription.orderNumber.description=Bestellnummer
type.Subscription.entryNumber.name=Eintragsnummer
type.Subscription.entryNumber.description=Eindeutige Eintragsnummer basierend auf der Gesamtmenge des tatsÃ¤chlichen Auftragseingangs
type.Subscription.startDate.name=Startdatum
type.Subscription.startDate.description=Startdatum
type.Subscription.endDate.name=Enddatum
type.Subscription.endDate.description=Enddatum
type.Subscription.cancelledDate.name=Abgebrochenes Datum
type.Subscription.cancelledDate.description=Storniertes Datum
type.Subscription.orderEntry.name=Auftragserfassung
type.Subscription.orderEntry.description=Auftragserfassung
type.Subscription.contractTerminationRule.name=Kï¿½ndigungsregeln
type.Subscription.contractTerminationRule.description=Regeln zur Kï¿½ndigung dieses Abonnements
type.Subscription.timezone.name=Zeitzone
type.Subscription.timezone.description=Zeitzone fï¿½r das Start- und Enddatum des Abonnements
type.Subscription.billingPriceCode.name=Billing Price Code
type.Subscription.billingPriceCode.description=FÃ¼r die Abrechnung dieses Abonnements verwendeter Preiscode

type.MinimumCommission.name=Mindestprovision
type.MinimumCommission.description=Mindestprovision
type.MinimumCommission.fee.name=GebÃ¼hr
type.MinimumCommission.fee.description=GebÃ¼hr
type.MinimumCommission.licenseType.name=Lizenz-Typ
type.MinimumCommission.licenseType.description=Lizenz-Typ
type.MinimumCommission.currency.name=WÃ¤hrung
type.MinimumCommission.currency.description=WÃ¤hrung
type.MinimumCommission.sastCommissionPercentage.name=Prozentsatz der Azena-Provision
type.MinimumCommission.sastCommissionPercentage.description=Prozentsatz der Azena-Provision

type.Eula.name=EULA
type.Eula.description=End User License Agreement
type.Eula.type.name=EULA-Typ
type.Eula.type.description=Standard oder Custom
type.Eula.customUrl.name=Custom-EULA-URL
type.Eula.customUrl.description=URL eines Custom-EULA
type.Eula.standardEulaAppendix.name=Standard-EULA-Anhang
type.Eula.standardEulaAppendix.description=Anhang eines Standard-EULA

type.CountryEula.name=Landesspezifisches EULA
type.CountryEula.description=Landesspezifisches End User License Agreement
type.CountryEula.eulaContainer.name=Container zu welchem das EULA gehï¿½rt
type.CountryEula.eulaContainer.description=Container zu welchem das EULA gehï¿½rt
type.CountryEula.country.name=Land
type.CountryEula.country.description=Land in welchem das EULA Anwendung findet
type.CountryEula.eula.name=EULA
type.CountryEula.eula.description=EULA welches in dem Land Anwendung findet

type.EulaContainer.name=EULA-Container
type.EulaContainer.description=End User License Agreement Container
type.EulaContainer.code.name=Bezeichner
type.EulaContainer.code.description=Eindeutiger Geschï¿½ftsschlï¿½ssel
type.EulaContainer.label.name=Anzeigename
type.EulaContainer.label.description=ï¿½bersetzter Anzeigename
type.EulaContainer.countryEulas.name=Landesspezifische EULAs
type.EulaContainer.countryEulas.description=Landesspezifische End User License Agreements
type.EulaContainer.apps.name=Apps
type.EulaContainer.apps.description=Apps in welchen die EULAs Anwendung finden

type.ThlConfigration.name=THL Konfiguration
type.ThlConfigration.description=THL Konfiguration
type.ThlConfigration.masterProductCode.name=Master-Produktcode
type.ThlConfigration.masterProductCode.description=Master-Produktcode
type.ThlConfigration.thlProductCode.name=THL-Produktcode
type.ThlConfigration.thlProductCode.description=THL-Produktcode
type.ThlConfigration.customerGroup.name=Kundengruppe
type.ThlConfigration.customerGroup.description=Kundengruppe
type.ThlConfigration.country.name=Land iso code
type.ThlConfigration.country.description=Land iso code
type.ThlConfigration.discountPercentage.name=Prozentsatz des Rabatts
type.ThlConfigration.discountPercentage.description=Prozentsatz des Rabatts

type.AbstractOrderEntry.subscriptions.name=Abonnements
type.AbstractOrderEntry.subscriptions.description=Abonnements
type.AbstractOrderEntry.buyerContracts.name=KaufvertrÃ¤ge
type.AbstractOrderEntry.buyerContracts.description=Mit dieser Position verknÃ¼pfte KaufvertrÃ¤ge
type.AbstractOrderEntry.billingPriceCode.name=Billing Price Code
type.AbstractOrderEntry.billingPriceCode.description=Der Berechnung dieser Position zugrunde liegender Preiscode

type.PriceRecalculationCronjob.name=Preisberechnung CronJob
type.PriceRecalculationCronjob.description=Cron-Job zur DurchfÃ¼hrung von Preisberechnungen bei Ãnderung der Wechselkurse
type.PriceRecalculationCronjob.ignoreLicenses.name=Lizenzen ignorieren
type.PriceRecalculationCronjob.ignoreLicenses.description=Der Preis wird fÃ¼r Lizenzen des konfigurierten Lizenztyps nicht neu berechnet

type.PspSellerAccount.name=PSP Seller Account
type.PspSellerAccount.description=Ein VerkÃ¤uferkonto bei der PSP
type.PspSellerAccount.accountId.name=AccountId
type.PspSellerAccount.accountId.description=AccountId des VerkÃ¤uferkontos bei der PSP
type.PspSellerAccount.user.name=Benutzer
type.PspSellerAccount.user.description=Der Benutzer des VerkÃ¤uferunternehmens, der den Onboarding-Prozess eingeleitet hat.
type.PspSellerAccount.status.name=Status
type.PspSellerAccount.status.description=Ein Status des VerkÃ¤uferkontos bei der PSP
type.PspSellerAccount.billingSystemStatus.name=Status des Abrechnungssystems
type.PspSellerAccount.billingSystemStatus.description=Synchronisationsstatus des Abrechnungssystems
type.PspSellerAccount.paymentProvider.name=Zahlungsdienstleister
type.PspSellerAccount.paymentProvider.description=Ein VerkÃ¤uferkonto, das fÃ¼r den Zahlungsdienstleister erstellt wurde.
type.PspSellerAccount.company.name=Developer Company
type.PspSellerAccount.company.description=Ein Developercompany des VerkÃ¤uferkontos bei der PSP

type.CountrySyncCronJob.name=CountrySyncCronJob
type.CountrySyncCronJob.description=Cron-Job, der LÃ¤nder vom LÃ¤nderdienst synchronisiert

type.CompanySyncCronJob.name=CompanySyncCronJob
type.CompanySyncCronJob.description=Cron-Job, der alle aktiven Unternehmen synchronisiert

type.ToolCompany.name=ToolCompany
type.ToolCompany.description=Die Firmen, die Tool-Lizenzen erstellen kÃ¶nnen
type.ToolCompany.company.name=IoT-Firma
type.ToolCompany.company.description=Das zugehÃ¶rige IoT Company


type.PrivateOfferRequest.name=Privat Angebotsanfrage
type.PrivateOfferRequest.description=Privat Angebotsanfrage
type.PrivateOfferRequest.developerCompany.name=Entwicklerunternehmens
type.PrivateOfferRequest.developerCompany.description=Entwicklerunternehmens
type.PrivateOfferRequest.integratorCompany.name=Integratorunternehmens
type.PrivateOfferRequest.integratorCompany.description=Integratorunternehmens
type.PrivateOfferRequest.app.name=App
type.PrivateOfferRequest.app.description=App
type.PrivateOfferRequest.message.name=Nachricht
type.PrivateOfferRequest.message.description=Der Kunde schreibt die Nachricht an den VerkÃ¤ufer, um ein Angebot anzufordern
type.PrivateOfferRequest.messageText.name=Nachrichtentext
type.PrivateOfferRequest.messageText.description=Der Kunde schreibt die Nachricht an den VerkÃ¤ufer, um ein Angebot anzufordern
type.PrivateOfferRequest.privateOfferRequestItems.name=Artikel fÃ¼r privat Angebotsanfrage
type.PrivateOfferRequest.privateOfferRequestItems.description=Satz von jedem Lizenztyp-Preiselement
type.PrivateOfferRequest.projectRegistration.name=Registrierung des privaten projekts
type.PrivateOfferRequest.projectRegistration.description=Weitere Informationen zum Projekt


type.PrivateOfferRequestItem.name=Artikel fÃ¼r privat Angebotsanfrage
type.PrivateOfferRequestItem.description=Artikel fÃ¼r privat Angebotsanfrage
type.PrivateOfferRequestItem.originalPrice.name=Original Preis
type.PrivateOfferRequestItem.originalPrice.description=Original Preis
type.PrivateOfferRequestItem.requestPrice.name=Preis anfragen
type.PrivateOfferRequestItem.requestPrice.description=Preis anfragen
type.PrivateOfferRequestItem.quantity.name=Menge
type.PrivateOfferRequestItem.quantity.description=Lizenzmenge
type.PrivateOfferRequestItem.licenseType.name=Lizenztyp
type.PrivateOfferRequestItem.licenseType.description=Art der Lizenz
type.PrivateOfferRequestItem.privateOfferRequest.name=Private Angebotsanfrage
type.PrivateOfferRequestItem.privateOfferRequest.description=Private Angebotsanfrage

type.PrivateOfferProjectRegistration.name=Registrierung des privaten projekts
type.PrivateOfferProjectRegistration.description=Weitere Informationen zum Projekt
type.PrivateOfferProjectRegistration.projectName.name=Projektname
type.PrivateOfferProjectRegistration.projectName.description=Projektname
type.PrivateOfferProjectRegistration.customerName.name=Kundenname
type.PrivateOfferProjectRegistration.customerName.description=Kundenname
type.PrivateOfferProjectRegistration.plannedStartDate.name=Geplanter Starttermin
type.PrivateOfferProjectRegistration.plannedStartDate.description=Geplanter Starttermin
type.PrivateOfferProjectRegistration.siteAddress.name=Standort-Adresse
type.PrivateOfferProjectRegistration.siteAddress.description=Standort-Adresse

type.PrivateOfferProjectAddress.name=Privates Angebot Projektadresse
type.PrivateOfferProjectAddress.description=Weitere Informationen zum Standort des Projekts
type.PrivateOfferProjectAddress.country.name=Land
type.PrivateOfferProjectAddress.country.description=Land
type.PrivateOfferProjectAddress.city.name=Stadt
type.PrivateOfferProjectAddress.city.description=Stadt
type.PrivateOfferProjectAddress.postalCode.name=Postleitzahl
type.PrivateOfferProjectAddress.postalCode.description=Postleitzahl
type.PrivateOfferProjectAddress.line1.name=Linie 1
type.PrivateOfferProjectAddress.line1.description=Linie 1

type.LicenseActivation.name=Lizenzaktivierung
type.LicenseActivation.description=Aktivierungsstatus einer Lizenz fÃ¼r ein Unternehmen
type.LicenseActivation.company.name=Integratorunternehmen
type.LicenseActivation.company.description=Das Integratorunternehmen, das die Lizenz aktiviert
type.LicenseActivation.appLicense.name=App-Lizenz
type.LicenseActivation.appLicense.description=Die Lizenz, die aktiviert wird
type.LicenseActivation.activationStatus.name=LizenzaktivierungStatus
type.LicenseActivation.activationStatus.description=Der Status der Lizenzaktivierung

type.OldCartRemovalCronJob.name=Cronjob zur LÃ¶schung des alten Warenkorbs
type.OldCartRemovalCronJob.description=Ein Cronjob zum LÃ¶schen alter WarenkÃ¶rbe.
type.OldCartRemovalCronJob.sites.name=Standorte
type.OldCartRemovalCronJob.sites.description=Seiten, fÃ¼r die alte WarenkÃ¶rbe gelÃ¶scht werden
type.OldCartRemovalCronJob.anonymousCartRemovalAge.name=Dauer bis zur LÃ¶schung des anonymen Warenkorbs
type.OldCartRemovalCronJob.anonymousCartRemovalAge.description=Anonyme WarenkÃ¶rbe, die Ã¤lter als die angegebene Sekundenanzahl sind, werden gelÃ¶scht
type.OldCartRemovalCronJob.cartRemovalAge.name=Dauer bis zur LÃ¶schung des Warenkorbs
type.OldCartRemovalCronJob.cartRemovalAge.description=WarenkÃ¶rbe, die Ã¤lter als die angegebene Sekundenanzahl sind, werden gelÃ¶scht

type.FollowAppSubscription.name=FollowAppSubscription
type.FollowAppSubscription.description=Tabelle zum Speichern aller Follow-App-Abonnements.
type.FollowAppSubscription.integrator.name=Integrator
type.FollowAppSubscription.integrator.description=Integrator, der abonniert hat, um die Ãnderungsbenachrichtigungen zu erhalten.
type.FollowAppSubscription.appCode.name=AppCode
type.FollowAppSubscription.appCode.description=Code der App, deren Ãnderungen der Integrator folgen mÃ¶chte.
type.FollowAppSubscription.followAppSubscriptionStatus.name=FollowAppSubscriptionStatus
type.FollowAppSubscription.followAppSubscriptionStatus.description=Status des Abonnements. es kann abonniert oder abgemeldet sein.

type.Order.ownFullAppOrder.name=EigeneFullAppOrder
type.Order.ownFullAppOrder.description=auf true setzen, wenn der Integrator seine eigene Unternehmens-App kauft.

type.InvoiceCreditNote.name=Gutschriften
type.InvoiceCreditNote.description=Gutschriftsdokumente (Storno, RÃ¼ckzahlung).
type.InvoiceCreditNote.code.name=Code
type.InvoiceCreditNote.code.description=Interner Code
type.InvoiceCreditNote.externalId.name=Externe ID
type.InvoiceCreditNote.externalId.description=Die ID der Gutschrift, die vom externen Abrechnungssystem angegeben wird
type.InvoiceCreditNote.displayName.name=Anzeigename
type.InvoiceCreditNote.displayName.description=Der Name der Gutschrift, die dem Benutzer angezeigt wird
type.InvoiceCreditNote.document.name=Dokument
type.InvoiceCreditNote.document.description=Der eigentliche Beleg (normalerweise ein PDF)
type.InvoiceCreditNote.originalInvoice.name=Die Originalrechnung
type.InvoiceCreditNote.originalInvoice.description=Die Originalrechnung
type.InvoiceCreditNote.netAmount.name=Nettobetrag
type.InvoiceCreditNote.netAmount.description=Nettobetrag der Gutschrift
type.InvoiceCreditNote.taxAmount.name=Steuern
type.InvoiceCreditNote.taxAmount.description=Steuern wie auf der Gutschrift ausgewiesen
type.InvoiceCreditNote.grossAmount.name=Bruttobetrag
type.InvoiceCreditNote.grossAmount.description=Bruttobetrag der Gutschrift
type.InvoiceCreditNote.issuanceDate.name=Gutschriftsdatum
type.InvoiceCreditNote.issuanceDate.description=Gutschriftsdatum
type.InvoiceCreditNote.type.name=Gutschrift-Typ
type.InvoiceCreditNote.type.description=Gutschrift-Typ

type.SelfBillingInvoiceCreditNote.name=Rechnungsgutschrift fÃ¼r Selbstabrechnung
type.SelfBillingInvoiceCreditNote.description=Die Gutschrift, die den Verkauf von Apps fÃ¼r das Entwicklerunternehmen reprÃ¤sentiert
type.SelfBillingInvoiceCreditNote.code.name=Code
type.SelfBillingInvoiceCreditNote.code.description=Interner Code der Gutschrift
type.SelfBillingInvoiceCreditNote.externalId.name=Externe ID
type.SelfBillingInvoiceCreditNote.externalId.description=Die ID der Gutschrift, die vom externen Abrechnungssystem vergeben wird
type.SelfBillingInvoiceCreditNote.displayName.name=Anzeigename
type.SelfBillingInvoiceCreditNote.displayName.description=Der Name der Gutschrift, die dem Benutzer angezeigt wird
type.SelfBillingInvoiceCreditNote.document.name=Dokument
type.SelfBillingInvoiceCreditNote.document.description=Das eigentliche Gutschriftdokument (normalerweise ein PDF)
type.SelfBillingInvoiceCreditNote.originalSelfBillingInvoice.name=Rechnung mit Gutschrift
type.SelfBillingInvoiceCreditNote.originalSelfBillingInvoice.description=Die Gutschriftsanzeige, fÃ¼r die die Gutschrift ausgestellt wird
type.SelfBillingInvoiceCreditNote.marketplaceShare.name=Marktplatzanteil
type.SelfBillingInvoiceCreditNote.marketplaceShare.description=Marktanteil wie auf der Gutschrift angegeben (der Betrag, den der Azena-Marktplatz dem VerkÃ¤ufer in Rechnung stellt)
type.SelfBillingInvoiceCreditNote.sellerShare.name=VerkÃ¤uferanteil
type.SelfBillingInvoiceCreditNote.sellerShare.description=Anteil des VerkÃ¤ufers wie auf der Gutschrift angegeben (dieser Wert ist normalerweise negativ, da der Azena-Marktplatz diesen Betrag dem VerkÃ¤ufer gutschreibt)
type.SelfBillingInvoiceCreditNote.marketplaceTaxAmount.name=Marketplace-Steuerbetrag
type.SelfBillingInvoiceCreditNote.marketplaceTaxAmount.description=Steuerbetrag, der dem VerkÃ¤ufer vom Marktplatz berechnet wird (dieser Wert ist normalerweise negativ, da der Azena-Marktplatz diesen Betrag dem VerkÃ¤ufer gutschreibt)
type.SelfBillingInvoiceCreditNote.creditNotes.name=Gutschriften
type.SelfBillingInvoiceCreditNote.creditNotes.description=FÃ¼r die Gutschrift ausgestellte Gutschriften
type.SelfBillingInvoiceCreditNote.issuanceDate.name=Gutschriftsdatum
type.SelfBillingInvoiceCreditNote.issuanceDate.description=Gutschriftsdatum
type.SelfBillingInvoiceCreditNote.type.name=Gutschrift-Typ
type.SelfBillingInvoiceCreditNote.type.description=Gutschrift-Typ

type.CronjobConfiguration.name=Cronjob-Konfiguration
type.CronjobConfiguration.description=Cronjob-Konfiguration
type.CronjobConfiguration.code.name=code
type.CronjobConfiguration.code.description= Code
type.CronjobConfiguration.baseStore.name=Basisspeicher
type.CronjobConfiguration.baseStore.description=Cronjobs-Basisspeicher
type.CronjobConfiguration.productCatalogVersion.name=Version des Cronjobs-Produktkatalogs
type.CronjobConfiguration.productCatalogVersion.description=Version des Cronjobs-Produktkatalogs

type.CronJob.configuration.name=Konfiguration
type.CronJob.configuration.description=Konfiguration des Cronjobs

type.BaseStore.defaultTerminationRule.name=Standard-Kï¿½ndigungsregel
type.BaseStore.defaultTerminationRule.description=Kï¿½ndigungsregeln, welche standardmï¿½ï¿½ig fï¿½r diesen Store angewandt werden

type.ContractTerminationRule.name=Kï¿½ndigungsregel
type.ContractTerminationRule.description=Regeln zur Kï¿½ndigung und Vertragslaufzeit
type.ContractTerminationRule.code.name=Code
type.ContractTerminationRule.code.description=Eindeutiger Bezeichner
type.ContractTerminationRule.noticePeriod.name=Kï¿½ndigungsfrist
type.ContractTerminationRule.noticePeriod.description=Spï¿½tester Zeitpunkt vor Ende einer Vertragsperiode, zu welcher der Vertrag zum ende dieser Vertragsperiode gekï¿½ndigt werden kann
type.ContractTerminationRule.initialPeriod.name=Erstlaufzeit
type.ContractTerminationRule.initialPeriod.description=Dauer der ersten Vertragsperiode
type.ContractTerminationRule.followUpPeriod.name=Folgelaufzeit
type.ContractTerminationRule.followUpPeriod.description=Dauer aller auf die Erstlaufzeit folgenden Vertragsperioden
type.ContractTerminationRule.gracePeriod.name=Schonfrist
type.ContractTerminationRule.gracePeriod.description=Zeitpunkt nach Ende einer Vertragsperiode, zu welchem die verbundene technische Lizenz ablï¿½uft
type.ContractTerminationRule.fixedPeriod.name=Festlaufzeit
type.ContractTerminationRule.fixedPeriod.description=Dauer der festen Vertragslaufzeit

type.TerminationRulePeriod.name=Zeitraum fï¿½r Kï¿½ndigungsregeln
type.TerminationRulePeriod.description=Zeitraum fï¿½r Kï¿½ndigungsregeln
type.TerminationRulePeriod.code.name=Code
type.TerminationRulePeriod.code.description=Eindeutiger Bezeichner
type.TerminationRulePeriod.value.name=Wert
type.TerminationRulePeriod.value.description=Wert des Zeitraums
type.TerminationRulePeriod.unit.name=Einheit
type.TerminationRulePeriod.unit.description=Einheit des Zeitraums

type.BlockedApkSignerCertSubjectCName.name=CN des Inhabers des blockierten APK-Signaturzertifikats
type.BlockedApkSignerCertSubjectCName.description=Common Name des Inhabers des blockierten APK-Signaturzertifikats
type.BlockedApkSignerCertSubjectCName.cnValue.name=CN Wert
type.BlockedApkSignerCertSubjectCName.cnValue.description=Common Name Wert

type.ClassificationAttributeValue.termBoost.name=Term Boost-Wert
type.ClassificationAttributeValue.termBoost.description=Der Wert wird verwendet, um den Wert des Klassifizierungsattributs in das Suchergebnis aufzunehmen
type.ClassificationAttributeValue.icon.name=Icon
type.ClassificationAttributeValue.icon.description=Icon des Attributwerts

type.PostSubscriptionPriceUpdateEvent.name=PostSubscriptionPriceUpdateEvent Event
type.PostSubscriptionPriceUpdateEvent.description=Events, die als Ergebnis einer Aktualisierung der Lizenzpreise erstellt wurden
type.PostSubscriptionPriceUpdateEvent.eventId.name=EventId
type.PostSubscriptionPriceUpdateEvent.eventId.description=Eindeutiger Bezeichner des Events
type.PostSubscriptionPriceUpdateEvent.appLicense.name=App-Lizenz
type.PostSubscriptionPriceUpdateEvent.appLicense.description=Die App-Lizenz, die PreisÃnderungen unterliegt
type.PostSubscriptionPriceUpdateEvent.eventStatus.name=EventStatus
type.PostSubscriptionPriceUpdateEvent.eventStatus.description=Status des Events

type.CountryStoreConfiguration.name=CountryStoreConfiguration
type.CountryStoreConfiguration.description=Landesspezifische Konfiguration des Basisspeichers.
type.CountryStoreConfiguration.country.name=Land
type.CountryStoreConfiguration.country.description=Land der Konfiguration.
type.CountryStoreConfiguration.baseStore.name=BaseStore
type.CountryStoreConfiguration.baseStore.description=Basisspeicher der Konfiguration.
type.CountryStoreConfiguration.languages.name=Sprachen
type.CountryStoreConfiguration.languages.description=Unterstï¿½tzte Sprachen im Land fï¿½r den Basisspeicher.
type.CountryStoreConfiguration.defaultLanguage.name=Standardsprache
type.CountryStoreConfiguration.defaultLanguage.description=Standardsprache im Land fï¿½r den Basisspeicher.
type.CountryStoreConfiguration.storefrontEnabled.name=storefrontEnabled
type.CountryStoreConfiguration.storefrontEnabled.description=definieren, ob ein Land fï¿½r den Mandanten aktiviert ist.
type.CountryStoreConfiguration.importedCompaniesCanBuy.name=importedCompaniesCanBuy
type.CountryStoreConfiguration.importedCompaniesCanBuy.description=definieren, ob migrierte Unternehmen Einkäufe tätigen dürfen

type.BuyerContract.name=Kaufvertrag
type.BuyerContract.description=Kaufvertrag
type.BuyerContract.code.name=Code
type.BuyerContract.code.description=Eindeutiger Bezeichner dieses Vertrages
type.BuyerContract.startDate.name=Startdatum
type.BuyerContract.startDate.description=Startdatum des Vertragszeitraums
type.BuyerContract.endDate.name=Enddatum
type.BuyerContract.endDate.description=Enddatum des Vertragszeitraums
type.BuyerContract.orderEntry.name=Bestellposition
type.BuyerContract.orderEntry.description=Die diesem Vertrag zugrunde liegende Bestellposition
type.BuyerContract.contractTerminationRule.name=Kï¿½ndigungsregeln
type.BuyerContract.contractTerminationRule.description=Regeln zur Kï¿½ndigung dieses Kaufvertrages
type.BuyerContract.timezone.name=Zeitzone
type.BuyerContract.timezone.description=Zeitzone fï¿½r das Start- und Enddatum des Kaufvertrages
type.BuyerContract.billingPriceCode.name=Abrechnungspreiscode
type.BuyerContract.billingPriceCode.description=Fï¿½r die Abrechnung dieses Kaufvertrages verwendeter Preiscode

type.FixedTermContract.name=Kaufvertrag mit fester Laufzeit
type.FixedTermContract.description=Kaufvertrag mit fester Laufzeit

type.SubscriptionContract.name=Abonnementvertrag
type.SubscriptionContract.description=Abonnementvertrag
type.SubscriptionContract.legacySubscriptionId.name=UUID
type.SubscriptionContract.legacySubscriptionId.description=UUID


type.Runtime.name=Laufzeit
type.Runtime.description=Laufzeit
type.Runtime.code.name=Code
type.Runtime.code.description=Eindeutiger Bezeichner dieser Laufzeit
type.Runtime.name.name=Name
type.Runtime.name.description=Lokalisierter Name der Laufzeit
type.Runtime.description.name=Beschreibung
type.Runtime.description.description=Beschreibung der Laufzeit
type.Runtime.defaultRuntimeTerminationRule.name=Standard-Kï¿½ndigungsregel
type.Runtime.defaultRuntimeTerminationRule.description=Kï¿½ndigungsregeln, welche standardmï¿½ï¿½ig fï¿½r diese Laufzeit angewandt werden

type.VariantProduct.runtime.name=Laufzeit des Variantenprodukts
type.VariantProduct.runtime.description=Laufzeit des Variantenprodukts
type.VariantProduct.bundleInfo.name=Variant Product bundle info
type.VariantProduct.bundleInfo.description=Variant Product bundle info

type.Category.iconCode.name=Kategorie-Icon-Code
type.Category.iconCode.description=Kategorie-Icon-Code

type.BundleInfo.name=BundleInfo
type.BundleInfo.description=Bundle-Info der Produktvariante
type.BundleInfo.code.name=Code
type.BundleInfo.code.description=Eindeutiger Bezeichner der Bundle-Info
type.BundleInfo.name.name=Name
type.BundleInfo.name.description=Name der Bundle-Info
type.BundleInfo.size.name=GrÃ¶Ãe
type.BundleInfo.size.description=GrÃ¶Ãe des BÃ¼ndels

type.CatalogUnawareMediaContainer.name=CatalogUnawareMediaContainer
type.CatalogUnawareMediaContainer.description=Katalog-unabhÃ¤ngiges Mediencontainer

type.EulaAcceptance.name=EULA-Zustimmung
type.EulaAcceptance.description=EULA-Zustimmung
type.EulaAcceptance.user.name=Benutzer
type.EulaAcceptance.user.description=Benutzer, der die EULA(s) akzeptiert hat
type.EulaAcceptance.acceptanceTimestamp.name=EULA-Zustimmungdatum
type.EulaAcceptance.acceptanceTimestamp.description=EULA-Zustimmungdatum
type.EulaAcceptance.eulaContainers.name=EULA(s)
type.EulaAcceptance.eulaContainers.description=EULA(s), die akzeptiert wurden
type.EulaAcceptance.acceptedEulasUrls.name=EULA(s)-URL
type.EulaAcceptance.acceptedEulasUrls.description=URLs der EULA(s), die akzeptiert wurden

type.AaDistributorCompany.name=AA-Distributor
type.AaDistributorCompany.description=AA-Distributor
type.AaDistributorCompany.umpId.name=UMP-ID
type.AaDistributorCompany.umpId.description=UMP-Distributor-ID
type.AaDistributorCompany.companyName.name=Firmenname
type.AaDistributorCompany.companyName.description=Firmenname dieses Distributors
type.AaDistributorCompany.countries.name=Lï¿½nder
type.AaDistributorCompany.countries.description=Lï¿½nder, in welchen dieser Distributor aktiv ist
type.AaDistributorCompany.aaExternalId.name=AA-Distributorreferenz
type.AaDistributorCompany.aaExternalId.description=Durch AA vergebene Distributorreferenz

type.PaymentInfoDraft.name=Payment Info Draft
type.PaymentInfoDraft.description=Payment Info Draft
type.PaymentInfoDraft.code.name=Code
type.PaymentInfoDraft.code.description=Code
type.PaymentInfoDraft.integrator.name=Integrator
type.PaymentInfoDraft.integrator.description=Integrator
type.PaymentInfoDraft.creationStatus.name=Erstellungsstatus
type.PaymentInfoDraft.creationStatus.description=Erstellungsstatus
type.PaymentInfoDraft.paymentProvider.name=Zahlungsdienstleister
type.PaymentInfoDraft.paymentProvider.description=Zahlungsdienstleister
type.PaymentInfoDraft.paymentMethodType.name=Art der Zahlungsmethode
type.PaymentInfoDraft.paymentMethodType.description=Art der Zahlungsmethode
type.PaymentInfoDraft.resultingPaymentInfo.name=Resultierende Zahlungsinformationen
type.PaymentInfoDraft.resultingPaymentInfo.description=Resultierende Zahlungsinformationen

type.CountryMigrationConfiguration.name=Konfiguration der Ländermigration
type.CountryMigrationConfiguration.description=Konfiguration für ein zu migrierendes Land
type.CountryMigrationConfiguration.country.name=Migrationsland
type.CountryMigrationConfiguration.country.description=Zu migrierendes Land
type.CountryMigrationConfiguration.contractStartDate.name=Vertragsstartdatum
type.CountryMigrationConfiguration.contractStartDate.description=Startdatum für zu migrierende Verträge
type.CountryMigrationConfiguration.sepaMandateCreationDueDate.name=Fälligkeitsdatum für die Erstellung des SEPA-Mandats
type.CountryMigrationConfiguration.sepaMandateCreationDueDate.description=Fälligkeitsdatum für die Erstellung des SEPA-Mandats
type.CountryMigrationConfiguration.firstMigrationNoticeDate.name=Erster Kündigungstermin
type.CountryMigrationConfiguration.firstMigrationNoticeDate.description=Datum, an dem Kunden erstmals über eine bevorstehende Vertragsmigration informiert wurden
type.CountryMigrationConfiguration.purchaseAllowedFromDate.name=Käufe ab Datum erlaubt
type.CountryMigrationConfiguration.purchaseAllowedFromDate.description=Datum, ab dem Käufe im migrierten Land erlaubt sind
type.CountryMigrationConfiguration.lmpEndDateForFutureDatedContracts.name=Enddatum für nicht aktive Verträge
type.CountryMigrationConfiguration.lmpEndDateForFutureDatedContracts.description=Enddatum für gekündigte nicht aktive UPM-Verträge
type.CountryMigrationConfiguration.sepaDDRecommended.name=SEPA-Lastschrift empfohlen
type.CountryMigrationConfiguration.sepaDDRecommended.description=Bestimmt, ob SEPA-Lastschrift als bevorzugte Zahlungsmethode für Kunden empfohlen werden soll.
type.CountryMigrationConfiguration.allowPurchasesWithoutMigratedContracts.name=allowPurchasesWithoutMigratedContracts
type.CountryMigrationConfiguration.allowPurchasesWithoutMigratedContracts.description=definieren Sie, ob Käufe für Unternehmen ohne migrierte Verträge zulässig sind

type.OpenOrderCancelBusinessProcess.name=Geschäftsprozess "Offene Bestellung stornieren"
type.OpenOrderCancelBusinessProcess.description=Prozess zur Bearbeitung der Stornierung einer vollständigen Bestellung mit dem Status ?OFFEN?.
type.OpenOrderCancelBusinessProcess.order.name=Bestellung
type.OpenOrderCancelBusinessProcess.order.description=Zu stornierende Bestellung

type.Order.openOrderProcesses.name=OpenOrderProcesses
type.Order.openOrderProcesses.description=Geschäftsprozesse "Offene Bestellung stornieren" der Bestellung.

type.TokenizedSepaDirectDebitPaymentInfo.name=Tokenisierte SEPA-Lastschrift-Zahlungsinformation
type.TokenizedSepaDirectDebitPaymentInfo.description=Zahlungsinformationen für tokenisierte SEPA-Lastschriftzahlungen
type.TokenizedSepaDirectDebitPaymentInfo.shopperReference.name=Käuferreferenz
type.TokenizedSepaDirectDebitPaymentInfo.shopperReference.description=Käuferreferenz. Kann entweder eine Firmen-UID oder eine Benutzer-UID sein, abhängig vom Umfang der Zahlungsinformation.
type.TokenizedSepaDirectDebitPaymentInfo.recurringReference.name=Wiederkehrende Referenz
type.TokenizedSepaDirectDebitPaymentInfo.recurringReference.description=Referenz für wiederkehrende Zahlungen
