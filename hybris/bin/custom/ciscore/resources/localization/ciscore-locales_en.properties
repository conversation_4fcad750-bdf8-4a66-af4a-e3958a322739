type.AppLicense.name=App License
type.AppLicense.description=A license for an App Instance
type.AppLicense.licenseType.name=License Type
type.AppLicense.licenseType.description=The used license type for the app
type.AppLicense.billingSystemStatus.name=Billing System Status
type.AppLicense.billingSystemStatus.description=Billing system synchronization status
type.AppLicense.groupPriceUpdateStatus.name=Group Price Update Status
type.AppLicense.groupPriceUpdateStatus.description=Group Price Update synchronization status
type.AppLicense.currentlyValidPrices.name=Current Prices
type.AppLicense.currentlyValidPrices.description=Currently active prices
type.AppLicense.icon.name=Icon
type.AppLicense.icon.description=Icon (of the base product)
type.AppLicense.availabilityStatus.name=Availability status
type.AppLicense.availabilityStatus.description=Availability status chosen by a user
type.AppLicense.acquisitionCount.name=Acquisition Count
type.AppLicense.acquisitionCount.description=Number of times the app license has been acquired
type.AppLicense.submittedBy.name=Submitted By
type.AppLicense.submittedBy.description=Dev<PERSON>per who saved the license
type.AppLicense.enabledCountries.name=Enabled countries
type.AppLicense.enabledCountries.description=Countries in which the app license is purchasable
type.AppLicense.purchasableInBillingSystem.name=Purchasable in billing system
type.AppLicense.purchasableInBillingSystem.description=App License is purchasable in the billing system
type.AppLicense.volumeDiscounts.name=Volumed Discounts
type.AppLicense.volumeDiscounts.description=Volume Discounts based on the minimum quantity
type.AppLicense.specifiedPrice.name=License price
type.AppLicense.specifiedPrice.description=License price
type.AppLicense.futurePrices.name=Future prices
type.AppLicense.futurePrices.description=Pending prices which will be used in future
type.AppLicense.userGroups.name=Application license user groups
type.AppLicense.userGroups.description=The user groups that have acquired the license
type.AppVersion.name=App Version
type.AppVersion.description=A Version of an app
type.AppVersion.code.name=Code
type.AppVersion.code.description=Code of the app version
type.AppVersion.apk.name=App Apk
type.AppVersion.apk.description=The binary file for the deployment of the app version
type.AppVersion.app.name=App
type.AppVersion.app.description=The corresponding app
type.AppVersion.changelog.name=App Changelog
type.AppVersion.changelog.description=The changelog of the app version
type.AppVersion.rejectionReason.name=Rejection Reason
type.AppVersion.rejectionReason.description=The reason why an app version is meant to be rejected.
type.AppVersion.approvalStatus.name=Approval Status
type.AppVersion.approvalStatus.description=The approval status of the appversion
type.AppVersion.catalogVersion.name=Catalog Version
type.AppVersion.catalogVersion.description=The catalog version containing this appversion
type.AppVersion.exportRegulationAcknowledged.name=Export Regulation Acknowledged
type.AppVersion.exportRegulationAcknowledged.description=Export Regulation Acknowledged before uploading the APK
type.AppVersion.dualUse.name=Dual Use
type.AppVersion.dualUse.description=App Version is classified as "Dual Use"
type.AppVersion.eccn.name=ECCN
type.AppVersion.eccn.description=Export Control Classification Number for this App Version
type.AppVersion.submittedBy.name=Submitted By
type.AppVersion.submittedBy.description=The User who submitted the App Version

type.ApkSignature.code.name=Code
type.ApkSignature.code.description=Code of the apk signature
type.ApkSignature.name=Apk Signature
type.ApkSignature.description=Contains the list of valid signatures for the APK
type.ApkSignature.certificateSha256.name=APK SHA-256 Digest
type.ApkSignature.certificateSha256.description=SHA-256 of the used signing certificate
type.ApkSignature.signatureVersion.name=Signature Version
type.ApkSignature.signatureVersion.description=Used Signature Version
type.ApkSignature.validTo.name=Valid to
type.ApkSignature.validTo.description=The date until which the signature is valid
type.ApkSignature.subjectCName.name=Subject's CN
type.ApkSignature.subjectCName.description=Subject Common Name

type.IoTCustomer.name=IoTCustomer
type.IoTCustomer.description=Customer of the ecosystem
type.IoTCustomer.company.name=Company
type.IoTCustomer.company.description=The developer's/integrator's company

type.Developer.name=Developer
type.Developer.description=Developer
type.Developer.integrator.name=Integrator
type.Developer.integrator.description=The integrator account associated with this developer
type.Developer.apps.name=Apps
type.Developer.apps.description=The Apps of the developer

type.Device.name=Device
type.Device.description=A generic IoT device
type.Device.appVersions.name=App Versions
type.Device.appVersions.description=The app versions associated with the device
type.Device.deviceID.name=Device ID
type.Device.deviceID.description=The ID of the device in the remote portal
type.Device.name.name=Device Name
type.Device.name.description=The name of the device
type.Device.versions.name=Compatible versions
type.Device.versions.description=Compatible app versions for this device

type.Integrator.name=Integrator
type.Integrator.description=A system integrator
type.Integrator.developer.name=Developer
type.Integrator.developer.description=The developer account associated with this integrator

type.App.name=App
type.App.description=An App
type.App.company.name=Company
type.App.company.description=The company developing this App (App)
type.App.packageName.name=Package Name
type.App.packageName.description=Unique Name of the Android Package
type.App.emailAddress.name=Support email address
type.App.emailAddress.description=Email address used for support
type.App.supportPhoneNumber.name=Support phone number
type.App.supportPhoneNumber.description=Support phone number
type.App.privacyPolicyUrl.name=Privacy policy URL
type.App.privacyPolicyUrl.description=URL pointing to the privacy policy
type.App.supportPageUrl.name=Support page URL
type.App.supportPageUrl.description=URL pointing to the support page
type.App.termsOfUseUrl.name=Terms of Use URL
type.App.termsOfUseUrl.description=URL pointing to the terms of use
type.App.productWebsiteUrl.name=Product or Company Website
type.App.productWebsiteUrl.description=URL of the product's or company's website
type.App.installCount.name=Installations
type.App.installCount.description=Number of times this app has been bought and installed
type.App.specifiedPrice.name=Specified price
type.App.specifiedPrice.description=Price specified by the developer. This price is given in the developer companies' currency.
type.App.subscriptionPrice.name=Specified subscription price
type.App.subscriptionPrice.description=Subscription price specified by the developer. This price is given in the developer companies' currency.
type.App.versions.name=Versions
type.App.versions.description=Versions of this app
type.App.enabledInStore.name=Enabled in Store
type.App.enabledInStore.description=App is enabled in the store
type.App.masterEnabled.name=System Enabled
type.App.masterEnabled.description=App Enabled on System level
type.App.owningCompanies.name=Company
type.App.owningCompanies.description=Companies that own the App
type.App.icon.name=Icon
type.App.icon.description=The app's icon to be displayed in the store
type.App.documentationFiles.name=Documentation files
type.App.documentationFiles.description=Detail description of the app
type.App.latestVersion.name=Latest Version
type.App.latestVersion.description=The latest version of this app
type.App.submittedBy.name=Submitted By
type.App.submittedBy.description=The User who submitted the App
type.App.useCases.name=Uses of App
type.App.useCases.description=Author suggested Uses cases of App
type.App.industries.name=Enabled Industries
type.App.industries.description=Enabled Industries that belongs to app
type.App.publishDate.name=Published date
type.App.publishDate.description=Publishing date to marketplace
type.App.video.name=Video for the App
type.App.video.description=Video for the App
type.App.permittedBuyerCompanies.name=Permitted Buyer Companies
type.App.permittedBuyerCompanies.description=Companies for which the app will be available RESTRICTED_BUYER mode
type.App.storeAvailabilityMode.name=Store Availability Mode
type.App.storeAvailabilityMode.description=Store availability mode for this app
type.App.newCountryAddingAllowed.name=New Country Adding Allowed
type.App.newCountryAddingAllowed.description=consent to add new ecosystem country to App enabled countries 
type.App.appIntegrations.name=App Integrations
type.App.appIntegrations.description=List of integrations with external systems or standards
type.App.eula.name=EULA
type.App.eula.description=End User License Agreement information
type.App.eulaContainers.name=Country-specific EULAs
type.App.eulaContainers.description=Country-specific End User License Agreements
type.App.privateOfferRequest.name=Private Offer Request
type.App.privateOfferRequest.description=Private Offer Request

type.StandardAppIntegration.name=Standard App Integration
type.StandardAppIntegration.description=Standard integration of an App with external systems or standards
type.StandardAppIntegration.name.name=Integration Name
type.StandardAppIntegration.name.description=The descriptive name of the Standard App Integration for Developers
type.StandardAppIntegration.description.name=Integration Description
type.StandardAppIntegration.description.description=A description of the Standard App Integration to be shown in the frontend
type.StandardAppIntegration.enabled.name=Integration Enabled
type.StandardAppIntegration.enabled.description=Whether the integration is visible in the Store
type.StandardAppIntegration.code.name=App Integration Code
type.StandardAppIntegration.code.description=Unique code for the standard integration
type.StandardAppIntegration.displayName.name=Public App Integration Name
type.StandardAppIntegration.displayName.description=Name shown in the store to Integrators
type.StandardAppIntegration.integrationType.name=Type of Integration
type.StandardAppIntegration.integrationType.description=External standard, gateway, etc
type.StandardAppIntegration.externalDescription.name=Public App Integration Description
type.StandardAppIntegration.externalDescription.description=Description shown in the Store to Integrators
type.StandardAppIntegration.order.name=Public App Integration order
type.StandardAppIntegration.order.description=Order of the App Integration shown in the Store

type.AppIntegration.name=App Integration
type.AppIntegration.description=Description of an integration with an external system or standard
type.AppIntegration.type.name=Integration type
type.AppIntegration.type.description=Whether standard or custom
type.AppIntegration.name.name=Integration name
type.AppIntegration.name.description=The name of a custom integration
type.AppIntegration.standardIntegration.name=Standard integration
type.AppIntegration.standardIntegration.description=For type standard: link to a StandardAppIntegration
type.AppIntegration.status.name=Status of the integration information
type.AppIntegration.status.description=Whether the integration is visible in the Store or not
type.AppIntegration.documentation.name=Integration documentation
type.AppIntegration.documentation.description=An optional PDF
type.AppIntegration.storeContentDraft.name=Draft
type.AppIntegration.storeContentDraft.description=App Draft this Integration belongs to
type.AppIntegration.app.name=App
type.AppIntegration.app.description=App this Integration belongs to

type.AbstractOrder.company.name=Company
type.AbstractOrder.company.description=IoT company with which the abstract order is associated.
type.AbstractOrder.requestOrderTime.name=Request Order Time
type.AbstractOrder.requestOrderTime.description=Time the order simulate request was sent to the billing backend
type.AbstractOrder.eulaAcceptance.name=EULA Acceptance
type.AbstractOrder.eulaAcceptance.description=Records the user's acceptance of the order items' EULA(s) on order checkout
type.AbstractOrder.activationMode.name=ActivationMode
type.AbstractOrder.activationMode.description=License activation mode of the order

type.PaymentInfo.paymentProvider.name=Payment Provider
type.PaymentInfo.paymentProvider.description=The payment provider to which the current payment info corresponds to.
type.PaymentInfo.companyScope.name=Company Scope
type.PaymentInfo.companyScope.description=Says if the mandate is for the company or the user

type.PaymentProvider.name=Payment Provider
type.PaymentProvider.description=A payment provider.

type.StripeCreditCardPaymentInfo.name=Stripe Payment Information
type.StripeCreditCardPaymentInfo.description=Information regarding the payment via Stripe
type.StripeCreditCardPaymentInfo.cardSummary.name=Card number (end)
type.StripeCreditCardPaymentInfo.cardSummary.description=The last 6 digits of the credit card number
type.StripeCreditCardPaymentInfo.subVariant.name=Card subtype
type.StripeCreditCardPaymentInfo.subVariant.description=Subtype of the credit card
type.StripeCreditCardPaymentInfo.stripeCcOwnerId.name=Customer id
type.StripeCreditCardPaymentInfo.stripeCcOwnerId.description=Id of the customer stored at Stripe
type.StripeCreditCardPaymentInfo.paymentMethodId.name=Payment method id
type.StripeCreditCardPaymentInfo.paymentMethodId.description=Id of the payment method stored at Stripe
type.StripeCreditCardPaymentInfo.reusable.name=Recurring
type.StripeCreditCardPaymentInfo.reusable.description=Card data is stored by the payment provider

type.SepaCreditTransferPaymentInfo.name=Stripe SEPA Credit Transfer Payment Information
type.SepaCreditTransferPaymentInfo.description=Information regarding the SEPA credit transfer payment via Stripe
type.SepaCreditTransferPaymentInfo.bankAccountIdentifier.name=Bank account identifier
type.SepaCreditTransferPaymentInfo.bankAccountIdentifier.description=Source Bank account identifier
type.SepaCreditTransferPaymentInfo.bankIdentifier.name=Bank identifier
type.SepaCreditTransferPaymentInfo.bankIdentifier.description=Source Bank identifier
type.SepaCreditTransferPaymentInfo.bankName.name=Bank name
type.SepaCreditTransferPaymentInfo.bankName.description=Name of the Source Bank
type.SepaCreditTransferPaymentInfo.accountHolder.name=Account Holder
type.SepaCreditTransferPaymentInfo.accountHolder.description=Account Holder
type.SepaCreditTransferPaymentInfo.sourceId.name=Source ID is the reference information for the SepaCreditTransfer data stored in the external payment provider
type.SepaCreditTransferPaymentInfo.sourceId.description=Source ID is the reference information for the SepaCreditTransfer data stored in the external payment provider
type.SepaCreditTransferPaymentInfo.customerId.name=Source ID is the reference information for the SepaCreditTransfer data stored in the external payment provider
type.SepaCreditTransferPaymentInfo.customerId.description=Source ID is the reference information for the SepaCreditTransfer data stored in the external payment provider

type.ZeroPaymentInfo.name=Zero Payment Information
type.ZeroPaymentInfo.description=Payment information used when the order total is zero

type.InvoiceBySellerPaymentInfo.name=Invoice by seller payment Information
type.InvoiceBySellerPaymentInfo.description=Information about payments for orders taken by seller

type.AchInternationalCreditTransferPaymentInfo.name=ACH International Payment Info
type.AchInternationalCreditTransferPaymentInfo.description=Payment info for ACH transfers from countries outside of USA

type.DirectDebitPaymentInfo.name=Direct Debit
type.DirectDebitPaymentInfo.description=Payment Info type representing direct debit payments

type.SepaMandatePaymentInfo.name=SEPA Mandate
type.SepaMandatePaymentInfo.description=Payment info type for SEPA mandates
type.SepaMandatePaymentInfo.mandateReference.name=Mandate reference
type.SepaMandatePaymentInfo.mandateReference.description=Uniquely identifies a SEPA mandate in connection with a creditor identifier
type.SepaMandatePaymentInfo.IBAN.name=IBAN
type.SepaMandatePaymentInfo.IBAN.description=IBAN of the debited bank account
type.SepaMandatePaymentInfo.accountHolderName.name=Account holder
type.SepaMandatePaymentInfo.accountHolderName.description=Account Holder name of the debited bank account
type.SepaMandatePaymentInfo.dateOfSignature.name=Date of signature
type.SepaMandatePaymentInfo.dateOfSignature.description=Date of signature of the account holder for this mandate

type.IoTCompany.name=IoT Company
type.IoTCompany.description=Company within the IoT ecosystem
type.IoTCompany.friendlyName.name=Friendly Company Name
type.IoTCompany.friendlyName.description=Friendly Company Name Description
type.IoTCompany.companyAccountId.name=Company ID
type.IoTCompany.companyAccountId.description=The Company's ID
type.IoTCompany.developers.name=Developers
type.IoTCompany.developers.description=Company's developers
type.IoTCompany.integrators.name=Integrators
type.IoTCompany.integrators.description=Company's integrators
type.IoTCompany.employees.name=Employees
type.IoTCompany.employees.description=Accounts associated with this company
type.IoTCompany.apps.name=Apps
type.IoTCompany.apps.description=Apps created by this company
type.IoTCompany.appReviews.name=App Reviews
type.IoTCompany.appReviews.description=Performed or pending app reviews of the company
type.IoTCompany.abstractOrders.name=Abstract Orders
type.IoTCompany.abstractOrders.description=All abstract orders (i.e. orders) associated with this company
type.IoTCompany.country.name=Country of the company
type.IoTCompany.country.description=Country for payouts to this company
type.IoTCompany.deactivationDate.name=Deactivation Date
type.IoTCompany.deactivationDate.description=The deactivation date for the company
type.IoTCompany.active.name=Active
type.IoTCompany.active.description=Tells if the company is active or deactive
type.IoTCompany.paymentInfos.name=Payment Information
type.IoTCompany.paymentInfos.description=Payment Information for the Company
type.IoTCompany.defaultPaymentInfo.name=Default Payment Information
type.IoTCompany.defaultPaymentInfo.description=Payment information of the company which will be used as default
type.IoTCompany.productContainers.name=Product Container
type.IoTCompany.productContainers.description=Product container administrated by this company
type.IoTCompany.purchasedApps.name=Purchased Apps
type.IoTCompany.purchasedApps.description=Apps that have been purchased by the company
type.IoTCompany.stripeCustomerId.name=Customer id
type.IoTCompany.stripeCustomerId.description=Customer id stored at Stripe
type.IoTCompany.stripeConnectAccounts.name=StripeConnect Accounts
type.IoTCompany.stripeConnectAccounts.description=StripeConnect accounts belonging to the company.
type.IoTCompany.approvalStatus.name=Approval Status
type.IoTCompany.approvalStatus.description=Indicating status of company whether is commercially approved
type.IoTCompany.payouts.name=Payouts
type.IoTCompany.payouts.description=Payouts transferred to this company
type.IoTCompany.billingSystemStatus.name=Billing System Status
type.IoTCompany.billingSystemStatus.description=Synchronization status of company with billing backend
type.IoTCompany.sepaEnabled.name=Sepa Enabled
type.IoTCompany.sepaEnabled.description=Indicates if bank transfer feature enabled for the company
type.IoTCompany.privateAppDrafts.name=Private App Drafts
type.IoTCompany.privateAppDrafts.description=Drafts of Apps in RESTRICTED_BUYER mode that will be available to this company
type.IoTCompany.privateApps.name=Private Apps
type.IoTCompany.privateApps.description=Apps in RESTRICTED_BUYER mode that are available to this company
type.IoTCompany.pspSellerAccounts.name=PSP Seller Accounts
type.IoTCompany.pspSellerAccounts.description=PSP Seller Accounts associated with this company
type.IoTCompany.privateOfferRequest.name=Private Offer Request
type.IoTCompany.privateOfferRequest.description=Private Offer Request
type.IoTCompany.manualAppApprovalEnabled.name=Manual app approval enabled
type.IoTCompany.manualAppApprovalEnabled.description=Manual approvals are required for the comapny
type.IoTCompany.ownAppsPurchaseEnabled.name=Own Apps Purchase enabled
type.IoTCompany.ownAppsPurchaseEnabled.description=when enabled,integrators can buy own company apps.
type.IoTCompany.store.name=Store
type.IoTCompany.store.description=store of the company, acts as a realm for the company.
type.IoTCompany.bpmdId.name=BPMD Id
type.IoTCompany.bpmdId.description=The BPMD Id of the company in sync with UMP.
type.IoTCompany.billingEmail.name=Billing Email
type.IoTCompany.billingEmail.description=The company email id used for billing.
type.IoTCompany.creditLimit.name=Credit Limit
type.IoTCompany.creditLimit.description=The credit limit of the company set at UMP.
type.IoTCompany.isManaged.name=The company is a managed account.
type.IoTCompany.isManaged.description=The company is a managed account.
type.IoTCompany.communicationLanguage.name=Communication language
type.IoTCompany.communicationLanguage.description=Communication language of this company (e.g. for invoices or emails)
type.IoTCompany.operationalStage.name=Operational stage
type.IoTCompany.operationalStage.description=The company operational stage on the user management.
type.IoTCompany.companyEmail.name=Company Email
type.IoTCompany.companyEmail.description=The company email address.

type.UnusedPaymentRemovalCronJob.name=UnusedPaymentRemovalCronJob
type.UnusedPaymentRemovalCronJob.description=Cronjob to remove unused paymentinfos
type.UnusedPaymentRemovalCronJob.age.name=Age
type.UnusedPaymentRemovalCronJob.age.description=Time for saving

type.PaymentTransactionEntry.cartHash.name=Cart Hash
type.PaymentTransactionEntry.cartHash.description=The hash calculated for the cart when the authorize request for the payment was placed

type.PaymentTransaction.type.name=Transaction type
type.PaymentTransaction.type.description=Intended type of the payment transaction
type.PaymentTransaction.paymentInstrument.name=Payment Instrument
type.PaymentTransaction.paymentInstrument.description=Payment Instrument

type.PaymentInstrument.name=Payment Instrument
type.PaymentInstrument.description=Additional payment instrument information for a payment transaction
type.PaymentInstrument.pspCustomerIdentifier.name=PSP Customer ID
type.PaymentInstrument.pspCustomerIdentifier.description=Identifier for the customer with the used PSP

type.SepaTransferPaymentInstrument.name=SEPA Transfer Payment Instrument
type.SepaTransferPaymentInstrument.description=Additional information for a SEPA transfer payment transaction
type.SepaTransferPaymentInstrument.bankName.name=Bank Name
type.SepaTransferPaymentInstrument.bankName.description=Credit transfer bank name
type.SepaTransferPaymentInstrument.bic.name=BIC
type.SepaTransferPaymentInstrument.bic.description=Bank Identifier Code
type.SepaTransferPaymentInstrument.iban.name=IBAN
type.SepaTransferPaymentInstrument.iban.description=International Bank Account Number

type.AchTransferPaymentInstrument.name=ACH Transfer Payment Instrument
type.AchTransferPaymentInstrument.description=Additional information for a ACH transfer payment transaction
type.AchTransferPaymentInstrument.accountNumber.name=Account Number
type.AchTransferPaymentInstrument.accountNumber.description=Account Number
type.AchTransferPaymentInstrument.routingNumber.name=Routing Number
type.AchTransferPaymentInstrument.routingNumber.description=Routing Number
type.AchTransferPaymentInstrument.bankName.name=Bank Name
type.AchTransferPaymentInstrument.bankName.description=Bank Name
type.AchTransferPaymentInstrument.bic.name=BIC
type.AchTransferPaymentInstrument.bic.description=Bank Identifier Code

type.Order.invoice.name=Invoice
type.Order.invoice.description=The invoice for the order
type.Order.invoices.name=Invoices
type.Order.invoices.description=The invoices for the order
type.Order.idempotencyKey.name=Idempotency Key
type.Order.idempotencyKey.description=Key to be used to ensure idempotency in the billing backend
type.Order.externalOrderId.name=External Order ID
type.Order.externalOrderId.description=External order ID which is set by the billing backend
type.Order.developerPayout.name=Developer Payout
type.Order.developerPayout.description=Payout to a developer company, which includes this order
type.Order.selfBillingInvoices.name=Self-Billing Invoices
type.Order.selfBillingInvoices.description=Self-Billing Invoices which assigned to this order
type.Order.mcfOrder.name=MCF applied?
type.Order.mcfOrder.description=Tells if the Minimum commission fee is applied to the order.
type.Order.firstInvoiceNote.name=First invoice note
type.Order.firstInvoiceNote.description=First invoice note
type.Order.secondInvoiceNote.name=Second invoice note
type.Order.secondInvoiceNote.description=Second invoice note

type.Cart.firstInvoiceNote.name=First invoice note
type.Cart.firstInvoiceNote.description=First invoice note
type.Cart.secondInvoiceNote.name=Second invoice note
type.Cart.secondInvoiceNote.description=Second invoice note

type.Customer.emailAddress.name=Email address
type.Customer.emailAddress.description=Customer's email address

type.PaymentInfo.company.name=Company
type.PaymentInfo.company.description=Company owning the Payment Info

type.Permission.name=Permission
type.Permission.description=A permission an app can require
type.Permission.technicalName.name=Technical Name
type.Permission.technicalName.description=Technical Name of the Permission
type.Permission.name.name=Name
type.Permission.name.description=The displayed name of the Permission
type.Permission.icon.name=Icon
type.Permission.icon.description=The icon representing the permission
type.Permission.blacklisted.name=Blacklisted
type.Permission.blacklisted.description=Blacklisted permissions must not be contained in apks uploaded from the developer console

type.DeviceCapability.name=Device Capabilities
type.DeviceCapability.description=Capabilities of a device (e.g pan-tilt-zoom capability)
type.DeviceCapability.displayName.name=Name
type.DeviceCapability.displayName.description=The display name of the capability
type.DeviceCapability.code.name=Code
type.DeviceCapability.code.description=Code of a device capability as declared in the uses-feature element of the manifest, e.g. com.securityandsafetythings.devicecapabilities.ptz

type.AppDraft.name=App Draft
type.AppDraft.description=A draft of an app
type.AppDraft.code.name=Code
type.AppDraft.code.description=The unique code of the App Draft
type.AppDraft.storeContentDraft.name=Store Content Draft
type.AppDraft.storeContentDraft.description=Draft of the content to be displayed in the store for the app
type.AppDraft.countriesAndPricesDraft.name=Countries and Prices Draft
type.AppDraft.countriesAndPricesDraft.description=Draft for the prices of the app in several currencies
type.AppDraft.approvalStatus.name=Approval Status
type.AppDraft.approvalStatus.description=The approval status of the App Draft
type.AppDraft.rejectionReasons.name=Rejection Reasons
type.AppDraft.rejectionReasons.description=The reasons why an app draft is meant to be rejected.
type.AppDraft.submittedBy.name=Submitted By
type.AppDraft.submittedBy.description=The User who submitted the App Draft
type.AppDraft.permittedBuyerCompanies.name=Permitted Buyer Companies
type.AppDraft.permittedBuyerCompanies.description=Companies for which the app will be available RESTRICTED_BUYER mode
type.AppDraft.storeAvailabilityMode.name=Store Availability Mode
type.AppDraft.storeAvailabilityMode.description=Store availability mode for this app
type.AppDraft.newCountryAddingAllowed.name=New Country Adding Allowed
type.AppDraft.newCountryAddingAllowed.description=consent to add new ecosystem country to App enabled countries 


type.RejectionReason.name=Rejection Reason
type.RejectionReason.description=Reason for the rejection of an app
type.RejectionReason.rejectionCode.name=Code of the rejection reason
type.RejectionReason.rejectionCode.description=Internal code for the rejection reason
type.RejectionReason.comment.name=Comment to the rejection reason
type.RejectionReason.comment.description=Comment explaining the reason for the rejection of the app draft
type.RejectionReason.appDraft.name=App Draft
type.RejectionReason.appDraft.description=App draft for which the rejection reason applies
type.RejectionReason.appVersionDraft.name=App Version Draft
type.RejectionReason.appVersionDraft.description=App version draft for which the rejection reason applies

type.StoreContentDraft.name=Store Content Draft
type.StoreContentDraft.description=Draft of the content to be displayed in the store for the app
type.StoreContentDraft.code.name=Code
type.StoreContentDraft.code.description=The unique code of the Store Content Draft
type.StoreContentDraft.name.name=Name
type.StoreContentDraft.name.description=Name of the App
type.StoreContentDraft.description.name=Description
type.StoreContentDraft.description.description=Description of the App
type.StoreContentDraft.icon.name=Icon
type.StoreContentDraft.icon.description=Icon of the App
type.StoreContentDraft.summary.name=Summary
type.StoreContentDraft.summary.description=Summary of the App
type.StoreContentDraft.logo.name=Logo
type.StoreContentDraft.logo.description=The App's logo. Deprecated, should not be used
type.StoreContentDraft.screenshots.name=Screenshots of the App
type.StoreContentDraft.screenshots.description=Screenshots of the App
type.StoreContentDraft.documentationFiles.name=Documentation files
type.StoreContentDraft.documentationFiles.description=Detail description of the app
type.StoreContentDraft.emailAddress.name=Email address
type.StoreContentDraft.emailAddress.description=Email address used for contact information
type.StoreContentDraft.supportPhoneNumber.name=Support phone number
type.StoreContentDraft.supportPhoneNumber.description=Support phone number
type.StoreContentDraft.privacyPolicyUrl.name=Privacy policy URL
type.StoreContentDraft.privacyPolicyUrl.description=URL pointing to the privacy policy
type.StoreContentDraft.supportPageUrl.name=Support page URL
type.StoreContentDraft.supportPageUrl.description=URL pointing to the support page
type.StoreContentDraft.termsOfUseUrl.name=Terms of Use URL
type.StoreContentDraft.termsOfUseUrl.description=URL pointing to the terms of use
type.StoreContentDraft.productWebsiteUrl.name=Product or Company Website
type.StoreContentDraft.productWebsiteUrl.description=URL of the product's or company's website
type.StoreContentDraft.industries.name=Enabled Industries
type.StoreContentDraft.industries.description=Enabled Industries for store content draft
type.StoreContentDraft.useCases.name=Applicable UseCases
type.StoreContentDraft.useCases.description=Author given Uses cases for the App.
type.StoreContentDraft.video.name=Video for the App
type.StoreContentDraft.video.description=Video for the App.
type.StoreContentDraft.appIntegrations.name=App Integrations
type.StoreContentDraft.appIntegrations.description=List of integrations with external systems or standards
type.StoreContentDraft.eula.name=EULA
type.StoreContentDraft.eula.description=End User License Agreement information

type.CountriesAndPricesDraft.name=Countries and Prices Draft
type.CountriesAndPricesDraft.description=Draft for the prices of the app in several currencies
type.CountriesAndPricesDraft.code.name=Code
type.CountriesAndPricesDraft.code.description=The unique code of the Countries and Prices Draft
type.CountriesAndPricesDraft.enabledInStore.name=Enabled in the store
type.CountriesAndPricesDraft.enabledInStore.description=Indicates that the app shall be enabled in the store
type.CountriesAndPricesDraft.appLicenses.name=Available Licenses
type.CountriesAndPricesDraft.appLicenses.description=Licenses under which the app will be purchasable
type.CountriesAndPricesDraft.specifiedPrice.name=Specified price
type.CountriesAndPricesDraft.specifiedPrice.description=Price specified by the developer
type.CountriesAndPricesDraft.subscriptionPrice.name=Specified subscription price
type.CountriesAndPricesDraft.subscriptionPrice.description=Subscription price specified by the developer
type.CountriesAndPricesDraft.pricingSaved.name=State of draft prices
type.CountriesAndPricesDraft.pricingSaved.description=Whether the draft prices were explicitly saved by the developer
type.CountriesAndPricesDraft.availabilitySaved.name=State of draft availability
type.CountriesAndPricesDraft.availabilitySaved.description=Whether the draft availability was explicitly saved by the developer

type.AppLicenseDraft.name=App License Draft
type.AppLicenseDraft.description=Draft instance of app license
type.AppLicenseDraft.licenseType.name=License type
type.AppLicenseDraft.licenseType.description=Supported license type
type.AppLicenseDraft.availabilityStatus.name=License enabled
type.AppLicenseDraft.availabilityStatus.description=Enable license visibility
type.AppLicenseDraft.countriesAndPricesDraft.name=Countries and Prices
type.AppLicenseDraft.countriesAndPricesDraft.description=Countries and Prices of app
type.AppLicenseDraft.submittedBy.name=Submitted By
type.AppLicenseDraft.submittedBy.description=Developer who saved the license
type.AppLicenseDraft.enabledCountries.name=Enabled countries
type.AppLicenseDraft.enabledCountries.description=Countries in which the app license shall be purchasable
type.AppLicenseDraft.volumeDiscounts.name=Volumed Discounts
type.AppLicenseDraft.volumeDiscounts.description=Volume Discounts based on the minimum quantity
type.AppLicenseDraft.specifiedPrice.name=License Price
type.AppLicenseDraft.specifiedPrice.description=License Price

type.SupportedLicense.name=Supported License
type.SupportedLicense.description=System wide supported license
type.SupportedLicense.licenseType.name=License type
type.SupportedLicense.licenseType.description=Supported license type
type.SupportedLicense.enabled.name=License enabled
type.SupportedLicense.enabled.description=If disabled, license will not be visible for any user

type.AppVersionDraft.name=App Version Draft
type.AppVersionDraft.description=Draft of an App Version
type.AppVersionDraft.code.name=Code
type.AppVersionDraft.code.description=The unique code of the App Version Draft
type.AppVersionDraft.apk.name=APK
type.AppVersionDraft.apk.description=The APK of the App Version
type.AppVersionDraft.changelog.name=App Changelog
type.AppVersionDraft.changelog.description=The Changelog of the App
type.AppVersionDraft.packageName.name=Package Name
type.AppVersionDraft.packageName.description=The unique name of the Android package of the App
type.AppVersionDraft.versionName.name=Version
type.AppVersionDraft.versionName.description=The Version of the App
type.AppVersionDraft.versionCode.name=Version Code
type.AppVersionDraft.versionCode.description=The version code of the app
type.AppVersionDraft.permissions.name=Permissions
type.AppVersionDraft.permissions.description=Permissions required by the App Version
type.AppVersionDraft.approvalStatus.name=Approval Status
type.AppVersionDraft.approvalStatus.description=The approval status of the App Draft
type.AppVersionDraft.rejectionReasons.name=Rejection Reasons
type.AppVersionDraft.rejectionReasons.description=The reasons why an app version draft is meant to be rejected.
type.AppVersionDraft.exportRegulationAcknowledged.name=Export Regulation Acknowledged
type.AppVersionDraft.exportRegulationAcknowledged.description=Export Regulation Acknowledged before uploading the APK
type.AppVersionDraft.dualUse.name=Dual Use
type.AppVersionDraft.dualUse.description=App Version is classified as "Dual Use"
type.AppVersionDraft.eccn.name=ECCN
type.AppVersionDraft.eccn.description=Export Control Classification Number for this App Version
type.AppVersionDraft.submittedBy.name=Submitted By
type.AppVersionDraft.submittedBy.description=The User who submitted the App Version Draft

type.ProductContainer.name=Product Container
type.ProductContainer.description=A product container containing an App and/or an App Draft
type.ProductContainer.code.name=Code
type.ProductContainer.code.description=The unique code of the product container
type.ProductContainer.app.name=App
type.ProductContainer.app.description=An App administrated in the product container
type.ProductContainer.appDraft.name=App Draft
type.ProductContainer.appDraft.description=An App Draft administrated in the product container
type.ProductContainer.appVersionDraft.name=App Version Draft
type.ProductContainer.appVersionDraft.description=Draft of an App Version
type.ProductContainer.title.name=Title
type.ProductContainer.title.description=Title of the product container (now deprecated, refer to App name / StoreContentDraft name).
type.ProductContainer.company.name=Company
type.ProductContainer.company.description=Company administrating the product container
type.ProductContainer.createdBy.name=Created By
type.ProductContainer.createdBy.description=The User who created the product container
type.ProductContainer.draft.name=Draft
type.ProductContainer.draft.description=Indicates that a product container is in draft state
type.ProductContainer.approved.name=Approved
type.ProductContainer.approved.description=Indicates that a product container contains an approved app
type.Country.currency.name=Currency
type.Country.currency.description=Currency of a country
type.Country.apps.name=Apps
type.Country.apps.description=Apps available for purchase in this country
type.Country.drafts.name=Drafts
type.Country.drafts.description=Drafts of apps that are to offered in this country
type.Country.enabledForDevelopers.name=Active for Developers
type.Country.enabledForDevelopers.description=The country has been activated for app developers
type.Country.enabledForIntegrators.name=Active for Integrators
type.Country.enabledForIntegrators.description=The country has been activated for integrators
type.Country.appLicenses.name=App Licenses
type.Country.appLicenses.description=App Licenses available in this country
type.Country.appLicenseDrafts.name=App License Drafts
type.Country.appLicenseDrafts.description=App License Drafts that shall be available in this country
type.Country.supportedPaymentProviders.name=Supported Payment Providers
type.Country.supportedPaymentProviders.description=The set of the supported payment providers in the country
type.Country.supportedPaymentMethods.name=Supported Payment Methods
type.Country.supportedPaymentMethods.description=The set of the supported payment methods in the country
type.Country.inEu.name=Inside Eu
type.Country.inEu.description=Checks if the country is in EU
type.Country.blockedCountriesCommercial.name=Blocked countries ISO codes
type.Country.blockedCountriesCommercial.description=Companies from these countries cannot purchase commercial licenses sold by companies based in the country. Synchronized from UMP - Countries Service.
type.Country.aaDistributorCompanies.name=AA Distributors
type.Country.aaDistributorCompanies.description=AA Distributors active in this country

type.Media.used.name=Used
type.Media.used.description=Actively attached to App or ProductContainer

type.ApkMedia.name=APK
type.ApkMedia.description=Media Container encapsulating APK-specific information
type.ApkMedia.signatures.name=APK Signatures
type.ApkMedia.signatures.description=Contains the list of valid signatures for the APK
type.ApkMedia.versionName.name=Version Name
type.ApkMedia.versionName.description=The version name of the APK (serving as a description of the version for the user)
type.ApkMedia.versionCode.name=Version Code
type.ApkMedia.versionCode.description=The version code of the APK (a unique identifier of the version)
type.ApkMedia.permissions.name=Permissions
type.ApkMedia.permissions.description=Permissions required by the APK
type.ApkMedia.packageName.name=Package Name
type.ApkMedia.packageName.description=Unique Name of the Android Package
type.ApkMedia.sdkAddonVersion.name=SDK Addon Version
type.ApkMedia.sdkAddonVersion.description=Version of the SAST SDK addon required by this APK
type.ApkMedia.minAndroidApiVersion.name=Minimal Android API Version
type.ApkMedia.minAndroidApiVersion.description=Minimum Android API version supported by this APK
type.ApkMedia.deviceCapabilities.name=Device Capabilities
type.ApkMedia.deviceCapabilities.description=Required capabilities of a device (e.g pan-tilt-zoom capability)

type.PdfMedia.name=Catalog Aware PDF
type.PdfMedia.description=A catalog aware PDF file

type.CatalogUnawarePdfMedia.name=Catalog Unaware PDF
type.CatalogUnawarePdfMedia.description=A catalog unaware PDF file

type.PdfMedia.app.name=App
type.PdfMedia.app.description=App this file belongs to

type.PdfMedia.storeContentDraft.name=Store Content Draft
type.PdfMedia.storeContentDraft.description=Store Content Draft this file belongs to

type.UnusedMediaRemovalCronJob.name=UnusedMediaRemovalCronJob
type.UnusedMediaRemovalCronJob.description=UnusedMediaRemovalCronJob
type.UnusedMediaRemovalCronJob.age.name=Age
type.UnusedMediaRemovalCronJob.age.description=Age an unused media object has before it is removed

type.FeatureToggle.name=Feature Toggle
type.FeatureToggle.description=Defines the current status of a specific feature
type.FeatureToggle.code.name=Code
type.FeatureToggle.code.description=The unique code of the feature toggle
type.FeatureToggle.enabled.name=Enabled
type.FeatureToggle.enabled.description=Flag to enable or disable the feature

type.PurchasedAppsAssignmentCronJob.name=Purchased Apps per Company Cron Job
type.PurchasedAppsAssignmentCronJob.description=Cron Job to get purchased apps per company.
type.PurchasedAppsAssignmentCronJob.days.name=Days
type.PurchasedAppsAssignmentCronJob.days.description=Number of days from now into the past that should be considered.

type.CustomerReview.showCompany.name=Show Company
type.CustomerReview.showCompany.description=Flag to track if a company should be displayed in a review.
type.CustomerReview.showName.name=Show Name
type.CustomerReview.showName.description=Flag to track if a name should be displayed in a review.

type.PriceLimit.name=Price Limit
type.PriceLimit.description=Defines price limits per currency.
type.PriceLimit.code.name=Code
type.PriceLimit.code.description=The ISO code of the currency or the code of the fallback entry.
type.PriceLimit.upperLimit.name=Upper Price Limit
type.PriceLimit.upperLimit.description=The highest allowed price for an app
type.PriceLimit.currency.name=Currency
type.PriceLimit.currency.description=The linked currency of non-fallback entries

type.Product.idempotencyKey.name=Idempotency Key
type.Product.idempotencyKey.description=Key to be used to ensure idempotency in the billing backend
type.Product.pendingProductInfo.name=Pending Product Info
type.Product.pendingProductInfo.description=Pending product info
type.Product.countryRestricted.name=Country Restricted
type.Product.countryRestricted.description=flag to indicate if the product is country restricted or not.


type.StripeConnectAccount.name=StripeConnect account
type.StripeConnectAccount.description=StripeConnect account to be used for payouts.
type.StripeConnectAccount.email.name=E-Mail
type.StripeConnectAccount.email.description=E-Mail-Address used for communication when setting up the StripeConnect account
type.StripeConnectAccount.accountId.name=account ID
type.StripeConnectAccount.accountId.description=ID of the StripeConnect account
type.StripeConnectAccount.user.name=User
type.StripeConnectAccount.user.description=User who started the set-up process of the StripeConnect account.
type.StripeConnectAccount.accountManager.name=Account Manager
type.StripeConnectAccount.accountManager.description=Backoffice employee who manages the StripeConnect account set-up on Azena side.
type.StripeConnectAccount.status.name=Status
type.StripeConnectAccount.status.description=Status of the StripeConnect account setup.
type.StripeConnectAccount.submissionDate.name=Submission Date
type.StripeConnectAccount.submissionDate.description=Date when the StripeConnect account was submitted.
type.StripeConnectAccount.company.name=Company
type.StripeConnectAccount.company.description=The company the StripeConnect account belongs to.
type.StripeConnectAccount.provideInformationInvoked.name=Provided information
type.StripeConnectAccount.provideInformationInvoked.description=Indicates if the onboarding workflow has been accessed at least once
type.StripeConnectAccount.exported.name=Exported
type.StripeConnectAccount.exported.description=True, if the account was exported to Pj-Eco

type.SdkAddon.name=SDK Addon Compatibility
type.SdkAddon.description=Compatibility of the SAST SDK addon
type.SdkAddon.version.name=SDK Addon Version
type.SdkAddon.version.description=Version of the SAST SDK addon
type.SdkAddon.maxSupportedMinAndroidApiVersion.name=Maximum Allowed Min Android API version
type.SdkAddon.maxSupportedMinAndroidApiVersion.description=Maximum Android API version that may be set in the Android manifest at the same time as this sdk addon version
type.SdkAddon.minOsVersion.name=Minimum required OS Version
type.SdkAddon.minOsVersion.description=Minimum required OS Version
type.SdkAddon.maxOsVersion.name=Maximum required OS Version
type.SdkAddon.maxOsVersion.description=Maximum required OS Version
type.SdkAddon.unsupportedLegacyVersion.name=Unsupported Legacy Version
type.SdkAddon.unsupportedLegacyVersion.description=Legacy version which is not supported anymore

type.ReservedPackageName.name=Reserved Package Names
type.ReservedPackageName.description=Package names that are reserved by some company
type.ReservedPackageName.company.name=Company
type.ReservedPackageName.company.description=The company who is allowed to upload apks starting with this prefix
type.ReservedPackageName.packageNamePrefix.name=Package name prefix
type.ReservedPackageName.packageNamePrefix.description=The prefix of the package name that is restricted

type.AccountDeletion.name=Account Deletion
type.AccountDeletion.description=State of an account deletion
type.AccountDeletion.uid.name=Uid
type.AccountDeletion.uid.description=Uid of the deleted account
type.AccountDeletion.processed.name=Processed
type.AccountDeletion.processed.description=Flag to show whether account has been processed

type.NavigationItem.name=Navigation Items
type.NavigationItem.description=Navigation links configuration
type.NavigationItem.uid.name=Uid
type.NavigationItem.uid.description=Uid of navigation items
type.NavigationItem.type.name=Type
type.NavigationItem.type.description=The type of navigation items: STORE, DEVCON oder GLOBAL
type.NavigationItem.group.name=Group
type.NavigationItem.group.description=The group of the item: HEADER, FOOTER ...
type.NavigationItem.text.name=Text
type.NavigationItem.text.description=The text, which you can see in the link
type.NavigationItem.url.name=URL
type.NavigationItem.url.description=The url of the link in the navigation item
type.NavigationItem.target.name=Target
type.NavigationItem.target.description=Target attribute in the link
type.NavigationItem.icon.name=Icon
type.NavigationItem.icon.description=The icon which can be used with this link
type.NavigationItem.index.name=Index
type.NavigationItem.index.description=Using index elements being sorted
type.NavigationItem.enabled.name=Enabled
type.NavigationItem.enabled.description=Flag to activate or deactivate the item.
type.NavigationItem.description.name=Navigation Items Description
type.NavigationItem.description.description=Description of NavigationItem description field
type.NavigationItem.itemCode.name=ItemCode
type.NavigationItem.itemCode.description=A code that identifies a navigation item within the base store
type.NavigationItem.store.name=Store
type.NavigationItem.store.description=The base store to which the navigation item belongs
type.NavigationItem.interpolatedUrl.name=URL Interpolation
type.NavigationItem.interpolatedUrl.description=Defines whether the URL attribute should be processed with variable interpolation
type.NavigationItem.customAttributes.name=Custom Attributes
type.NavigationItem.customAttributes.description=Custom HTML attributes that should be added to the navigation item in the UI

type.NavigationItemAttribute.name=Navigation Item Attribute
type.NavigationItemAttribute.description=Additional attribute for a Navigation Items
type.NavigationItemAttribute.name.name=Name
type.NavigationItemAttribute.name.description=Name of the HTML attribute
type.NavigationItemAttribute.value.name=Value
type.NavigationItemAttribute.value.description=Value for the HTML attribute (optional). Blank or null values are to be treated as boolean attribute with value true by the UI.
type.NavigationItemAttribute.navigationItem.name=Navigation Item
type.NavigationItemAttribute.navigationItem.description=The owning navigation item

type.Invoice.name=Invoice
type.Invoice.description=Invoice
type.Invoice.code.name=Code
type.Invoice.code.description=Internal code of the invoice
type.Invoice.externalId.name=External Id
type.Invoice.externalId.description= The id of the invoice given by the external billing system
type.Invoice.displayName.name=Display name
type.Invoice.displayName.description=The name of the invoide displayed to the user
type.Invoice.document.name=Document
type.Invoice.document.description=The actual invoice document (usually a pdf)
type.Invoice.order.name=Order
type.Invoice.order.description=The order for which the invoice is issued
type.Invoice.netAmount.name=Net Amount
type.Invoice.netAmount.description=Net total amount as stated on the invoice document
type.Invoice.taxAmount.name=Tax Amount
type.Invoice.taxAmount.description=Total tax amount as stated on the invoice document
type.Invoice.grossAmount.name=Gross Amount
type.Invoice.grossAmount.description=Gross total amount as stated on the invoice document
type.Invoice.status.name=Status
type.Invoice.status.description=Invoice Status
type.Invoice.invoiceDate.name=Invoice Date
type.Invoice.invoiceDate.description=Invoice Date
type.Invoice.creditNotes.name=Credit notes
type.Invoice.creditNotes.description=Credit notes issued for the invoice
type.Invoice.invoiceItems.name=Invoice Items
type.Invoice.invoiceItems.description=Items on the invoice


type.InvoiceItem.name=Invoice Item
type.InvoiceItem.description=Invoice Item
type.InvoiceItem.originalInvoice.name=Original Invoice
type.InvoiceItem.originalInvoice.description=Original Invoice
type.InvoiceItem.positionId.name=Position Id
type.InvoiceItem.positionId.description=Position Id for for the Invoice Item
type.InvoiceItem.productId.name=Product Id
type.InvoiceItem.productId.description=ID of the product on Invoice Item
type.InvoiceItem.quantity.name=Quantity
type.InvoiceItem.quantity.description=Quantity of the products on the Invoice Item
type.InvoiceItem.netPrice.name=Net Price
type.InvoiceItem.netPrice.description=Net price of the product on the Invoice Item
type.InvoiceItem.currency.name=Currency
type.InvoiceItem.currency.description=Currency of the price of the product on the Invoice Item


type.SelfBillingInvoice.name=Self Billing Invoice
type.SelfBillingInvoice.description=The invoice representing the sale of apps for the developer company
type.SelfBillingInvoice.code.name=Code
type.SelfBillingInvoice.code.description=Internal code of the self billing invoice
type.SelfBillingInvoice.externalId.name=External ID
type.SelfBillingInvoice.externalId.description=The id of the self billing invoice given by the external billing system
type.SelfBillingInvoice.displayName.name=Display Name
type.SelfBillingInvoice.displayName.description=The name of the self billing invoice displayed to the user
type.SelfBillingInvoice.document.name=Document
type.SelfBillingInvoice.document.description=The actual invoice document (usually a pdf)
type.SelfBillingInvoice.order.name=Order
type.SelfBillingInvoice.order.description=The order for which the self billing invoice is issued
type.SelfBillingInvoice.marketplaceShare.name=Marketplace Share
type.SelfBillingInvoice.marketplaceShare.description=Marketplace share as stated on the invoice document (the amount the Azena marketplace charges the seller)
type.SelfBillingInvoice.sellerShare.name=Seller Share
type.SelfBillingInvoice.sellerShare.description=Seller share as stated on the invoice document (this value is usually negative since the Azena marketplace credits this amount to the seller)
type.SelfBillingInvoice.marketplaceTaxAmount.name=Marketplace Tax Amount
type.SelfBillingInvoice.marketplaceTaxAmount.description=Tax amount charged by the marketplace to the seller (this value is usually negative since the Azena marketplace credits this amount to the seller)
type.SelfBillingInvoice.creditNotes.name=Credit notes
type.SelfBillingInvoice.creditNotes.description=Credit notes issued for the self billing invoice
type.SelfBillingInvoice.status.name=Status
type.SelfBillingInvoice.status.description=Invoice Status
type.SelfBillingInvoice.invoiceDate.name=Self Billing Invoice Date
type.SelfBillingInvoice.invoiceDate.description=Self Billing Invoice Date

type.UserSpecificOfflineToken.name=User Specific Offline Token
type.UserSpecificOfflineToken.description=Stores User's offline tokens
type.UserSpecificOfflineToken.userId.name=UserId
type.UserSpecificOfflineToken.userId.description=UMP UserId
type.UserSpecificOfflineToken.offlineToken.name=Offline Token
type.UserSpecificOfflineToken.offlineToken.description=Offline Token
type.UserGroup.appLicenses.name=User Group application licenses
type.UserGroup.appLicenses.description=Appliation licenses available for user group
type.DeveloperPayout.name=Developer Payout
type.DeveloperPayout.description=Payout transferred to a developer company
type.DeveloperPayout.amount.name=Amount
type.DeveloperPayout.amount.description=Paid out amount (in minor currency units)
type.DeveloperPayout.currency.name=Currency
type.DeveloperPayout.currency.description=Currency of the payout
type.DeveloperPayout.payoutDate.name=Payout Date
type.DeveloperPayout.payoutDate.description=Date of the actual payout
type.DeveloperPayout.company.name=Company
type.DeveloperPayout.company.description=Company which got paid out
type.DeveloperPayout.orders.name=Orders
type.DeveloperPayout.orders.description=Orders included in this payout
type.DeveloperPayout.payoutId.name=Developer Payout Id
type.DeveloperPayout.payoutId.description=Id of the payout object on Stripe side
type.DeveloperPayout.status.name=Status
type.DeveloperPayout.status.description=Status of the payout at Stripe
type.DeveloperPayout.paymentProvider.name=Payment Provider
type.DeveloperPayout.paymentProvider.description=Payment Provider

type.Industry.name=Industry
type.Industry.description=Supported Industry
type.Industry.name.name=Name of Industry
type.Industry.name.description=Specify industry name
type.Industry.enabled.name=Is Enabled
type.Industry.enabled.description=If Disabled, industry will not be visible to developer
type.Industry.index.name=Order Index
type.Industry.index.description=Order in which industries will be sorted. Default is 0
type.Industry.storeContentDraft.name=Store Content Draft
type.Industry.storeContentDraft.description=Store Content Draft for which industry belong to
type.Industry.app.name=App
type.Industry.app.description=Apps for which industry belong to

type.UseCase.name=UseCase
type.UseCase.description=Uses cases of the apps in the Application Store 
type.UseCase.name.name=Name 
type.UseCase.name.description=Name of the use case 
type.UseCase.index.name=Index 
type.UseCase.index.description=Index of this useCase among the others
type.UseCase.enabled.name=Enabled
type.UseCase.enabled.description=If Disabled, UseCase will not be visible to developer
type.UseCase.storeContentDrafts.name=StoreContentDrafts 
type.UseCase.storeContentDrafts.description=StoreContentDrafts assigned to this usecase
type.UseCase.apps.name=App
type.UseCase.apps.description=Apps assigned to this use-case

type.StoreContentDraft2UseCase.name=StoreContentDraft2UseCase
type.StoreContentDraft2UseCase.description=StoreContentDraft2UseCase relation
type.StoreContentDraft2UseCase.storeContentDrafts.name=storeContentDrafts
type.StoreContentDraft2UseCase.storeContentDrafts.description=storeContentDrafts association of the use-cases
type.StoreContentDraft2UseCase.useCases.name=UseCases
type.StoreContentDraft2UseCase.useCases.description=useCases association of the StoreContentDrafts

type.App2UseCase.name=App2UseCase
type.App2UseCase.description=App to UseCase relation
type.App2UseCase.apps.name=apps
type.App2UseCase.apps.description=apps associated to the use-case
type.App2UseCase.useCases.name=useCases
type.App2UseCase.useCases.description=useCases associated to the app


type.AppVideo.name=App Video
type.AppVideo.description=Video for the App
type.AppVideo.code.name=Code
type.AppVideo.code.description=Code of the app video
type.AppVideo.type.name=Video Provider
type.AppVideo.type.description=The external provider for the video
type.AppVideo.source.name=Video URL
type.AppVideo.source.description=The link to the video
type.AppVideo.status.name=Video Status
type.AppVideo.status.description=The status of the video
type.AppVideo.lastStatusCheck.name=Status Check
type.AppVideo.lastStatusCheck.description=The time when the status was last checked
type.AppVideo.externalId.name=Video ID
type.AppVideo.externalId.description=The identifier used in the provider service for the video

type.PriceDraft.name=Price Draft
type.PriceDraft.description=Price Draft for the App License
type.PriceDraft.currency.name=Currency
type.PriceDraft.currency.description=Currency of the Price Draft
type.PriceDraft.amount.name=Amount
type.PriceDraft.amount.description=Amount of the Price Draft
type.PriceDraft.minQuantity.name=Minimum quantity
type.PriceDraft.minQuantity.description=Minimum quantity for which the resulting PriceRow should be applicable
type.PriceDraft.scaledPriceDiscount.name=Discount Value
type.PriceDraft.scaledPriceDiscount.description=Informational value containing the discount percentage for scaled prices
type.PriceDraft.validFrom.name=Valid From
type.PriceDraft.validFrom.description=Price validity for the App
type.PriceDraft.userPriceGroup.name=User Price Group
type.PriceDraft.userPriceGroup.description=Prices for certain customers
type.PriceDraft.billingPriceCode.name=Billing Price Code
type.PriceDraft.billingPriceCode.description=Billing Code for Price Draft

type.PriceRow.scaledPriceDiscount.name=Discount Value
type.PriceRow.scaledPriceDiscount.description=Informational value containing the discount percentage for scaled prices
type.PriceRow.billingRowId.name=Billing Row Id
type.PriceRow.billingRowId.description=Billing ID for Price
type.PriceRow.billingPriceCode.name=Billing Price Code
type.PriceRow.billingPriceCode.description=Billing Code for Price

type.PendingProductInfo.name=Pending Product Info
type.PendingProductInfo.description=PendingProductInfo contains information about pending product attributes.
type.PendingProductInfo.name.name=Name Attribute
type.PendingProductInfo.name.description=Pending name attribute of the product
type.PendingProductInfo.prices.name=Price Attributes
type.PendingProductInfo.prices.description=Pending price attributes of the product
type.PendingProductInfo.groupPriceDraft.name=Group Price Draft
type.PendingProductInfo.groupPriceDraft.description=Group Price drafts is the collection of the pending group prices
type.PendingProductInfo.useLatestChargePlan.name=Use Latest Charge Plan
type.PendingProductInfo.useLatestChargePlan.description=Instructs BRIM to use the latest version of the products charge plan

type.VolumeDiscount.name=Volume Discounts
type.VolumeDiscount.description=Volume Discount based on the minimum quantity for the particular product
type.VolumeDiscount.appLicense.name=App License
type.VolumeDiscount.appLicense.description=The volume discount is applicable for this App License
type.VolumeDiscount.minQuantity.name=Minimum Quantity
type.VolumeDiscount.minQuantity.description=The volume discount is applicable for this minimum quantity
type.VolumeDiscount.discount.name=Discount
type.VolumeDiscount.discount.description=The discount percentage on the base price
type.VolumeDiscount.active.name=Active
type.VolumeDiscount.active.description=Is the discount active

type.VolumeDiscountDraft.name=Volume Discount Drafts
type.VolumeDiscountDraft.description=Volume Discount based on the minimum quantity for the particular product
type.VolumeDiscountDraft.appLicenseDraft.name=App License Draft
type.VolumeDiscountDraft.appLicenseDraft.description=The volume discount is applicable for this App License Draft
type.VolumeDiscountDraft.minQuantity.name=Minimum Quantity
type.VolumeDiscountDraft.minQuantity.description=The volume discount is applicable for this minimum quantity
type.VolumeDiscountDraft.discount.name=Discount
type.VolumeDiscountDraft.discount.description=The discount percentage on the base price

type.Subscription.name=Subscription
type.Subscription.description=Subscription
type.Subscription.uuid.name=UUID
type.Subscription.uuid.description=UUID
type.Subscription.productCode.name=Product Code
type.Subscription.productCode.description=Product Code
type.Subscription.orderNumber.name=Order number
type.Subscription.orderNumber.description=Order number
type.Subscription.entryNumber.name=Entry number
type.Subscription.entryNumber.description=Unique entry number based on total quantity of actual order entry
type.Subscription.startDate.name=Start date
type.Subscription.startDate.description=Start date
type.Subscription.endDate.name=End date
type.Subscription.endDate.description=End date
type.Subscription.cancelledDate.name=Cancelled date
type.Subscription.cancelledDate.description=Cancelled date
type.Subscription.orderEntry.name=Order Entry
type.Subscription.orderEntry.description=Order Entry
type.Subscription.contractTerminationRule.name=Contract Termination Rule
type.Subscription.contractTerminationRule.description=Termination Rule for this subscription
type.Subscription.timezone.name=Time zone
type.Subscription.timezone.description=Time zone for the contract start and end dates.
type.Subscription.billingPriceCode.name=Billing Price Code
type.Subscription.billingPriceCode.description=Price code currently used for billing of this subscription

type.MinimumCommission.name=Minimum Commission
type.MinimumCommission.description=Minimum Commission
type.MinimumCommission.fee.name=Fee
type.MinimumCommission.fee.description=Fee
type.MinimumCommission.licenseType.name=License Type
type.MinimumCommission.licenseType.description=License Type
type.MinimumCommission.currency.name=Currency
type.MinimumCommission.currency.description=Currency
type.MinimumCommission.sastCommissionPercentage.name=Azena Commission Percentage
type.MinimumCommission.sastCommissionPercentage.description=Azena Commission Percentage

type.Eula.name=EULA
type.Eula.description=End User License Agreement information
type.Eula.type.name=Type of EULA
type.Eula.type.description=Whether Standard or Customer
type.Eula.customUrl.name=Custom EULA URL
type.Eula.customUrl.description=The URL of a custom EULA
type.Eula.standardEulaAppendix.name=Standard EULA Appendix
type.Eula.standardEulaAppendix.description=Appendix to a Standard EULA

type.CountryEula.name=Country-specific EULA
type.CountryEula.description=Country-specific End User License Agreement
type.CountryEula.eulaContainer.name=Container
type.CountryEula.eulaContainer.description=Container the EULA is part of
type.CountryEula.country.name=Country
type.CountryEula.country.description=Country the EULA applies to
type.CountryEula.eula.name=EULA
type.CountryEula.eula.description=EULA applying to the country

type.EulaContainer.name=EULA Container
type.EulaContainer.description=End User License Agreement Container
type.EulaContainer.code.name=Identifier
type.EulaContainer.code.description=Unique business key
type.EulaContainer.label.name=Display name
type.EulaContainer.label.description=Localized display name
type.EulaContainer.countryEulas.name=Country-specific EULAs
type.EulaContainer.countryEulas.description=Country-specific End User License Agreements
type.EulaContainer.apps.name=Apps
type.EulaContainer.apps.description=Apps the EULAs are applicable to
type.EulaContainer.EulaAcceptance.name=EULA acceptance
type.EulaContainer.EulaAcceptance.description=EULA acceptance

type.ThlConfigration.name=THL Configuration
type.ThlConfigration.description=THL Configuration
type.ThlConfigration.masterProductCode.name=Master product code
type.ThlConfigration.masterProductCode.description=Master product code
type.ThlConfigration.thlProductCode.name=THL product code
type.ThlConfigration.thlProductCode.description=THL product code
type.ThlConfigration.customerGroup.name=Customer group
type.ThlConfigration.customerGroup.description=Customer group
type.ThlConfigration.country.name=Country iso code
type.ThlConfigration.country.description=Country iso code
type.ThlConfigration.discountPercentage.name=Discount percentage
type.ThlConfigration.discountPercentage.description=Discount percentage

type.AbstractOrderEntry.subscriptions.name=Subscriptions
type.AbstractOrderEntry.subscriptions.description=Subscriptions
type.AbstractOrderEntry.buyerContracts.name=Buyer Contracts
type.AbstractOrderEntry.buyerContracts.description=Buyer contracts for this order position
type.AbstractOrderEntry.billingPriceCode.name=Billing Price Code
type.AbstractOrderEntry.billingPriceCode.description=Price Code used for the calculation of this entry

type.PriceRecalculationCronjob.name=PriceRecalculationCronJob
type.PriceRecalculationCronjob.description=Cron job to do price recalculations upon change in exchange rates
type.PriceRecalculationCronjob.ignoreLicenses.name=ignore licenses
type.PriceRecalculationCronjob.ignoreLicenses.description=price recalculation will not be carried out for licenses of configured licensetype

type.PspSellerAccount.name=PSP Seller Account
type.PspSellerAccount.description=A seller account at a Payment Service Provider
type.PspSellerAccount.accountId.name=AccountId
type.PspSellerAccount.accountId.description=AccountId of the seller account at Payment Service Provider
type.PspSellerAccount.user.name=User
type.PspSellerAccount.user.description=The user of seller company who initiated the onboarding process.
type.PspSellerAccount.status.name=Status
type.PspSellerAccount.status.description=A status of the seller account at Payment Service Provider
type.PspSellerAccount.billingSystemStatus.name=Billing System Status
type.PspSellerAccount.billingSystemStatus.description=Billing system synchronization status
type.PspSellerAccount.paymentProvider.name=Payment Provider
type.PspSellerAccount.paymentProvider.description=A seller account created for Payment Service Provider.
type.PspSellerAccount.company.name=Developer Company
type.PspSellerAccount.company.description=A developer company of the seller account at Payment Service Provider
type.PspSellerAccount.dpgAccounts.name=DPG Accounts
type.PspSellerAccount.dpgAccounts.description=List of seller accounts at DPG

type.CountrySyncCronJob.name=CountrySyncCronJob
type.CountrySyncCronJob.description=Cron job which syncs countries from country service

type.CompanySyncCronJob.name=CompanySyncCronJob
type.CompanySyncCronJob.description=Cron job which syncs all active companies

type.ToolCompany.name=ToolCompany
type.ToolCompany.description=Companies that are allowed to create Tool licenses
type.ToolCompany.company.name=IoT Company
type.ToolCompany.company.description=The associated IoT Company


type.PrivateOfferRequest.name=Private Offer Request
type.PrivateOfferRequest.description=Private Offer Request
type.PrivateOfferRequest.developerCompany.name=Developer Company
type.PrivateOfferRequest.developerCompany.description=Developer Company
type.PrivateOfferRequest.integratorCompany.name=Integrator Company
type.PrivateOfferRequest.integratorCompany.description=Integrator Company
type.PrivateOfferRequest.app.name=App
type.PrivateOfferRequest.app.description=App
type.PrivateOfferRequest.message.name=Message
type.PrivateOfferRequest.message.description=Customer write the message to seller for requesting offer
type.PrivateOfferRequest.messageText.name=MessageText
type.PrivateOfferRequest.messageText.description=Customer write the message to seller for requesting offer
type.PrivateOfferRequest.privateOfferRequestItems.name=Private Offer Request Item
type.PrivateOfferRequest.privateOfferRequestItems.description=Set of each license type price item
type.PrivateOfferRequest.projectRegistration.name=Private Offer Project Registration
type.PrivateOfferRequest.projectRegistration.description=Additional information about the Project subject to a private offer


type.PrivateOfferRequestItem.name=Private Offer Item
type.PrivateOfferRequestItem.description=Each item for one license type
type.PrivateOfferRequestItem.originalPrice.name=Original Price
type.PrivateOfferRequestItem.originalPrice.description=Original Price
type.PrivateOfferRequestItem.requestPrice.name=Request Price
type.PrivateOfferRequestItem.requestPrice.description=Request Price
type.PrivateOfferRequestItem.quantity.name=Quantity
type.PrivateOfferRequestItem.quantity.description=Quantity of license
type.PrivateOfferRequestItem.licenseType.name=License Type
type.PrivateOfferRequestItem.licenseType.description=Type of license
type.PrivateOfferRequestItem.privateOfferRequest.name=Private Offer Request
type.PrivateOfferRequestItem.privateOfferRequest.description=Private Offer Request

type.PrivateOfferProjectRegistration.name=Private Offer Project Registration
type.PrivateOfferProjectRegistration.description=Additional information about the Project subject to a private offer
type.PrivateOfferProjectRegistration.projectName.name=Project Name
type.PrivateOfferProjectRegistration.projectName.description=Project Name
type.PrivateOfferProjectRegistration.customerName.name=Customer Name
type.PrivateOfferProjectRegistration.customerName.description=Customer Name
type.PrivateOfferProjectRegistration.plannedStartDate.name=Planned Start Date
type.PrivateOfferProjectRegistration.plannedStartDate.description=Planned Start Date
type.PrivateOfferProjectRegistration.siteAddress.name=Site Address
type.PrivateOfferProjectRegistration.siteAddress.description=Site Address

type.PrivateOfferProjectAddress.name=Private Offer Project Address
type.PrivateOfferProjectAddress.description=Additional information about the location of the Project subject to a private offer
type.PrivateOfferProjectAddress.country.name=Country
type.PrivateOfferProjectAddress.country.description=Country
type.PrivateOfferProjectAddress.city.name=City
type.PrivateOfferProjectAddress.city.description=City
type.PrivateOfferProjectAddress.postalCode.name=Postal Code
type.PrivateOfferProjectAddress.postalCode.description=Postal Code
type.PrivateOfferProjectAddress.line1.name=Line 1
type.PrivateOfferProjectAddress.line1.description=Line 1

type.LicenseActivation.name=License Activation
type.LicenseActivation.description=Entity reflects a License's activation status for a Company
type.LicenseActivation.company.name=Company
type.LicenseActivation.company.description=The Integrator company activating the App License
type.LicenseActivation.appLicense.name=App License
type.LicenseActivation.appLicense.description=The App License to be activated
type.LicenseActivation.activationStatus.name=Activation Status
type.LicenseActivation.activationStatus.description=The license activation status

type.OldCartRemovalCronJob.name=Old Cart Removal Cronjob
type.OldCartRemovalCronJob.description= A Cron Job to clean up old carts.
type.OldCartRemovalCronJob.sites.name=Sites
type.OldCartRemovalCronJob.sites.description=Sites for which old carts will be removed 
type.OldCartRemovalCronJob.anonymousCartRemovalAge.name=Anonymous cart removal age
type.OldCartRemovalCronJob.anonymousCartRemovalAge.description=Anonymous carts older then specified number of seconds will be cleaned up
type.OldCartRemovalCronJob.cartRemovalAge.name=Cart removal age
type.OldCartRemovalCronJob.cartRemovalAge.description=Carts older then specified number of seconds will be cleaned up

type.FollowAppSubscription.name=FollowAppSubscription
type.FollowAppSubscription.description=Table to hold all follow app subscriptions.
type.FollowAppSubscription.integrator.name=Integrator
type.FollowAppSubscription.integrator.description=Integrator who subscribed to receive the change notifications.
type.FollowAppSubscription.appCode.name=AppCode
type.FollowAppSubscription.appCode.description=Code of the app whose changes the integrator wants to follow.
type.FollowAppSubscription.followAppSubscriptionStatus.name=FollowAppSubscriptionStatus
type.FollowAppSubscription.followAppSubscriptionStatus.description=Status of the Subscription. it can be Subscribed or Unsubscribed.

type.Order.ownFullAppOrder.name=OwnFullAppOrder
type.Order.ownFullAppOrder.description=set to true when integrator purchases his own company app.

type.InvoiceCreditNote.name=Credit note
type.InvoiceCreditNote.description=Credit note (Reversal or Refund) documents. 
type.InvoiceCreditNote.code.name=Code
type.InvoiceCreditNote.code.description=Internal code of the credit note
type.InvoiceCreditNote.externalId.name=External Id
type.InvoiceCreditNote.externalId.description=The id of the credit note given by the external billing system
type.InvoiceCreditNote.displayName.name=Display name
type.InvoiceCreditNote.displayName.description=User friendly name
type.InvoiceCreditNote.document.name=Document
type.InvoiceCreditNote.document.description=The actual credit note document (usually a pdf)
type.InvoiceCreditNote.originalInvoice.name=Invoice
type.InvoiceCreditNote.originalInvoice.description=The invoice for which the credit note is issued
type.InvoiceCreditNote.netAmount.name=Net Amount
type.InvoiceCreditNote.netAmount.description=Net total amount as stated on the credit note document
type.InvoiceCreditNote.taxAmount.name=Tax Amount
type.InvoiceCreditNote.taxAmount.description=Total tax amount as stated on the credit note document
type.InvoiceCreditNote.grossAmount.name=Gross Amount
type.InvoiceCreditNote.grossAmount.description=Gross total amount as stated on the credit note document
type.InvoiceCreditNote.issuanceDate.name=Issuance Date
type.InvoiceCreditNote.issuanceDate.description=Issuance Date
type.InvoiceCreditNote.type.name=Credit note type
type.InvoiceCreditNote.type.description=Credit note type

type.SelfBillingInvoiceCreditNote.name=Self Billing Invoice Credit Note
type.SelfBillingInvoiceCreditNote.description=The credit note representing the sale of apps for the developer company
type.SelfBillingInvoiceCreditNote.code.name=Code
type.SelfBillingInvoiceCreditNote.code.description=Internal code of the credit note
type.SelfBillingInvoiceCreditNote.externalId.name=External ID
type.SelfBillingInvoiceCreditNote.externalId.description=The id of the credit note given by the external billing system
type.SelfBillingInvoiceCreditNote.displayName.name=Display Name
type.SelfBillingInvoiceCreditNote.displayName.description=The name of the credit note displayed to the user
type.SelfBillingInvoiceCreditNote.document.name=Document
type.SelfBillingInvoiceCreditNote.document.description=The actual credit note document (usually a pdf)
type.SelfBillingInvoiceCreditNote.originalSelfBillingInvoice.name=Self Billing Invoice
type.SelfBillingInvoiceCreditNote.originalSelfBillingInvoice.description=The Self Billing Invoice for which the credit note is issued
type.SelfBillingInvoiceCreditNote.marketplaceShare.name=Marketplace Share
type.SelfBillingInvoiceCreditNote.marketplaceShare.description=Marketplace share as stated on the credit note (the amount the Azena marketplace charges the seller)
type.SelfBillingInvoiceCreditNote.sellerShare.name=Seller Share
type.SelfBillingInvoiceCreditNote.sellerShare.description=Seller share as stated on the credit note (this value is usually negative since the Azena marketplace credits this amount to the seller)
type.SelfBillingInvoiceCreditNote.marketplaceTaxAmount.name=Marketplace Tax Amount
type.SelfBillingInvoiceCreditNote.marketplaceTaxAmount.description=Tax amount charged by the marketplace to the seller (this value is usually negative since the Azena marketplace credits this amount to the seller)
type.SelfBillingInvoiceCreditNote.creditNotes.name=Credit notes
type.SelfBillingInvoiceCreditNote.creditNotes.description=Credit notes issued for the self billing invoice
type.SelfBillingInvoiceCreditNote.issuanceDate.name=Issuance Date
type.SelfBillingInvoiceCreditNote.issuanceDate.description=Issuance Date
type.SelfBillingInvoiceCreditNote.type.name=Credit note type
type.SelfBillingInvoiceCreditNote.type.description=Credit note type


type.CronjobConfiguration.name=CronjobConfiguration
type.CronjobConfiguration.description=Cronjob Configuration
type.CronjobConfiguration.code.name=code
type.CronjobConfiguration.code.description= code
type.CronjobConfiguration.baseStore.name=base store
type.CronjobConfiguration.baseStore.description=cronjobs base store
type.CronjobConfiguration.productCatalogVersion.name=cronjobs product catalog version
type.CronjobConfiguration.productCatalogVersion.description=cronjobs product catalog version

type.CronJob.configuration.name=configuration
type.CronJob.configuration.description=configuration of the cronjob 

type.BaseStore.defaultTerminationRule.name=Default Termination Rule
type.BaseStore.defaultTerminationRule.description=Termination Rule applied to subscriptions in the context of this store

type.ContractTerminationRule.name=Contract Termination Rule
type.ContractTerminationRule.description=Rules for termination of a contract
type.ContractTerminationRule.code.name=Code
type.ContractTerminationRule.code.description=Unique Identifier
type.ContractTerminationRule.noticePeriod.name=Notice Period
type.ContractTerminationRule.noticePeriod.description=Latest point in time before end of the current contract period at which a contract cancellation is effective for the end of the current period
type.ContractTerminationRule.initialPeriod.name=Initial Period
type.ContractTerminationRule.initialPeriod.description=Duration of the initial contract period
type.ContractTerminationRule.followUpPeriod.name=Follow-Up Period
type.ContractTerminationRule.followUpPeriod.description=Duration of all contract periods following the initial contract period
type.ContractTerminationRule.gracePeriod.name=Grace Period
type.ContractTerminationRule.gracePeriod.description=Point in time after a contract period at which the associated technical license expires
type.ContractTerminationRule.fixedPeriod.name=Fixed Period
type.ContractTerminationRule.fixedPeriod.description=Duration of the fixed contract period

type.TerminationRulePeriod.name=Termination Rule Period
type.TerminationRulePeriod.description=Period for use in Termination Rules
type.TerminationRulePeriod.code.name=Code
type.TerminationRulePeriod.code.description=Unique Identifier
type.TerminationRulePeriod.value.name=Value
type.TerminationRulePeriod.value.description=Value of this Period
type.TerminationRulePeriod.unit.name=Unit
type.TerminationRulePeriod.unit.description=Unit of this Period

type.BlockedApkSignerCertSubjectCName.name=Blocked APK Signer Certificate Subject CN
type.BlockedApkSignerCertSubjectCName.description=Blocked Subject Common Name for APK signature validation
type.BlockedApkSignerCertSubjectCName.cnValue.name=CN value
type.BlockedApkSignerCertSubjectCName.cnValue.description=Common Name value

type.ClassificationAttributeValue.termBoost.name=Term Boost Value
type.ClassificationAttributeValue.termBoost.description=Value is used to boot the Classification Attribute Value in the search result
type.ClassificationAttributeValue.icon.name=Icon
type.ClassificationAttributeValue.icon.description=Icon representing the attribute value

type.PostSubscriptionPriceUpdateEvent.name=PostSubscriptionPriceUpdateEvent Event
type.PostSubscriptionPriceUpdateEvent.description=Events created as a result of an update in a license's prices
type.PostSubscriptionPriceUpdateEvent.eventId.name=EventId
type.PostSubscriptionPriceUpdateEvent.eventId.description=Unique identifier of the event
type.PostSubscriptionPriceUpdateEvent.appLicense.name=AppLicense
type.PostSubscriptionPriceUpdateEvent.appLicense.description=App license subject to price change
type.PostSubscriptionPriceUpdateEvent.eventStatus.name=EventStatus
type.PostSubscriptionPriceUpdateEvent.eventStatus.description=Status of the event

type.CountryStoreConfiguration.name=CountryStoreConfiguration
type.CountryStoreConfiguration.description=Country specifc base store configuration.
type.CountryStoreConfiguration.country.name=Country
type.CountryStoreConfiguration.country.description=country of the configuration.
type.CountryStoreConfiguration.baseStore.name=BaseStore
type.CountryStoreConfiguration.baseStore.description=base store of the configuration.
type.CountryStoreConfiguration.languages.name=Languages
type.CountryStoreConfiguration.languages.description=supported languages in the country for the base store.
type.CountryStoreConfiguration.defaultLanguage.name=defaultLanguage
type.CountryStoreConfiguration.defaultLanguage.description=default language in the country for the base store.
type.CountryStoreConfiguration.storefrontEnabled.name=storefrontEnabled
type.CountryStoreConfiguration.storefrontEnabled.description=define if a country is enabled for the tenant.
type.CountryStoreConfiguration.importedCompaniesCanBuy.name=importedCompaniesCanBuy
type.CountryStoreConfiguration.importedCompaniesCanBuy.description=define if imported companies can make purchases

type.BuyerContract.name=Buyer contract
type.BuyerContract.description=Buyer contract
type.BuyerContract.code.name=Code
type.BuyerContract.code.description=Code to uniquely identify this contract
type.BuyerContract.startDate.name=Start date
type.BuyerContract.startDate.description=Start date of the contract period
type.BuyerContract.endDate.name=End date
type.BuyerContract.endDate.description=End date of the contract period
type.BuyerContract.orderEntry.name=Order entry
type.BuyerContract.orderEntry.description=Order entry for this contract
type.BuyerContract.contractTerminationRule.name=Contract Termination Rule
type.BuyerContract.contractTerminationRule.description=Termination Rule for this contract
type.BuyerContract.timezone.name=Time zone
type.BuyerContract.timezone.description=Time zone for the contract start and end dates.
type.BuyerContract.billingPriceCode.name=Billing Price Code
type.BuyerContract.billingPriceCode.description=Price code currently used for billing of this contract


type.FixedTermContract.name=Fixed contract
type.FixedTermContract.description=Buyer contract with fixed duration

type.SubscriptionContract.name=Subscription contract
type.SubscriptionContract.description=Buyer contract
type.SubscriptionContract.legacySubscriptionId.name=UUID
type.SubscriptionContract.legacySubscriptionId.description=UUID

type.Runtime.name=Runtime
type.Runtime.description=Runtime
type.Runtime.code.name=Code
type.Runtime.code.description=Unique identifier of the Runtime
type.Runtime.name.name=Name
type.Runtime.name.description=Localized name of the Runtime
type.Runtime.description.name=Description
type.Runtime.description.description=Runtime description
type.Runtime.defaultRuntimeTerminationRule.name=Default Termination Rule
type.Runtime.defaultRuntimeTerminationRule.description=Termination Rule applied to licenses with this runtime

type.VariantProduct.runtime.name=Variant Product runtime
type.VariantProduct.runtime.description=Variant Product runtime
type.VariantProduct.bundleInfo.name=Variant Product bundle info
type.VariantProduct.bundleInfo.description=Variant Product bundle info

type.Category.iconCode.name=Category icon code
type.Category.iconCode.description=Category icon code

type.BundleInfo.name=BundleInfo
type.BundleInfo.description=Bundle Info of the Product Variant
type.BundleInfo.code.name=Code
type.BundleInfo.code.description=Unique identifier of the Bundle Info
type.BundleInfo.name.name=Name
type.BundleInfo.name.description=Name of the Bundle Info
type.BundleInfo.size.name=Size
type.BundleInfo.size.description=Size of the bundle

type.CatalogUnawareMediaContainer.name=CatalogUnawareMediaContainer
type.CatalogUnawareMediaContainer.description=Catalog unaware Media Container

type.EulaAcceptance.name=EULA Acceptance
type.EulaAcceptance.description=Records the user's acceptance of the order items' EULA(s) on order checkout
type.EulaAcceptance.user.name=User
type.EulaAcceptance.user.description=User that accepted the EULA(s)
type.EulaAcceptance.acceptanceTimestamp.name=EULA Acceptance timestamp
type.EulaAcceptance.acceptanceTimestamp.description=EULA Acceptance timestamp
type.EulaAcceptance.eulaContainers.name=EULA(s)
type.EulaAcceptance.eulaContainers.description=EULA(s) that were accepted
type.EulaAcceptance.acceptedEulasUrls.name=EULA(s) URLs
type.EulaAcceptance.acceptedEulasUrls.description=URLs of the EULA(s) that were accepted

type.AaDistributorCompany.name=AA Distributor Company
type.AaDistributorCompany.description=AA Distributor Company
type.AaDistributorCompany.umpId.name=UMP ID
type.AaDistributorCompany.umpId.description=UMP Distributor ID
type.AaDistributorCompany.companyName.name=Company Name
type.AaDistributorCompany.companyName.description=Company Name of the distributor
type.AaDistributorCompany.countries.name=Countries
type.AaDistributorCompany.countries.description=Countries for which this distributor is active
type.AaDistributorCompany.aaExternalId.name=AA external ID
type.AaDistributorCompany.aaExternalId.description=AA external ID

type.PaymentInfoDraft.name=Payment Info Draft
type.PaymentInfoDraft.description=Payment Info Draft
type.PaymentInfoDraft.code.name=Code
type.PaymentInfoDraft.code.description=Internal code of the Payment Info Draft
type.PaymentInfoDraft.integrator.name=Integrator
type.PaymentInfoDraft.integrator.description=Owner of the Payment Info Draft
type.PaymentInfoDraft.creationStatus.name=Creation Status
type.PaymentInfoDraft.creationStatus.description=Creation Status. SUCCEEDED drafts will result in a Payment Info
type.PaymentInfoDraft.paymentProvider.name=Payment Provider
type.PaymentInfoDraft.paymentProvider.description=Payment Provider
type.PaymentInfoDraft.paymentMethodType.name=Payment Method Type
type.PaymentInfoDraft.paymentMethodType.description=Payment Method Type
type.PaymentInfoDraft.resultingPaymentInfo.name=Resulting Payment Info
type.PaymentInfoDraft.resultingPaymentInfo.description=Payment Info created from this draft

type.CountryMigrationConfiguration.name=Country Migration Configuration
type.CountryMigrationConfiguration.description=Configuration for a country to migrate
type.CountryMigrationConfiguration.country.name=Migration country
type.CountryMigrationConfiguration.country.description=Country to migrate
type.CountryMigrationConfiguration.contractStartDate.name=Contract start date
type.CountryMigrationConfiguration.contractStartDate.description=Start date for contracts to migrate
type.CountryMigrationConfiguration.sepaMandateCreationDueDate.name=SEPA DD mandate creation due date
type.CountryMigrationConfiguration.sepaMandateCreationDueDate.description=Due date for the creation of a SEPA DD Payment Info to be used for the migration
type.CountryMigrationConfiguration.firstMigrationNoticeDate.name=First notice date
type.CountryMigrationConfiguration.firstMigrationNoticeDate.description=Date when customers were first informed about an impending contract migration
type.CountryMigrationConfiguration.purchaseAllowedFromDate.name=Purchase allowed from date
type.CountryMigrationConfiguration.purchaseAllowedFromDate.description=Date from which purchases are allowed in the migrated country
type.CountryMigrationConfiguration.lmpEndDateForFutureDatedContracts.name=Non-active contract end date
type.CountryMigrationConfiguration.lmpEndDateForFutureDatedContracts.description=End date for cancelled non-active UPM contracts
type.CountryMigrationConfiguration.sepaDDRecommended.name=SEPA Direct Debit Recommended
type.CountryMigrationConfiguration.sepaDDRecommended.description=Determines whether SEPA Direct Debit should be recommended as a preferred payment method for customers
type.CountryMigrationConfiguration.allowPurchasesWithoutMigratedContracts.name=allowPurchasesWithoutMigratedContracts
type.CountryMigrationConfiguration.allowPurchasesWithoutMigratedContracts.description=define if purchases are allowed for companies without migrated contracts

type.OpenOrderCancelBusinessProcess.name=Open Order Cancel Business Process
type.OpenOrderCancelBusinessProcess.description=Process for handling the cancellation of full order with OPEN status.
type.OpenOrderCancelBusinessProcess.order.name=Order
type.OpenOrderCancelBusinessProcess.order.description=Order

type.Order.openOrderProcesses.name=OpenOrderProcesses
type.Order.openOrderProcesses.description=Open Order Cancel business processes of the Order.

type.TokenizedSepaDirectDebitPaymentInfo.name=Tokenized SEPA Direct Debit Payment Info
type.TokenizedSepaDirectDebitPaymentInfo.description=Payment information for Tokenized SEPA Direct Debit payments
type.TokenizedSepaDirectDebitPaymentInfo.shopperReference.name=Shopper Reference
type.TokenizedSepaDirectDebitPaymentInfo.shopperReference.description=Shopper Reference. Can be either a companyUid or a userUid, depends on the scope of paymentInfo.
type.TokenizedSepaDirectDebitPaymentInfo.recurringReference.name=Recurring Reference
type.TokenizedSepaDirectDebitPaymentInfo.recurringReference.description=Reference for recurring payments
