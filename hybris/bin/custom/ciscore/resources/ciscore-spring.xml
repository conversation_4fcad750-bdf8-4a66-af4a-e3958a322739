<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:util="http://www.springframework.org/schema/util" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/tx
           http://www.springframework.org/schema/tx/spring-tx.xsd
           http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd http://www.springframework.org/schema/util https://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.sast.cis.core"/>

    <tx:annotation-driven transaction-manager="txManager"/>

    <task:annotation-driven executor="tenantAwareTaskExecutor"/>

    <bean id="tenantAwareThreadFactory" class="de.hybris.platform.core.TenantAwareThreadFactory">
        <constructor-arg name="tenant" ref="tenantFactory"/>
    </bean>

    <bean id="tenantAwareTaskExecutor" class="com.sast.cis.core.service.security.TenantAwareTaskExecutorFactory"/>

    <bean id="abstractModelConverter" class="com.sast.cis.core.converter.appediting.AbstractModelConverter" abstract="true"/>


    <bean id="ciscoreSystemSetup" class="com.sast.cis.core.setup.CiscoreSystemSetup">
        <!-- constructor arg injection example -->
        <constructor-arg ref="ciscoreService"/>
    </bean>

    <bean id="ciscoreService" class="com.sast.cis.core.service.impl.DefaultCiscoreService">
        <!-- setter injection example -->
        <property name="modelService" ref="modelService"/>
        <property name="mediaService" ref="mediaService"/>
        <property name="flexibleSearchService" ref="flexibleSearchService"/>
    </bean>

    <alias name="cisStockConverter" alias="stockConverter"/>
    <bean id="cisStockConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="de.hybris.platform.commercefacades.product.data.StockData"/>
        <property name="populators">
            <list>
                <ref bean="cisStockPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisCartValidationStrategy" alias="cartValidationStrategy"/>
    <bean id="cisCartValidationStrategy" class="com.sast.cis.core.strategy.cart.CisCartValidationStrategy"
          parent="defaultCartValidationStrategy"/>

    <bean id="apkMetaDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.ApkMetaData"/>
        <property name="populators">
            <list>
                <ref bean="apkMetaDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="pdfDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.PdfData"/>
        <property name="populators">
            <list>
                <ref bean="pdfDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="industryConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.IndustryData"/>
        <property name="populators">
            <list>
                <ref bean="industryPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appVideoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AppVideoData"/>
        <property name="populators">
            <list>
                <ref bean="appVideoDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="devCountryConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.DevCountryData"/>
        <property name="populators">
            <list>
                <ref bean="devCountryPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="shopProductBasicPopulatorList" alias="shopProductBasicPopulatorList"/>
    <bean id="shopProductBasicPopulatorList" parent="abstractPopulatorList">
        <property name="populators">
            <list>
                <ref bean="productBasicPopulator"/>
                <ref bean="shopAppVersionToProductDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="cisIotCompanyDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.IotCompanyData"/>
        <property name="populators">
            <list>
                <ref bean="cisIotCompanyDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="cisProductOverviewConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.ProductContainerOverviewData"/>
        <property name="populators">
            <list>
                <ref bean="productContainerPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="volumeDiscountConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.VolumeDiscountModel"/>
        <property name="populators">
            <list>
                <ref bean="volumeDiscountPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="volumeDiscountDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.VolumeDiscountData"/>
        <property name="populators">
            <list>
                <ref bean="volumeDiscountDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="volumeDiscountDraftConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.VolumeDiscountDraftModel"/>
        <property name="populators">
            <list>
                <ref bean="volumeDiscountDraftPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="volumeDiscountDraftDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.VolumeDiscountData"/>
        <property name="populators">
            <list>
                <ref bean="volumeDiscountDraftDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appVersionDraftMetaDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AppVersionMetaData"/>
        <property name="populators">
            <list>
                <ref bean="appVersionDraftMetaDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appVersionMetaDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AppVersionMetaData"/>
        <property name="populators">
            <list>
                <ref bean="appVersionMetaDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="draftStoreContentConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.StoreContentData"/>
        <property name="populators">
            <list>
                <ref bean="productContainerRelatedDataPopulator"/>
                <ref bean="draftStoreContentPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appStoreContentConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.StoreContentData"/>
        <property name="populators">
            <list>
                <ref bean="productContainerRelatedDataPopulator"/>
                <ref bean="appStoreContentPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appIntegrationDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AppIntegrationData"/>
        <property name="populators">
            <list>
                <ref bean="appIntegration2AppIntegrationDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="standardAppIntegrationDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AppIntegrationData"/>
        <property name="populators">
            <list>
                <ref bean="standardAppIntegration2AppIntegrationDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appIntegrationData2AppIntegrationConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.AppIntegrationModel"/>
        <property name="populators">
            <list>
                <ref bean="appIntegrationData2AppIntegrationPopulator"/>
                <ref bean="customAppIntegrationData2AppIntegrationPopulator"/>
                <ref bean="standardAppIntegrationData2AppIntegrationPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="publicAppIntegrationDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.PublicAppIntegrationData"/>
        <property name="populators">
            <list>
                <ref bean="appIntegration2PublicAppIntegrationDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="eula2EulaDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.EulaData"/>
        <property name="populators">
            <list>
                <ref bean="eula2EulaDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="eulaData2EulaConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.EulaModel"/>
        <property name="populators">
            <list>
                <ref bean="eulaData2EulaPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="countryEulaDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.CountryEulaData"/>
        <property name="populators">
            <list>
                <ref bean="countryEulaDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appAvailabilityConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AppAvailabilityData"/>
        <property name="populators">
            <list>
                <ref bean="productContainer2AvailabilityDataPopulator"/>
                <ref bean="draftContainer2AppAvailabilityDataPopulator"/>
                <ref bean="productContainerRelatedDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appPricesConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AppPricesData"/>
        <property name="populators">
            <list>
                <ref bean="productContainerRelatedDataPopulator"/>
                <ref bean="productContainer2AppPricesDataPopulator"/>
                <ref bean="draftContainer2AppPricesDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="licensePriceDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.LicensePriceData"/>
        <property name="populators">
            <list>
                <ref bean="appLicense2LicensePriceDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="licenseDraftPriceDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.LicensePriceData"/>
        <property name="populators">
            <list>
                <ref bean="appLicenseDraft2LicensePriceDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="licenseAvailabilityDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.LicenseAvailabilityData"/>
        <property name="populators">
            <list>
                <ref bean="appLicense2LicenseAvailabilityDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="licenseDraftAvailabilityDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.LicenseAvailabilityData"/>
        <property name="populators">
            <list>
                <ref bean="appLicenseDraft2LicenseAvailabilityDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="permittedBuyerDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.PermittedBuyerData"/>
        <property name="populators">
            <list>
                <ref bean="company2PermittedBuyerDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="navigationItemConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.NavigationItemData"/>
        <property name="populators">
            <list>
                <ref bean="navigationItemPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appVersionsOverviewConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.VersionsOverviewData"/>
        <property name="populators">
            <list>
                <ref bean="productContainerRelatedDataPopulator"/>
                <ref bean="appVersionsOverviewPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="selfBillingInvoiceConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.SelfBillingInvoiceData"/>
        <property name="populators">
            <list>
                <ref bean="selfBillingInvoicePopulator"/>
            </list>
        </property>
    </bean>

    <bean id="priceToPriceRowModelConverter" parent="abstractModelConverter">
        <property name="targetClass" value="de.hybris.platform.europe1.model.PriceRowModel"/>
        <property name="populators">
            <list>
                <ref bean="priceToPriceRowModelPopulator"/>
            </list>
        </property>
    </bean>

    <bean parent="modifyPopulatorList">
        <property name="list" ref="defaultOrderEntryProductConverter"/>
        <property name="add" ref="productIconPopulator"/>
    </bean>

    <bean id="appCodePrepareInterceptorMapping"
          class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="appCodePrepareInterceptor"/>
        <property name="typeCode" value="App"/>
    </bean>

    <bean id="appLicenseCodePrepareInterceptorMapping"
          class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="appLicenseCodePrepareInterceptor"/>
        <property name="typeCode" value="AppLicense"/>
        <property name="order" value="1"/> <!-- must be called before other interceptors to set the code -->
    </bean>

    <bean id="createUserInterceptorMapping"
          class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="userCreateInterceptor"/>
        <property name="typeCode" value="IoTCustomer"/>
    </bean>

    <bean id="cartCodeGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${keygen.cart.code.name}"/>
        <property name="digits" value="${keygen.cart.code.digits}"/>
        <property name="start" value="${keygen.cart.code.start}"/>
        <property name="type" value="${keygen.cart.code.type}"/>
        <property name="template" value="${keygen.cart.code.template}"/>
    </bean>

    <bean id="paymentTransactionCodeGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${keygen.transaction.code.name}"/>
        <property name="digits" value="${keygen.transaction.code.digits}"/>
        <property name="start" value="${keygen.transaction.code.start}"/>
        <property name="type" value="${keygen.transaction.code.type}"/>
        <property name="template" value="${keygen.transaction.code.template}"/>
    </bean>

    <bean id="appCodeGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${keygen.app.code.name}"/>
        <property name="digits" value="${keygen.app.code.digits}"/>
        <property name="start" value="${keygen.app.code.start}"/>
        <property name="type" value="${keygen.app.code.type}"/>
        <property name="template" value="${keygen.app.code.template}"/>
    </bean>

    <bean id="priceDraftCodeGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${keygen.pricedraft.code.name}"/>
        <property name="digits" value="${keygen.pricedraft.code.digits}"/>
        <property name="start" value="${keygen.pricedraft.code.start}"/>
        <property name="type" value="${keygen.pricedraft.code.type}"/>
        <property name="template" value="${keygen.pricedraft.code.template}"/>
    </bean>

    <bean id="productContainerCodeGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${keygen.productContainer.code.name}"/>
        <property name="digits" value="${keygen.productContainer.code.digits}"/>
        <property name="start" value="${keygen.productContainer.code.start}"/>
        <property name="type" value="${keygen.productContainer.code.type}"/>
        <property name="template" value="${keygen.productContainer.code.template}"/>
    </bean>

    <bean id="appVersionDraftCodeGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${keygen.appVersionDraft.code.name}"/>
        <property name="digits" value="${keygen.appVersionDraft.code.digits}"/>
        <property name="start" value="${keygen.appVersionDraft.code.start}"/>
        <property name="type" value="${keygen.appVersionDraft.code.type}"/>
        <property name="template" value="${keygen.appVersionDraft.code.template}"/>
    </bean>

    <bean id="buyerContractCodeGenerator" class="de.hybris.platform.servicelayer.keygenerator.impl.PersistentKeyGenerator">
        <property name="key" value="${keygen.buyerContract.code.name}"/>
        <property name="digits" value="${keygen.buyerContract.code.digits}"/>
        <property name="start" value="${keygen.buyerContract.code.start}"/>
        <property name="type" value="${keygen.buyerContract.code.type}"/>
        <property name="template" value="${keygen.buyerContract.code.template}"/>
    </bean>

    <bean id="s3ApkMediaStorageStrategy" class="com.sast.cis.core.strategy.media.S3ApkMediaStorageStrategy">
        <property name="locationHashService" ref="mediaLocationHashService"/>
        <property name="s3StorageServiceFactory" ref="s3StorageServiceFactory"/>
        <property name="mediaHeadersRegistry" ref="mediaHeadersRegistry"/>
    </bean>

    <bean id="s3StaticMediaUrlStrategy" class="com.sast.cis.core.strategy.media.S3StaticMediaURLStrategy">
        <constructor-arg name="s3StorageServiceFactory" ref="s3StorageServiceFactory"/>
    </bean>

    <bean id="localMediaUrlStrategy" class="com.sast.cis.core.strategy.media.LocalMediaUrlStrategy">
        <constructor-arg name="modelService" ref="modelService"/>
    </bean>

    <alias name="cisSearchResultProductPopulator" alias="commerceSearchResultProductPopulator"/>
    <bean id="cisSearchResultProductPopulator" class="com.sast.cis.core.converter.CisSearchResultProductPopulator"
          parent="defaultCommerceSearchResultProductPopulator">
        <property name="imageFormatMapping" ref="cisImageFormatMapping"/>
    </bean>

    <alias name="cisSearchResponseFacetsPopulator" alias="commerceSearchResponseFacetsPopulator"/>
    <bean id="cisSearchResponseFacetsPopulator" class="com.sast.cis.core.converter.CisSearchResponseFacetsPopulator"
          parent="defaultCommerceSearchResponseFacetsPopulator">
    </bean>

    <alias name="cisCommerceSearchResultProductConverter" alias="commerceSearchResultProductConverter"/>
    <bean id="cisCommerceSearchResultProductConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="de.hybris.platform.commercefacades.product.data.ProductData"/>
        <property name="populators">
            <list>
                <ref bean="cisSearchResultCompanyDataPopulator"/>
                <ref bean="cisSearchResultProductPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisPriceDataFactory" alias="priceDataFactory"/>
    <bean id="cisPriceDataFactory" class="com.sast.cis.core.factory.CisPriceDataFactory" parent="defaultPriceDataFactory"/>

    <bean id="appUrlValueResolver" class="com.sast.cis.core.resolver.AppUrlValueResolver" parent="productUrlsValueResolver"/>

    <bean id="sastClientHttpRequestFactory"
          class="org.springframework.http.client.HttpComponentsClientHttpRequestFactory">
        <property name="connectTimeout" value="${resttemplate.connectiontimeout}"/>
        <property name="readTimeout" value="${resttemplate.readtimeout}"/>
    </bean>

    <bean id="avscannerClientHttpRestFactory"
          class="org.springframework.http.client.HttpComponentsClientHttpRequestFactory">
        <property name="connectTimeout" value="${avscanner.client.connectiontimeout}"/>
        <property name="readTimeout" value="${avscanner.client.readtimeout}"/>
    </bean>

    <alias name="cmsDataImportService" alias="sampleDataImportService"/>
    <bean id="cmsDataImportService" class="com.sast.cis.core.service.CmsDataImportService"
          parent="defaultSampleDataImportService">
    </bean>

    <bean id="productImportService" class="com.sast.cis.core.service.ProductImportService"
          parent="defaultSampleDataImportService">
    </bean>

    <alias name="cisCommercePlaceOrderStrategy"
           alias="commercePlaceOrderStrategy"/>
    <bean id="cisCommercePlaceOrderStrategy"
          class="com.sast.cis.core.strategy.CisCommercePlaceOrderStrategy"
          parent="defaultCommercePlaceOrderStrategy"/>

    <bean id="productScreenshotPopulatorList" parent="abstractPopulatorList">
        <property name="populators">
            <list>
                <ref bean="productScreenshotPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisConfiguredProductPopulator" alias="productDataConfiguredPopulator"/>
    <bean id="cisConfiguredProductPopulator" class="de.hybris.platform.converters.impl.DefaultModifableConfigurablePopulator">
        <property name="populators">
            <map key-type="com.sast.cis.core.enums.ProductDataOption">
                <entry key="CHANGELOGS" value-ref="appVersionChangelogPopulator"/>
                <entry key="PRICE" value-ref="productPricePopulatorList"/>
                <entry key="GALLERY" value-ref="productScreenshotPopulatorList"/>
                <entry key="BASIC" value-ref="shopProductBasicPopulatorList"/>
                <entry key="REVIEW" value-ref="productReviewPopulatorList"/>
                <entry key="PDFS" value-ref="productPdfsPopulatorList"/>
                <entry key="URL" value-ref="productUrlPopulator"/>
                <entry key="ACQUISITION_COUNT" value-ref="productAcquisitionCountPopulator"/>
                <entry key="LICENSES" value-ref="productLicensesPopulator"/>
                <entry key="LICENSES_WITHOUT_PRICES" value-ref="productLicensesWithoutPricesPopulator"/>
                <entry key="SPECIAL_OFFER" value-ref="productSpecialOfferPopulator"/>
            </map>
        </property>
    </bean>

    <bean id="gridItemConfiguredPopulator" class="de.hybris.platform.converters.impl.DefaultModifableConfigurablePopulator">
        <property name="populators">
            <map key-type="com.sast.cis.core.enums.CategoryPageOption">
                <entry key="BASIC" value-ref="simpleProductDataPopulator"/>
            </map>
        </property>
    </bean>

    <alias name="cisFacetSearchQueryFilterQueriesPopulator" alias="facetSearchQueryFilterQueriesPopulator"/>
    <bean id="cisFacetSearchQueryFilterQueriesPopulator"
          class="com.sast.cis.core.converter.product.CisFacetSearchQueryFilterQueriesPopulator"
          parent="abstractFacetSearchQueryPopulator"/>

    <alias name="defaultProductReviewPopulatorList" alias="productReviewPopulatorList"/>
    <bean id="defaultProductReviewPopulatorList" parent="abstractPopulatorList">
        <property name="populators">
            <list>
                <ref bean="cisProductReviewsPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="productLicenseNoPricePermissionPopulatorList" parent="abstractPopulatorList">
        <property name="populators">
            <list>
                <ref bean="productLicensesWithoutPricesPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisProductReviewsPopulator" alias="cisProductReviewsPopulator"/>
    <bean id="cisProductReviewsPopulator" parent="baseProductPopulator"
          class="com.sast.cis.core.converter.review.CisProductReviewsPopulator">
        <property name="customerReviewConverter" ref="customerReviewConverter"/>
        <property name="customerReviewService" ref="customerReviewService"/>
        <property name="commonI18NService" ref="commonI18NService"/>
    </bean>

    <alias name="defaultProductPdfsPopulatorList" alias="productPdfsPopulatorList"/>
    <bean id="defaultProductPdfsPopulatorList" parent="abstractPopulatorList">
        <property name="populators">
            <list>
                <ref bean="productPdfsPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="useCaseConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.UseCaseData"/>
        <property name="populators">
            <list>
                <ref bean="useCasePopulator"/>
            </list>
        </property>
    </bean>

    <bean id="shopProductVariantFacade" class="com.sast.cis.core.facade.ShopProductVariantFacade" parent="defaultProductVariantFacade">
        <property name="productConverter" ref="shopProductConverter"/>
        <property name="productDataConfiguredPopulator" ref="productDataConfiguredPopulator"/>
    </bean>

    <bean id="shopProductConverter" parent="cisProductConverter">
        <property name="populators">
            <list merge="true">
                <ref bean="productIconPopulator"/>
                <ref bean="productPermissionsPopulator"/>
                <ref bean="productDeviceCapabilityPopulator"/>
                <ref bean="contactDataPopulator"/>
                <ref bean="ownCompanyAppPermissionPopulator"/>
                <ref bean="integratorCountryBlockedInDeveloperCountryPopulator"/>
                <ref bean="productCountryEulasPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisProductConverter" alias="productConverter"/>
    <bean id="cisProductConverter" parent="defaultProductConverter">
        <property name="populators">
            <list merge="true">
                <ref bean="appNameAndDescriptionPopulator"/>
                <ref bean="followAppDataConverter"/>
            </list>
        </property>
    </bean>

    <alias name="cisProductBasicPopulator" alias="productBasicPopulator"/>
    <bean id="cisProductBasicPopulator" parent="baseProductPopulator"
          class="com.sast.cis.core.converter.CisProductBasicPopulator">
    </bean>

    <bean parent="modifyPopulatorList">
        <property name="list" ref="customerConverter"/>
        <property name="add" ref="cisCustomerPopulator"/>
    </bean>

    <bean id="removeDataOnSetURLMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="cisRemoveDataOnSetURLPrepareInterceptor"/>
        <property name="typeCode" value="Media"/>
    </bean>

    <bean id="mediaModelDefaultsPrepareInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="cisMediaModelPrepareInterceptor"/>
        <property name="typeCode" value="Media"/>
    </bean>

    <bean id="logAddressRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logAddressRemovalInterceptor"/>
        <property name="typeCode" value="Address"/>
    </bean>

    <bean id="logApkMediaRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logApkMediaRemovalInterceptor"/>
        <property name="typeCode" value="ApkMedia"/>
    </bean>

    <bean id="logAppDraftRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logAppDraftRemovalInterceptor"/>
        <property name="typeCode" value="AppDraft"/>
    </bean>

    <bean id="logAppVersionDraftRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logAppVersionDraftRemovalInterceptor"/>
        <property name="typeCode" value="AppVersionDraft"/>
    </bean>

    <bean id="logAppVersionRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logAppVersionRemovalInterceptor"/>
        <property name="typeCode" value="AppVersion"/>
    </bean>

    <bean id="logPaymentInfoRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logPaymentInfoRemovalInterceptor"/>
        <property name="typeCode" value="PaymentInfo"/>
    </bean>

    <bean id="logProductRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logProductRemovalInterceptor"/>
        <property name="typeCode" value="Product"/>
    </bean>

    <bean id="logProductContainerRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logProductContainerRemovalInterceptor"/>
        <property name="typeCode" value="ProductContainer"/>
    </bean>

    <bean id="logCountryRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logCountryRemovalInterceptor"/>
        <property name="typeCode" value="Country"/>
    </bean>

    <bean id="logSelfBillingInvoiceRemovalInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="logSelfBillingInvoiceRemovalInterceptor"/>
        <property name="typeCode" value="SelfBillingInvoice"/>
    </bean>

    <bean id="detailPageDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.DetailProductData"/>
        <property name="populators">
            <list>
                <ref bean="detailProductDataPopulator"/>
                <ref bean="detailProductDataGalleryPopulator"/>
                <ref bean="detailProductDataLicensePopulator"/>
                <ref bean="detailProductDataVersionPopulator"/>
                <ref bean="detailProductDataFollowAppPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appLicenseValidateInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="appLicenseValidateInterceptor"/>
        <property name="typeCode" value="AppLicense"/>
    </bean>

    <bean id="packageNameConsistencyValidateInterceptor" class="com.sast.cis.core.interceptor.PackageNameConsistencyValidateInterceptor"/>
    <bean id="packageNameConsistencyValidateInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="packageNameConsistencyValidateInterceptor"/>
        <property name="typeCode" value="AppVersion"/>
    </bean>

    <bean id="appVersionsConsistencyValidateInterceptor" class="com.sast.cis.core.interceptor.AppVersionsConsistencyValidateInterceptor"/>
    <bean id="appVersionsConsistencyValidateInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="appVersionsConsistencyValidateInterceptor"/>
        <property name="typeCode" value="AppVersion"/>
    </bean>

    <bean id="activeCountryCurrencyValidateInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="activeCountryCurrencyValidateInterceptor"/>
        <property name="typeCode" value="Country"/>
    </bean>

    <bean id="stripeConnectAccountConsistencyInterceptorMapping"
          class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="stripeConnectAccountConsistencyInterceptor"/>
        <property name="typeCode" value="StripeConnectAccount"/>
    </bean>

    <bean id="buyerContractPrepareInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="buyerContractPrepareInterceptor"/>
        <property name="typeCode" value="BuyerContract"/>
    </bean>

    <bean id="fixedTermContractPrepareInterceptorMapping" class="de.hybris.platform.servicelayer.interceptor.impl.InterceptorMapping">
        <property name="interceptor" ref="fixedTermContractPrepareInterceptor"/>
        <property name="typeCode" value="FixedTermContract"/>
        <property name="replacedInterceptors" ref="buyerContractPrepareInterceptor"/>
    </bean>

    <bean id="getAcquisitionsPerAppLicenseJobPerformable" class="com.sast.cis.core.job.GetAcquisitionsPerAppLicenseJobPerformable"
          parent="abstractJobPerformable"/>
    <bean id="exportProductsJobPerformable" class="com.sast.cis.core.billingintegration.job.ExportProductsJobPerformable"
          parent="abstractJobPerformable"/>
    <bean id="priceRecalculationJobPerformable" class="com.sast.cis.core.job.PriceRecalculationJobPerformable"
          parent="abstractJobPerformable"/>
    <bean id="removeOldCronJobHistoryEntriesJobPerformable"
          class="com.sast.cis.core.job.maintenance.RemoveOldCronJobHistoryEntriesJobPerformable"
          parent="abstractJobPerformable"/>
    <bean id="orderStatusOverdueJobPerformable"
          class="com.sast.cis.core.job.OrderStatusOverdueJobPerformable"
          parent="abstractJobPerformable"/>
    <bean id="extractMasterWithThlContractsJobPerformable" class="com.sast.cis.thl.job.ExtractMasterWithThlContractsJobPerformable"
          parent="abstractJobPerformable"/>

    <alias name="cisProductPopulator" alias="productPopulator"/>
    <bean id="cisProductPopulator" class="com.sast.cis.core.converter.CisProductPopulator" parent="defaultProductPopulator"/>

    <bean parent="modifyPopulatorList">
        <property name="list" ref="extendedCartConverter"/>
        <property name="add" ref="cisAbstractOrderPopulator"/>
    </bean>

    <bean id="permissionConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.PermissionData"/>
        <property name="populators">
            <list merge="true">
                <ref bean="permissionPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="deviceCapabilityConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.DeviceCapabilityData"/>
        <property name="populators">
            <list merge="true">
                <ref bean="deviceCapabilityPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisExtendedCartConverter" alias="extendedCartConverter"/>
    <bean id="cisExtendedCartConverter" parent="defaultExtendedCartConverter">
        <property name="populators">
            <list merge="true">
                <ref bean="cisOrderPaymentInfoPopulator"/>
            </list>
        </property>
    </bean>


    <alias alias="cartFactory" name="cisCartFactory"/>
    <bean id="cisCartFactory" class="com.sast.cis.core.factory.CisCartFactory"/>

    <alias name="cisUserFacade" alias="userFacade"/>
    <bean name="cisUserFacade" class="com.sast.cis.core.facade.user.CisUserFacade" parent="defaultUserFacade"/>

    <bean id="defaultAddressVerificationService"
          class="de.hybris.platform.commerceservices.address.impl.DefaultAddressVerificationService">
        <property name="baseStoreService" ref="baseStoreService"/>
    </bean>

    <alias name="cisAddressVerificationService" alias="addressVerificationService"/>
    <bean id="cisAddressVerificationService" class="com.sast.cis.core.service.impl.CisAddressVerificationService"
          parent="defaultAddressVerificationService"/>

    <alias alias="orderHistoryPopulator" name="cisOrderHistoryPopulator"/>
    <bean id="cisOrderHistoryPopulator" class="com.sast.cis.core.converter.order.CisOrderHistoryPopulator">
        <property name="cisMediaContainerService" ref="cisMediaContainerService"/>
        <property name="cisOrderService" ref="cisOrderService"/>
    </bean>

    <alias alias="customerPopulator" name="cisCustomerPopulator"/>
    <bean id="cisCustomerPopulator" class="com.sast.cis.core.converter.CisCustomerPopulator"
          parent="defaultCustomerPopulator"/>

    <alias alias="generateMerchantTransactionCodeStrategy" name="cisGenerateMerchantTransactionCodeStrategy"/>
    <bean id="cisGenerateMerchantTransactionCodeStrategy"
          class="com.sast.cis.core.converter.paying.CisGenerateMerchantTransactionCodeStrategy"/>

    <alias name="cisProductModelUrlResolver" alias="productModelUrlResolver"/>
    <bean id="cisProductModelUrlResolver" class="com.sast.cis.core.resolver.CisProductModelUrlResolver"
          parent="defaultProductModelUrlResolver">
        <property name="defaultPattern" value="/p/{product-code}"/>
    </bean>

    <bean id="iotCompanySsoDataToModelConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.IoTCompanyModel"/>
        <property name="populators">
            <list>
                <ref bean="iotCompanySsoDataToModelPopulator"/>
                <ref bean="aaCompanyAttributesPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="nonBlockingProcessExecutor" class="com.sast.cis.core.util.NonBlockingLimitingProcessExecutor">
        <constructor-arg index="0" name="limit" value="${os.processexecutor.limit}"/>
        <constructor-arg index="1" name="timeoutInMs" value="2000"/>
    </bean>

    <alias name="cisImageMagickService" alias="imageMagickService"/>
    <bean id="cisImageMagickService" class="de.hybris.platform.mediaconversion.imagemagick.DefaultImageMagickService">
        <property name="processExecutor" ref="nonBlockingProcessExecutor"/>
        <property name="osConfigurationService" ref="osConfigurationService"/>
        <property name="configurationService" ref="configurationService"/>
        <property name="securityService" ref="imageMagickSecurityService"/>
        <property name="tmpDir" value="/tmp/imconvert"/>
    </bean>

    <bean id="imageMagickMediaConversionStrategy"
          class="com.sast.cis.core.strategy.media.CisImageMagickMediaConversionStrategy">
        <property name="configurationService" ref="configurationService"/>
        <property name="imageMagickService" ref="imageMagickService"/>
        <property name="mediaService" ref="mediaService"/>
        <property name="mimeMappingStrategy" ref="mimeMappingStrategy"/>
        <property name="convertedMediaCreationStrategy" ref="convertedMediaCreationStrategy"/>
        <property name="catalogUnawareConvertedMediaCreationStrategy" ref="catalogUnawareConvertedMediaCreationStrategy"/>
        <property name="tmpDir" value="/tmp/imconvert"/>
    </bean>

    <bean id="catalogUnawareConvertedMediaCreationStrategy"
          class="com.sast.cis.core.strategy.media.CatalogUnawareConvertedMediaCreationStrategy">
        <property name="modelService" ref="modelService"/>
        <property name="mediaService" ref="mediaService"/>
    </bean>

    <alias name="cisCommerceCartRestorationStrategy"
           alias="commerceCartRestorationStrategy"/>
    <bean id="cisCommerceCartRestorationStrategy"
          class="com.sast.cis.core.strategy.cart.CisCommerceCartRestorationStrategy"
          parent="defaultCommerceCartRestorationStrategy"/>

    <alias name="cisAddToCartStrategy" alias="commerceAddToCartStrategy"/>
    <bean id="cisAddToCartStrategy"
          class="com.sast.cis.core.strategy.cart.CisAddToCartStrategy"
          parent="defaultCommerceAddToCartStrategy"/>

    <alias name="cisUpdateCartEntryStrategy" alias="commerceUpdateCartEntryStrategy"/>
    <bean id="cisUpdateCartEntryStrategy"
          class="com.sast.cis.core.strategy.cart.CisUpdateCartEntryStrategy"
          parent="defaultCommerceUpdateCartEntryStrategy"/>

    <bean id="cisCoreCustomerCleanupHookMergeDirective" depends-on="customerCleanupHooks" parent="listMergeDirective">
        <property name="add" ref="cisCustomerCleanupHook"/>
    </bean>

    <alias name="defaultCustomerReviewPopulator" alias="customerReviewPopulator"/>
    <bean id="defaultCustomerReviewPopulator"
          class="com.sast.cis.core.converter.review.CisCustomerReviewPopulator">
        <property name="principalConverter" ref="principalConverter"/>
    </bean>

    <bean id="s3StorageServiceFactory" class="com.sast.cis.core.service.media.CisS3StorageServiceFactory"/>

    <bean id="clusterLogoutEventListener" class="com.sast.cis.core.logout.ClusterLogoutEventListener" parent="abstractEventListener">
        <property name="cisHttpSessionManager" ref="cisHttpSessionManager"/>
    </bean>

    <bean id="clusterLogoutAllEventListener" class="com.sast.cis.core.logout.ClusterLogoutAllEventListener" parent="abstractEventListener">
        <property name="cisHttpSessionManager" ref="cisHttpSessionManager"/>
    </bean>

    <alias name="cisCartService" alias="cartService"/>
    <bean id="cisCartService" class="com.sast.cis.core.service.CisCartService" parent="defaultCartService"/>

    <alias name="cisOrderFacade" alias="orderFacade"/>
    <bean id="cisOrderFacade" class="com.sast.cis.core.facade.CisOrderFacade" parent="defaultOrderFacade"/>

    <bean id="emailAddressVirtualReferenceValuesExtractor" class="com.sast.cis.core.audit.EmailAddressVirtualReferenceValuesExtractor">
        <property name="modelService" ref="modelService"/>
    </bean>

    <bean id="uidVirtualReferenceValuesExtractor" class="com.sast.cis.core.audit.UidVirtualReferenceValuesExtractor">
        <property name="modelService" ref="modelService"/>
    </bean>

    <bean id="internalUserUidTranslationService" class="com.sast.cis.core.service.customer.InternalUserUidTranslationService">
    </bean>

    <alias name="cisOrderConverter" alias="orderConverter"/>
    <bean id="cisOrderConverter" parent="accOrderConverter">
        <property name="populators">
            <list merge="true">
                <ref bean="cisOrderPopulator"/>
                <ref bean="cisOrderPaymentInfoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="ssoUidVirtualReferenceValuesExtractor" class="com.sast.cis.core.audit.SsoUidVirtualReferenceValuesExtractor">
        <property name="modelService" ref="modelService"/>
        <property name="internalUserUidTranslationService" ref="internalUserUidTranslationService"/>
    </bean>

    <alias name="cisCountryPopulator" alias="countryPopulator"/>
    <bean id="cisCountryPopulator" class="com.sast.cis.core.converter.country.CisCountryPopulator" parent="defaultCountryPopulator"/>

    <bean id="cisCartFacade" class="com.sast.cis.core.facade.CisCartFacade" parent="defaultCartFacade"/>

    <bean id="cisMediaContainerService" parent="defaultMediaContainerService"
          class="com.sast.cis.core.service.media.CisMediaContainerService"/>

    <bean id="cisImageFormatMapping" parent="defaultImageFormatMapping">
        <property name="mapping">
            <map>
                <entry key="Small-Icon" value="44Wx44H"/>
                <entry key="Medium-Icon" value="90Wx90H"/>
                <entry key="Large-Icon" value="118Wx118H"/>
                <entry key="Thumbnail-Screenshot" value="210Wx118H"/>
                <entry key="Gallery-Screenshot" value="1280Wx720H"/>
            </map>
        </property>
    </bean>

    <alias name="productIconPopulator" alias="productPrimaryImagePopulator"/>
    <bean id="productIconPopulator" parent="defaultProductPrimaryImagePopulator"
          class="com.sast.cis.core.converter.product.ProductIconPopulator">
        <property name="imageFormats">
            <list>
                <value>Small-Icon</value>
                <value>Medium-Icon</value>
                <value>Large-Icon</value>
            </list>
        </property>
        <property name="imageFormatMapping" ref="cisImageFormatMapping"/>
        <property name="mediaContainerService" ref="cisMediaContainerService"/>
    </bean>

    <bean id="productScreenshotPopulator" parent="defaultProductGalleryImagesPopulator">
        <property name="imageFormats">
            <list>
                <value>Thumbnail-Screenshot</value>
                <value>Gallery-Screenshot</value>
            </list>
        </property>
        <property name="imageFormatMapping" ref="cisImageFormatMapping"/>
        <property name="mediaContainerService" ref="cisMediaContainerService"/>
    </bean>

    <bean id="clientImageConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.ClientImageData"/>
        <property name="populators">
            <list>
                <ref bean="clientImagePopulator"/>
            </list>
        </property>
    </bean>

    <bean id="appSyncService" class="com.sast.cis.core.service.AppSyncService" parent="defaultCatalogSynchronizationService"/>

    <alias name="cisRestTemplate" alias="restTemplate"/>

    <alias name="defaultCisItemInfoService" alias="cisItemInfoService"/>
    <bean id="defaultCisItemInfoService" class="com.sast.cis.backoffice.editor.service.DefaultCisItemInfoService">
        <property name="modelService" ref="modelService"/>
        <property name="typeService" ref="typeService"/>
        <property name="l10nService" ref="l10nService"/>
        <property name="attributeValueBuildersByClassName">
            <map>
                <!-- keys are Java class names, except for the "default" attribute value builder -->
                <!-- this is an attribute value builder for any hybris item model -->
                <entry key="ItemModel" value-ref="itemModelAttributeValueBuilder"/>
                <!-- this is a "catch-all" attribute value builder that calls the object's toString() method -->
                <entry key="default" value-ref="toStringAttributeValueBuilder"/>
            </map>
        </property>
    </bean>

    <bean id="dateAttributeValueBuilder" class="com.sast.cis.backoffice.editor.attribute.CisDateAttributeValueBuilder"/>

    <bean id="toStringAttributeValueBuilder" class="com.sast.cis.backoffice.editor.attribute.CisToStringAttributeValueBuilder"/>

    <bean id="itemModelAttributeValueBuilder" class="com.sast.cis.backoffice.editor.attribute.DefaultCisItemModelAttributeValueBuilder">
        <property name="modelService" ref="modelService"/>
        <property name="typeService" ref="typeService"/>
        <property name="itemModelQualifiers">
            <list>
                <value>code</value>
                <value>name</value>
                <value>uid</value>
                <value>pk</value>
            </list>
        </property>
    </bean>

    <bean id="keycloakAuthenticationProvider"
          class="org.keycloak.adapters.springsecurity.authentication.KeycloakAuthenticationProvider"/>

    <bean id="exportInformationEntryDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.ExportInformationEntryData"/>
        <property name="populators">
            <list>
                <ref bean="exportInformationEntryDataPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="defaultFollowAppMethodHook" alias="followAppMethodHook"/>
    <bean id="defaultFollowAppMethodHook" class="com.sast.cis.core.order.hook.FollowAppMethodHook"/>

    <alias name="defaultCisCommercePlaceOrderMethodHook" alias="cisCommercePlaceOrderMethodHook"/>
    <bean id="defaultCisCommercePlaceOrderMethodHook" class="com.sast.cis.core.order.hook.CisCommercePlaceOrderMethodHook"/>

    <bean id="cisCommercePlaceOrderMethodHookMergeDirective" depends-on="commercePlaceOrderMethodHooks"
          parent="listMergeDirective">
        <property name="add" ref="cisCommercePlaceOrderMethodHook"/>
    </bean>

    <bean id="followAppMethodHookMergeDirective" depends-on="commercePlaceOrderMethodHooks"
          parent="listMergeDirective">
        <property name="add" ref="followAppMethodHook"/>
    </bean>

    <bean id="preOrderPlacementValidationHook" class="com.sast.cis.core.order.hook.PreOrderPlacementValidationHook"/>
    <bean id="preOrderPlacementValidationHookMergeDirective" depends-on="commercePlaceOrderMethodHooks" parent="listMergeDirective">
        <property name="add" ref="preOrderPlacementValidationHook"/>
    </bean>

    <alias name="cisPdtCriteriaFactory" alias="PDTCriteriaFactory"/>
    <bean id="cisPdtCriteriaFactory" class="com.sast.cis.core.factory.CisPdtCriteriaFactory" parent="defaultPDTCriteriaFactory"/>

    <alias name="cisSLFindPriceStrategy" alias="defaultSLFindPriceStrategy" />
    <alias name="cisSLFindPriceStrategy" alias="slFindPriceStrategy" />

    <alias name="cisMediaPermissionService" alias="mediaPermissionService"/>
    <bean id="cisMediaPermissionService" class="com.sast.cis.core.security.service.CisMediaPermissionService"
          parent="defaultMediaPermissionService"/>

    <bean id="orderDetailsLinkConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.UrlData"/>
        <property name="populators">
            <list>
                <ref bean="orderDetailsLinkPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="orderManagementItemConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.OrderManagementItemData"/>
        <property name="populators">
            <list>
                <ref bean="orderManagementItemPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="orderManagementAppConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.OrderManagementAppData"/>
        <property name="populators">
            <list>
                <ref bean="orderManagementAppPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="cisCreditCardPaymentInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.CreditCardPaymentInfoData"/>
        <property name="populators">
            <list>
                <ref bean="cisCreditCardPaymentInfoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="cisInvoicePaymentInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.InvoicePaymentInfoData"/>
        <property name="populators">
            <list>
                <ref bean="invoicePaymentInfoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="sepaCreditTransferPaymentInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.SepaCreditTransferPaymentInfoData"/>
        <property name="populators">
            <list>
                <ref bean="sepaCreditTransferPaymentInfoPopulator"/>
                <ref bean="sepaCreditTransferPaymentInfoServicePopulator"/>
            </list>
        </property>
    </bean>
    <bean id="zeroPaymentInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.PaymentInfoData"/>
        <property name="populators">
            <list>
                <ref bean="zeroPaymentInfoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="achInternationalCreditTransferPaymentInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.AchCreditTransferPaymentInfoData"/>
        <property name="populators">
            <list>
                <ref bean="achInternationalCreditTransferPaymentInfoPopulator"/>
                <ref bean="achInternationalCreditTransferPaymentInfoServicePopulator"/>
            </list>
        </property>
    </bean>

    <bean id="invoiceBySellerPaymentInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.PaymentInfoData"/>
        <property name="populators">
            <list>
                <ref bean="invoiceBySellerPaymentInfoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="sepaMandatePaymentInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.SepaMandatePaymentInfoData"/>
        <property name="populators">
            <list>
                <ref bean="sepaMandatePaymentInfoPopulator"/>
                <ref bean="sepaMandatePaymentInfoServicePopulator"/>
            </list>
        </property>
    </bean>

    <bean id="paymentInfoDraftConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.PaymentInfoDraftData"/>
        <property name="populators">
            <list>
                <ref bean="paymentInfoDraftPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="cisMiniCartDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.MiniCartData"/>
        <property name="populators">
            <list>
                <ref bean="miniCartDataPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisSolrSearchQueryDecoderPopulator" alias="solrSearchQueryDecoderPopulator"/>
    <bean id="cisSolrSearchQueryDecoderPopulator"
          class="com.sast.cis.core.productsearch.converter.CisSolrSearchQueryDecoderPopulator"
    />

    <util:map id="paymentInfoConverters" map-class="java.util.HashMap" key-type="java.lang.String"
              value-type="de.hybris.platform.converters.impl.AbstractPopulatingConverter">
        <entry key="com.sast.cis.core.model.StripeCreditCardPaymentInfoModel" value-ref="cisCreditCardPaymentInfoConverter"/>
        <entry key="com.sast.cis.core.model.SepaCreditTransferPaymentInfoModel"
               value-ref="sepaCreditTransferPaymentInfoConverter"/>
        <entry key="com.sast.cis.core.model.ZeroPaymentInfoModel"
               value-ref="zeroPaymentInfoConverter"/>
        <entry key="com.sast.cis.core.model.AchInternationalCreditTransferPaymentInfoModel"
               value-ref="achInternationalCreditTransferPaymentInfoConverter"/>
        <entry key="com.sast.cis.core.model.InvoiceBySellerPaymentInfoModel"
               value-ref="invoiceBySellerPaymentInfoConverter"/>
        <entry key="com.sast.cis.payment.dpg.model.DpgCreditCardPaymentInfoModel"
               value-ref="cisCreditCardPaymentInfoConverter"/>
        <entry key="com.sast.cis.core.model.SepaMandatePaymentInfoModel"
               value-ref="sepaMandatePaymentInfoConverter"/>
    </util:map>

    <bean id="checkoutInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.CheckoutInfoData"/>
        <property name="populators">
            <list>
                <ref bean="checkoutInfoPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisSessionOverrideCheckoutFlowFacade" alias="checkoutFlowFacade"/>
    <bean id="cisSessionOverrideCheckoutFlowFacade" class="com.sast.cis.core.facade.flow.CisSessionOverrideCheckoutFlowFacade"
          parent="sessionOverrideCheckoutFlowFacade"/>

    <alias alias="dynamicAttributesOrderStatusDisplay" name="orderDisplayStatusHandler"/>
    <bean id="orderDisplayStatusHandler" class="com.sast.cis.core.handler.OrderDisplayStatusHandler">
        <constructor-arg ref="cisOrderService"/>
    </bean>

    <alias alias="processParameterHelper" name="processParameterHelper"/>
    <bean id="processParameterHelper" class="de.hybris.platform.processengine.helpers.impl.DefaultProcessParameterHelper">
        <property name="modelService" ref="modelService"/>
    </bean>

    <bean parent="modifyPopulatorList">
        <property name="list" ref="defaultOrderEntryProductConverter"/>
        <property name="add" ref="productVolumePricesPopulator"/>
    </bean>

    <bean id="commercePriceDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.ScalePriceInfo"/>
        <property name="populators">
            <list>
                <ref bean="commercePriceDataPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="subscriptionConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.dto.portal.SubscriptionDto"/>
        <property name="populators">
            <list>
                <ref bean="subscriptionPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="countryModelConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="de.hybris.platform.core.model.c2l.CountryModel"/>
        <property name="populators">
            <list>
                <ref bean="countryModelPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="countrySyncJobPerformable" class="com.sast.cis.core.country.sync.job.CountrySyncJobPerformable"
          parent="abstractJobPerformable"/>


    <!--
    The `paymentServices` field defines the precedence of the preferred PSP.
    The first payment service supporting the purchase is used.
    See CartPaymentServiceStrategy::getPaymentService for details
    -->
    <alias name="cartPaymentServiceStrategy" alias="paymentServiceStrategy"/>
    <bean id="cartPaymentServiceStrategy" class="com.sast.cis.core.paymentintegration.impl.CartPaymentServiceStrategy">
        <property name="paymentServices">
            <util:list value-type="com.sast.cis.core.paymentintegration.PaymentService">
                <ref bean="zeroPaymentService"/>
                <ref bean="boschTransferPaymentService"/>
                <ref bean="pgwPaymentService"/>
                <ref bean="dpgPaymentService"/>
            </util:list>
        </property>
    </bean>

    <alias name="dynamicPaymentAuthorizationStrategy" alias="commercePaymentAuthorizationStrategy"/>

    <bean id="licenseActivationToLicenseActivationMsgConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.LicenseActivationMessage"/>
        <property name="populators">
            <list>
                <ref bean="licenseActivationToLicenseActivationMsgPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="privateOfferRequestDataToModelConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.PrivateOfferRequestModel"/>
        <property name="populators">
            <list>
                <ref bean="privateOfferRequestModelPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="privateOfferRequestItemDataToModelConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.PrivateOfferRequestItemModel"/>
        <property name="populators">
            <list>
                <ref bean="privateOfferRequestItemModelPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="privateOfferProjectAddressDataToModelConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.PrivateOfferProjectAddressModel"/>
        <property name="populators">
            <list>
                <ref bean="privateOfferProjectAddressModelPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="privateOfferProjectRegistrationDataToModelConverter" parent="abstractModelConverter">
        <property name="targetClass" value="com.sast.cis.core.model.PrivateOfferProjectRegistrationModel"/>
        <property name="populators">
            <list>
                <ref bean="privateOfferProjectRegistrationModelPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="app2FollowAppSubscriptionDataConverter" alias="followAppDataConverter"/>
    <bean id="app2FollowAppSubscriptionDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.FollowAppData"/>
        <property name="populators">
            <list>
                <ref bean="productDataFollowAppPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="invoiceCreditNoteModelToDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.InvoiceCreditNoteData"/>
        <property name="populators">
            <list>
                <ref bean="invoiceCreditNotePopulator"/>
            </list>
        </property>
    </bean>

    <bean id="invoiceModelToDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.InvoiceData"/>
        <property name="populators">
            <list>
                <ref bean="invoicePopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisCloneAbstractOrderHookList" alias="cloneAbstractOrderHookList"/>
    <util:list id="cisCloneAbstractOrderHookList" value-type="de.hybris.platform.order.strategies.ordercloning.CloneAbstractOrderHook">
        <ref bean="taggingCloneAbstractOrderStrategy"/>
    </util:list>

    <bean id="companySyncJobPerformable" class="com.sast.cis.core.company.sync.job.CompanySyncJobPerformable"
          parent="abstractJobPerformable"/>

    <bean id="basicAppDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.BasicAppData"/>
        <property name="populators">
            <list>
                <ref bean="basicAppDataPopulator"/>
            </list>
        </property>
    </bean>
    <bean id="sbiDataConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.SBIData"/>
        <property name="populators">
            <list>
                <ref bean="selfBillingInvoiceDataPopulator"/>
            </list>
        </property>
    </bean>
    <bean parent="modifyPopulatorList">
        <property name="list" ref="defaultOrderEntryConverter"/>
        <property name="add" ref="orderEntryFuturePricePopulator"/>
    </bean>

    <util:list id="indexFieldsToBoost" value-type="java.lang.String">
        <value>packages</value>
        <value>licenses</value>
    </util:list>

    <bean id="solrClassificationAttributeValueTermBoostListener"
          class="com.sast.cis.core.search.context.SolrClassificationAttributeValueTermBoostListener">
        <property name="indexFieldsToBoost" ref="indexFieldsToBoost"/>
        <property name="commonI18NService" ref="commonI18NService"/>
    </bean>

    <bean id="solrTermBoostListenerDefinition" parent="solrListenerDefinition">
        <property name="priority" value="-11000"/>
        <property name="listener" ref="solrClassificationAttributeValueTermBoostListener"/>
    </bean>

    <bean id="directParentCategoriesSource" class="com.sast.cis.core.resolver.DirectParentCategoriesSource" parent="defaultCategorySource"/>

    <bean id="categoryFullPathsValueProvider" class="com.sast.cis.core.resolver.CategoryFullPathsValueProvider"
          parent="categoryPathValueProvider">
        <property name="categorySource" ref="directParentCategoriesSource"/>
    </bean>

    <bean id="priceDtoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.category.data.PriceDTO"/>
        <property name="populators">
            <list>
                <ref bean="priceDataPopulator"/>
            </list>
        </property>
    </bean>
    <bean id="productDtoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.category.data.ProductDTO"/>
        <property name="populators">
            <list>
                <ref bean="productDtoPopulator"/>
                <ref bean="productDataFeatureDtoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="featureDtoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.category.data.FeatureDTO"/>
        <property name="populators">
            <list>
                <ref bean="featureDtoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="featureValueDtoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.category.data.FeatureValueDTO"/>
        <property name="populators">
            <list>
                <ref bean="featureValueDtoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="imageDtoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.category.data.ImageDTO"/>
        <property name="populators">
            <list>
                <ref bean="imageDtoPopulator"/>
            </list>
        </property>
    </bean>

    <bean id="runtimeConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.RuntimeData"/>
        <property name="populators">
            <list>
                <ref bean="runtimePopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisHacInitUpdateFacade" alias="hacInitUpdateFacade"/>
    <bean id="cisHacInitUpdateFacade" class="com.sast.cis.core.facade.hac.CisHacInitUpdateFacade">
        <property name="notifier" ref="systemTrayNotifier"/>
    </bean>
    <bean id="firstLevelCategoryNameValueResolver" class="com.sast.cis.core.resolver.FirstLevelCategoryNameValueResolver" parent="abstractValueResolver">
        <constructor-arg ref="categoryService" />
        <property name="qualifierProvider" ref="languageQualifierProvider" />
    </bean>
    <bean id="bundleInfoConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="com.sast.cis.core.data.BundleInfoData"/>
        <property name="populators">
            <list>
                <ref bean="bundleInfoPopulator"/>
            </list>
        </property>
    </bean>

    <alias name="cisFeatureConverter" alias="featureConverter"/>
    <bean id="cisFeatureConverter" parent="defaultFeatureConverter">
        <property name="populators">
            <list>
                <ref bean="extendedFeaturePopulator"/>
            </list>
        </property>
    </bean>
    <alias name="productPriceWithFrequencyPopulator" alias="productPricePopulator"/>
    <bean id="productPriceWithFrequencyPopulator" class="com.sast.cis.core.converter.product.ProductPriceWithFrequencyPopulator">
        <constructor-arg name="appLicenseService" ref="appLicenseService"/>
        <constructor-arg name="productPricePopulator" ref="defaultProductPricePopulator"/>
        <constructor-arg name="commercePriceService" ref="commercePriceService"/>
    </bean>

    <bean id="onlyProductPriceConverter" parent="abstractPopulatingConverter">
        <property name="targetClass" value="de.hybris.platform.commercefacades.product.data.ProductData"/>
        <property name="populators">
            <list>
                <ref bean="productPriceWithFrequencyPopulator"/>
                <ref bean="productSpecialOfferPopulator" />
            </list>
        </property>
    </bean>

</beans>
