package com.sast.cis.core.converter.order;

import com.sast.cis.core.converter.CartItemDataConverter;
import com.sast.cis.core.converter.PriceDataConverter;
import com.sast.cis.core.data.OrderSummaryData;
import de.hybris.platform.commercefacades.order.data.AbstractOrderData;
import de.hybris.platform.converters.impl.AbstractPopulatingConverter;

import java.util.stream.Collectors;

public abstract class BaseOrderConverter<SOURCE extends AbstractOrderData, TARGET extends OrderSummaryData>
        extends AbstractPopulatingConverter<SOURCE, TARGET> {
    private final PriceDataConverter priceDataConverter;
    private final CartItemDataConverter cartItemDataConverter;

    public BaseOrderConverter(PriceDataConverter priceDataConverter, CartItemDataConverter cartItemDataConverter) {
        this.priceDataConverter = priceDataConverter;
        this.cartItemDataConverter = cartItemDataConverter;
    }

    @Override
    public void populate(SOURCE order, TARGET orderSummary) {
        orderSummary
                .withEntries(order.getEntries().stream().map(cartItemDataConverter::convert).collect(Collectors.toList()))
                .withPaymentAddress(order.getPaymentAddress())
                .withTotalPrice(priceDataConverter.toPriceData(order.getTotalPrice()))
                .withTotalTax(priceDataConverter.toPriceData(order.getTotalTax()))
                .withTotalPriceWithTax(priceDataConverter.toPriceData(order.getTotalPriceWithTax()))
                .withOwnAppsPurchase(order.isOwnAppsPurchase())
                .withPaymentInfo(order.getPaymentInfoData());


        if (order.getPaymentInfoData() != null) {
            var orderPaymentInfoData = order.getPaymentInfoData();
            orderSummary.setPaymentMethod(orderPaymentInfoData.getPaymentMethod());
        }
    }
}
