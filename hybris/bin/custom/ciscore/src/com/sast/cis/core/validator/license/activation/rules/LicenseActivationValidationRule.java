package com.sast.cis.core.validator.license.activation.rules;

import com.sast.cis.core.model.LicenseActivationModel;
import com.sast.cis.core.validator.license.activation.LicenseActivationValidationError;

import java.util.Set;

public interface LicenseActivationValidationRule {

    Set<LicenseActivationValidationError> validate(final LicenseActivationModel licenseActivation);

    boolean applies(final LicenseActivationModel licenseActivation);
}
