package com.sast.cis.core.billingintegration.handler;

import com.sast.cis.core.billingintegration.dto.Price;
import com.sast.cis.core.billingintegration.dto.PriceRecurrence;
import com.sast.cis.core.billingintegration.dto.ProductExportData;
import com.sast.cis.core.billingintegration.exception.PriceUpdateException;
import com.sast.cis.core.billingintegration.validator.PriceUpdateValidator;
import com.sast.cis.core.currency.CurrencySupportEvaluator;
import com.sast.cis.core.enums.EventProcessingStatus;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.PendingProductInfoModel;
import com.sast.cis.core.model.PostSubscriptionPriceUpdateEventModel;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.core.service.app.PriceRowFilterStrategy;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.europe1.model.PDTRowModel;
import de.hybris.platform.europe1.model.PriceRowModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.time.TimeService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.sast.cis.core.billingintegration.dto.PriceRecurrence.*;
import static com.sast.cis.core.enums.Feature.FEATURE_REJECT_PRICES_WITH_UNSUPPORTED_CURRENCIES;

@Component
@Slf4j
@AllArgsConstructor
public class ProductPriceUpdateHandler implements ProductUpdateHandler {
    private final ModelService modelService;
    private final Converter<Price, PriceRowModel> priceToPriceRowModelConverter;
    private final PriceUpdateValidator priceUpdateValidator;
    private final TimeService timeService;
    private final PriceRowFilterStrategy userPriceGroupPriceRowFilterStrategy;
    private final CurrencySupportEvaluator currencySupportEvaluator;
    private final FeatureToggleService featureToggleService;

    /**
     * Compares prices provided by the billing backend with existing prices and pending prices.
     *
     * What happens here is as follows:
     * 1. Get latest prices from billing backend (end date in the future)
     * 2. Get current and future prices from product
     *      we are not interested in
     *      1. prices which are already not active anymore
     *      2. customer specific prices.
     * 3. Adjust existing price validity periods
     *    - if a price starts at or after the earliest start date of the new prices from billing backend, discard the price because
     *      it will never become active
     *    - If a price starts before the earliest start date of the new prices from billing backend, set its end date
     *      to the earliest start date of prices from billing backend
     *   - since customer specific prices are not fetched, so their validity is not changed.
     * 4. Compare the updated price rows with the pending price updates
     *
     */
    @Override
    public void handle(AppLicenseModel appLicense, ProductExportData exportData, PendingProductInfoModel pendingProductInfo) {
        if (CollectionUtils.isEmpty(exportData.getPrices())) {
            LOG.error("Did not receive any pricing information from the billing backend. "
                + "This is highly improbable and requires manual investigation.");
            return;
        }
        Map<CurrencyModel, List<PriceRowModel>> billingPriceRowsByCurrency = groupByCurrency(extractPriceRows(exportData.getPrices()));
        Map<CurrencyModel, List<PriceRowModel>> existingPriceRowsByCurrency = groupByCurrency(getNonCustomerSpecificPriceRows(appLicense));

        Date now = getCurrentTime();
        LOG.info("Starting product price update for appLicense: {} and its supported currencies: {}", appLicense.getCode(), billingPriceRowsByCurrency.keySet().stream().map(CurrencyModel::getIsocode).toList());

        List<PriceRowModel> rowsToAdd = new LinkedList<>();
        List<PriceRowModel> rowsToRemove = new LinkedList<>();

        for (Map.Entry<CurrencyModel, List<PriceRowModel>> billingPriceRowsForCurrency : billingPriceRowsByCurrency.entrySet()) {
            final CurrencyModel currency = billingPriceRowsForCurrency.getKey();
            LOG.info("Processing price rows for currency {}", currency.getIsocode());
            if (!isCurrencySupportedForProduct(currency, appLicense)) {
                LOG.warn("Received prices with unsupported currency {} for product {}", currency.getIsocode(), appLicense.getCode());
                if (featureToggleService.isEnabledOrDefault(FEATURE_REJECT_PRICES_WITH_UNSUPPORTED_CURRENCIES, false)) {
                    LOG.warn("Skip prices with unsupported currency {} for product {}", currency.getIsocode(), appLicense.getCode());
                    continue;
                }
            }
            PriceRowUpdateData priceRowUpdateData = updatePricesForCurrency(
                existingPriceRowsByCurrency.getOrDefault(billingPriceRowsForCurrency.getKey(), List.of()),
                billingPriceRowsForCurrency.getValue(),
                now);

            rowsToAdd.addAll(priceRowUpdateData.getRowsToAdd());
            rowsToRemove.addAll(priceRowUpdateData.getRowsToRemove());
        }

        if (pendingProductInfo == null || CollectionUtils.isEmpty(pendingProductInfo.getPrices())) {
            LOG.warn("Skipping update validation because there are no pending product updates for app license {}", appLicense.getCode());
        } else {
            priceUpdateValidator.validate(rowsToAdd, pendingProductInfo.getPrices());
        }

        if (CollectionUtils.isNotEmpty(rowsToRemove)) {
            modelService.removeAll(rowsToRemove);
        }

        if (CollectionUtils.isNotEmpty(rowsToAdd)) {
            rowsToAdd.forEach(priceRow -> assignProduct(priceRow, appLicense));
            modelService.saveAll(rowsToAdd);

            createPostSubscriptionPriceUpdateEvent(appLicense);
        }

        if (pendingProductInfo != null) {
            pendingProductInfo.setPrices(null);
            modelService.save(pendingProductInfo);
            modelService.refresh(pendingProductInfo);
        }
    }

    private Collection<PriceRowModel> getNonCustomerSpecificPriceRows(AppLicenseModel appLicense) {
        return userPriceGroupPriceRowFilterStrategy.filterPriceRows(appLicense.getEurope1Prices());
    }

    private void createPostSubscriptionPriceUpdateEvent(final AppLicenseModel appLicense) {
        if (!LicenseType.SUBSCRIPTION.equals(appLicense.getLicenseType())) {
            return;
        }
        LOG.info("Create PostSubscriptionPriceUpdateEvent event for license '{}'", appLicense.getCode());
        final PostSubscriptionPriceUpdateEventModel postSubscriptionPriceUpdateEvent = new PostSubscriptionPriceUpdateEventModel();
        postSubscriptionPriceUpdateEvent.setEventId(UUID.randomUUID().toString());
        postSubscriptionPriceUpdateEvent.setEventStatus(EventProcessingStatus.NEW);
        postSubscriptionPriceUpdateEvent.setAppLicense(appLicense);
        modelService.save(postSubscriptionPriceUpdateEvent);
    }

    private PriceRowUpdateData updatePricesForCurrency(List<PriceRowModel> existingPriceRows, List<PriceRowModel> billingPriceRows,
        Date now) {
        // we can ignore prices from billing which are not active anymore
        List<PriceRowModel> activeOrFutureBillingPrices = billingPriceRows.stream()
            .filter(priceRowModel -> priceRowModel.getEndTime() == null || priceRowModel.getEndTime().after(now))
            .toList();
        LOG.info("Found {} active or future prices", activeOrFutureBillingPrices.size());

        // retrieve the earliest start date from the set of active or future prices from billing backend
        Date earliestBillingStartTime = activeOrFutureBillingPrices.stream()
            .min(Comparator.comparing(PriceRowModel::getStartTime))
            .map(PriceRowModel::getStartTime)
            .orElseThrow(() -> new PriceUpdateException(
                String.format("Couldn't retrieve the earliest start date from: %s",
                    activeOrFutureBillingPrices.stream()
                        .map(priceRowModel -> String.format("[price: %s, currency: %s]",
                            priceRowModel.getPrice(), priceRowModel.getCurrency()))
                        .collect(Collectors.joining(", ")))
            ));

        // gather existing prices which end after the earliest start date of the new prices provided by billing backend
        List<PriceRowModel> activeOrFutureExistingPrices = existingPriceRows.stream()
            .filter(priceRowModel -> priceRowModel.getEndTime() == null || priceRowModel.getEndTime().after(earliestBillingStartTime))
            .toList();

        List<PriceRowModel> priceRowsToRemove = new LinkedList<>();
        List<PriceRowModel> priceRowsToUpdate = new LinkedList<>(activeOrFutureBillingPrices);

        // adjust existing price rows so that they end right before the new prices from billing start
        for (PriceRowModel activeOrFutureExistingPrice : activeOrFutureExistingPrices) {
            // discard existing price rows which would start exactly at or after the new price rows from billing because they would never become active
            if (activeOrFutureExistingPrice.getStartTime().equals(earliestBillingStartTime) || activeOrFutureExistingPrice.getStartTime()
                .after(earliestBillingStartTime)) {
                priceRowsToRemove.add(activeOrFutureExistingPrice);
            } else {
                // adjust end date of other existing price rows to the earliest start date of prices from billing backend
                activeOrFutureExistingPrice.setEndTime(earliestBillingStartTime);
                priceRowsToUpdate.add(activeOrFutureExistingPrice);
            }
        }

        return PriceRowUpdateData.builder()
            .rowsToAdd(priceRowsToUpdate)
            .rowsToRemove(priceRowsToRemove)
            .build();
    }

    private void assignProduct(PriceRowModel priceRow, AppLicenseModel appLicense) {
        if (priceRow.getProductId() == null && priceRow.getProduct() == null) {
            priceRow.setProduct(appLicense);
        }
    }

    private Map<CurrencyModel, List<PriceRowModel>> groupByCurrency(Collection<PriceRowModel> priceRows) {
        return CollectionUtils.emptyIfNull(priceRows).stream()
            .collect(Collectors.groupingBy(PriceRowModel::getCurrency, HashMap::new, Collectors.toList()));
    }

    private List<PriceRowModel> extractPriceRows(List<Price> prices) {
        final Set<PriceRecurrence> applicableRecurrences = Set.of(ONE_TIME, YEARLY, VARIANT);
        return CollectionUtils.emptyIfNull(prices).stream()
            .filter(price -> price.getRecurrence() != null && applicableRecurrences.contains(price.getRecurrence()))
            .map(priceToPriceRowModelConverter::convert)
            .collect(Collectors.toList());
    }

    private boolean isCurrencySupportedForProduct(final CurrencyModel currency, final AppLicenseModel appLicense) {
        return currencySupportEvaluator.isCurrencySupportedForAppLicense(currency, appLicense);
    }

    private Date getCurrentTime() {
        return timeService.getCurrentTime();
    }

    @Builder
    @Getter
    private static class PriceRowUpdateData {
        @Builder.Default
        private final List<PriceRowModel> rowsToRemove = List.of();

        @Builder.Default
        private final List<PriceRowModel> rowsToAdd = List.of();
    }
}
