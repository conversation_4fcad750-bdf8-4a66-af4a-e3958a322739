package com.sast.cis.core.validator.app;

import com.sast.cis.core.data.LicenseBaseData;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.validator.app.rules.base.BaseValidationRule;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LicenseBaseDataValidator extends AbstractLicenseDataRuleValidator<LicenseBaseData> {
    protected LicenseBaseDataValidator(List<BaseValidationRule> validationRules, ErrorMessageService errorMessageService) {
        super(validationRules, errorMessageService);
    }
}
