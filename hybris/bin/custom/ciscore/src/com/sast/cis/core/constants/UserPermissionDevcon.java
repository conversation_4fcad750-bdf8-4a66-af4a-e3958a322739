package com.sast.cis.core.constants;

import java.util.Arrays;

public enum UserPermissionDevcon {
    DEVELOPER_AUTHORIZATION("DEVELOPER_AUTHORIZATION"),
    EDIT_APP("EDIT_APP"),
    EDIT_DRAFT("EDIT_DRAFT"),
    PUBLISH_APP("PUBLISH_APP"),
    EDIT_APP_PRICE("EDIT_APP_PRICE"),
    EDIT_PAYOUT_DETAILS("EDIT_PAYOUT_DETAILS");

    private final String value;

    UserPermissionDevcon(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static UserPermissionDevcon fromValue(String value) {
        return Arrays.stream(UserPermissionDevcon.values()).filter(v -> v.value.equals(value))
            .findFirst().orElse(null);
    }
}
