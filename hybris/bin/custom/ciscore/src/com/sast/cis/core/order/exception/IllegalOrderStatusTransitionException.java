package com.sast.cis.core.order.exception;

import de.hybris.platform.core.enums.OrderStatus;

public class IllegalOrderStatusTransitionException extends RuntimeException {

    private IllegalOrderStatusTransitionException(final String msg) {
        super(msg);
    }

    public static IllegalOrderStatusTransitionException forTransition(
        final OrderStatus initialStatus,
        final OrderStatus targetStatus,
        final String orderCode) {

        final String msg = String.format(
            "ALERT: Illegal status transition from '%s' to '%s' for order '%s'",
            initialStatus,
            targetStatus,
            orderCode
        );
        return new IllegalOrderStatusTransitionException(msg);
    }
}
