package com.sast.cis.core.followapp.service;

import com.sast.cis.core.followapp.dao.FollowAppSubscriptionDao;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.FollowAppSubscriptionModel;
import com.sast.cis.core.model.IntegratorModel;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.SearchResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.sast.cis.core.enums.FollowAppSubscriptionStatus.SUBSCRIBED;
import static com.sast.cis.core.enums.FollowAppSubscriptionStatus.UNSUBSCRIBED;

@Service
@AllArgsConstructor
public class FollowAppService {
    private final FollowAppSubscriptionDao followAppSubscriptionDao;
    private final ModelService modelService;

    public boolean isSubscribed(IntegratorModel integrator, AppModel appModel) {
        FollowAppSubscriptionModel followAppSubscription = findFollowAppSubscription(integrator, appModel);
        return (followAppSubscription != null && SUBSCRIBED.equals(followAppSubscription.getFollowAppSubscriptionStatus()));
    }

    @Transactional
    public void subscribe(IntegratorModel integrator, AppModel appModel) {
        FollowAppSubscriptionModel followAppSubscription = findFollowAppSubscription(integrator, appModel);
        if (followAppSubscription == null) {
            followAppSubscription = createFollowAppSubscription(integrator, appModel);
        }
        followAppSubscription.setFollowAppSubscriptionStatus(SUBSCRIBED);
        modelService.save(followAppSubscription);
    }

    @Transactional
    public void unsubscribe(IntegratorModel integrator, AppModel appModel) {
        FollowAppSubscriptionModel followAppSubscription = findFollowAppSubscription(integrator, appModel);
        if (followAppSubscription != null) {
            followAppSubscription.setFollowAppSubscriptionStatus(UNSUBSCRIBED);
            modelService.save(followAppSubscription);
        }
    }

    private FollowAppSubscriptionModel createFollowAppSubscription(IntegratorModel integrator, AppModel appModel) {
        FollowAppSubscriptionModel followAppSubscription = modelService.create(FollowAppSubscriptionModel.class);
        followAppSubscription.setAppCode(appModel.getCode());
        followAppSubscription.setIntegrator(integrator);
        return followAppSubscription;
    }

    public SearchResult<FollowAppSubscriptionModel> getFollowAppSubscriptionsOfApp(AppModel app, int start, int count) {
        return followAppSubscriptionDao.findByAppCode(app.getCode(), SUBSCRIBED, start, count);
    }

    private FollowAppSubscriptionModel findFollowAppSubscription(IntegratorModel integrator, AppModel appModel) {
        return followAppSubscriptionDao
            .findByIntegratorAndAppCode(integrator, appModel.getCode());
    }

}
