package com.sast.cis.core.ordercancel.bp.actions;

import com.sast.cis.core.model.OpenOrderCancelBusinessProcessModel;
import com.sast.cis.core.order.service.OrderRejectionManager;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class InitiateOrderCancellationInBrimAction extends AbstractSimpleDecisionAction<OpenOrderCancelBusinessProcessModel> {

    private final OrderRejectionManager orderRejectionManager;

    @Override
    public Transition executeAction(final OpenOrderCancelBusinessProcessModel openOrderCancelBusinessProcess) {
        final OrderModel order = openOrderCancelBusinessProcess.getOrder();
        try {
            LOG.info("Initiating order cancellation for order with code={}", order.getCode());
            orderRejectionManager.initiateOrderRejection(order);
        } catch (final Exception e) {
            LOG.error("ALERT Rejection for Order with code={} failed. See details.", order.getCode(), e);
            return Transition.NOK;
        }
        return Transition.OK;
    }
}
