package com.sast.cis.core.paymentintegration.paymentinfo;

import com.sast.cis.core.data.PaymentInfoData;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.facade.payment.data.FinalizePaymentInfoCreationRequest;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.PaymentInfoDraftModel;
import com.sast.cis.core.paymentintegration.data.PaymentInfoCreationRequest;
import com.sast.cis.core.paymentintegration.paymentinfo.data.FinalizePaymentInfoCreationResult;
import com.sast.cis.core.paymentintegration.paymentinfo.data.PaymentInfoCreationNotificationStatus;
import com.sast.cis.core.paymentintegration.paymentinfo.data.PaymentInfoCreationResult;
import com.sast.cis.core.paymentintegration.paymentinfo.draft.PaymentInfoDraftService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.sast.cis.core.enums.PaymentInfoDraftCreationStatus.WAITING_FOR_CONFIRMATION;

/**
 * Create a payment outside the scope of a checkout process.
 * Supports only PGW with the SEPA Direct Debit method type.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PaymentInfoCreationService {

    private final PaymentInfoCreationServiceResolver serviceResolver;
    private final PaymentInfoDraftService paymentInfoDraftService;

    /**
     * Create a payment info for the integrator.
     * Supports only PGW with SEPA Direct Debit.
     * <br/>
     * The payment info creation requires redirecting the user to the HPP.
     * This method will create a payment info draft, and instruct the browser to redirect the user to the HPP.
     * The payment info will be created only after a successful redirect from the HPP back to the Store.
     *
     * @param paymentInfoData the payment info data
     * @param integrator the integrator
     * @return the response will contain the resulting payment info draft code, and the user action parameters
     */
    public PaymentInfoCreationResult createPaymentInfoForIntegrator(
        @NonNull final PaymentInfoData paymentInfoData, @NonNull final IntegratorModel integrator) {

        LOG.info("Create payment info {} for integrator: {}", paymentInfoData, integrator.getUid());

        final PaymentProvider paymentProvider = paymentInfoData.getPaymentProvider();
        final PaymentMethodType paymentMethod = paymentInfoData.getPaymentMethod();

        final PaymentInfoCreationRequest request = new PaymentInfoCreationRequest(integrator, paymentMethod);

        return serviceResolver.getPaymentInfoCreationService(paymentProvider).initiateStoredPaymentInfoCreation(request);
    }

    /**
     * Handle the payment info creation notification.
     * <br/>
     * This method is invoked after a redirect from the HPP back to the Store.
     *
     * @param paymentInfoDraftId the payment info draft id
     * @param notificationStatus the notification status
     */
    public void handlePaymentInfoCreationNotification(
        @NonNull final String paymentInfoDraftId, @NonNull final PaymentInfoCreationNotificationStatus notificationStatus) {

        LOG.info("Handle payment info creation notification of type {} for payment info draft: {}", notificationStatus, paymentInfoDraftId);
        final PaymentInfoDraftModel paymentInfoDraft = paymentInfoDraftService.getPaymentInfoDraftByCodeOrThrow(paymentInfoDraftId);
        final PaymentProvider paymentProvider = paymentInfoDraft.getPaymentProvider();
        final ProviderPaymentInfoCreationService providerService = serviceResolver.getPaymentInfoCreationService(paymentProvider);
        providerService.handlePaymentInfoCreationNotification(paymentInfoDraft, notificationStatus);
    }

    /**
     * Attempt to finalize the creation of pending payment infos.
     *
     * @param finalizePaymentInfoCreationRequest contains the list of payment info draft ids to finalize
     * @return the result of the operation. Contains a list of the payment info drafts that are still pending and should be retried.
     */
    public FinalizePaymentInfoCreationResult finalizePendingPaymentInfoCreation(
        @NonNull final FinalizePaymentInfoCreationRequest finalizePaymentInfoCreationRequest) {

        final List<PaymentInfoDraftModel> paymentInfoDrafts = finalizePaymentInfoCreationRequest.getPaymentDraftIds().stream()
            .map(this::finalizePendingPaymentInfoCreation)
            .toList();

        final List<PaymentInfoDraftModel> paymentInfoDraftsToRetry = paymentInfoDrafts.stream()
            .filter(paymentInfoDraft -> WAITING_FOR_CONFIRMATION.equals(paymentInfoDraft.getCreationStatus()))
            .toList();

        LOG.warn(
            "ALERT: Payment info drafts '{}' are still pending and should be retried",
            paymentInfoDraftsToRetry.stream().map(PaymentInfoDraftModel::getCode).toList()
        );

        return new FinalizePaymentInfoCreationResult(paymentInfoDraftsToRetry);
    }

    private PaymentInfoDraftModel finalizePendingPaymentInfoCreation(final String paymentInfoDraftId) {
        final PaymentInfoDraftModel infoDraft = paymentInfoDraftService.getPaymentInfoDraftByCodeOrThrow(paymentInfoDraftId);
        serviceResolver.getPaymentInfoCreationService(infoDraft.getPaymentProvider()).finalizePendingPaymentInfoCreation(infoDraft);
        return infoDraft;
    }
}
