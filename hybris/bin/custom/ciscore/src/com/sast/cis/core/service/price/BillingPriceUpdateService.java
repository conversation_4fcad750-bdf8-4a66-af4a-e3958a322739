package com.sast.cis.core.service.price;

import com.google.common.base.Preconditions;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.service.price.licensepricehandler.LicensePriceUpdateHandler;
import com.sast.cis.core.util.PriceUtil;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class BillingPriceUpdateService {
    private static final Logger LOG = LoggerFactory.getLogger(BillingPriceUpdateService.class);

    private final PriceUtil priceUtil;
    private final List<LicensePriceUpdateHandler> updateHandlers;

    public void updatePricesForLicense(AppLicenseModel appLicense) {
        Preconditions.checkArgument(appLicense != null, "Given appLicense is null");
        LOG.info("Updating prices of license with code={}", appLicense.getCode());
        Set<CurrencyModel> activeCurrencies = priceUtil.getActiveCurrencies();

        Optional<LicensePriceUpdateHandler> priceUpdateHandler = getApplicableHandler(appLicense);

        try {
            priceUpdateHandler.ifPresent(handler -> handler.updatePrices(appLicense, activeCurrencies));
        } catch (RuntimeException e) {
            throw new LicensePriceUpdateFailedException(String.format("Price update failed for license %s.", appLicense.getCode()), e);
        }
    }

    private Optional<LicensePriceUpdateHandler> getApplicableHandler(AppLicenseModel appLicense) {
        Set<LicensePriceUpdateHandler> applicableHandlers = updateHandlers.stream()
            .filter(updateHandler -> updateHandler.isApplicable(appLicense))
            .collect(Collectors.toUnmodifiableSet());

        if (applicableHandlers.size() > 1) {
            String applicableHandlerNames = applicableHandlers.stream()
                .map(handler -> handler.getClass().getSimpleName())
                .collect(Collectors.joining(", "));
            throw new LicensePriceUpdateFailedException(
                String.format("Found more than one applicable license price handler: [%s]", applicableHandlerNames));
        }

        return applicableHandlers.stream().findFirst();
    }
}
