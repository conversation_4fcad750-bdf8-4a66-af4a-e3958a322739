package com.sast.cis.core.validator.license.activation;

import com.sast.cis.core.model.LicenseActivationModel;
import com.sast.cis.core.validator.license.activation.rules.LicenseActivationValidationRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import static de.hybris.platform.servicelayer.util.ServicesUtil.validateParameterNotNull;

@Component
@RequiredArgsConstructor
@Slf4j
public class LicenseActivationValidator {

    private final List<LicenseActivationValidationRule> activationValidationRules;

    public void validate(@NotNull final LicenseActivationModel licenseActivation) {
        validateParameterNotNull(licenseActivation, "LicenseActivation must not be null");

        final Set<LicenseActivationValidationError> violations = new LinkedHashSet<>();
        CollectionUtils.emptyIfNull(activationValidationRules).forEach(rule ->
            violations.addAll(executeRule(rule, licenseActivation))
        );

        if (!violations.isEmpty()) {
            LOG.error(
                "License '{}' Activation for Company '{}' failed with validation errors: '{}'",
                licenseActivation.getAppLicense().getCode(),
                licenseActivation.getCompany().getUid(),
                violations
            );
            throw LicenseActivationValidationException.withErrors(violations);
        }
    }

    private Set<LicenseActivationValidationError> executeRule(
        final LicenseActivationValidationRule rule,
        final LicenseActivationModel licenseActivation) {

        if (rule.applies(licenseActivation)) {
            return rule.validate(licenseActivation);
        }
        return Set.of();
    }
}
