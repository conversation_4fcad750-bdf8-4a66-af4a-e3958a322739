package com.sast.cis.core.facade.cart;

import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.data.PlaceOrderData;
import com.sast.cis.core.distributor.AaDistributorNotFoundException;
import com.sast.cis.core.distributor.AaDistributorService;
import de.hybris.platform.core.model.order.CartModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@RequiredArgsConstructor
@Slf4j
@Component
public class AaDistributorExtensionHandler implements SessionCartExtensionHandler {

    @Resource
    private final AaDistributorService aaDistributorService;

    @Override
    public void extendSessionCart(@NonNull CartModel sessionCart, @NonNull PlaceOrderData placeOrderData) {

        if (!isAaStore(sessionCart)) {
            return;
        }

        // set nothing, if the buyer company does not have any distributors
        if (aaDistributorService.getDistributorsForBuyerCompany().isEmpty()) {
            LOG.warn("No distributors found for the buyer company. Skipping setting distributor in session cart.");
            return;
        }

        // throw exception if the distributor ID is not set in placeOrderData or distributor not found
        sessionCart.setAaDistributorCompany(aaDistributorService.findAaDistributorByID(placeOrderData.getDistributorID()).orElseThrow(
            () -> AaDistributorNotFoundException.forDistributorId(placeOrderData.getDistributorID())
        ));
    }

    private boolean isAaStore(CartModel sessionCart) {
        return sessionCart.getStore() != null && BaseStoreEnum.AA.getBaseStoreUid().equals(sessionCart.getStore().getUid());
    }
}
