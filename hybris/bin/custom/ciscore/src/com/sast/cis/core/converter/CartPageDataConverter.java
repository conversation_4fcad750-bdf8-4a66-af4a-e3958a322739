package com.sast.cis.core.converter;

import com.sast.cis.core.data.CartItemData;
import com.sast.cis.core.data.CartPageData;
import com.sast.cis.core.service.AbstractOrderHashService;
import de.hybris.platform.commercefacades.order.data.CartData;
import de.hybris.platform.order.CartService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CartPageDataConverter {

    @Resource
    private AbstractOrderHashService abstractOrderHashService;

    @Resource
    private CartService cartService;

    @Resource
    private CartItemDataConverter cartItemDataConverter;

    @Resource
    private PriceDataConverter priceDataConverter;

    public CartPageData convert(CartData cartData) {
        CartPageData cartPageData = new CartPageData();
        if (cartData.getTotalPrice() == null || cartData.getTotalTax() == null) {
            return cartPageData;
        }

        List<CartItemData> cartItems = cartData
            .getEntries()
            .stream()
            .map(cartItemDataConverter::convert)
            .collect(Collectors.toList());

        cartPageData
            .withTotalPrice(priceDataConverter.toPriceData(cartData.getTotalPrice()))
            .withTotalTax(priceDataConverter.toPriceData(cartData.getTotalTax()))
            .withTotalPriceWithTax(priceDataConverter.toPriceData(cartData.getTotalPriceWithTax()))
            .withCartItems(cartItems)
            .withCartCode(cartData.getCode())
            .withOwnAppsPurchase(cartData.isOwnAppsPurchase())
            .withDeveloperCompanyName(cartItems.stream().findFirst().map(CartItemData::getCompanyName).orElse(""));

        if (cartData.getOriginalTotalPrice() != null){
            cartPageData.withTotalPriceWithoutDiscount(priceDataConverter.toPriceData(cartData.getOriginalTotalPrice()));
        }

        if (cartService.hasSessionCart()) {
            cartPageData.setCartHash(abstractOrderHashService.calculateHash(cartService.getSessionCart()));
        }
        return cartPageData;
    }
}
