package com.sast.cis.core.service;

import com.google.common.base.Throwables;
import de.hybris.platform.core.model.security.PrincipalModel;
import de.hybris.platform.jalo.ConsistencyCheckException;
import de.hybris.platform.servicelayer.exceptions.ModelCreationException;
import de.hybris.platform.servicelayer.exceptions.ModelSavingException;
import de.hybris.platform.servicelayer.exceptions.UnknownIdentifierException;
import de.hybris.platform.servicelayer.interceptor.InterceptorException;
import de.hybris.platform.servicelayer.interceptor.impl.UniqueAttributesInterceptor;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.session.SessionExecutionBody;
import de.hybris.platform.servicelayer.session.SessionService;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.function.Supplier;

@Service
public class PrincipalCreationService<T extends PrincipalModel> {

    private static final Logger LOG = LoggerFactory.getLogger(PrincipalCreationService.class);

    @Resource
    private ModelService modelService;

    @Resource
    private SessionService sessionService;

    @Resource
    private FlexibleSearchService flexibleSearchService;

    public T createOrFind(T item, Supplier<T> instantiator, Object... auxiliaryItems) {
        try {
            saveItemTransactionally(item, auxiliaryItems);
        } catch (ModelSavingException | ModelCreationException e) {
            if (!exceptionIsDueToAlreadyExistingItem(e)) {
                throw e;
            }
            LOG.debug("Item to save or at least one of the auxiliary item(s) already exist(s)", e);
            return getExistingPrincipal(item, instantiator);
        }
        modelService.refresh(item);
        return item;
    }

    private void saveItemTransactionally(T item, Object... auxiliaryItems) {
        sessionService.executeInLocalView(new SessionExecutionBody() {
            @Override
            public Object execute() {
                modelService.enableTransactions();
                modelService.saveAll(ArrayUtils.insert(0, auxiliaryItems, item));
                LOG.info("Created new {} with uid={}. Additionally created: {}", item.getClass(), item.getUid(), auxiliaryItems);
                return item;
            }
        });
    }

    private boolean exceptionIsDueToAlreadyExistingItem(Exception e) {
        return modelService.isUniqueConstraintErrorAsRootCause(e)
                || isJaloUserGroupDuplicateUidException(e)
                || (e.getCause() instanceof UniqueAttributesInterceptor.AmbiguousUniqueKeysException)
                || ((e.getCause() instanceof InterceptorException)
                        && ((InterceptorException) e.getCause()).getInterceptor().getClass().equals(UniqueAttributesInterceptor.class));
    }

    // Companies are user groups, and there is an additional check in de.hybris.platform.jalo.user.UserGroup#createItem
    // that throws an exception with this message if there is another group with the same UID already.
    private boolean isJaloUserGroupDuplicateUidException(Exception e) {
        Throwable rootCause = Throwables.getRootCause(e);
        if (rootCause instanceof ConsistencyCheckException) {
            return rootCause.getMessage() != null && rootCause.getMessage().startsWith("Duplicate userGroup for uid");
        }
        return false;
    }

    private T getExistingPrincipal(T item, Supplier<T> instantiator) {
        LOG.info("Found existing entity for uid={}", item.getUid());
        T example = instantiator.get();
        example.setUid(item.getUid());
        try {
            return flexibleSearchService.getModelByExample(example);
        } catch (UnknownIdentifierException e) {
            LOG.error("ALERT Principal with uid={} could neither be created nor found. Aborting", example.getUid());
            throw new IllegalStateException("Principal could neither be created nor found for uid=" + example.getUid(), e);
        }
    }
}
