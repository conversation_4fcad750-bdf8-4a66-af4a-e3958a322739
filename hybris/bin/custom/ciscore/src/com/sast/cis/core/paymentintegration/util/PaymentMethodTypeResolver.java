package com.sast.cis.core.paymentintegration.util;

import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.model.*;
import de.hybris.platform.core.model.order.payment.CreditCardPaymentInfoModel;
import de.hybris.platform.core.model.order.payment.InvoicePaymentInfoModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;

import java.util.Optional;

public class PaymentMethodTypeResolver {

    private PaymentMethodTypeResolver() {}

    public static Optional<PaymentMethodType> forPaymentInfo(PaymentInfoModel paymentInfo) {
        if (paymentInfo instanceof CreditCardPaymentInfoModel) {
            return Optional.of(PaymentMethodType.CREDIT_CARD);
        } else if (paymentInfo instanceof SepaCreditTransferPaymentInfoModel) {
            return Optional.of(PaymentMethodType.SEPA_CREDIT);
        } else if (paymentInfo instanceof AchInternationalCreditTransferPaymentInfoModel) {
            return Optional.of(PaymentMethodType.ACH_INTERNATIONAL);
        } else if (paymentInfo instanceof ZeroPaymentInfoModel) {
            return Optional.of(PaymentMethodType.ZERO);
        } else if (paymentInfo instanceof InvoiceBySellerPaymentInfoModel) {
            return Optional.of(PaymentMethodType.INVOICE_BY_SELLER);
        } else if (paymentInfo instanceof InvoicePaymentInfoModel) {
            return Optional.of(PaymentMethodType.INVOICE);
        } else if (paymentInfo instanceof SepaMandatePaymentInfoModel) {
            return Optional.of(PaymentMethodType.SEPA_DIRECTDEBIT);
        }
        return Optional.empty();
    }
}
