package com.sast.cis.core.resolver;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppLicenseService;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.solrfacetsearch.config.IndexConfig;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import de.hybris.platform.solrfacetsearch.provider.FieldNameProvider;
import de.hybris.platform.solrfacetsearch.provider.FieldValue;
import de.hybris.platform.solrfacetsearch.provider.FieldValueProvider;
import de.hybris.platform.solrfacetsearch.provider.impl.AbstractPropertyFieldValueProvider;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@SuppressWarnings("deprecation")
@Component
@Slf4j
@AllArgsConstructor
public class AppCountriesValueProvider extends AbstractPropertyFieldValueProvider implements FieldValueProvider {
    private final FieldNameProvider solrFieldNameProvider;

    private final AppLicenseService appLicenseService;

    @Override
    public Collection<FieldValue> getFieldValues(IndexConfig indexConfig, IndexedProperty indexedProperty, Object app) {
        if (!(app instanceof AppModel)) {
            LOG.warn("Model {} is not an App; cannot index countries", app);
            return ImmutableList.of();
        }

        Collection<CountryModel> countries = appLicenseService.getCountryAvailability((AppModel) app);

        if (countries.isEmpty()) {
            return ImmutableList.of();
        }

        return countries.stream()
            .flatMap(country -> createFieldValue(country, indexedProperty))
            .collect(Collectors.toList());
    }

    private Stream<FieldValue> createFieldValue(CountryModel country, IndexedProperty indexedProperty) {
        String isocode = country.getIsocode();
        return solrFieldNameProvider.getFieldNames(indexedProperty, null).stream().map(fieldName -> new FieldValue(fieldName, isocode));
    }
}

