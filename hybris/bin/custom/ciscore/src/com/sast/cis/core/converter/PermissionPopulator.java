package com.sast.cis.core.converter;

import com.sast.cis.core.data.PermissionData;
import com.sast.cis.core.model.PermissionModel;
import com.sast.cis.core.service.PermissionService;
import de.hybris.platform.converters.Populator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PermissionPopulator implements Populator<PermissionModel, PermissionData> {

    @Resource
    private PermissionService permissionService;

    @Override
    public void populate(PermissionModel source, PermissionData target) {
        target.setName(permissionService.getName(source));
        target.setIconUrl(permissionService.getIconUrl(source));
    }
}
