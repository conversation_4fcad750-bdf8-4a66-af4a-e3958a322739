package com.sast.cis.core.exceptions.payment;

import java.io.Serial;

public class PaymentUpdateFailedException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;

    public PaymentUpdateFailedException() {
    }

    public PaymentUpdateFailedException(String message) {
        super(message);
    }

    public PaymentUpdateFailedException(String message, Throwable cause) {
        super(message, cause);
    }

    public PaymentUpdateFailedException(Throwable cause) {
        super(cause);
    }

    public PaymentUpdateFailedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
