package com.sast.cis.core.order.validation;

import com.sast.cis.core.eula.EulaAcceptanceService;
import com.sast.cis.core.exceptions.cart.MandatoryEulaNotAcceptedException;
import com.sast.cis.core.model.EulaAcceptanceModel;
import de.hybris.platform.core.model.order.CartModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class EulaAcceptanceValidationRule implements PreOrderPlacementValidationRule {

    private final EulaAcceptanceService eulaAcceptanceService;

    public void validate(@NonNull final CartModel cart) {
        final boolean orderRequiresEulaAcceptance = eulaAcceptanceService.orderRequiresEulaAcceptance(cart);
        if (!orderRequiresEulaAcceptance) {
            return;
        }
        final EulaAcceptanceModel eulaAcceptance = cart.getEulaAcceptance();
        if (eulaAcceptance == null) {
            throw MandatoryEulaNotAcceptedException.forCart(cart.getCode());
        }
    }
}
