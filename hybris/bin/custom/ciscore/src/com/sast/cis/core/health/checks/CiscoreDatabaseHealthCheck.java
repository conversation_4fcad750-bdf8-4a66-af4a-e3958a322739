package com.sast.cis.core.health.checks;

import com.sast.cis.core.health.CisHealthCheck;
import com.sast.cis.core.health.result.CisHealthCheckResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;

@Service
@RequiredArgsConstructor
@Slf4j
public class CiscoreDatabaseHealthCheck implements CisHealthCheck {
    private static final int CONNECTION_CHECK_TIMEOUT_SECONDS = 2;
    private static final String CHECK_NAME = "CiscoreDatabaseHealthCheck";

    private final DatabaseMasterTenantConnectionProvider databaseMasterTenantConnectionProvider;

    @Override
    public CisHealthCheckResult checkReadiness() {
        return checkDatabaseQuery();
    }

    @Override
    public CisHealthCheckResult checkStartup() {
        return checkDatabaseQuery();
    }

    private CisHealthCheckResult checkDatabaseQuery() {
        try (Connection connection = databaseMasterTenantConnectionProvider.getConnection()) {
            if (connection.isValid(CONNECTION_CHECK_TIMEOUT_SECONDS)) {
                return CisHealthCheckResult.healthy(CHECK_NAME);
            }
            return CisHealthCheckResult.unhealthy(CHECK_NAME, "Database connection is not valid.");
        } catch (Throwable e) {
            LOG.error("Exception while checking database connection", e);
            return CisHealthCheckResult.unhealthy(CHECK_NAME, String.format("Exception while checking database connection: %s", e.getMessage()));
        }
    }
}
