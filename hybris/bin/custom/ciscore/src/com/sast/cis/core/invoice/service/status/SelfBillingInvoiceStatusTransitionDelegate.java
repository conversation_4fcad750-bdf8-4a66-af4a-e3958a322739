package com.sast.cis.core.invoice.service.status;

import com.sast.cis.core.enums.SelfBillingInvoiceStatus;
import com.sast.cis.core.invoice.validation.SelfBillingInvoiceStatusTransitionValidator;
import com.sast.cis.core.model.SelfBillingInvoiceModel;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;


@Component
@Slf4j
@RequiredArgsConstructor
public class SelfBillingInvoiceStatusTransitionDelegate {

    private final SelfBillingInvoiceStatusResolver sbiStatusResolver;
    private final ModelService modelService;
    private final SelfBillingInvoiceStatusTransitionValidator selfBillingInvoiceStatusTransitionValidator;

    public void updateSelfBillingInvoiceStatusOnSelfBillingInvoiceCreditNoteCreation(@NotNull final SelfBillingInvoiceModel selfBillingInvoice) {
        LOG.info("Update Self Billing Invoice '{}' status on credit note creation", selfBillingInvoice.getExternalId());

        modelService.refresh(selfBillingInvoice);

        final SelfBillingInvoiceStatus selfBillingInvoiceStatus = sbiStatusResolver.resolveStatusForSelfBillingInvoiceWithCreditNotes(selfBillingInvoice);
        setSelfBillingInvoiceStatusTo(selfBillingInvoice, selfBillingInvoiceStatus);
    }

    private void setSelfBillingInvoiceStatusTo(final SelfBillingInvoiceModel selfBillingInvoice, final SelfBillingInvoiceStatus targetStatus) {
        LOG.info("Set Self Billing Invoice '{}' status from '{}' to '{}'", selfBillingInvoice.getExternalId(), selfBillingInvoice.getStatus(), targetStatus);

        selfBillingInvoiceStatusTransitionValidator.validateStatusTransition(selfBillingInvoice, targetStatus);
        selfBillingInvoice.setStatus(targetStatus);
        modelService.save(selfBillingInvoice);
    }
}
