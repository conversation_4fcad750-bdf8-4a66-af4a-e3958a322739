package com.sast.cis.core.service.license;

import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.service.AppLicenseService;
import de.hybris.platform.core.model.c2l.CountryModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.sast.cis.core.dao.CatalogVersion.ONLINE;

/**
 * This class provides utility functions to search for Licenses in the context of a country.
 *
 * The utility functions require the Product Catalog to be set in the session.
 */
@Component
@RequiredArgsConstructor
public class CountryAvailabilityAwareLicenseFinder {

    private final AppLicenseService appLicenseService;

    /**
     * Find the license for a given sellerProductId.
     * <p/>
     * Apps and Licenses are duplicated for each country. This method will return the license for the given country.
     *
     * @param sellerProductId - the sellerProductId of the license
     * @param country - the country for which the license is being searched
     * @return the AppLicenseService for the given sellerProductId and company
     */
    public Optional<AppLicenseModel> bySellerProductIdForCountry(final String sellerProductId, final CountryModel country) {
        final String normalizedSellerProductId = normalizeSellerProductId(sellerProductId);

        return appLicenseService.getAppLicenseForSellerProductIdAndCountry(normalizedSellerProductId, country, ONLINE);
    }

    public AppLicenseModel bySellerProductIdForCountryOrThrow(final String sellerProductId, final CountryModel country) {
        return bySellerProductIdForCountry(sellerProductId, country)
            .orElseThrow(() -> AppLicenseNotFoundException.forSellerProductIdAndCountry(sellerProductId, country));
    }

    private String normalizeSellerProductId(final String materialNumber) {
        if (materialNumber.length() == 13 && materialNumber.endsWith("999")) {
            return materialNumber.substring(0, 10);
        }
        return materialNumber;
    }
}
