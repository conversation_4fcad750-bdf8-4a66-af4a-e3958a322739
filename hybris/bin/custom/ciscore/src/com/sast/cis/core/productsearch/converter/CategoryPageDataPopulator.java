package com.sast.cis.core.productsearch.converter;

import com.google.common.collect.ImmutableMap;
import com.sast.cis.core.data.CategoryPageData;
import com.sast.cis.core.data.FacetData;
import com.sast.cis.core.data.FacetItem;
import com.sast.cis.core.data.SimpleProductData;
import com.sast.cis.core.enums.CategoryPageOption;
import com.sast.cis.core.productsearch.dto.ProductFacetIndex;
import com.sast.cis.core.service.security.UserPermissionService;
import de.hybris.platform.commercefacades.product.data.CategoryData;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.commercefacades.search.data.SearchStateData;
import de.hybris.platform.commerceservices.search.facetdata.ProductCategorySearchPageData;
import de.hybris.platform.commerceservices.search.pagedata.PaginationData;
import de.hybris.platform.converters.ConfigurablePopulator;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class CategoryPageDataPopulator implements
    ConfigurablePopulator<ProductCategorySearchPageData<SearchStateData, ProductData, CategoryData>, CategoryPageData, CategoryPageOption> {
    private static final Map<String, String> AVAILABILITY_FACET_INDEX_MAP = ImmutableMap.of(
        ProductFacetIndex.LICENSE_TYPES.getCode(), ProductFacetIndex.LICENSE_TYPES.getCode(),
        ProductFacetIndex.COUNTRIES.getCode(), ProductFacetIndex.COUNTRIES.getCode(),
        ProductFacetIndex.PRIVATE_APPS.getCode(), ProductFacetIndex.PRIVATE_APPS.getCode()
    );

    private final ConfigurablePopulator<ProductData, SimpleProductData, CategoryPageOption> gridItemConfiguredPopulator;
    private final UserPermissionService userPermissionService;

    @SuppressWarnings({ "deprecation", "removal" })
    @Override
    public void populate(ProductCategorySearchPageData<SearchStateData, ProductData, CategoryData> searchPageData,
        CategoryPageData categoryPage, Collection<CategoryPageOption> productDataOptions) {
        if (CollectionUtils.isNotEmpty(searchPageData.getResults())) {
            List<SimpleProductData> gridItems = searchPageData.getResults().stream()
                .map(g -> createGridItem(g, productDataOptions))
                .collect(Collectors.toList());

            categoryPage.setProducts(gridItems);
            PaginationData pagination = searchPageData.getPagination();
            categoryPage.setTotalNumberOfResults(pagination.getTotalNumberOfResults());
            categoryPage.setPageNumber(pagination.getCurrentPage() + 1);
            categoryPage.setTotalNumberOfPages(pagination.getNumberOfPages());
            categoryPage.setPageSize(pagination.getPageSize());
            categoryPage.setSearchQuery(searchPageData.getFreeTextSearch());
            categoryPage.setFacets(buildFacetsFromSolr(searchPageData.getFacets()));
        } else {
            categoryPage
                .withProducts(Collections.emptyList())
                .withFacets(Collections.emptyList());
        }
    }

    private SimpleProductData createGridItem(ProductData productData, Collection<CategoryPageOption> productDataOptions) {
        SimpleProductData simpleProductData = new SimpleProductData();
        gridItemConfiguredPopulator.populate(productData, simpleProductData, productDataOptions);

        return simpleProductData;
    }

    private List<FacetData> buildFacetsFromSolr(
        List<de.hybris.platform.commerceservices.search.facetdata.FacetData<SearchStateData>> solrFacets) {
        if (CollectionUtils.isEmpty(solrFacets)) {
            return Collections.emptyList();
        }

        final var result = new ArrayList<FacetData>();

        Arrays.stream(ProductFacetIndex.values())
            .filter(facetIndex -> userPermissionService.isAuthenticated() || facetIndex.isPublic())
            .sorted((facet1, facet2) -> Long.compare(facet2.getOrderIndex(), facet1.getOrderIndex()))
            .forEach(facetIndex -> {
                FacetData facet = new FacetData()
                    .withGroup(facetIndex.getCode())
                    .withOrderIndex(facetIndex.getOrderIndex());

                solrFacets.forEach(solrFacet -> {
                    String facetCode = solrFacet.getCode();

                    if (facetIndex.getCode().equals(facetCode)) {
                        final var facetItems = new ArrayList<FacetItem>();
                        solrFacet.getValues()
                            .stream()
                            .sorted((item1, item2) -> Long.compare(item2.getCount(), item1.getCount()))
                            .forEach(value -> facetItems.add(
                                new FacetItem()
                                    .withName(value.getName())
                                    .withCount(value.getCount())
                                    .withFacetValue(value.getName())
                                    .withFacetIndex(
                                        AVAILABILITY_FACET_INDEX_MAP.getOrDefault(facetIndex.getCode(), facetIndex.getCode()))
                            ));

                        facet.setItems(facetItems);
                        result.add(facet);
                    }
                });

            });

        return result;
    }
}
