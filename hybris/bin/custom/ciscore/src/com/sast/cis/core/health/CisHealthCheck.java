package com.sast.cis.core.health;


import com.sast.cis.core.health.result.CisHealthCheckResult;

/**
 * Performs a startup and readiness check
 */
public interface CisHealthCheck {
    /**
     * Performs a readiness check.
     * <br/>
     * A readiness check assesses whether this application instance is currently able to process requests.
     *
     * @return health check result in status HEALTHY if the application can currently process requests, UNHEALTHY otherwise
     */
    CisHealthCheckResult checkReadiness();

    /**
     * Performs a startup check.
     * <br/>
     * A startup check assesses whether this application has finished starting up.
     *
     * @return health check result in status HEALTHY if the application has started, UNHEALTHY otherwise
     */
    CisHealthCheckResult checkStartup();
}
