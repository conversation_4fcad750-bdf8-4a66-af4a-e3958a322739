package com.sast.cis.core.paymentintegration.data;

import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthorizationResult {
    private PaymentTransactionEntryModel paymentTransactionEntry;
    private AuthorizationStatus authorizationStatus;
    private Map<String, String> userActionParameters = Map.of();
}
