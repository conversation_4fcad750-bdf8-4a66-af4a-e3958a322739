package com.sast.cis.core.strategy.media;

import de.hybris.platform.amazon.media.services.S3StorageServiceFactory;
import de.hybris.platform.amazon.media.url.S3MediaURLStrategy;
import de.hybris.platform.media.MediaSource;
import de.hybris.platform.media.storage.MediaStorageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.VisibleForTesting;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;

import java.util.stream.Collectors;

@Slf4j
public class S3StaticMediaURLStrategy extends S3MediaURLStrategy {
    @Value("${media.cdn.url:}")
    private String cdnUrl;

    public S3StaticMediaURLStrategy(S3StorageServiceFactory s3StorageServiceFactory) {
        super(s3StorageServiceFactory);
    }

    @Override
    public String getUrlForMedia(MediaStorageConfigService.MediaFolderConfig config, MediaSource media) {
        String url = super.getUrlForMedia(config, media);
        return changeToCdnHost(url);
    }

    @VisibleForTesting
    String changeToCdnHost(String url) {
        if (!StringUtils.isEmpty(cdnUrl)) {
            final var uriComponents = UriComponentsBuilder.fromHttpUrl(url).host(cdnUrl).build();
            // path without sysMaster/images
            final var newPath = uriComponents.getPathSegments().stream()
                .skip(2)
                .collect(Collectors.joining("/"));

            final var newUrl = UriComponentsBuilder.fromUri(uriComponents.toUri())
                .replacePath(newPath)
                .build()
                .toString();
            LOG.info("new Url : {}", newUrl);
            return newUrl;
        }
        return url;
    }

    @PostConstruct
    public void init() {
        LOG.info("Images cdn: {}", cdnUrl);
    }
}
