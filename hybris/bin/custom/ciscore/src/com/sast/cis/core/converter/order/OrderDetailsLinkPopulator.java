package com.sast.cis.core.converter.order;

import com.sast.cis.core.data.UrlData;
import com.sast.cis.core.model.SepaCreditTransferPaymentInfoModel;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.order.OrderModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class OrderDetailsLinkPopulator implements Populator<OrderModel, UrlData> {
    private static final String ORDER_DETAILS_LINK_BASE_URL = "/shop/my-account/orders/";

    @Override
    public void populate(OrderModel order, UrlData urlData) {
        if (order == null || StringUtils.isEmpty(order.getCode())) {
            throw new IllegalStateException("Trying to convert a null object or order code is empty");
        }
        boolean sepaCreditTransferUsed = order.getPaymentInfo() instanceof SepaCreditTransferPaymentInfoModel;
        urlData
            .withCode(order.getCode())
            .withUrl(ORDER_DETAILS_LINK_BASE_URL + order.getCode())
            .withOwnAppOrder(order.isOwnFullAppOrder())
            .withSepaPaymentUsed(sepaCreditTransferUsed);
    }

}
