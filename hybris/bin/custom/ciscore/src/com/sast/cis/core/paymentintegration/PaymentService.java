package com.sast.cis.core.paymentintegration;

import com.sast.cis.core.data.PaymentInfoData;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter;
import com.sast.cis.core.paymentintegration.data.AuthorizationResult;
import com.sast.cis.core.paymentintegration.data.CheckoutInfo;
import com.sast.cis.core.paymentintegration.data.PaymentRedirectResult;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.NonNull;

import java.util.Set;

public interface PaymentService {
    /**
     * Return payment provider for the implementation
     * @return
     */
    PaymentProvider getPaymentProvider();

    /**
     * Prepares checkout information (may be psp specific), returns
     * matching stored PaymentInfos and necessary data for the UI to create new PaymentInfos.
     * <p><em>Must</em> be called in preparation of payment method selection by the user.</p>
     * <p>This method may have sideeffects from creating a checkout at the payment provider.</p>
     *
     * @param authorizationParameter Parameter for subsequent authorization
     * @param paymentMethod Payment method for which the checkout is prepared for an implementing PaymentService
     * @return
     */
    CheckoutInfo prepareCheckout(@NonNull AuthorizationParameter authorizationParameter, @NonNull PaymentMethodType paymentMethod);

    /**
     * Confirms the paymentInfo attached to the given order with the payment provider.
     * <p>This method is called after a paymentInfo has been attached to the order and before payment authorization is performed.</p>
     *
     * @param authorizationParameter Parameter for subsequent authorization
     */
    void confirmSelectedPaymentInfo(@NonNull AuthorizationParameter authorizationParameter);

    /**
     * Attempt to authorize a given cart with the attached PaymentInfo.
     * May come back with successful status, failed status or demanding user interaction, after which this method can be called again.
     *
     * @param authorizationParameter Parameter for subsequent authorization
     * @return Payment transaction entry representing the performed authorization
     */
    PaymentTransactionEntryModel authorize(@NonNull AuthorizationParameter authorizationParameter);

    /**
     * Provides the detailed authorization result for the given payment transaction.
     * <p>If statuses SUCCESS or NOOP are returned, no further action is required and the authorization must be considered successful.</p>
     * <p>If status REQUIRE_USER is returned, further PSP-specific user interaction is required, with data required by the UI
     * returned in the userActionParameters map.</p>
     * <p>If status ERROR is returned, the authorization failed and the user must be instructed to choose a different payment.</p>
     *
     * @param paymentTransactionEntryModel Payment transaction entry representing the performed authorization
     * @return Detailed result of the performed authorization
     */
    AuthorizationResult getAuthorizationResult(@NonNull PaymentTransactionEntryModel paymentTransactionEntryModel);

    /**
     * Attempt to capture a given paymentTransaction
     *
     * @param paymentTransaction
     */
    void capture(PaymentTransactionModel paymentTransaction);

    /**
     * Attempt to refund a given payment transaction
     *
     * @param paymentTransaction
     */
    void refund(PaymentTransactionModel paymentTransaction);

    /**
     * Notify about the result of a user redirect (e.g. hosted payment page).
     * <br/>
     * Implementations can choose to ignore this call, but must not throw in this case.
     * <br/>
     * Payment redirect result can have the following statuses:
     * <ul>
     *     <li>SUCCESS: User has called the success redirect URL</li>
     *     <li>FAILURE: User has called the failure redirect URL</li>
     *     <li>CANCEL: User has called the cancel redirect URL</li>
     * </ul>
     * These statuses correspond to redirect statuses as provided by PaymentRedirectTargetProvider
     *
     * @param redirectResult The redirect result
     */
    default void notifyRedirectResult(@NonNull final PaymentRedirectResult redirectResult) {
        // noop by default
    }

    /**
     * Check whether this payment service supports the given payment method for the given buyer and seller combination.
     *
     * <p>The rules can be rather complex and very PSP-specific, so we need to provide all information at once.</p>
     *
     * @param paymentMethod PaymentMethod to be evaluated
     * @param buyerCompany Buyer company for the evaluated purchase
     * @param licenses all licenses to be purchased
     * @return
     */
    boolean supportsPurchase(@NonNull PaymentMethodType paymentMethod, @NonNull IoTCompanyModel buyerCompany,
        @NonNull Set<AppLicenseModel> licenses);

    /**
     * Check whether this payment service supports the given payment method for the given seller and licenseType.
     * <p>This method returns whether a given seller can be paid for the given licenseType via the given payment method using
     * the implemented PSP.</p>
     *
     * <p>The rules can be rather complex and very PSP-specific, so we need to provide all information at once.</p>
     *
     * @param paymentMethod Payment method to be evaluated
     * @param sellerCompany Seller company which shall receive payouts
     * @return
     */
    boolean supportsSale(@NonNull PaymentMethodType paymentMethod, @NonNull IoTCompanyModel sellerCompany);

    /**
     * Check whether this payment service can setup the payment for the given seller.
     *
     * The rule is very PSP-specific.
     *
     * @param sellerCompany
     * @return
     */
    boolean canSetupPayment(IoTCompanyModel sellerCompany);

    /**
     * Check whether the given seller has valid payout account with this payment service.
     *
     * The rule is very PSP-specific.
     *
     * @param sellerCompany
     * @return
     */
    boolean isPayoutAccountValidated(IoTCompanyModel sellerCompany);

    /**
     * Initiate seller onboarding process for given company
     *
     * @param sellerCompany
     */
    void createSellerAccount(IoTCompanyModel sellerCompany);

    /**
     * Create payment info based on the given data for the given integrator.
     *
     * @param integrator
     * @param paymentInfoData
     * @return
     */
    PaymentInfoModel createPaymentInfo(IntegratorModel integrator, PaymentInfoData paymentInfoData);
}
