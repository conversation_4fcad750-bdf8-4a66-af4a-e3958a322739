package com.sast.cis.core.resolver;

import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.media.CisMediaContainerService;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import de.hybris.platform.solrfacetsearch.config.exceptions.FieldValueProviderException;
import de.hybris.platform.solrfacetsearch.indexer.IndexerBatchContext;
import de.hybris.platform.solrfacetsearch.indexer.spi.InputDocument;
import de.hybris.platform.solrfacetsearch.provider.ValueResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.Collection;
import java.util.Optional;

import static com.sast.cis.core.constants.MediaFormat.LARGE_ICON;

@Component
public class LogoUrlValueResolver implements ValueResolver<AppModel> {

    private static final Logger LOG = LoggerFactory.getLogger(LogoUrlValueResolver.class);

    @Resource
    private CisMediaContainerService cisMediaContainerService;

    @Override
    public void resolve(InputDocument inputDocument, IndexerBatchContext indexerBatchContext, Collection<IndexedProperty> collection,
        AppModel app) throws FieldValueProviderException {
        LOG.debug("Converting icon for app with code={}", app.getCode());

        if (app.getIcon() == null) {
            return;
        }
        MediaModel appIcon = cisMediaContainerService.getMediaForFormat(app.getIcon(), LARGE_ICON);
        LOG.debug("Done converting icon. Resulting media with code={} and url={}", appIcon.getCode(), appIcon.getURL());

        Optional<IndexedProperty> indexedProperty = collection.stream().findFirst();
        if (indexedProperty.isPresent()) {
            LOG.debug("Adding url to solr index.");
            inputDocument.addField(indexedProperty.get(), appIcon.getURL());
        }
    }
}
