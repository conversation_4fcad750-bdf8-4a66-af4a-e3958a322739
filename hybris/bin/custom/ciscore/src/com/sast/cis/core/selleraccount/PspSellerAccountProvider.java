package com.sast.cis.core.selleraccount;

import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.PspSellerAccountModel;
import com.sast.cis.core.service.order.OrderSellerProvider;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Optional;

import static com.sast.cis.core.enums.BillingSystemStatus.IN_SYNC;
import static com.sast.cis.core.enums.PspSellerAccountStatus.ACTIVE;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;

@RequiredArgsConstructor
public abstract class PspSellerAccountProvider<T extends PspSellerAccountModel> {
    private final OrderSellerProvider orderSellerProvider;

    /**
     * Retrieves the active seller account to be used for the given order.
     * A seller account is considered active and matching the given order only if all the following criteria are met:
     * <ol>
     *     <li>Payment provider matches the implementation</li>
     *     <li>Account status is ACTIVE</li>
     *     <li>Billing system status is IN_SYNC</li>
     *     <li>Type matches the implementation's seller account model class</li>
     * </ol>
     *
     * @param order order for which the seller account is to be determined
     * @return the seller account for the given order, or empty if none could be found
     */
    public Optional<T> getSellerAccountForOrder(@NonNull final AbstractOrderModel order) {
        final IoTCompanyModel sellerForOrder = orderSellerProvider.getSellerForOrderOrThrow(order);
        return getActiveSellerAccountForCompany(sellerForOrder);
    }

    public T getSellerAccountForOrderOrThrow(@NonNull final AbstractOrderModel order) {
        return getSellerAccountForOrder(order).orElseThrow(
            () -> new RuntimeException("Can not determine %s seller account for order %s".formatted(getPaymentProvider(), order.getCode()))
        );
    }

    /**
     * Retrieves the active seller account for the given company.
     * A seller account is considered active only if all the following criteria are met:
     * <ol>
     *     <li>Payment provider matches the implementation</li>
     *     <li>Account status is ACTIVE</li>
     *     <li>Billing system status is IN_SYNC</li>
     *     <li>Type matches the implementation's seller account model class</li>
     * </ol>
     *
     * @param company the company for which the seller account is to be determined
     * @return the seller account for the given company, or empty if none could be found
     * @throws IllegalStateException if more than one seller account could be found
     */
    public Optional<T> getActiveSellerAccountForCompany(@NonNull final IoTCompanyModel company) {
        final PaymentProvider paymentProvider = getPaymentProvider();
        final Class<T> sellerAccountType = getSellerAccountType();
        final List<T> sellerAccounts = emptyIfNull(company.getPspSellerAccounts()).stream()
            .filter(pspSellerAccount -> paymentProvider == pspSellerAccount.getPaymentProvider())
            .filter(pspSellerAccount -> ACTIVE == pspSellerAccount.getStatus())
            .filter(pspSellerAccount -> IN_SYNC == pspSellerAccount.getBillingSystemStatus())
            .filter(sellerAccountType::isInstance)
            .map(sellerAccountType::cast)
            .toList();

        if (sellerAccounts.size() > 1) {
            throw new IllegalStateException(
                String.format("Company %s has more than one active %s seller account", company.getUid(), paymentProvider)
            );
        }

        return sellerAccounts.stream().findFirst();
    }

    public T getActiveSellerAccountForCompanyOrThrow(@NonNull final IoTCompanyModel company) {
        final PaymentProvider provider = getPaymentProvider();
        return getActiveSellerAccountForCompany(company).orElseThrow(
            () -> new RuntimeException("Can not determine %s seller account for company %s".formatted(provider, company.getUid()))
        );
    }

    protected abstract PaymentProvider getPaymentProvider();

    protected abstract Class<T> getSellerAccountType();
}
