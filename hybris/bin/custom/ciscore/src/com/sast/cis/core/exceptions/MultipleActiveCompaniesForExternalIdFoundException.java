package com.sast.cis.core.exceptions;

import lombok.Getter;

@Getter
public class MultipleActiveCompaniesForExternalIdFoundException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private final String companyId;

    public MultipleActiveCompaniesForExternalIdFoundException(String message, String companyId) {
        super(message);
        this.companyId = companyId;
    }

    public static MultipleActiveCompaniesForExternalIdFoundException forCompanyId(final String companyId) {
        return new MultipleActiveCompaniesForExternalIdFoundException(
            String.format("multiple companies found for externalCompanyId='%s'", companyId),
            companyId
        );
    }
}
