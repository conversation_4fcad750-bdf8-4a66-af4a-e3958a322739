package com.sast.cis.core.dao;

import com.sast.cis.core.model.IndustryModel;
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.search.SearchResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Repository
public class IndustryDao {
    private static final String FIND_INDUSTRY_BY_IDS = "SELECT {" + IndustryModel.PK + "} "
        + "FROM {Industry} "
        + "WHERE {" + IndustryModel.PK + "} in (?industryIds)";

    private FlexibleSearchService flexibleSearchService;

    @Autowired
    public IndustryDao(FlexibleSearchService flexibleSearchService) {
        this.flexibleSearchService = flexibleSearchService;
    }

    public List<IndustryModel> getAllEnabledIndustries() {
        IndustryModel industry = new IndustryModel();
        industry.setEnabled(true);

        return flexibleSearchService.getModelsByExample(industry);
    }

    public Set<IndustryModel> getIndustriesByIds(Set<Long> industryIds) {
        if (CollectionUtils.isEmpty(industryIds)) {
            return Collections.emptySet();
        }

        FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_INDUSTRY_BY_IDS);
        query.addQueryParameter("industryIds", industryIds);
        SearchResult<IndustryModel> searchResult = flexibleSearchService.search(query);

        return new HashSet<>(searchResult.getResult());
    }
}