package com.sast.cis.core.paymentintegration.impl;

import com.google.common.base.Preconditions;
import com.sast.cis.core.paymentintegration.PaymentTransactionService;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.dto.TransactionStatus;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Optional;
import java.util.stream.Stream;

import static de.hybris.platform.payment.enums.PaymentTransactionType.AUTHORIZATION;

@Service
@Slf4j
public class PaymentTransactionServiceImpl implements PaymentTransactionService {
    @Override
    public Optional<PaymentTransactionModel> getAuthorizedTransactionForCurrentPaymentInfo(AbstractOrderModel order) {
        Preconditions.checkArgument(order != null, "Given order is null");
        Preconditions.checkArgument(order.getPaymentInfo() != null, "Given order %s has no payment info", order.getCode());
        PaymentInfoModel paymentInfo = order.getPaymentInfo();
        return getTransactionStream(order, AUTHORIZATION)
            .filter(paymentTransaction -> paymentTransaction.getInfo() != null)
            // we have to compare the code here because the payment info may be a clone
            .filter(paymentTransaction -> paymentInfo.getCode().equals(paymentTransaction.getInfo().getCode()))
            .filter(this::isNotCancelled)
            .filter(this::isSuccessfullyAuthorized)
            .findFirst();
    }

    private Stream<PaymentTransactionModel> getTransactionStream(AbstractOrderModel order, PaymentTransactionType paymentTransactionType) {
        return order.getPaymentTransactions().stream()
            .filter(paymentTransaction -> paymentTransactionType.equals(paymentTransaction.getType()))
            .sorted(Comparator.comparing(PaymentTransactionModel::getModifiedtime).reversed());
    }

    /*
    A payment transaction is cancelled if it has at least one entry of type CANCEL
     */
    private boolean isNotCancelled(PaymentTransactionModel paymentTransaction) {
        return paymentTransaction.getEntries().stream()
            .noneMatch(transactionEntry -> PaymentTransactionType.CANCEL.equals(transactionEntry.getType()));
    }

    /*
    A payment transaction is fully authorized if it has at least one entry of type AUTHORIZATION in status ACCEPTED
     */
    private boolean isSuccessfullyAuthorized(PaymentTransactionModel paymentTransaction) {
        return paymentTransaction.getEntries().stream()
            .filter(transactionEntry -> AUTHORIZATION.equals(transactionEntry.getType()))
            .anyMatch(transactionEntry -> TransactionStatus.ACCEPTED.name().equals(transactionEntry.getTransactionStatus()));
    }
}
