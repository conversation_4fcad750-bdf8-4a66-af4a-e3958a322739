package com.sast.cis.core.order.service;

import com.sast.cis.core.order.exception.IllegalOrderStatusTransitionException;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

import static de.hybris.platform.core.enums.OrderStatus.*;
import static java.util.Optional.ofNullable;

/**
 * Service responsible for managing and validating order status transitions.
 * <p>
 * This service enforces business rules by ensuring that an order can only transition between valid statuses as defined by the system.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderStatusTransitionService {
    private static final Set<OrderStatus> SUCCESS_STATUSES = Set.of(OPEN, COMPLETED);

    private static final Map<OrderStatus, Set<OrderStatus>> VALID_STATUS_TRANSITIONS = Map.of(
        // Initial status of the order.
        CREATED, Set.of(COMPLETED, OPEN, AWAITING_LICENSE_ACTIVATION, ON_VALIDATION, REJECTED, ERROR),
        // OPEN orders are for orders with future dated contracts
        OPEN, Set.of(COMPLETED, REJECTED, REJECTION_IN_PROGRESS, ERROR),
        // Owner company is still in VALIDATING operational stage, not OPERATIONAL. After the approval event is received the order will proceed to the next status.
        ON_VALIDATION, Set.of(COMPLETED, OPEN, AWAITING_LICENSE_ACTIVATION, REJECTED, ERROR, CANCELLED),
        // Order is waiting for the license to be activated in LMP. After the activation the order will be exported to BRIM.
        AWAITING_LICENSE_ACTIVATION, Set.of(COMPLETED, OPEN, REJECTED, ERROR, CANCELLED),
        // This is a temporary status for orders that are being rejected.
        REJECTION_IN_PROGRESS, Set.of(REJECTED, ERROR),
        // A rejected order can be re-exported.
        REJECTED, Set.of(COMPLETED, OPEN, ERROR, CANCELLED),
        CANCELLING, Set.of(CANCELLED, ERROR),
        // Order export can be retried after an error.
        ERROR, Set.of(COMPLETED, OPEN, AWAITING_LICENSE_ACTIVATION, REJECTED),
        // Final state. A COMPLETED order cannot be transitioned to any other status.
        COMPLETED, Set.of()
    );

    private final ModelService modelService;

    public void transitionStatus(@NonNull final OrderModel order, @NonNull final OrderStatus targetStatus) {
        transitionStatus(order, targetStatus, null);
    }

    public void transitionStatus(
        @NonNull final OrderModel order,
        @NonNull final OrderStatus targetStatus,
        final String statusInfo) {

        LOG.info("Transition Order with code '{}' from '{}' to '{}'", order.getCode(), order.getStatus(), targetStatus);

        final OrderStatus currentStatus = order.getStatus();
        if (currentStatus == null) {
            throw new IllegalStateException("ALERT: Order with code '%s' has no status".formatted(order.getCode()));
        }

        if (currentStatus.equals(targetStatus)) {
            LOG.info("Order with code={} is already in {} status", order.getCode(), targetStatus);
            return;
        }

        if (!isValidTransition(currentStatus, targetStatus)) {
            throw IllegalOrderStatusTransitionException.forTransition(currentStatus, targetStatus, order.getCode());
        }

        ofNullable(statusInfo).ifPresent(order::setStatusInfo);
        order.setStatus(targetStatus);
        modelService.save(order);
        modelService.refresh(order);

        LOG.info(
            "Order with code={} successfully transitioned from {} to {} status", order.getCode(), currentStatus, targetStatus
        );
    }

    /**
     * Validates whether an order can transition from its current status to the target status.
     * If the current and target statuses are the same, the transition is considered valid.
     *
     * @param order       The order whose transition is to be validated
     * @param targetStatus The target status
     * @return true if the transition is valid, false otherwise
     */
    public boolean canOrderBeTransitionedTo(@NonNull final OrderModel order, @NonNull final OrderStatus targetStatus) {
        LOG.debug("Checking if Order with code={} can be transitioned to {} status", order.getCode(), targetStatus);
        final OrderStatus currentStatus = order.getStatus();
        return currentStatus.equals(targetStatus) || isValidTransition(currentStatus, targetStatus);
    }

    /**
     * Check if the transition from currentStatus to targetStatus represents a 'success' transition.
     * A success transition means that the transition is valid and the targetStatus is one of the successful outcomes.
     * COMPLETED for orders that have been created in BRIM, and whose contracts have been successfully activated.
     * OPEN for orders that have been created in BRIM, but whose contracts have not been activated yet. This is relevant for migration orders.
     *
     * @param currentStatus The current status of the order
     * @param newStatus The target status of the order
     * @return true if the transition is valid and the targetStatus is a success status, false otherwise
     */
    public boolean isSuccessStatusTransition(@NonNull final OrderStatus currentStatus, @NonNull final OrderStatus newStatus) {
        LOG.debug("Checking if transition from {} to {} is a success transition", currentStatus, newStatus);
        return isValidTransition(currentStatus, newStatus) && SUCCESS_STATUSES.contains(newStatus);
    }

    /**
     * Check if the status represents a 'success' status.
     *
     * @param status The status to be checked
     * @return true if the status is a success status, false otherwise
     */
    public boolean isSuccessStatus(@NonNull final OrderStatus status) {
        LOG.debug("Checking if status {} is a success transition", status);
        return SUCCESS_STATUSES.contains(status);
    }

    private boolean isValidTransition(final OrderStatus currentStatus, final OrderStatus newStatus) {
        final Set<OrderStatus> validStatuses = VALID_STATUS_TRANSITIONS.getOrDefault(currentStatus, Set.of());
        return validStatuses.contains(newStatus);
    }
}
