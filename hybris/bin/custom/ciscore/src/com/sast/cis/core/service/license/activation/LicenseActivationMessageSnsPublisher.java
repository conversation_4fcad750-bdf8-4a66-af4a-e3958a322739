package com.sast.cis.core.service.license.activation;

import com.amazonaws.services.sns.model.AmazonSNSException;
import com.sast.cis.core.constants.AwsResource;
import com.sast.cis.core.data.LicenseActivationMessage;
import com.sast.cis.core.model.LicenseActivationModel;
import com.sast.cis.core.service.ObjectMapperService;
import com.sast.cis.core.service.aws.MessageQueueService;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Publish License Activation messages to an SNS topic.
 *
 * The order of delivery is important, and therefore it is assumed that the destination topic is a FIFO topic.
 *
 * FIFO topics require Message Group IDs to be set.
 * The order of delivery matters only when messages change the activation status for the same company, and therefore the company identifier is used as Message Group ID.
 *
 * @see <a href="https://docs.aws.amazon.com/sns/latest/dg/fifo-message-grouping.html">FIFO Message Grouping</a>
 */
@Slf4j
@RequiredArgsConstructor
@Component
class LicenseActivationMessageSnsPublisher implements LicenseActivationMessagePublisher {

    private final MessageQueueService messageQueueService;

    private final Converter<LicenseActivationModel, LicenseActivationMessage> licenseActivationToLicenseActivationMsgConverter;

    private final ObjectMapperService objectMapperService;

    /**
     * Publish a License Activation message to be consumed by DMP
     *
     * @param licenseActivation Company license activation status
     *
     * @throws LicenseActivationException - Exception thrown when publication fails
     */
    public void publish(final LicenseActivationModel licenseActivation) {
        final LicenseActivationMessage licenseActivationMessage =
            licenseActivationToLicenseActivationMsgConverter.convert(licenseActivation);
        final String jsonMessage = objectMapperService.toJsonUnescaped(licenseActivationMessage);
        try {
            messageQueueService.publishMessageToFifoTopic(
                jsonMessage,
                AwsResource.LICENSE_ACTIVATION_TOPIC,
                licenseActivation.getCompany().getUid()
            );
        } catch (final AmazonSNSException ex) {
            throw new LicenseActivationException(
                String.format("Exception thrown when publishing message for activation '%s'", licenseActivation.getPk()),
                ex
            );
        }
    }
}
