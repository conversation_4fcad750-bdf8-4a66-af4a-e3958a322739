package com.sast.cis.core.exceptions.migration;

import de.hybris.platform.core.model.c2l.CountryModel;

public class CountryMigrationConfigurationNonFoundException extends RuntimeException {

    private CountryMigrationConfigurationNonFoundException(final String message) {
        super(message);
    }

    public static CountryMigrationConfigurationNonFoundException forCountry(final CountryModel country) {
        return new CountryMigrationConfigurationNonFoundException(
            "Country Migration Configuration with isoCode '%s' not found".formatted(country.getIsocode())
        );
    }
}
