package com.sast.cis.core.interceptor;

import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.servicelayer.interceptor.InterceptorContext;
import de.hybris.platform.servicelayer.interceptor.InterceptorException;
import de.hybris.platform.servicelayer.interceptor.ValidateInterceptor;
import org.springframework.stereotype.Component;

@Component
public class ActiveCountryCurrencyValidateInterceptor implements ValidateInterceptor<CountryModel> {

    @Override
    public void onValidate(CountryModel country, InterceptorContext context) throws InterceptorException {
        if (country.getActive() && country.getCurrency() == null) {
            throw new InterceptorException("Need to specify currency for active country with isocode=" + country.getIsocode());
        }
    }
}
