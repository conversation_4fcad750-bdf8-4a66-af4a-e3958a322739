package com.sast.cis.core.paymentintegration.paymentinfo;

import com.sast.cis.core.data.TokenizedSepaDirectDebitPaymentInfoData;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
public abstract class TokenizedSepaDirectDebitPaymentInfoService
    implements PaymentInfoService<TokenizedSepaDirectDebitPaymentInfoData, TokenizedSepaDirectDebitPaymentInfoModel> {

    private final IntegratorPaymentInfoService integratorPaymentInfoService;

    public Set<TokenizedSepaDirectDebitPaymentInfoModel> getPaymentInfos(
        @NonNull final IntegratorModel integrator,
        @NonNull final String merchantId) {

        final PaymentProvider provider = getPaymentProvider();
        return integratorPaymentInfoService.getPaymentInfos(integrator, provider, TokenizedSepaDirectDebitPaymentInfoModel.class)
            .stream()
            .filter(sepaMandatePaymentInfo -> merchantId.equals(sepaMandatePaymentInfo.getPgwMerchantId()))
            .collect(Collectors.toSet());
    }

    public Optional<TokenizedSepaDirectDebitPaymentInfoModel> getDefaultPaymentInfo(
        @NonNull final IntegratorModel integrator,
        @NonNull final String merchantId) {

        final PaymentProvider provider = getPaymentProvider();
        return integratorPaymentInfoService.getDefaultPaymentInfo(integrator, provider, TokenizedSepaDirectDebitPaymentInfoModel.class)
            .filter(sepaMandatePaymentInfo -> merchantId.equals(sepaMandatePaymentInfo.getPgwMerchantId()));
    }

    public Optional<TokenizedSepaDirectDebitPaymentInfoModel> getCartPaymentInfo(
        @NonNull final AbstractOrderModel cart,
        @NonNull final String merchantId) {

        final PaymentProvider provider = getPaymentProvider();
        return integratorPaymentInfoService.getCartPaymentInfo(cart, provider, TokenizedSepaDirectDebitPaymentInfoModel.class)
            .filter(sepaMandatePaymentInfo -> merchantId.equals(sepaMandatePaymentInfo.getPgwMerchantId()));
    }

    @Override
    public TokenizedSepaDirectDebitPaymentInfoModel createPaymentInfo(
        @NonNull final IntegratorModel integrator,
        @NonNull final TokenizedSepaDirectDebitPaymentInfoData paymentInfoData) {
        return null;
    }

    @Override
    public void populatePaymentInfoData(
        @NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo,
        @NonNull final TokenizedSepaDirectDebitPaymentInfoData paymentInfoData) {

    }

    @Override
    public void removePaymentInfo(@NonNull final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {

    }
}
