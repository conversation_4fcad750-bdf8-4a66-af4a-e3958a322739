package com.sast.cis.core.service.customer;

import com.sast.cis.core.model.DeveloperModel;
import org.springframework.stereotype.Service;

import static com.sast.cis.core.constants.CiscoreConstants.DEVELOPER_SUFFIX;

@Service
public class InternalDeveloperUidTranslationService extends InternalCustomerUidTranslationService<DeveloperModel> {

    @Override
    String getUidSuffix() {
        return DEVELOPER_SUFFIX;
    }
}
