package com.sast.cis.core.converter;

import com.sast.cis.core.data.AppVideoData;
import com.sast.cis.core.data.IotCompanyData;
import com.sast.cis.core.data.PublicAppIntegrationData;
import com.sast.cis.core.enums.AppIntegrationStatus;
import com.sast.cis.core.enums.AppVideoStatus;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.AppLicenseService;
import de.hybris.platform.commercefacades.product.converters.populator.ProductPopulator;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.commercefacades.user.data.CountryData;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class CisProductPopulator extends ProductPopulator {

    @Resource
    private AppLicenseService appLicenseService;

    @Resource
    private Converter<AppVideoModel, AppVideoData> appVideoConverter;

    @Resource
    private Converter<AppIntegrationModel, PublicAppIntegrationData> publicAppIntegrationDataConverter;

    @Resource
    private Converter<IoTCompanyModel, IotCompanyData> cisIotCompanyDataConverter;

    @Resource
    private Converter<CountryModel, CountryData> countryConverter;

    @Override
    public void populate(ProductModel product, ProductData productData) {
        super.populate(product, productData);

        AppModel app;
        if (product instanceof AppLicenseModel) {
            app = (AppModel) ((AppLicenseModel) product).getBaseProduct();
        } else if (product instanceof AppModel) {
            app = (AppModel) product;
        } else {
            return;
        }

        productData.setName(app.getName());
        productData.setProductNote(app.getProductNote());
        productData.setDescription(app.getDescription());
        productData.setSummary(app.getSummary());

        final IoTCompanyModel company = app.getCompany();
        productData.setCompany(cisIotCompanyDataConverter.convert(company));

        Set<CountryData> countries = appLicenseService.getAllLicenses(app).stream()
            .flatMap(license -> license.getEnabledCountries().stream())
            .map(this::convertToCountryData)
            .collect(Collectors.toSet());
        productData.setEnabledCountries(countries);

        AppVersionModel latestVersion = app.getLatestVersion();
        if (latestVersion != null && latestVersion.getApk() != null) {
            ApkMediaModel apk = latestVersion.getApk();
            productData.setVersionName(apk.getVersionName());
            productData.setSdkAddonVersion(apk.getSdkAddonVersion());
        }

        productData.setAppIntegrations(populatePublicAppIntegrationData(app.getAppIntegrations()));

        AppVideoModel video = app.getVideo();
        if (video != null && AppVideoStatus.NOT_FOUND != video.getStatus()) {
            productData.setVideo(appVideoConverter.convert(video));
        }
    }

    private CountryData convertToCountryData(CountryModel country) {
        return countryConverter.convert(country);
    }

    private List<PublicAppIntegrationData> populatePublicAppIntegrationData(List<AppIntegrationModel> appIntegrations) {
        return CollectionUtils.emptyIfNull(appIntegrations)
            .stream()
            .filter(integration -> AppIntegrationStatus.ENABLED.equals(integration.getStatus()))
            .map(publicAppIntegrationDataConverter::convert)
            .collect(Collectors.toList());
    }
}
