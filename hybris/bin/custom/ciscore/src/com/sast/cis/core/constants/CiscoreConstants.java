package com.sast.cis.core.constants;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;

/**
 * Global class for all Ciscore constants. You can add global constants for your extension into this class.
 */
@SuppressWarnings("deprecation")
public final class CiscoreConstants extends GeneratedCiscoreConstants {
    public static final String EXTENSIONNAME = "ciscore";
    public static final String PLATFORM_LOGO_CODE = "ciscorePlatformLogo";
    public static final String CIS_PRODUCT_CATALOG = "cisProductCatalog";
    public static final String CATEGORY_CODE = "cat_00001";
    public static final String VERSION_VARIANT_CODE = "appLicense";
    public static final String APK_MEDIA_FOLDER_NAME = "apks";
    public static final String INVOICES_MEDIA_FOLDER_NAME = "invoices";
    public static final String SELF_BILLING_INVOICES_MEDIA_FOLDER_NAME = "self-billing-invoices";
    public static final String IOT_STORE_BASE_SITE_UID = "iotstore";
    public static final String IOT_STORE_BASE_STORE_UID = "iotstore";
    public static final String DEVELOPER_CONSOLE_BASE_SITE_UID = "developerconsole";
    public static final String DEVELOPER_CONSOLE_BASE_STORE_UID = "developerconsole";
    public static final String S3_APK_FOLDER_SEPARATOR_PROXY = "%";
    public static final String PIECES = "pieces";
    public static final String SCREENSHOT_MEDIA_TYPE = "screenshot";
    public static final String DOCUMENTATION_MEDIA_TYPE = "documentation-files";
    public static final String DOCUMENTATION_MEDIA_FOLDER = "documentation-files";
    public static final String IMAGES_MEDIA_FOLDER = "images";
    public static final String CATALOG_UNAWARE_IMAGES_MEDIA_FOLDER = "catalog-unaware-images";
    public static final String COMPRESSED_FILE_TYPE = "compressed-files";
    public static final String COMPRESSED_FILE_FOLDER = "compressed-files";
    public static final String EU_FULL_VAT = "eu-vat-full";
    public static final String INTEGRATOR_SUFFIX = "@shop";
    public static final String DEVELOPER_SUFFIX = "@devcon";
    public static final String DEFAULT_PERMISSION_ICON_CODE = "defaultPermissionIcon";
    public static final String CISSHOPFRONTEND_CONTEXT_PATH = "cisshopfrontend.webroot";
    public static final String EVALUATION_SUFFIX = "_evaluation";
    public static final String FULL_SUFFIX = "_full";
    public static final String SUBSCRIPTION_SUFFIX = "_sub";
    public static final String TOOL_SUFFIX = "_tool";
    public static final DateTimeFormatter ISO_8601_DATE_TIME_FORMATTER = new DateTimeFormatterBuilder().appendInstant(3).toFormatter();
    public static final String ACCESS_DENIED = "/accessdenied";
    public static final String PAGE_NOT_FOUND = "/pagenotfound";
    public static final String NUMBER_OF_PDF_DOCUMENTS = "public.listing.max.number.of.documents";
    public static final String DESCRIPTION_LENGTH = "public.listing.description.length";
    public static final String SUMMARY_LENGTH = "public.listing.summary.length";
    public static final String EMAIL_LENGTH = "public.listing.email.length";
    public static final String URL_LENGTH = "public.listing.url.length";
    public static final String PHONE_LENGTH = "public.listing.phone.length";
    public static final String NAME_LENGTH = "public.listing.name.length";
    public static final String DEV_CONSOLE_BASE_URL_PROPERTY = "website.developerconsole.https";
    public static final String INFO_URL_PROPERTY = "info.url";
    public static final String ACCOUNTS_URL_PROPERTY = "accounts.url";
    public static final String CORPORATE_HOME_PROPERTY = "corporate.home";
    public static final String SKIP_EMAIL_TO_CUSTOMER = "SkipEmailToCustomer";
    public static final String SUBSCRIPTION_TERMINATION_RULE_INITIAL_PERIOD_IN_YEARS = "subscription.termination.rule.initial.period.in.years";
    public static final String SUBSCRIPTION_TERMINATION_RULE_FOLLOWUP_PERIOD_IN_YEARS = "subscription.termination.rule.followup.period.in.years";
    public static final String SUBSCRIPTION_TERMINATION_RULE_NOTICE_PERIOD_IN_DAYS = "subscription.termination.rule.notice.period.in.days";
    public static final String STORE_AVAILABILITY_MODE = "storeAvailabilityMode";
    public static final int PRIVATE_OFFER_REQUEST_MESSAGE_MAX_LENGTH = 1000;
    public static final String EMAIL_RECIPIENT_PRIVATE_OFFER_PROPERTY = "email.recipient.private.offer";
    public static final String DEVCON_URL_PROPERTY = "website.developerconsole.https";
    public static final String IMAGE_FORMAT_ERROR = "imageFormatError";
    public static final String GENERIC_MEDIA_FORMAT_ERROR = "genericMediaFormatError";
    public static final String IMAGE_RESOLUTION_ERROR = "imageResolutionError";
    public static final String INTEGRATOR_GROUP = "integratorgroup";
    public static final String DEVELOPER_GROUP = "developergroup";
    public static final String KOREA_ISO_CODE = "KR";
    public static final String PRIVATE_OFFER_PLANNED_START_DATE_FORMAT = "yyyy-MM";
    public static final int DEFAULT_TERM_OF_PAYMENT_IN_DAYS = 30;
    public static final int MIN_ANDROID_API_VERSION_VALUE = 27;
    public static final String DEVELOPER_PORTAL_URL = "developerportal.url";
    public static final String PRINCIPAL_JWT_CLAIM_NAME = "preferred_username";
    public static final String DMP_URL_AZENA = "shop.header.cameras.url";
    public static final String LMP_URL_AA = "aa.dmp.url";

    private CiscoreConstants() {
        // empty to avoid instantiating this constant class
    }
}
