package com.sast.cis.core.interceptor.payout;

import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.StripeConnectAccountModel;
import de.hybris.platform.servicelayer.interceptor.InterceptorContext;
import de.hybris.platform.servicelayer.interceptor.InterceptorException;
import de.hybris.platform.servicelayer.interceptor.ValidateInterceptor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class StripeConnectAccountConsistencyInterceptor implements ValidateInterceptor<StripeConnectAccountModel> {
    @Override
    public void onValidate(StripeConnectAccountModel stripeConnectAccount, InterceptorContext context) throws InterceptorException {
        IoTCompanyModel company = stripeConnectAccount.getCompany();

        if (CollectionUtils.isEmpty(company.getStripeConnectAccounts())) {
            return;
        }

        if (company.getStripeConnectAccounts().contains(stripeConnectAccount)) {
            return;
        }

        throw new InterceptorException(String
            .format("The company with uid=%s has already a different StripeConnectAccount. Cannot save StripeConnectAccount",
                company.getUid()));
    }
}
