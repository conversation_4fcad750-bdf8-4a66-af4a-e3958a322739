package com.sast.cis.core.service.app;

import com.sast.cis.core.data.AppIntegrationData;
import com.sast.cis.core.model.AppIntegrationModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.StoreContentDraftModel;

import java.util.List;

public interface AppIntegration {
    List<AppIntegrationData> getDevconAppIntegrationData(List<AppIntegrationModel> existingAppIntegrations);

    void updateIntegrations(AppModel app, List<AppIntegrationData> appIntegrations);

    void updateIntegrations(StoreContentDraftModel storeContentDraft, List<AppIntegrationData> appIntegrations);
}
