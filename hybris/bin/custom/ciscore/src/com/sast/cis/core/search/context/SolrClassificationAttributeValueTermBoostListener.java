package com.sast.cis.core.search.context;

import de.hybris.platform.catalog.model.classification.ClassificationAttributeValueModel;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import de.hybris.platform.solrfacetsearch.search.BoostField;
import de.hybris.platform.solrfacetsearch.search.SearchQuery;
import de.hybris.platform.solrfacetsearch.search.context.FacetSearchContext;
import de.hybris.platform.solrfacetsearch.search.context.FacetSearchListener;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Locale;

import static de.hybris.platform.solrfacetsearch.search.BoostField.BoostType.MULTIPLICATIVE;
import static de.hybris.platform.solrfacetsearch.search.SearchQuery.QueryOperator.EQUAL_TO;

@Setter
@Slf4j
public class SolrClassificationAttributeValueTermBoostListener implements FacetSearchListener {
    private List<String> indexFieldsToBoost;
    private CommonI18NService commonI18NService;

    @Override
    public void beforeSearch(FacetSearchContext facetSearchContext) {
        if (CollectionUtils.isEmpty(indexFieldsToBoost)) {
            LOG.debug("Nothing to boost as no index fields configured to boost");
            return;
        }
        indexFieldsToBoost.forEach(indexFieldToBoost -> addBoostField(facetSearchContext, indexFieldToBoost));
    }

    private void addBoostField(FacetSearchContext facetSearchContext, String indexField) {
        IndexedProperty indexedProperty = facetSearchContext.getIndexedType().getIndexedProperties().get(indexField);
        if (indexedProperty == null ) {
            LOG.debug("Configured boost config is not applicable for field {} .", indexField);
            return;
        }
        if ( indexedProperty.getClassAttributeAssignment() == null) {
            LOG.debug("No classification attribute assigned for index fields {}.", indexField);
            return;
        }
        var attributeValues = indexedProperty.getClassAttributeAssignment().getAttributeValues();
        CollectionUtils.emptyIfNull(attributeValues).stream()
            .filter(v -> v.getTermBoost() != null)
            .forEach(attributeValue -> addTermBoostField(facetSearchContext.getSearchQuery(), indexedProperty, attributeValue));
    }

    private void addTermBoostField(SearchQuery searchQuery, IndexedProperty indexedProperty,
        ClassificationAttributeValueModel attributeValue) {
        String localizedAttributeValue = getLocalizedValue(searchQuery, attributeValue);
        if (StringUtils.isEmpty(localizedAttributeValue)) {
            LOG.debug("localized value is empty, not boosting the field for field {}.", indexedProperty.getName());
            return;
        }
        final BoostField boostField = new BoostField(indexedProperty.getName(), EQUAL_TO, localizedAttributeValue,
            attributeValue.getTermBoost(), MULTIPLICATIVE);
        LOG.debug("adding term boost for field {} with boost value {}.", boostField.getField(), boostField.getBoostValue());
        searchQuery.addBoost(boostField);
    }

    private String getLocalizedValue(SearchQuery searchQuery, ClassificationAttributeValueModel attributeValue) {
        Locale locale = commonI18NService.getLocaleForIsoCode(searchQuery.getLanguage());
        return attributeValue.getName(locale);
    }

    @Override
    public void afterSearch(FacetSearchContext facetSearchContext) {
        // no op
    }

    @Override
    public void afterSearchError(FacetSearchContext facetSearchContext) {
        // no op
    }
}
