package com.sast.cis.core.dao.buyercontract;

import com.google.common.base.Preconditions;
import com.sast.cis.core.model.BuyerContractModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.SubscriptionContractModel;
import com.sast.cis.core.service.DaoQueryService;
import com.sast.cis.thl.enums.MigrationStatus;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.search.SearchResult;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class BuyerContractDao {
    private static final String PRODUCT_PARAMETER = "product";
    private static final String FIND_COMPANIES_WITH_ACTIVE_SUBSCRIPTIONS_FOR_PRODUCT =
        "SELECT DISTINCT {company." + IoTCompanyModel.PK + "}"
            + " FROM {" + IoTCompanyModel._TYPECODE + " AS company"
                + " JOIN " + OrderModel._TYPECODE + " AS order"
                    + " ON {order." + OrderModel.COMPANY + "} = {company." + IoTCompanyModel.PK + "}"
                + " JOIN " + OrderEntryModel._TYPECODE + " AS orderEntry"
                    + " ON  {orderEntry." + OrderEntryModel.ORDER + "} = {order." + OrderModel.PK + "}"
                + " JOIN " + ProductModel._TYPECODE + " AS product"
                    + " ON  {orderEntry." + OrderEntryModel.PRODUCT + "} = {product." + ProductModel.PK + "}"
                + " JOIN " + SubscriptionContractModel._TYPECODE + " AS subscriptionContract"
                    + " ON {subscriptionContract." + SubscriptionContractModel.ORDERENTRY + "} = {orderEntry." + OrderEntryModel.PK + "}}"
            + " WHERE {product." + ProductModel.CODE + "} = ?" + PRODUCT_PARAMETER
            + " AND {subscriptionContract." + SubscriptionContractModel.ENDDATE + "} IS NULL"
            + " AND {company." + IoTCompanyModel.DEACTIVATIONDATE + "} IS NULL";

    private static final String FIND_UNCANCELLED_UNMIGRATED_SUBS =
        "SELECT DISTINCT {sc.pk}  \n"
            + "FROM {" + SubscriptionContractModel._TYPECODE + " AS sc "
            + "    JOIN " + OrderEntryModel._TYPECODE + " AS oe ON {sc.orderEntry} = {oe.pk} "
            + "    JOIN " + ProductModel._TYPECODE + " AS pr ON {oe.product} = {pr.pk} "
            + "    JOIN " + OrderModel._TYPECODE + " AS o ON {o.pk} = {oe.order} "
            + "    JOIN " + IoTCompanyModel._TYPECODE + " AS c ON {c.pk} = {o.company} "
            + "}"
            + "WHERE {sc.endDate} is null AND {c.deactivationDate} is null AND {sc.cancelledDate} is null"
            + " AND {pr.code} = ?code AND {sc.startDate} >= ?start AND {sc.startDate} < ?end";


    private final DaoQueryService daoQueryService;
    private final FlexibleSearchService flexibleSearchService;

    public Optional<BuyerContractModel> getByContractCode(final String contractCode) {
        Preconditions.checkArgument(StringUtils.isNotBlank(contractCode), "Given contractCode is blank");

        BuyerContractModel sampleContract = new BuyerContractModel();
        sampleContract.setCode(contractCode);
        return daoQueryService.getSingleModelByExample(sampleContract);
    }

    public Optional<SubscriptionContractModel> getByLegacySubscriptionId(final String legacySubscriptionId) {
        Preconditions.checkArgument(StringUtils.isNotBlank(legacySubscriptionId), "Given legacySubscriptionId is blank");

        SubscriptionContractModel sampleContract = new SubscriptionContractModel();
        sampleContract.setLegacySubscriptionId(legacySubscriptionId);
        return daoQueryService.getSingleModelByExample(sampleContract);
    }

    public List<IoTCompanyModel> findCompaniesWithActiveSubscriptionsForProduct(ProductModel product) {
        final FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_COMPANIES_WITH_ACTIVE_SUBSCRIPTIONS_FOR_PRODUCT);
        query.addQueryParameter(PRODUCT_PARAMETER, product.getCode());

        final SearchResult<IoTCompanyModel> searchResult = flexibleSearchService.search(query);
        return searchResult.getResult();
    }

    public List<SubscriptionContractModel> listUncancelledUnmigratedSubscriptions(String code, Date start, Date end) {
        final FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_UNCANCELLED_UNMIGRATED_SUBS);
        query.addQueryParameter("code", code);
        query.addQueryParameter("start", start);
        query.addQueryParameter("end", end);

        final SearchResult<SubscriptionContractModel> searchResult = flexibleSearchService.search(query);
        return searchResult.getResult();
    }
}
