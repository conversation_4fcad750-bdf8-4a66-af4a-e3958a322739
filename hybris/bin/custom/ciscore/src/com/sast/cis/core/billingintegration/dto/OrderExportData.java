package com.sast.cis.core.billingintegration.dto;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Builder
@Getter
@EqualsAndHashCode
@ToString
public class OrderExportData implements Serializable {
    private final String id;
    private final OrderExportResult orderExportResult;
    private final String billingOrderId;
    private final List<String> errors;
    private final List<ContractItemData> contractItems;
}
