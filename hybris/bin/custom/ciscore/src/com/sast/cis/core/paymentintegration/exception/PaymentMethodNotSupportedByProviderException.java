package com.sast.cis.core.paymentintegration.exception;

import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.enums.PaymentProvider;

public class PaymentMethodNotSupportedByProviderException extends RuntimeException {

    private PaymentMethodNotSupportedByProviderException(final String message) {
        super(message);
    }

    public static PaymentMethodNotSupportedByProviderException forMethodAndProvider(
        final PaymentMethodType paymentMethodType,
        final PaymentProvider paymentProvider) {

        return new PaymentMethodNotSupportedByProviderException(
            "Payment Method '%s' not supported by provider '%s'".formatted(paymentMethodType, paymentProvider)
        );
    }
}
