package com.sast.cis.core.service;

import org.apache.commons.lang3.StringUtils;
import org.owasp.html.PolicyFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HtmlSanitizingService {
    private PolicyFactory htmlSanitizer;

    @Autowired
    public HtmlSanitizingService(PolicyFactory htmlSanitizer) {
        this.htmlSanitizer = htmlSanitizer;
    }

    public String sanitizeInput(String dirtyInput) {
        if (StringUtils.isBlank(dirtyInput)) {
            return dirtyInput;
        }

        return htmlSanitizer.sanitize(dirtyInput);
    }
}
