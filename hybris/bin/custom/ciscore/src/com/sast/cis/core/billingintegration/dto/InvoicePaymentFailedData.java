package com.sast.cis.core.billingintegration.dto;

import com.sast.cis.core.enums.PaymentProvider;
import lombok.Builder;
import lombok.NonNull;

import java.io.Serializable;

@Builder
public record InvoicePaymentFailedData(
        @NonNull String invoiceId,
        PaymentProvider paymentProvider,
        String pspPaymentReference,
        String pspErrorCode,
        String pspErrorMessage,
        String pspError
) implements Serializable {
}
