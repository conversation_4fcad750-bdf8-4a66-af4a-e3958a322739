package com.sast.cis.core.jalo;

import com.sast.cis.core.constants.CiscoreConstants;
import de.hybris.platform.jalo.JaloSession;
import de.hybris.platform.jalo.extension.ExtensionManager;
import org.apache.log4j.Logger;

public class CiscoreManager extends GeneratedCiscoreManager
{
	@SuppressWarnings("unused")
	private static final Logger log = Logger.getLogger( CiscoreManager.class.getName() );
	
	public static final CiscoreManager getInstance()
	{
		ExtensionManager em = JaloSession.getCurrentSession().getExtensionManager();
		return (CiscoreManager) em.getExtension(CiscoreConstants.EXTENSIONNAME);
	}
	
}
