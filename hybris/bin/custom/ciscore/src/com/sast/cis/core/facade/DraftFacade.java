package com.sast.cis.core.facade;

import com.sast.cis.core.data.ProductContainerRelatedData;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.exceptions.web.NotFoundException;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.service.ErrorMessageService;
import com.sast.cis.core.service.ProductContainerService;
import com.sast.cis.core.service.TranslationService;
import de.hybris.platform.servicelayer.model.ModelService;
import org.slf4j.Logger;

import javax.annotation.Resource;

import java.util.Optional;
import java.util.function.BiFunction;

import static com.sast.cis.core.constants.devcon.DevconErrorCode.*;

public abstract class DraftFacade<DATA extends ProductContainerRelatedData, MODEL> {

    @Resource
    protected ModelService modelService;

    @Resource
    protected ProductContainerService productContainerService;

    @Resource
    protected TranslationService translationService;

    @Resource
    protected ErrorMessageService errorMessageService;

    protected MODEL createOrUpdateDraft(DATA data, BiFunction<DATA, ProductContainerModel, MODEL> modelPopulator) {
        Optional<ProductContainerModel> productContainerOptional = productContainerService
            .getProductContainerForCode(data.getProductContainerCode());
        if (productContainerOptional.isEmpty()) {
            getLogger().debug("Product container for code={} does not exist.", data.getProductContainerCode());
            throw new NotFoundException(translationService.translate(errorMessageService.createErrorMessage(PRODUCTCONTAINER_NOT_FOUND)));
        }
        ProductContainerModel productContainer = productContainerOptional.get();
        if (!productContainer.isDraft()) {
            getLogger().warn("Product container for code={} is not in draft mode -- while trying to update draft.",
                data.getProductContainerCode());
            throw new NotFoundException(translationService.translate(errorMessageService.createErrorMessage(PRODUCTCONTAINER_NOT_FOUND)));
        }

        getLogger().debug("Update store content data for product container with code={}", productContainer.getCode());
        MODEL draftModel = null;
        try {
            draftModel = modelPopulator.apply(data, productContainer);
        } catch (Exception e) {
            getLogger().error("Exception while updating app draft with productContainer.code={}", data.getProductContainerCode(), e);
            throw new BadRequestException(translationService.translate(errorMessageService.createErrorMessage(GENERAL_ERROR)));
        }

        if (draftModel == null) {
            getLogger().warn("Could not update app draft");
            throw new BadRequestException(translationService.translate(errorMessageService.createErrorMessage(APPVERSION_NOT_FOUND)));
        }

        modelService.saveAll(productContainer, productContainer.getAppDraft(), draftModel);
        productContainerService.updateProductContainerChangeDate(productContainer);
        getLogger().info("Persisted draft for product container with code={}", productContainer.getCode());

        return draftModel;
    }

    protected abstract Logger getLogger();
}
