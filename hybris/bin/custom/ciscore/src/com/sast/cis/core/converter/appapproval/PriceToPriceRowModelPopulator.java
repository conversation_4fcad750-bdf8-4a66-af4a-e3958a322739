package com.sast.cis.core.converter.appapproval;

import com.sast.cis.core.billingintegration.dto.Price;
import de.hybris.platform.commerceservices.i18n.CommerceCommonI18NService;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.europe1.model.PriceRowModel;
import de.hybris.platform.product.UnitService;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Currency;
import java.util.Date;

import static com.sast.cis.core.constants.CiscoreConstants.PIECES;

@Component
public class PriceToPriceRowModelPopulator implements Populator<Price, PriceRowModel> {

    private final UnitService unitService;

    private final CommerceCommonI18NService commerceCommonI18NService;

    public PriceToPriceRowModelPopulator(UnitService unitService, CommerceCommonI18NService commerceCommonI18NService) {
        this.unitService = unitService;
        this.commerceCommonI18NService = commerceCommonI18NService;
    }

    @Override
    public void populate(Price price, PriceRowModel priceRow) {
        if (price == null) {
            throw new ConversionException("Required price is null");
        }

        priceRow.setStartTime(Date.from(price.getValidFrom().toInstant(ZoneOffset.UTC)));

        LocalDateTime validTo = price.getValidTo();
        if (validTo != null) {
            priceRow.setEndTime(Date.from(validTo.toInstant(ZoneOffset.UTC)));
        }

        priceRow.setCurrency(getCurrency(price.getCurrency()));
        priceRow.setPrice(price.getAmount().doubleValue());
        priceRow.setUnit(unitService.getUnitForCode(PIECES));
        priceRow.setUnitFactor(1);
        priceRow.setMinqtd((long) price.getMinQuantity());
        priceRow.setScaledPriceDiscount(price.getScaledPriceDiscount());
        priceRow.setNet(true);
    }

    private CurrencyModel getCurrency(Currency currency) {
        return commerceCommonI18NService.getAllCurrencies().stream()
            .filter(currencyModel -> currencyModel.getIsocode().equals(currency.getCurrencyCode()))
            .findFirst().orElseThrow();
    }
}
