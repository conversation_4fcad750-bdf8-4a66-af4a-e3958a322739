package com.sast.cis.core.resolver;

import de.hybris.platform.category.model.CategoryModel;
import de.hybris.platform.commerceservices.search.solrfacetsearch.provider.impl.DefaultCategorySource;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.solrfacetsearch.config.IndexConfig;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;

import java.util.Collection;
import java.util.Set;

/**
 * Source of categories for CategoryFullPathsValueProvider.
 * Includes direct parent categories only.
 */
public class DirectParentCategoriesSource extends DefaultCategorySource {

    @Override
    public Collection<CategoryModel> getCategoriesForConfigAndProperty(
        final IndexConfig indexConfig, final IndexedProperty indexedProperty, final Object model) {

        final Set<ProductModel> products = getProducts(model);
        return super.getDirectSuperCategories(products);
    }
}
