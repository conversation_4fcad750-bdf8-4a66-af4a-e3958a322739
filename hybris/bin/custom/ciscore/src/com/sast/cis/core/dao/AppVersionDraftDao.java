package com.sast.cis.core.dao;

import com.sast.cis.core.model.AppVersionDraftModel;
import com.sast.cis.core.service.DaoQueryService;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Optional;

@Repository
public class AppVersionDraftDao {

    @Resource
    private DaoQueryService daoQueryService;

    public Optional<AppVersionDraftModel> getAppVersionDraftForCode(String code) {
        AppVersionDraftModel appVersionDraftModel = new AppVersionDraftModel();
        appVersionDraftModel.setCode(code);
        return daoQueryService.getSingleModelByExample(appVersionDraftModel);
    }
}
