package com.sast.cis.core.dao.selfbillinginvoice;

import com.sast.cis.core.exceptions.selfbillinginvoice.SelfBillingInvoiceNotFoundException;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.SelfBillingInvoiceModel;
import com.sast.cis.core.service.DaoQueryService;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.servicelayer.data.PaginationData;
import de.hybris.platform.core.servicelayer.data.SearchPageData;
import de.hybris.platform.core.servicelayer.data.SortData;
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.search.SearchResult;
import de.hybris.platform.servicelayer.search.paginated.PaginatedFlexibleSearchParameter;
import de.hybris.platform.servicelayer.search.paginated.PaginatedFlexibleSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

import static com.sast.cis.core.model.SelfBillingInvoiceModel.*;
import static de.hybris.platform.servicelayer.util.ServicesUtil.validateParameterNotNull;

@Repository
@RequiredArgsConstructor
@Slf4j
public class SelfBillingInvoiceDao {

    private static final String JOIN = " JOIN ";
    private static final String DEVELOPER_COMPANY_PARAMETER = "developerCompany";

    private static final String FIND_SELF_BILLING_INVOICES_FOR_ORDER = "SELECT {sbi." + PK + "} "
        + "FROM {" + _TYPECODE + " AS sbi"
        + JOIN + OrderModel._TYPECODE + " as order"
        + " ON {sbi." + ORDER + "} = {order." + OrderModel.PK + "}}"
        + " WHERE {order." + OrderModel.CODE + "} = ?" + OrderModel.CODE;

    private static final String FIND_SELF_BILLING_INVOICES_FOR_DEVELOPER_COMPANY =
        "SELECT { sbi." + PK + "} "
            + " FROM {" + _TYPECODE + " AS sbi } "
            + " WHERE EXISTS ({{ "
            + " SELECT {order." + OrderModel.PK +"}"
            + " FROM {" +  OrderModel._TYPECODE + " AS order "
            + JOIN + OrderEntryModel._TYPECODE + " AS orderEntry"
            + " ON  { order." + OrderModel.PK + "} = { orderEntry." + OrderEntryModel.ORDER + "}"
            + JOIN + AppLicenseModel._TYPECODE + " AS appLicense"
            + " ON  { orderEntry." + OrderEntryModel.PRODUCT + "} = { appLicense." + AppLicenseModel.PK + "}"
            + JOIN + AppModel._TYPECODE + " AS app"
            + " ON  { app." + AppModel.PK + "} = { appLicense." + AppLicenseModel.BASEPRODUCT + "}}"
            + " WHERE {sbi." + ORDER + "} = {order." + OrderModel.PK + "} "
            + "AND {app." + AppModel.COMPANY + "} = ?" + DEVELOPER_COMPANY_PARAMETER
        + " }})";

    private static final Map<String, String> sortCodeToQueryAlias = Map.of(INVOICEDATE.toLowerCase(), "sbi", MODIFIEDTIME.toLowerCase(), "sbi");

    private final FlexibleSearchService flexibleSearchService;
    private final PaginatedFlexibleSearchService paginatedFlexibleSearchService;

    private final DaoQueryService daoQueryService;

    public List<SelfBillingInvoiceModel> findSelfBillingInvoicesForOrder(OrderModel order) {
        FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_SELF_BILLING_INVOICES_FOR_ORDER);
        query.addQueryParameter(OrderModel.CODE, order.getCode());

        SearchResult<SelfBillingInvoiceModel> searchResult = flexibleSearchService.search(query);

        return searchResult.getResult();
    }

    public List<SelfBillingInvoiceModel> findSelfBillingInvoicesForCompany(IoTCompanyModel developerCompany) {
        FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_SELF_BILLING_INVOICES_FOR_DEVELOPER_COMPANY);
        query.addQueryParameter(DEVELOPER_COMPANY_PARAMETER, developerCompany);
        SearchResult<SelfBillingInvoiceModel> searchResult = flexibleSearchService.search(query);

        return searchResult.getResult();
    }

    public SearchPageData<SelfBillingInvoiceModel> getSelfBillingInvoices(IoTCompanyModel company,
        PaginationData paginationData, List<SortData> sorts) {
        validateParameterNotNull(company, "Company must not be null");
        validateParameterNotNull(paginationData, "paginationData must not be null");
        validateParameterNotNull(sorts, "sorts must not be null");

        FlexibleSearchQuery flexibleSearchQuery = new FlexibleSearchQuery(FIND_SELF_BILLING_INVOICES_FOR_DEVELOPER_COMPANY);
        SearchPageData<SelfBillingInvoiceModel> searchPageData = new SearchPageData<SelfBillingInvoiceModel>()
            .withPagination(paginationData)
            .withSorts(sorts);

        PaginatedFlexibleSearchParameter searchParameter = getPaginatedFlexibleSearchParameter(flexibleSearchQuery, searchPageData);

        flexibleSearchQuery.addQueryParameter(DEVELOPER_COMPANY_PARAMETER, company);
        return paginatedFlexibleSearchService.search(searchParameter);
    }

    private PaginatedFlexibleSearchParameter getPaginatedFlexibleSearchParameter(FlexibleSearchQuery flexibleSearchQuery,
        SearchPageData<SelfBillingInvoiceModel> searchPageData) {
        PaginatedFlexibleSearchParameter searchParameter = new PaginatedFlexibleSearchParameter();
        searchParameter.setFlexibleSearchQuery(flexibleSearchQuery);
        searchParameter.setSearchPageData(searchPageData);
        searchParameter.setSortCodeToQueryAlias(sortCodeToQueryAlias);
        return searchParameter;
    }

    public SelfBillingInvoiceModel getByExternalIdOrThrow(final String externalId) {
        final SelfBillingInvoiceModel selfBillingInvoiceExample = new SelfBillingInvoiceModel();
        selfBillingInvoiceExample.setExternalId(externalId);
        return daoQueryService.getSingleModelByExample(selfBillingInvoiceExample)
            .orElseThrow(() -> SelfBillingInvoiceNotFoundException.forExternalId(externalId));
    }
}
