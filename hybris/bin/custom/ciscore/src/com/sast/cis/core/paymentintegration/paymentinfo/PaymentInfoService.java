package com.sast.cis.core.paymentintegration.paymentinfo;

import com.sast.cis.core.data.PaymentInfoData;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.IntegratorModel;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;

public interface PaymentInfoService<S extends PaymentInfoData, T extends PaymentInfoModel> {

    PaymentProvider getPaymentProvider();

    T createPaymentInfo(IntegratorModel integrator, S paymentInfoData);

    void removePaymentInfo(T paymentInfo);

    default void populatePaymentInfoData(T paymentInfo, S paymentInfoData) {
        // noop
    }
}
