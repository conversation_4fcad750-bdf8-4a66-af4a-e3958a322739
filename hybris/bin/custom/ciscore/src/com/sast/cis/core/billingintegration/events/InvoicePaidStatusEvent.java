package com.sast.cis.core.billingintegration.events;

import de.hybris.platform.servicelayer.event.ClusterAwareEvent;
import de.hybris.platform.servicelayer.event.PublishEventContext;
import de.hybris.platform.servicelayer.event.events.AbstractEvent;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;


@Getter
@RequiredArgsConstructor
public class InvoicePaidStatusEvent extends AbstractEvent implements ClusterAwareEvent {
    @NonNull
    private final String sellerInvoiceDocumentNumber;

    @NonNull
    private final String buyerInvoiceDocumentNumber;

    @NonNull
    private final String pspAuthorizationReference;

    @NonNull
    private final String pspCaptureReference;

    @NonNull
    private final String pspPaymentReference;

    private final boolean paid;

    @Override
    public boolean canPublish(@NonNull PublishEventContext publishEventContext) {
        return (publishEventContext.getSourceNodeId() == publishEventContext.getTargetNodeId());
    }
}
