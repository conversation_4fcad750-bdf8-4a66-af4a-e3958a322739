package com.sast.cis.core.util;

import de.hybris.platform.commercefacades.product.data.ImageData;
import de.hybris.platform.commercefacades.product.data.ImageDataType;
import de.hybris.platform.commercefacades.product.data.ProductData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class ProductDataImageHelper {
    public List<Map<String, ImageData>> getGalleryImages(ProductData productData) {
        return getGalleryImages(productData.getImages());
    }

    public List<Map<String, ImageData>> getGalleryImages(Collection<ImageData> imageDataCollection) {
        final List<Map<String, ImageData>> galleryImages = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(imageDataCollection)) {
            final List<ImageData> images = new ArrayList<>();
            for (final ImageData image : imageDataCollection) {
                if (ImageDataType.GALLERY.equals(image.getImageType())) {
                    images.add(image);
                }
            }
            images.sort(Comparator.comparing(ImageData::getGalleryIndex));

            if (CollectionUtils.isNotEmpty(images)) {
                addFormatsToGalleryImages(galleryImages, images);
            }
        }
        return galleryImages;
    }

    private void addFormatsToGalleryImages(List<Map<String, ImageData>> galleryImages, List<ImageData> images) {
        int currentIndex = images.get(0).getGalleryIndex().intValue();
        Map<String, ImageData> formats = new HashMap<>();
        for (final ImageData image : images) {
            if (currentIndex != image.getGalleryIndex().intValue()) {
                galleryImages.add(formats);
                formats = new HashMap<>();
                currentIndex = image.getGalleryIndex().intValue();
            }
            formats.put(image.getFormat(), image);
        }
        if (!formats.isEmpty()) {
            galleryImages.add(formats);
        }
    }
}
