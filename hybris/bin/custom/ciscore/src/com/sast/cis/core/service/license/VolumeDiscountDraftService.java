package com.sast.cis.core.service.license;

import com.sast.cis.core.data.ErrorMessageData;
import com.sast.cis.core.data.VolumeDiscountData;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.exceptions.web.NotFoundException;
import com.sast.cis.core.model.AppLicenseDraftModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.model.VolumeDiscountDraftModel;
import com.sast.cis.core.validator.license.VolumeDiscountValidator;
import de.hybris.platform.core.PK;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.sast.cis.core.enums.LicenseType.FULL;

@Service
@AllArgsConstructor
@Slf4j
public class VolumeDiscountDraftService {
    private final Converter<VolumeDiscountData, VolumeDiscountDraftModel> volumeDiscountDraftConverter;
    private final ModelService modelService;
    private final VolumeDiscountValidator volumeDiscountValidator;

    public List<VolumeDiscountDraftModel> getDiscountsForProduct(ProductContainerModel productContainer) {
        Optional<AppLicenseDraftModel> fullLicense = getFullLicense(productContainer);

        return fullLicense.isPresent() ?
            new ArrayList<>(fullLicense.get().getVolumeDiscounts()) :
            Collections.emptyList();
    }

    public VolumeDiscountDraftModel updateDiscount(long discountPk, ProductContainerModel productContainer, VolumeDiscountData discount) {
        VolumeDiscountDraftModel existingDiscount = getDiscountForFullLicense(discountPk, productContainer);
        volumeDiscountValidator.validateDiscount(existingDiscount.getAppLicenseDraft(), discount);

        volumeDiscountDraftConverter.convert(discount, existingDiscount);
        modelService.save(existingDiscount);

        return existingDiscount;
    }

    public void removeDiscount(long discountPk, ProductContainerModel productContainer) {
        AppLicenseDraftModel appLicense = getLicenseOrThrow(productContainer);
        VolumeDiscountDraftModel existingDiscount = getDiscountForFullLicense(discountPk, productContainer);
        modelService.remove(existingDiscount);
        modelService.refresh(appLicense);
    }

    public VolumeDiscountDraftModel addVolumeDiscount(ProductContainerModel productContainer, VolumeDiscountData discount) {
        AppLicenseDraftModel appLicense = getLicenseOrThrow(productContainer);
        volumeDiscountValidator.validateDiscount(appLicense, discount);

        VolumeDiscountDraftModel newDiscount = volumeDiscountDraftConverter.convert(discount);
        newDiscount.setAppLicenseDraft(appLicense);

        List<VolumeDiscountDraftModel> existingDiscounts = new ArrayList<>(appLicense.getVolumeDiscounts());
        existingDiscounts.add(newDiscount);
        appLicense.setVolumeDiscounts(existingDiscounts);
        modelService.saveAll(newDiscount, appLicense);

        return newDiscount;
    }

    private Optional<AppLicenseDraftModel> getFullLicense(ProductContainerModel productContainer) {
        if (productContainer.getAppDraft() == null ||
            productContainer.getAppDraft().getCountriesAndPricesDraft() == null ||
            CollectionUtils.isEmpty(productContainer.getAppDraft().getCountriesAndPricesDraft().getAppLicenses())) {
            return Optional.empty();
        }

        return productContainer.getAppDraft().getCountriesAndPricesDraft().getAppLicenses()
            .stream()
            .filter(license -> LicenseType.FULL.equals(license.getLicenseType()))
            .findFirst();
    }

    private VolumeDiscountDraftModel getDiscountForFullLicense(long discountPk, ProductContainerModel productContainer) {
        AppLicenseDraftModel appLicense = getLicenseOrThrow(productContainer);

        return appLicense.getVolumeDiscounts()
            .stream()
            .filter(discount -> discount.getPk().getLong() == discountPk)
            .findFirst()
            .orElseThrow(
                () -> new NotFoundException(new ErrorMessageData().withMessage("Volume discount not found"))
            );
    }

    private AppLicenseDraftModel getLicenseOrThrow(ProductContainerModel productContainer) {
        return getFullLicense(productContainer).orElseThrow(
            () -> new NotFoundException(new ErrorMessageData().withMessage("Product container does not have a full license"))
        );
    }

    public void removeAllDiscounts(ProductContainerModel productContainer) {
        AppLicenseDraftModel appLicense = getLicenseOrThrow(productContainer);
        modelService.removeAll(appLicense.getVolumeDiscounts());
        modelService.refresh(appLicense);
    }

    public void updateDiscounts(AppLicenseDraftModel licenseDraft, List<VolumeDiscountData> newVolumeDiscounts) {
        if (!FULL.equals(licenseDraft.getLicenseType())) {
            LOG.debug("volume discounts are not applicable for license type {}", licenseDraft.getLicenseType());
            return;
        }

        List<VolumeDiscountDraftModel> discounts = CollectionUtils.emptyIfNull(newVolumeDiscounts).stream()
            .map(volumeDiscountData -> getOrCreateVolumeDiscount(licenseDraft, volumeDiscountData)).collect(
                Collectors.toList());
        Predicate<VolumeDiscountDraftModel> removedEntriesPredicate = Predicate.not(
            existingDiscount -> discounts.stream()
                .anyMatch(
                    newDiscount -> newDiscount.getPk() != null && existingDiscount.getPk() == newDiscount.getPk()
                )
        );
        List<VolumeDiscountDraftModel> entriesToDelete = licenseDraft.getVolumeDiscounts().stream()
            .filter(
                removedEntriesPredicate
            )
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(entriesToDelete)) {
            modelService.removeAll(entriesToDelete);
        }
        modelService.saveAll(discounts);
        licenseDraft.setVolumeDiscounts(discounts);
        modelService.save(licenseDraft);
        modelService.refresh(licenseDraft);
    }

    private VolumeDiscountDraftModel getOrCreateVolumeDiscount(AppLicenseDraftModel licenseDraft, VolumeDiscountData entry) {
        VolumeDiscountDraftModel draftDiscount = licenseDraft.getVolumeDiscounts().stream()
            .filter(vd -> entry.getPk() != null
                && PK.fromLong(entry.getPk()).equals(vd.getPk())
                && entry.getDiscount() == vd.getDiscount()
                && entry.getMinQuantity() == vd.getMinQuantity()
            )
            .findFirst()
            .orElse(volumeDiscountDraftConverter.convert(entry));
        draftDiscount.setAppLicenseDraft(licenseDraft);
        return draftDiscount;
    }
}
