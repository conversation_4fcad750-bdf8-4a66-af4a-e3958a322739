package com.sast.cis.core.followapp.service;

import com.sast.cis.core.followapp.dto.NewAppVersionFollowersNotifyData;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.resolver.DetailUrlResolver;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class FollowAppEmailService {
    private static final String CAMERAS_URL = "shop.header.cameras.url";
    private final ConfigurationService configurationService;
    private final DetailUrlResolver detailUrlResolver;

    public NewAppVersionFollowersNotifyData getNewAppVersionFollowersNotifyData(AppModel app,
        List<IntegratorModel> integrators) {
        Set<String> recipients = integrators.stream().map(IntegratorModel::getEmailAddress).collect(Collectors.toSet());

        String integratorPortalUrl = configurationService.getConfiguration().getString(CAMERAS_URL);
        var followAppNotifyContext = new NewAppVersionFollowersNotifyData();
        followAppNotifyContext.setIntegratorPortalUrl(integratorPortalUrl);
        followAppNotifyContext.setAppDetailUrl(getAppShopUrl(app));
        followAppNotifyContext.setAppName(app.getName());
        followAppNotifyContext.setRecipients(recipients);
        return followAppNotifyContext;
    }

    private String getAppShopUrl(final AppModel app) {
        return detailUrlResolver.resolve(app);
    }

}
