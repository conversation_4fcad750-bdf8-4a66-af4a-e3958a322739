package com.sast.cis.core.productsearch.dto;

import com.sast.cis.core.constants.shop.ProductSortField;
import com.sast.cis.core.productsearch.ProductFilter;
import lombok.Data;


@Data
public class ProductSearchQuery {
    private ProductSortField sortBy;

    private int page;

    private int pageSize;

    private String query;

    @ProductFilter(index = ProductFacetIndex.LICENSE_TYPES)
    private String licenseTypes;

    @ProductFilter(index = ProductFacetIndex.COUNTRIES)
    private String countries;

    @ProductFilter(index = ProductFacetIndex.PRIVATE_APPS)
    private String privateApps;

    @ProductFilter(index = ProductFacetIndex.INDUSTRIES)
    private String industries;

    @ProductFilter(index = ProductFacetIndex.USE_CASES)
    private String useCases;

    @ProductFilter(index = ProductFacetIndex.COMPANY_UID)
    private String companyUid;

    @ProductFilter(index = ProductFacetIndex.HARDWARE_REQUIREMENTS)
    private String hardwareRequirements;

    @ProductFilter(index = ProductFacetIndex.PACKAGES)
    private String packages;

    @ProductFilter(index = ProductFacetIndex.LICENSES)
    private String licenses;

    @ProductFilter(index = ProductFacetIndex.VEHICLE_TYPE)
    private String vehicleType;

    @ProductFilter(index = ProductFacetIndex.LICENSE_RUNTIMES)
    private String licenseRuntimes;

    @ProductFilter(index = ProductFacetIndex.FIRST_LEVEL_CATEGORIES)
    private String firstLevelCategories;

    public ProductSearchQuery() {
        this.sortBy = ProductSortField.PUBLISH_DATE;
        this.page = 0;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = ProductSortField.toEnum(sortBy);
    }
}
