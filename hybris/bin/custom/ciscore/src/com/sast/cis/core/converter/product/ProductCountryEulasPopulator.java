package com.sast.cis.core.converter.product;

import com.sast.cis.aa.core.model.MaterialModel;
import com.sast.cis.core.data.CountryEulaData;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.EulaContainerModel;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static java.util.stream.Stream.concat;

@Component
@RequiredArgsConstructor
public class ProductCountryEulasPopulator implements Populator<ProductModel, ProductData> {

    private final Converter<EulaContainerModel, CountryEulaData> countryEulaDataConverter;

    @Override
    public void populate(ProductModel product, ProductData productData) {
        if (product instanceof AppModel app) {
            final List<CountryEulaData> countryEulas = concat(appEulas(app), materialEulas(app))
                .map(countryEulaDataConverter::convert)
                .distinct()
                .toList();
            productData.setCountryEulas(countryEulas);
        } else if (product instanceof AppLicenseModel appLicense) {
            populate(appLicense.getBaseProduct(), productData);
        }
    }

    private Stream<EulaContainerModel> appEulas(AppModel app) {
        return app.getEulaContainers().stream();
    }

    private Stream<EulaContainerModel> materialEulas(AppModel app) {
        return app.getBoms().stream()
            .map(MaterialModel::getEulaContainers)
            .flatMap(Set::stream);
    }
}
