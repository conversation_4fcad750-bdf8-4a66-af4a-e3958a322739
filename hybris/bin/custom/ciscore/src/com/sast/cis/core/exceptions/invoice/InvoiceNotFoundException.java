package com.sast.cis.core.exceptions.invoice;

public class InvoiceNotFoundException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    InvoiceNotFoundException(String msg) {
        super(msg);
    }

    public static InvoiceNotFoundException forExternalId(final String externalId) {
        return new InvoiceNotFoundException(
            String.format("Invoice with external id '%s' not found", externalId)
        );
    }
}
