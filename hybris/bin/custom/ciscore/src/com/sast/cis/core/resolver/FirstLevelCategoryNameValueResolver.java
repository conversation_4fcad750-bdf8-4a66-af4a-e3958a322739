package com.sast.cis.core.resolver;

import com.sast.cis.core.model.AppModel;
import de.hybris.platform.category.CategoryService;
import de.hybris.platform.category.model.CategoryModel;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import de.hybris.platform.solrfacetsearch.indexer.IndexerBatchContext;
import de.hybris.platform.solrfacetsearch.indexer.spi.InputDocument;
import de.hybris.platform.solrfacetsearch.provider.Qualifier;
import de.hybris.platform.solrfacetsearch.provider.impl.AbstractValueResolver;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Locale;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
public class FirstLevelCategoryNameValueResolver extends AbstractValueResolver<AppModel, Object, Set<String>> {
    private final CategoryService categoryService;

    @Override
    @SneakyThrows
    protected void addFieldValues(InputDocument inputDocument, IndexerBatchContext indexerBatchContext, IndexedProperty indexedProperty,
        AppModel app, ValueResolverContext<Object, Set<String>> valueResolverContext) {
        Set<String> qualifierData = valueResolverContext.getQualifierData();
        if (CollectionUtils.isEmpty(qualifierData)) {
            LOG.warn("Cannot index localized property {} due to missing lang qualifier provider", indexedProperty.getName());
            return;
        }
        inputDocument.addField(indexedProperty, qualifierData, valueResolverContext.getFieldQualifier());
    }

    @Override
    protected Set<String> loadQualifierData(IndexerBatchContext batchContext, Collection<IndexedProperty> indexedProperties,
        AppModel app,
        Qualifier qualifier) {
        Locale locale = qualifier.getValueForType(Locale.class);
        return categoryService.getCategoryPathForProduct(app, CategoryModel.class).stream()
            .filter(this::filterFirstLevelCategoryUnderRoot)
            .map(category -> category.getName(locale))
            .collect(Collectors.toSet());
    }

    private boolean filterFirstLevelCategoryUnderRoot(CategoryModel category) {
        return category.getSupercategories().stream().anyMatch(superCategory -> superCategory.getSupercategories().isEmpty());
    }

}
