package com.sast.cis.core.country.sync.service;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Stopwatch;
import com.sast.cis.core.data.MasterCountryData;
import com.sast.cis.core.data.StoreMasterCountryData;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Set;

import static java.util.concurrent.TimeUnit.MILLISECONDS;

@Service
@Slf4j
@AllArgsConstructor
public class MasterCountryDataService {
    private static final String COUNTRY_SERVICE_ENDPOINT_KEY = "country.api.url";
    private static final String COUNTRY_SERVICE_V2_ENDPOINT_KEY = "country.v2.api.url";

    @VisibleForTesting
    private final RestTemplate cisRestTemplate;
    private final ConfigurationService configurationService;

    public Set<MasterCountryData> getCountries() {
        Stopwatch stopWatch = Stopwatch.createUnstarted();
        stopWatch.start();
        String countryEndpointUrl = configurationService.getConfiguration().getString(COUNTRY_SERVICE_ENDPOINT_KEY);
        MasterCountryData[] masterCountries = performGet(MasterCountryData[].class, countryEndpointUrl);
        stopWatch.stop();
        LOG.info("Retrieved countries. The request took country service request_time={} ms", stopWatch.elapsed(MILLISECONDS));
        return Set.of(masterCountries);
    }

    public Set<StoreMasterCountryData> getStoreCountries(String tenant) {
        String tenantSpecificEndpointUrlBase = configurationService.getConfiguration().getString(COUNTRY_SERVICE_V2_ENDPOINT_KEY);
        String url = String.format("%s/%s", tenantSpecificEndpointUrlBase, tenant);
        StoreMasterCountryData[] masterCountries = performGet(StoreMasterCountryData[].class, url);
        return Set.of(masterCountries);
    }

    private <T> T performGet(Class<T> entityClass, String url) {
        return cisRestTemplate.getForObject(url, entityClass);
    }
}
