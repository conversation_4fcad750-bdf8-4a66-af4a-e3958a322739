package com.sast.cis.core.validator;

import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.enums.LicenseAvailabilityStatus;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseDraftModel;
import com.sast.cis.core.model.CountriesAndPricesDraftModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.ErrorMessageService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sast.cis.core.constants.devcon.DevconErrorCode.*;
import static com.sast.cis.core.constants.devcon.DevconInputField.COUNTRIES_AND_PRICES;
import static com.sast.cis.core.constants.devcon.DevconInputField.FULL_PRICE;

@Component
@AllArgsConstructor
public class PricingReleasabilityValidator {

    private final ErrorMessageService errorMessageService;
    private final AppLicenseService appLicenseService;

    public Set<DevconErrorMessage> validateDraft(CountriesAndPricesDraftModel countriesAndPricesDraft) {
        Set<DevconErrorMessage> validationErrors = new LinkedHashSet<>();

        if (countriesAndPricesDraft == null || !countriesAndPricesDraft.isPricingSaved()) {
            validationErrors.add(errorMessageService.createErrorMessage(COUNTRIES_AND_PRICES_NULL, COUNTRIES_AND_PRICES));
            return validationErrors;
        }

        Set<AppLicenseDraftModel> enabledLicenses = countriesAndPricesDraft.getAppLicenses()
            .stream()
            .filter(draft -> LicenseAvailabilityStatus.PUBLISHED.equals(draft.getAvailabilityStatus()))
            .collect(Collectors.toSet());

        if (enabledLicenses.isEmpty()) {
            return validationErrors;
        }

        if (containsInvalidLicenses(enabledLicenses)) {
            validationErrors.add(errorMessageService.createErrorMessage(PAYOUT_ACCOUNT_NEEDED_FOR_LICENSE, COUNTRIES_AND_PRICES));
            return validationErrors;
        }

        return validatePriceIfFullLicenseEnabled(enabledLicenses);
    }

    private boolean containsInvalidLicenses(Set<AppLicenseDraftModel> enabledLicenses) {
        return enabledLicenses.stream()
            .anyMatch(license -> !appLicenseService.isValidLicenseTypeForSelling(license.getLicenseType()));
    }

    private Set<DevconErrorMessage> validatePriceIfFullLicenseEnabled(Set<AppLicenseDraftModel> enabledLicenses) {
        return getFullLicense(enabledLicenses)
            .map(this::validatePrices)
            .orElse(Collections.emptySet());
    }

    private Optional<AppLicenseDraftModel> getFullLicense(Set<AppLicenseDraftModel> enabledLicenses) {
        return enabledLicenses
            .stream()
            .filter(license -> LicenseType.FULL.equals(license.getLicenseType())
                && LicenseAvailabilityStatus.PUBLISHED.equals(license.getAvailabilityStatus()))
            .findFirst();
    }

    private Set<DevconErrorMessage> validatePrices(final AppLicenseDraftModel license) {
        Set<DevconErrorMessage> validationErrors = new LinkedHashSet<>();
        final Double specifiedPrice = license.getSpecifiedPrice();
        if (specifiedPrice == null || specifiedPrice < 0) {
            validationErrors.add(errorMessageService.createErrorMessage(PRICE_NOT_VALID, FULL_PRICE));
            return validationErrors;
        }
        return validationErrors;
    }
}
