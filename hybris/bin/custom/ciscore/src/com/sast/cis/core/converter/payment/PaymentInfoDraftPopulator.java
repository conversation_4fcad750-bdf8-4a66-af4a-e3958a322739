package com.sast.cis.core.converter.payment;

import com.sast.cis.core.data.PaymentInfoDraftData;
import com.sast.cis.core.model.PaymentInfoDraftModel;
import de.hybris.platform.converters.Populator;
import lombok.NonNull;
import org.springframework.stereotype.Component;

@Component
public class PaymentInfoDraftPopulator implements Populator<PaymentInfoDraftModel, PaymentInfoDraftData> {

    @Override
    public void populate(@NonNull final PaymentInfoDraftModel paymentInfoDraft, @NonNull final PaymentInfoDraftData paymentInfoDraftData) {
        paymentInfoDraftData.setCode(paymentInfoDraft.getCode());
        paymentInfoDraftData.setPaymentMethodType(paymentInfoDraft.getPaymentMethodType());
        paymentInfoDraftData.setPaymentProvider(paymentInfoDraft.getPaymentProvider());
        paymentInfoDraftData.setCreationStatus(paymentInfoDraft.getCreationStatus());
    }
}
