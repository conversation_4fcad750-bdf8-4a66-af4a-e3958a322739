package com.sast.cis.core.job;

import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.AppSyncService;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.commerceservices.impersonation.ImpersonationContext;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.cronjob.enums.CronJobStatus;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

public class GetAcquisitionsPerAppLicenseJobPerformable extends AbstractConfigurableCronJobPerformable<CronJobModel> {
    private static final Logger LOG = LoggerFactory.getLogger(GetAcquisitionsPerAppLicenseJobPerformable.class);

    @Resource
    private AppLicenseService appLicenseService;

    @Resource
    private AppSyncService appSyncService;

    @Override
    protected PerformResult performInternal(CronJobModel cronJob, ImpersonationContext context) {
        CatalogVersionModel stagedCatalogVersion = cronJob.getConfiguration().getProductCatalogVersion();
        List<AppLicenseModel> stagedUpdatedAppLicenses;
        String catalogId = "";
        if (stagedCatalogVersion != null && stagedCatalogVersion.getCatalog() != null) {
            catalogId = stagedCatalogVersion.getCatalog().getId();
        }
        try {
            stagedUpdatedAppLicenses = appLicenseService.refreshNumberOfPurchasedAppLicenses(
                    cronJob.getConfiguration().getBaseStore().getUid(), catalogId);
        } catch (Exception e) {
            LOG.error("Could not save acquisition count to applicenses due to exception {}", e.getMessage(), e);
            return new PerformResult(CronJobResult.FAILURE, CronJobStatus.FINISHED);
        }

        appSyncService.performSync(stagedUpdatedAppLicenses, stagedCatalogVersion.getCatalog().getId());
        LOG.info("Successfully updated acquisition count for {} applicenses.", stagedUpdatedAppLicenses.size());
        return new PerformResult(CronJobResult.SUCCESS, CronJobStatus.FINISHED);
    }
}
