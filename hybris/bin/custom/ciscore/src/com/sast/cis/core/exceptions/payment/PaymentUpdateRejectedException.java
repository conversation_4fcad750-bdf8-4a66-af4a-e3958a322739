package com.sast.cis.core.exceptions.payment;

import java.io.Serial;

public class PaymentUpdateRejectedException extends RuntimeException{
    @Serial
    private static final long serialVersionUID = 1L;

    public PaymentUpdateRejectedException() {
    }

    public PaymentUpdateRejectedException(String message) {
        super(message);
    }

    public PaymentUpdateRejectedException(String message, Throwable cause) {
        super(message, cause);
    }

    public PaymentUpdateRejectedException(Throwable cause) {
        super(cause);
    }

    public PaymentUpdateRejectedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
