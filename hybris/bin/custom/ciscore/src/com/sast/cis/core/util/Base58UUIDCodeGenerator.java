package com.sast.cis.core.util;

import com.google.common.base.Preconditions;
import lombok.NonNull;

import java.math.BigInteger;
import java.util.UUID;
import java.util.regex.Pattern;

public final class Base58UUIDCodeGenerator {
    private static final String BASE58_CHARACTERS = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
    private static final BigInteger BASE58_BASE = BigInteger.valueOf(58);
    private static final int BASE58_UUID_MAXLEN = 22; // 58^21 < 2^128 < 58^22
    private static final Pattern VALID_PREFIX_PATTERN = Pattern.compile("^[A-Za-z0-9]+$");

    private Base58UUIDCodeGenerator() {
        // noop
    }

    /**
     * Generates a code consisting of a prefix, an underscore separator and a Base58-UUID.
     *
     * Base58 only uses alphanumeric characters and avoids easily confused characters (I, l, O, 0).
     * The resulting UUID is left-padded with 1's (which has the value of zero for the chosen alphabet).
     *
     * The given prefix must be of nonzero length and may only contain alphanumeric characters.
     * The returned code is guaranteed to be URL-safe.
     *
     * @param prefix a string containing alphanumeric characters used as prefix
     * @return A string of the following format: [prefix]_[Base58-UUID]
     * @throws IllegalArgumentException if given prefix is blank
     */
    public static String generateCode(final String prefix) {
        Preconditions.checkArgument(isValidPrefix(prefix),
            "Given prefix %s does not match pattern %s", prefix, VALID_PREFIX_PATTERN.pattern());
        return String.format("%s_%s", prefix, generateBase58UUID());
    }

    /**
     * Generates a code consisting of a prefix and a Base58-UUID without separator
     *
     * Base58 only uses alphanumeric characters and avoids easily confused characters (I, l, O, 0).
     * The resulting UUID is left-padded with 1's (which has the value of zero for the chosen alphabet).
     *
     * The given prefix must be of nonzero length and may only contain alphanumeric characters.
     * The returned code is guaranteed to be URL-safe if the given separator is URL-safe
     *
     * @param prefix a string containing alphanumeric characters used as prefix
     * @return A string of the following format: [prefix][Base58-UUID]
     * @throws IllegalArgumentException if given prefix is blank
     */
    public static String generateCodeWithoutSeparator(@NonNull final String prefix) {
        Preconditions.checkArgument(isValidPrefix(prefix),
                "Given prefix %s does not match pattern %s", prefix, VALID_PREFIX_PATTERN.pattern());
        return String.format("%s%s", prefix, generateBase58UUID());
    }

    /**
     * Helper method to validate a given prefix.
     * It is encouraged to validate a potential prefix at the earliest possible time if provided by configuration.
     *
     * @param prefix a string to validate as a code prefix with generateCode()
     * @return whether the given prefix is valid for generateBase58Code.
     */
    public static boolean isValidPrefix(final String prefix) {
        return prefix != null && VALID_PREFIX_PATTERN.matcher(prefix).matches();
    }

    private static BigInteger toBigInteger(final UUID uuid) {
        BigInteger lsb = new BigInteger(Long.toUnsignedString(uuid.getLeastSignificantBits()));
        BigInteger msb = new BigInteger(Long.toUnsignedString(uuid.getMostSignificantBits()));

        return lsb.add(msb.shiftLeft(64));
    }

    private static String generateBase58UUID() {
        var bigIntegerUUID = toBigInteger(UUID.randomUUID());
        StringBuilder stringBuilder = new StringBuilder(BASE58_UUID_MAXLEN);
        do {
            stringBuilder.insert(0, BASE58_CHARACTERS.charAt(bigIntegerUUID.mod(BASE58_BASE).intValue()));
            bigIntegerUUID = bigIntegerUUID.divide(BASE58_BASE);
        } while(bigIntegerUUID.compareTo(BigInteger.ZERO) > 0);

        while (stringBuilder.length() < BASE58_UUID_MAXLEN) {
            stringBuilder.insert(0, BASE58_CHARACTERS.charAt(0));
        }
        return stringBuilder.toString();
    }
}
