package com.sast.cis.core.service;

import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.search.SearchResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

@Service
public class DaoQueryService {

    @Resource
    private FlexibleSearchService flexibleSearchService;

    public <T> Optional<T> searchSingleResult(FlexibleSearchQuery query) {
        return searchSingleResult(query, () -> new IllegalStateException("Found more than one result for " + query));
    }

    public <T> Optional<T> searchSingleResult(FlexibleSearchQuery query, Supplier<? extends RuntimeException> multipleResultsException) {
        SearchResult<T> searchResult = flexibleSearchService.search(query);
        return returnSingleResult(searchResult.getResult(), multipleResultsException);
    }

    public <T> Optional<T> searchAnyResult(FlexibleSearchQuery query) {
        SearchResult<T> searchResult = flexibleSearchService.search(query);
        return returnFirstResult(searchResult.getResult());
    }

    private <T> Optional<T> returnSingleResult(List<T> entitiesFound, Supplier<? extends RuntimeException> multipleResultsException) {
        if (entitiesFound.size() > 1) {
            throw multipleResultsException.get();
        }
        return returnFirstResult(entitiesFound);
    }

    private <T> Optional<T> returnFirstResult(List<T> entitiesFound) {
        if (entitiesFound.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(entitiesFound.get(0));
    }

    public <T> Optional<T> getSingleModelByExample(T example) {
        return getSingleModelByExample(example,
            () -> new IllegalStateException("Found more than one item querying for example: " + example));
    }

    public <T> Optional<T> getSingleModelByExample(T example, Supplier<? extends RuntimeException> multipleResultsException) {
        List<T> entitiesFound = getModelsByExample(example);
        return returnSingleResult(entitiesFound, multipleResultsException);
    }

    public <T> List<T> getModelsByExample(final T example) {
        return flexibleSearchService.getModelsByExample(example);
    }
}
