package com.sast.cis.core.resolver;

import com.sast.cis.companyprofile.enums.CompanyProfileStatus;
import com.sast.cis.companyprofile.model.CompanyProfileModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import de.hybris.platform.solrfacetsearch.config.IndexedProperty;
import de.hybris.platform.solrfacetsearch.config.exceptions.FieldValueProviderException;
import de.hybris.platform.solrfacetsearch.indexer.IndexerBatchContext;
import de.hybris.platform.solrfacetsearch.indexer.spi.InputDocument;
import de.hybris.platform.solrfacetsearch.provider.ValueResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class CompanyHasPublishedProfileValueResolver implements ValueResolver<AppModel> {

    @Override
    public void resolve(
        final InputDocument inputDocument,
        final IndexerBatchContext indexerBatchContext,
        final Collection<IndexedProperty> collection,
        final AppModel app) throws FieldValueProviderException {

        if (app == null || app.getCompany() == null) {
            LOG.error("App to index was null or had no company. {}, PKs: {}", app, indexerBatchContext.getPks());
            return;
        }
        final Optional<IndexedProperty> indexedProperty = collection.stream().findFirst();
        if (indexedProperty.isEmpty()) {
            return;
        }
        final IoTCompanyModel company = app.getCompany();
        final CompanyProfileModel companyProfile = company.getCompanyProfile();
        final boolean hasPublishedProfile = Objects.nonNull(companyProfile)
            && CompanyProfileStatus.PUBLISHED.equals(companyProfile.getStatus());
        inputDocument.addField(indexedProperty.get(), hasPublishedProfile);
    }
}
