package com.sast.cis.core.service.country;

import de.hybris.platform.core.model.c2l.CountryModel;
import lombok.NonNull;
import org.apache.commons.collections4.SetUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class IntegratorCountryService {

    public boolean isIntegratorCountryBlockedInDeveloperCountry(
        @NonNull final CountryModel integratorCountry,
        @NonNull final CountryModel developerCountry) {

        final Set<String> blockedCountriesCommercial = developerCountry.getBlockedCountriesCommercial();
        return SetUtils.emptyIfNull(blockedCountriesCommercial).contains(integratorCountry.getIsocode());
    }
}
