package com.sast.cis.shop.frontend.security.impl;

import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.strategy.UserAuthorizationStrategy;
import de.hybris.platform.core.model.user.UserModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ShopUserAuthorizationStrategy implements UserAuthorizationStrategy {
    private static final Logger LOG = LoggerFactory.getLogger(ShopUserAuthorizationStrategy.class);

    @Override
    public boolean isUserAccessDenied(UserModel user) {
        if (!(user instanceof IntegratorModel)) {
            String errorMessage = user == null ? "User does not exist. {}" : "User with pk=" + user.getPk() + " is not an Integrator. {}";
            LOG.warn(errorMessage, "Access denied to shop");
            return true;
        }
        return false;
    }
}
