package com.sast.cis.shop.frontend.i18n;

import com.sast.cis.shop.frontend.validator.SessionCountryLanguageValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Component
@RequiredArgsConstructor
public class I18nSessionInitializer {

    private final I18nStoreFacade i18nStoreFacade;

    private final BaseStoreI18nFacade baseStoreI18nFacade;

    private final MultiCountryLanguageStoreSessionFacade multiCountryLanguageStoreSessionFacade;

    private final SessionCountryLanguageValidator sessionCountryLanguageValidator;

    public void initializeSession(final HttpServletRequest httpServletRequest) {
        initSessionFromCookies(httpServletRequest);
        initSessionFromUniqueActiveCountry();
    }

    private void initSessionFromCookies(final HttpServletRequest httpServletRequest) {
        i18nStoreFacade.setI18nConfigurationToSession(httpServletRequest);
    }

    private void initSessionFromUniqueActiveCountry() {
        final List<String> countryIsoCodes = baseStoreI18nFacade.getActiveCountries();
        if (countryIsoCodes.size() == 1) {
            final var countryIsoCode = countryIsoCodes.get(0);
            multiCountryLanguageStoreSessionFacade.setUserSelectedCountry(countryIsoCode);
            final var currentLanguage = multiCountryLanguageStoreSessionFacade.getCurrentLanguage();
            if (!sessionCountryLanguageValidator.isValidCountryLanguage(countryIsoCode, currentLanguage.getIsocode(),
                baseStoreI18nFacade.getCurrentBaseStore())) {
                baseStoreI18nFacade.getDefaultLanguageForCountry(countryIsoCode)
                    .ifPresent(multiCountryLanguageStoreSessionFacade::setCurrentLanguage);
            }
        }
    }
}
