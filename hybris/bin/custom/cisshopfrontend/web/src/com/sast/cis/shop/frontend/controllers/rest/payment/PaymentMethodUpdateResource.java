package com.sast.cis.shop.frontend.controllers.rest.payment;

import com.sast.cis.core.data.CreditCardPaymentInfoData;
import com.sast.cis.core.data.OrderPaymentUpdate;
import com.sast.cis.core.data.OrderPaymentUpdateData;
import com.sast.cis.core.exceptions.payment.PaymentUpdateFailedException;
import com.sast.cis.core.exceptions.payment.PaymentUpdateRejectedException;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.exceptions.web.InternalServerException;
import com.sast.cis.core.facade.user.PaymentUpdateFacade;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@Slf4j
@RestController
@RequestMapping("/api/orders/{orderCode}/payment")
@Api(tags = "Orders")
@RequiredArgsConstructor
public class PaymentMethodUpdateResource {
    private final PaymentUpdateFacade paymentUpdateFacade;

    @PostMapping
    @ResponseStatus(HttpStatus.OK)
    @ApiOperation(value = "Creates payment update data for user-determined change of payment for an existing order")
    public OrderPaymentUpdateData createPaymentUpdate(final @PathVariable @NotNull String orderCode) {
        LOG.info("Calling createPaymentUpdate");
        try {
            return paymentUpdateFacade.createPaymentUpdate(orderCode);
        } catch (PaymentUpdateFailedException e) {
            throw new InternalServerException();
        } catch (PaymentUpdateRejectedException e) {
            throw new BadRequestException();
        }
    }

    @PostMapping("/create-card-and-set")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation(value = "Creates a new CC payment info and updates an existing order with it")
    public void updateWithNewPaymentInfo(final @PathVariable @NotNull String orderCode,
                                         final @RequestBody @NotNull CreditCardPaymentInfoData paymentInfoData) {
        LOG.info("Calling updateWithNewPaymentInfo");
        paymentUpdateFacade.createPaymentInfoAndAuthorize(orderCode, paymentInfoData);
    }

    @PostMapping("/set")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @ApiOperation(value = "Uses an existing payment info and updates an existing order with it")
    public void updateWithExistingPaymentInfo(final @PathVariable String orderCode,
                                              final @RequestBody @NotNull OrderPaymentUpdate orderPaymentUpdate) {
        LOG.info("Calling updateWithExistingPaymentInfo");
        paymentUpdateFacade.setPaymentInfoAndAuthorize(orderCode, orderPaymentUpdate);
    }
}
