/*
 * [y] hybris Platform
 *
 * Copyright (c) 2017 SAP SE or an SAP affiliate company.  All rights reserved.
 *
 * This software is the confidential and proprietary information of SAP
 * ("Confidential Information"). You shall not disclose such Confidential
 * Information and shall use it only in accordance with the terms of the
 * license agreement you entered into with SAP.
 */
package com.sast.cis.shop.frontend.renderer;

import com.sast.cis.web.util.CmsLinkComponentUrlResolver;
import de.hybris.platform.acceleratorcms.component.renderer.CMSComponentRenderer;
import de.hybris.platform.category.model.CategoryModel;
import de.hybris.platform.cms2.enums.LinkTargets;
import de.hybris.platform.cms2.model.contents.components.CMSLinkComponentModel;
import de.hybris.platform.commercefacades.product.data.CategoryData;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.taglibs.standard.tag.common.core.UrlSupport;
import org.owasp.html.HtmlPolicyBuilder;
import org.owasp.html.PolicyFactory;
import org.springframework.beans.factory.annotation.Required;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.PageContext;

import java.io.IOException;

/**
 */
public class CMSLinkComponentRenderer implements CMSComponentRenderer<CMSLinkComponentModel> {
    private static final Logger LOG = Logger.getLogger(CMSLinkComponentRenderer.class);

    @Resource
    private CmsLinkComponentUrlResolver urlResolver;

    protected static final PolicyFactory policy = new HtmlPolicyBuilder().allowStandardUrlProtocols()
        .allowElements("a", "span")
        .allowAttributes("href", "style", "class", "title", "target", "download", "rel", "rev",
            "hreflang", "type", "text", "accesskey", "contenteditable", "contextmenu", "dir", "draggable",
            "dropzone", "hidden", "id", "lang", "spellcheck", "tabindex", "translate")
        .onElements("a")
        .allowAttributes("class")
        .onElements("span")
        .toFactory();

    private Converter<ProductModel, ProductData> productUrlConverter;
    private Converter<CategoryModel, CategoryData> categoryUrlConverter;

    protected Converter<ProductModel, ProductData> getProductUrlConverter() {
        return productUrlConverter;
    }

    @Required
    public void setProductUrlConverter(final Converter<ProductModel, ProductData> productUrlConverter) {
        this.productUrlConverter = productUrlConverter;
    }

    protected Converter<CategoryModel, CategoryData> getCategoryUrlConverter() {
        return categoryUrlConverter;
    }

    @Required
    public void setCategoryUrlConverter(final Converter<CategoryModel, CategoryData> categoryUrlConverter) {
        this.categoryUrlConverter = categoryUrlConverter;
    }

    protected String getUrl(CMSLinkComponentModel component) {
        return urlResolver.resolveUrl(component, productUrlConverter, categoryUrlConverter);
    }

    @Override
    public void renderComponent(final PageContext pageContext, final CMSLinkComponentModel component) throws ServletException,
        IOException {
        try {
            final String url = getUrl(component);
            final String encodedUrl = UrlSupport.resolveUrl(url, null, pageContext);
            final String linkName = component.getLinkName();

            StringBuilder html = new StringBuilder();

            if (StringUtils.isNotBlank(linkName) && StringUtils.isBlank(encodedUrl)) {
                // <span class="empty-nav-item">${component.linkName}</span>
                html.append("<span class=\"empty-nav-item\">");
                html.append(linkName);
                html.append("</span>");
            } else {
                // <a href="${encodedUrl}" ${component.styleAttributes} title="${component.linkName}"
                // ${component.target == null || component.target == 'SAMEWINDOW' ? '' : 'target="_blank"'}>${component.linkName}</a>

                html.append("<a href=\"");
                html.append(encodedUrl);
                html.append("\" ");

                // Write additional attributes onto the link
                if (component.getStyleAttributes() != null) {
                    html.append(component.getStyleAttributes());
                }

                if (StringUtils.isNotBlank(linkName)) {
                    html.append(" title=\"");
                    html.append(linkName);
                    html.append("\" ");
                }

                if (component.getTarget() != null && !LinkTargets.SAMEWINDOW.equals(component.getTarget())) {
                    html.append(" target=\"_blank\"");
                }
                html.append(">");
                if (StringUtils.isNotBlank(linkName)) {
                    html.append(linkName);
                }
                html.append("</a>");
            }

            String sanitizedHTML = policy.sanitize(html.toString());
            final JspWriter out = pageContext.getOut();
            out.write(sanitizedHTML);
        } catch (final JspException e) {
            if (LOG.isDebugEnabled()) {
                LOG.debug(e);
            }
        }
    }
}
