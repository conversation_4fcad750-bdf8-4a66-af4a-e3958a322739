package com.sast.cis.shop.frontend.i18n;

import com.sast.cis.core.productsearch.dto.ProductSearchQuery;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class I18nCookieGeneratorUnitTest {

    private final I18nCookieGenerator i18nCookieGenerator = new I18nCookieGenerator();

    @Mock
    private HttpServletResponse httpServletResponse;

    @Captor
    private ArgumentCaptor<Cookie> cookieArgumentCaptor;

    @Test
    public void whenGenerateCookie_thenCreateCookieAndSetToResponse() {
        final String cookieValue = "de";

        i18nCookieGenerator.generateCookie(httpServletResponse, I18nCookieType.STORE_SELECTED_COUNTRY, cookieValue);

        verify(httpServletResponse).addCookie(cookieArgumentCaptor.capture());
        final Cookie cookie = cookieArgumentCaptor.getValue();
        assertThat(cookie).isNotNull();
        assertThat(cookie.getName()).isEqualTo(I18nCookieType.STORE_SELECTED_COUNTRY.getCookieName());
        assertThat(cookie.getValue()).isEqualTo(cookieValue);
        assertThat(cookie.isHttpOnly()).isFalse();
        assertThat(cookie.getSecure()).isTrue();
    }
}
