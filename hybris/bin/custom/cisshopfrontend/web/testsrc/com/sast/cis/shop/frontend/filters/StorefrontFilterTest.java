package com.sast.cis.shop.frontend.filters;

import com.sast.cis.shop.frontend.i18n.I18nSessionInitializer;
import com.sast.cis.shop.frontend.i18n.I18nStoreFacade;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.acceleratorstorefrontcommons.history.BrowseHistory;
import de.hybris.platform.commercefacades.storesession.StoreSessionFacade;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpMethod;
import org.springframework.util.PathMatcher;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Locale;
import java.util.Set;

import static org.mockito.Mockito.*;

@UnitTest
public class StorefrontFilterTest {
    private static final String REQUESTEDURL = "http://requestedurl.hybris.de";
    private static final String SERVLET_PATH = "/cart/export";
    private static final String EXCLUDEDURL_PATTERN = "/**/cart/export";

    private StorefrontFilter filter;

    @Mock
    private BrowseHistory browseHistory;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private HttpSession session;

    @Mock
    private FilterChain filterChain;

    @Mock
    private StoreSessionFacade storeSessionFacade;

    @Mock
    private Enumeration<Locale> locales;

    @Mock
    private PathMatcher pathMatcher;

    @Mock
    private I18nSessionInitializer i18nSessionInitializer;

    @Before
    public void initFilter() {
        MockitoAnnotations.initMocks(this);
        filter = new StorefrontFilter();
        filter.setBrowseHistory(browseHistory);
        filter.setStoreSessionFacade(storeSessionFacade);
        filter.setPathMatcher(pathMatcher);
        filter.setI18nSessionInitializer(i18nSessionInitializer);

        final Set<String> excludedUrlSet = new HashSet<>();
        excludedUrlSet.add(EXCLUDEDURL_PATTERN);
        filter.setRefererExcludeUrlSet(excludedUrlSet);

        when(request.getSession()).thenReturn(session);
        when(request.getLocales()).thenReturn(locales);
        final StringBuffer requestUrlSb = new StringBuffer();
        requestUrlSb.append(REQUESTEDURL);
        when(request.getRequestURL()).thenReturn(requestUrlSb);
        when(request.getRequestURI()).thenReturn(requestUrlSb.toString());
        when(request.getServletPath()).thenReturn(SERVLET_PATH);
        when(pathMatcher.match(EXCLUDEDURL_PATTERN, SERVLET_PATH)).thenReturn(true);
    }

    @Test
    public void shouldStoreOriginalRefererOnGET() throws IOException, ServletException {
        when(request.getMethod()).thenReturn(HttpMethod.GET.toString());
        when(request.getHeader(StorefrontFilter.AJAX_REQUEST_HEADER_NAME)).thenReturn(null);
        when(filter.isRequestPathExcluded(request)).thenReturn(false);
        filter.doFilterInternal(request, response, filterChain);
        verify(session).setAttribute(StorefrontFilter.ORIGINAL_REFERER, REQUESTEDURL);
    }

    @Test
    public void shouldNotStoreOriginalRefererOnPOST() throws IOException, ServletException {
        when(request.getMethod()).thenReturn(HttpMethod.POST.toString());
        filter.doFilterInternal(request, response, filterChain);
        verify(session, never()).setAttribute(StorefrontFilter.ORIGINAL_REFERER, REQUESTEDURL);
    }

    @Test
    public void shouldNotStoreOriginalRefererOnAjax() throws IOException, ServletException {
        when(request.getMethod()).thenReturn(HttpMethod.GET.toString());
        when(request.getHeader(StorefrontFilter.AJAX_REQUEST_HEADER_NAME)).thenReturn("1");
        filter.doFilterInternal(request, response, filterChain);
        verify(session, never()).setAttribute(StorefrontFilter.ORIGINAL_REFERER, REQUESTEDURL);
    }

    @Test
    public void shouldNotStoreOriginalRefererOnExcludedUrls() throws IOException, ServletException {
        when(request.getMethod()).thenReturn(HttpMethod.GET.toString());
        when(request.getHeader(StorefrontFilter.AJAX_REQUEST_HEADER_NAME)).thenReturn(null);
        filter.doFilterInternal(request, response, filterChain);
        verify(session, never()).setAttribute(StorefrontFilter.ORIGINAL_REFERER, REQUESTEDURL);
    }

    @Test
    @SneakyThrows
    public void givenNewSession_whenDoFilterInternal_thenShouldInvokeI18nConfigurationInitialization() {
        when(session.isNew()).thenReturn(Boolean.TRUE);

        filter.doFilterInternal(request, response, filterChain);

        verify(i18nSessionInitializer).initializeSession(request);
    }

    @Test
    @SneakyThrows
    public void givenSessionNotInitialized_whenDoFilterInternal_thenShouldInvokeI18nConfigurationInitialization() {
        when(session.isNew()).thenReturn(Boolean.FALSE);
        when(session.getAttribute(filter.getClass().getName())).thenReturn(null);

        filter.doFilterInternal(request, response, filterChain);

        verify(i18nSessionInitializer).initializeSession(request);
    }

    @Test
    @SneakyThrows
    public void givenSessionInitialized_whenDoFilterInternal_thenShouldNotInvokeI18nConfigurationInitialization() {
        when(session.isNew()).thenReturn(Boolean.FALSE);
        when(session.getAttribute(filter.getClass().getName())).thenReturn("initialized");

        filter.doFilterInternal(request, response, filterChain);

        verify(i18nSessionInitializer, never()).initializeSession(request);
    }
}
