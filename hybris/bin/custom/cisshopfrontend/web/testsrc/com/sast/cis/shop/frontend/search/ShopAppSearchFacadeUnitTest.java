package com.sast.cis.shop.frontend.search;

import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.i18n.FallbackCountryProvider;
import com.sast.cis.core.i18n.I18nSessionAttributesReader;
import com.sast.cis.core.productsearch.ShopProductService;
import com.sast.cis.core.productsearch.dto.ProductSearchQuery;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.search.data.SearchFilterQueryData;
import org.apache.commons.configuration.PropertiesConfiguration;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static com.sast.cis.core.productsearch.dto.ProductFacetIndex.COUNTRIES;
import static com.sast.cis.core.productsearch.dto.ProductFacetIndex.LICENSE_TYPES;
import static com.sast.cis.shop.frontend.constants.CisshopfrontendConstants.CATEGORY_PAGE_SIZE_DEFAULT;
import static de.hybris.platform.commerceservices.search.solrfacetsearch.data.FilterQueryOperator.OR;
import static java.util.Set.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class ShopAppSearchFacadeUnitTest {

    @Mock
    private PropertiesConfiguration shopFrontendProperties;

    @Mock
    private ShopProductService service;

    @Mock
    private I18nSessionAttributesReader i18nSessionAttributesReader;

    @Mock
    private FallbackCountryProvider fallbackCountryProvider;

    @InjectMocks
    private ShopAppSearchFacade shopAppSearchFacade;

    @Captor
    private ArgumentCaptor<ProductSearchQuery> productSearchQueryArgumentCaptor;

    @Captor
    private ArgumentCaptor<List<SearchFilterQueryData>> searchFilterQueryCaptor;

    private final String selectedCountry = "AT";

    private final String fallbackCountry = "DE";

    @Before
    public void setup() {
        when(i18nSessionAttributesReader.getUserSelectedCountry()).thenReturn(selectedCountry);
        when(fallbackCountryProvider.getCountry()).thenReturn(fallbackCountry);
    }

    @Test
    public void getApps_shouldInvokeShopProductServiceWithInputLicenseType() {
        final String testLicenseType = LicenseType.EVALUATION.getCode();
        final ProductSearchQuery productSearchQuery = new ProductSearchQuery();
        productSearchQuery.setLicenseTypes(testLicenseType);

        shopAppSearchFacade.getApps(productSearchQuery);

        verify(service).getProducts(productSearchQueryArgumentCaptor.capture(), anyList());
        assertThat(productSearchQueryArgumentCaptor.getValue().getLicenseTypes()).isEqualTo(testLicenseType);
    }

    @Test
    public void whenGetApps_thenInvokeShopProductServiceWithAdditionalFilterQueryForLicenseTypeAndCountries() {
        final ProductSearchQuery productSearchQuery = new ProductSearchQuery();

        shopAppSearchFacade.getApps(new ProductSearchQuery());

        verify(service).getProducts(eq(productSearchQuery), searchFilterQueryCaptor.capture());
        final List<SearchFilterQueryData> licenseTypeFilter = searchFilterQueryCaptor.getValue();
        assertThat(licenseTypeFilter)
            .usingFieldByFieldElementComparator()
            .containsExactly(
                new SearchFilterQueryData()
                    .withKey(LICENSE_TYPES.getCode())
                    .withValues(of("NOT TOOL"))
                    .withOperator(OR),
                new SearchFilterQueryData()
                    .withKey(COUNTRIES.getCode())
                    .withValues(of(selectedCountry))
                    .withOperator(OR)
            );
    }

    @Test
    public void givenNoSelectedCountry_whenGetApps_thenInvokeShopProductServiceWithAdditionalFilterQueryForLicenseTypeAndCountries() {
        when(i18nSessionAttributesReader.getUserSelectedCountry()).thenReturn(null);

        final ProductSearchQuery productSearchQuery = new ProductSearchQuery();

        shopAppSearchFacade.getApps(new ProductSearchQuery());

        verify(service).getProducts(eq(productSearchQuery), searchFilterQueryCaptor.capture());
        final List<SearchFilterQueryData> licenseTypeFilter = searchFilterQueryCaptor.getValue();
        assertThat(licenseTypeFilter)
            .usingFieldByFieldElementComparator()
            .containsExactly(
                new SearchFilterQueryData()
                    .withKey(LICENSE_TYPES.getCode())
                    .withValues(of("NOT TOOL"))
                    .withOperator(OR),
                new SearchFilterQueryData()
                    .withKey(COUNTRIES.getCode())
                    .withValues(of(fallbackCountry))
                    .withOperator(OR)
            );
    }

    @Test
    public void givenPageSizeSetToZero_whenGetApps_thenSetDefaultPageSize() {
        final int defaultPageSize = 10;
        when(shopFrontendProperties.getInt(CATEGORY_PAGE_SIZE_DEFAULT)).thenReturn(defaultPageSize);
        final ProductSearchQuery productSearchQuery = new ProductSearchQuery();
        productSearchQuery.setPageSize(0);

        shopAppSearchFacade.getApps(productSearchQuery);

        verify(service).getProducts(productSearchQueryArgumentCaptor.capture(), anyList());
        assertThat(productSearchQueryArgumentCaptor.getValue().getPageSize()).isEqualTo(defaultPageSize);
    }
}
