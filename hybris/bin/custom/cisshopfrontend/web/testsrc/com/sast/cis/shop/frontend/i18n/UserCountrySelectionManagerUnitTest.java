package com.sast.cis.shop.frontend.i18n;

import com.sast.cis.shop.frontend.validator.SessionCountryLanguageValidator;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.session.SessionService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.sast.cis.core.i18n.StoreI18nConstants.SELECTED_COUNTRY_SESSION_ATTR_KEY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class UserCountrySelectionManagerUnitTest {
    private final String countryIsoCode = "pt";
    @Mock
    private SessionService defaultSessionService;
    @Mock
    private SessionCountryLanguageValidator sessionCountryLanguageValidator;
    @Mock
    private BaseStoreService baseStoreService;
    @InjectMocks
    private  UserCountrySelectionManager userCountrySelectionManager;
    @Mock
    private BaseStoreModel baseStore;
    @Before
    public void setUp() throws Exception {
        when(baseStoreService.getCurrentBaseStore()).thenReturn(baseStore);
        when(sessionCountryLanguageValidator.isValidCountry(countryIsoCode,baseStore)).thenReturn(true);
    }

    @Test
    public void givenCountryIsValid_whenSetUserSelectedCountry_thenSetSelectedCountrySessionAttribute() {

        userCountrySelectionManager.setUserSelectedCountry(countryIsoCode);

        verify(defaultSessionService).setAttribute(SELECTED_COUNTRY_SESSION_ATTR_KEY, countryIsoCode);
    }

    @Test
    public void givenCountryIsInvalid_whenSetUserSelectedCountry_thenNotSetSelectedCountrySessionAttribute() {
        when(sessionCountryLanguageValidator.isValidCountry(countryIsoCode, baseStore)).thenReturn(false);

        userCountrySelectionManager.setUserSelectedCountry(countryIsoCode);

        verify(defaultSessionService, never()).setAttribute(eq(SELECTED_COUNTRY_SESSION_ATTR_KEY), anyString());
    }

    @Test
    public void whenGetUserSelectedCountry_thenGetSelectedCountrySessionAttribute() {
        when(defaultSessionService.getAttribute(SELECTED_COUNTRY_SESSION_ATTR_KEY)).thenReturn(countryIsoCode);

        final String selectedCountry = userCountrySelectionManager.getUserSelectedCountry();

        assertThat(selectedCountry).isEqualTo(countryIsoCode);
    }
}