package com.sast.cis.shop.frontend.controllers.rest;

import com.google.gson.Gson;
import com.sast.cis.core.data.LicenseActivationData;
import com.sast.cis.core.enums.LicenseActivationStatus;
import com.sast.cis.core.validator.license.activation.LicenseActivationValidationException;
import com.sast.cis.shop.frontend.messages.UserMessageFactory;
import com.sast.cis.shop.frontend.handlers.HttpExceptionHandler;
import com.sast.cis.shop.frontend.license.LicenseActivationFacade;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class LicenseActivationResourceTest {

    private static final String LICENSE_ACTIVATION_RESOURCE_PATH = "/api/company/current/license/{licenseCode}/activation";

    private static final Gson gson = new Gson();

    @Mock
    private LicenseActivationFacade licenseActivationFacade;

    @Mock
    private UserMessageFactory userMessageFactory;

    @InjectMocks
    private LicenseActivationResource licenseActivationResource;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(licenseActivationResource)
            .setControllerAdvice(new HttpExceptionHandler(userMessageFactory))
            .build();
    }

    @Test
    public void givenExistingLicenseActivation_whenGet_thenReturnOkWithResult() throws Exception {
        final String testLicense = "A_00001_Tool";
        final LicenseActivationData licenseActivationData = new LicenseActivationData()
            .withActivationStatus(LicenseActivationStatus.ACTIVE)
            .withLicenseCode(testLicense)
            .withCompanyId("company_uid");

        when(licenseActivationFacade.getLicenseActivationStatusForCurrentCompany(testLicense))
            .thenReturn(licenseActivationData);

        final MvcResult mvcResult = mockMvc.perform(get(LICENSE_ACTIVATION_RESOURCE_PATH, testLicense)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(status().isOk())
            .andReturn();

        final LicenseActivationData response = gson.fromJson(mvcResult.getResponse().getContentAsString(), LicenseActivationData.class);
        assertThat(response).isEqualToComparingFieldByField(licenseActivationData);
    }

    @Test
    public void whenLicenseActivationValidationExceptionThrows_thenReturnBadRequest() throws Exception {
        when(licenseActivationFacade.getLicenseActivationStatusForCurrentCompany(anyString()))
            .thenThrow(LicenseActivationValidationException.withErrors(Set.of()));

        mockMvc.perform(get(LICENSE_ACTIVATION_RESOURCE_PATH, "dummy_license")
             .contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(status().isBadRequest())
            .andReturn();
    }
}
