package com.sast.cis.shop.frontend.controllers.pages.directsales;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.acceleratorservices.storefront.util.PageTitleResolver;
import de.hybris.platform.cms2.model.pages.ContentPageModel;
import de.hybris.platform.cms2.model.pages.PageTemplateModel;
import de.hybris.platform.cms2.servicelayer.services.CMSPageService;
import de.hybris.platform.cms2.servicelayer.services.CMSPreviewService;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.ui.Model;

import static de.hybris.platform.acceleratorstorefrontcommons.controllers.pages.AbstractPageController.CMS_PAGE_MODEL;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class ProductSelectionControllerUnitTest {

    @Mock
    private CMSPreviewService cmsPreviewService;

    @Mock
    private CMSPageService cmsPageService;

    @Mock
    private PageTitleResolver pageTitleResolver;

    @InjectMocks
    private ProductSelectionController controller;

    @Mock
    private Model model;

    @Mock
    private PageTemplateModel pageTemplate;

    @Mock
    private ContentPageModel contentPage;

    @Before
    public void setUp() throws Exception {
        when(cmsPageService.getPageForLabelOrId(anyString(), any())).thenReturn(contentPage);
    }

    @Test
    @SneakyThrows
    public void whenGetCompanyProfile_thenReturnRedirect() {
        controller.productSelection("productCode", model);

        verify(model).addAttribute(CMS_PAGE_MODEL, contentPage);
    }
}
