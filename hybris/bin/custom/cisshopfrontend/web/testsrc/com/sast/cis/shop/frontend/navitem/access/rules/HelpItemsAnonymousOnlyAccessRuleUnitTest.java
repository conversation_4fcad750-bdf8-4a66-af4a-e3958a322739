package com.sast.cis.shop.frontend.navitem.access.rules;

import com.sast.cis.core.data.NavigationItemData;
import com.sast.cis.core.enums.NavigationItemGroup;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(DataProviderRunner.class)
public class HelpItemsAnonymousOnlyAccessRuleUnitTest {

    private final HelpItemsAnonymousOnlyAccessRule accessRule = new HelpItemsAnonymousOnlyAccessRule();

    @Rule
    public MockitoRule mockito = MockitoJUnit.rule();

    @Mock
    private NavigationItemData navigationItemData;

    @Before
    public void setup() {
        when(navigationItemData.getItemCode()).thenReturn("storeHelpIntroToStore");
        when(navigationItemData.getGroup()).thenReturn(NavigationItemGroup.HELP.getCode());
    }

    @DataProvider
    public static Object[][] navigationItems() {
        return new Object[][] {
            new Object[] { "storeHelpIntroToStore", true },
            new Object[] { "globalHelpContactEmail", true },
            new Object[] { "unknown_id", false },
            new Object[] { "globalHelpSupport", false }
        };
    }

    @Test
    @UseDataProvider("navigationItems")
    public void testRuleApplicability(final String givenItemCode, final boolean expectedResult) {
        when(navigationItemData.getItemCode()).thenReturn(givenItemCode);

        final boolean applies = accessRule.applies(navigationItemData);

        assertThat(applies).isEqualTo(expectedResult);
    }

    @Test
    public void givenAuthenticatedUser_thenItemIsNotAccessible() {
        final boolean isItemAccessible = accessRule.isItemAccessible(navigationItemData, true);

        assertThat(isItemAccessible).isFalse();
    }

    @Test
    public void givenUnauthenticatedUser_thenItemIsAccessible() {
        final boolean isItemAccessible = accessRule.isItemAccessible(navigationItemData, false);

        assertThat(isItemAccessible).isTrue();
    }
}
