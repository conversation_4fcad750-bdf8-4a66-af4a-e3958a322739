website.name=IOT Store
error.back.url=Back to application
error.message.title=A system error has occurred.
error.message.subtitle=Please try again.
error.message.imprint=Corporate Information

account.confirmation.address.added=Address created successfully
account.confirmation.address.removed=Address removed successfully
account.confirmation.address.updated=Address updated successfully
account.confirmation.default.address.changed=Your default address was updated.
account.confirmation.payment.details.removed=Your payment details were removed.
account.confirmation.signout.subtitle=Your cart will be waiting for you when you sign back in.
account.confirmation.signout.title=You have signed out of your account.
account.confirmation.close.title=Your account has been closed.
account.error.account.exists.with.email.address.subtitle=Please choose an alternative email address.
account.error.account.exists.with.email.address.title=An account already exists for email address {0}
account.error.account.not.found=No account was found for the email address provided.
account.error.login.please=You must login to access this page. Please log in with your credentials below.

address.building=Building Name or Number
address.building_and_room=Building Name and Room Number
address.country=Country
address.country.invalid=Please select a country
address.phone.invalid=Incorrect phone number
address.default=Make this my default address
address.district=District
address.district.invalid=Please enter a district
address.district_and_street=District and Street
address.error.formentry.invalid=Errors were found with the address you provided. Please check the errors below and re-submit your address.
address.firstName=First Name
address.firstName.invalid=Please enter a first name
address.furtherSubarea=Further subarea number, house number
address.furtherSubarea.invalid=Please enter further subarea number
address.lastName.invalid=Please enter a last name
address.line1=Address Line 1
address.line1.invalid=Please enter address Line 1
address.line2=Address Line 2
address.line2.invalid=Please enter address Line 2
address.postalcode=Postal Code
address.postcode=Post Code
address.postcode.invalid=Please enter post code
address.postcodeJP=Postal Code preceded by postal symbol
address.postcodeJP.invalid=Please enter post code preceded by postal symbol
address.prefecture=Prefecture name
address.prefecture.invalid=Please select a prefecture
address.province=Province
address.province.invalid=Please enter a province
address.regionIso.invalid=Please enter a region
address.required=Fields marked* are required
address.room=Room Number
address.room.invalid=Please enter a room number
address.selectCountry=Country
address.selectPrefecture=Prefecture
address.selectProvince=Province
address.selectState=State / Province
address.state=State / Province
address.street=Street Name with Street Number
address.subarea=Subarea
address.subarea.invalid=Please enter a subarea
address.surname=Last Name
address.title=Title
address.title.invalid=Please select a title
address.companyName=Company
address.companyName.invalid=Please enter a company name
address.title.pleaseSelect=title
address.townCity=City
address.townCity.invalid=Please enter a Town/City
address.townJP=City, village, city ward
address.townJP.invalid=Please enter a city, village, city ward
address.zipcode=Zip / Postal Code
address.phone=Phone number

aria.pickupinstore.loading=Loading... Please wait...
aria.pickupinstore.storesloaded=Stores loaded

basket.add.free.gift=Add your free gift to Cart
basket.add.to.basket=Add to cart
basket.add.to.cart=Add
basket.added.to.basket=Added to Your Shopping Cart
basket.added.to.basket.error=The app could not be added to your shopping cart
basket.added.to.mobile.basket=Added Item
basket.confirmation.items.added=Your saved items were added to your cart.
cart.empty=You cannot proceed to checkout with empty cart.
cart.unpayable=Could not determine any suitable payment method for your cart. Please reduce the total amount of the cart.
cart.preparationFailure=A technical error occurred while preparing the checkout for your cart, please try again later. Should the issue persist, please contact our customer support.
basket.error.entrygroup.remove=Unable to remove cart entry group #{0} due to an error.
basket.error.no.pickup.location=Please select stores for the entries highlighted in red
cart.addFailed=Error occurred while adding to Cart
basket.error.product.removed=Sorry, one or more products were removed from your cart as they are not in stock or are no longer available.
cart.invalidQuantity=The maximum order quantity of a individual app is {0} for Permanent licenses, and {1} for Subscription licenses.
basket.error.quantity.invalid.binding=Invalid quantity: please provide a valid quantity number to add this product to your cart
basket.error.quantity.notNull=Quantity field cannot be empty.
basket.error.invalid.configuration=You have invalid product configuration. {0} issue(s) must be resolved before checkout.
basket.error.invalid.configuration.edit=Resolve issues
basket.error.single.company=Only products of a single company can be added to the cart.
cart.noSelfPurchases=You canât purchase apps developed by your own company
cart.unsupportedLicense=Purchased license is not supported.
basket.error.validation.not.possible=Cart validation is currently not possible. Please try again later.
basket.information.merge.successful=The cart saved against your user account and your current cart have been merged. Click here to visit your cart.
basket.information.quantity.adjusted=Your items have been modified to match available stock levels
basket.information.quantity.noItemsAdded.maxOrderQuantityExceeded=Unfortunately the quantity you chose exceeded the maximum order quantity for this product. The quantity in your cart has been reduced to the maximum order quantity.
basket.information.quantity.noItemsAdded.noStock=Sorry, there is insufficient stock for your cart.
basket.information.quantity.noItemsAdded.success=Quantity of apps did not change.
basket.information.quantity.reducedNumberOfItemsAdded.lowStock=A lower quantity of this product has been added to your cart due to insufficient stock
basket.information.quantity.reducedNumberOfItemsAdded.maxOrderQuantityExceeded=Unfortunately the quantity you chose exceeded the maximum order quantity for this product. The quantity in your cart has been reduced to the maximum order quantity.
basket.items=Items
basket.page.cartHelpContent=Need Help? Contact us or call Customer Service at 1-###-###-####. If you are calling regarding your shopping cart, please reference the Shopping Cart ID above.
basket.page.cartHelpMessageMobile=Need help? <br />Call our team to help you directly <a href="tel:1-222-333-444">1-222-333-444</a><br />Your cart ID for assistance is {0}
basket.page.cartId=Cart ID:
basket.page.cartIdShort=ID:
basket.page.entry.action.REMOVE=Remove
basket.page.entry.action.EDIT=Edit
basket.page.entry.unknownAction=Unknown cart entry action
basket.page.free=FREE
basket.page.itemNumber=Item no.
basket.page.itemPrice=Item price
basket.page.message.remove=Product has been removed from your cart.
basket.page.message.update=Product quantity has been updated.
basket.page.message.update.pickupinstoreitem=Pickup in store item was updated.
basket.page.message.update.pickupinstoreitem.toship=Pickup in store item was updated to shipping.
basket.page.message.update.reducedNumberOfItemsAdded.lowStock=The cart quantity of <a href="{3}" style="text-decoration: underline">{0}</a> has been reduced to {1} from {2} due to insufficient stock.
basket.page.message.update.reducedNumberOfItemsAdded.noStock=<a href="{1}" style="text-decoration: underline">{0}</a> has been removed from the cart due to insufficient stock.
basket.page.needHelp=Need help?
basket.page.number=No.
basket.page.item=App
basket.page.price=Price
basket.page.availability=Availability
basket.page.product=Product
basket.page.productdetails=Product Details
basket.page.qty=Qty
basket.page.quantity=Quantity
basket.page.delivery=Delivery
basket.page.remove=Remove
basket.page.comment.menu=Comment
basket.page.shipping=Shipping
basket.page.id=Item ID
basket.page.shipping.change.store=Change Store
basket.page.shipping.find.store=Find a Store
basket.page.shipping.pickup=Pickup
basket.page.shipping.ship=Ship
basket.page.title=Item
basket.page.title.pickupFrom=Pick Up from:
basket.page.title.yourDeliveryItems=Your Delivery Items
basket.page.title.yourDeliveryItems.pickup=Order items to be picked up will appear later in the checkout.
basket.page.title.yourItems=Your Cart
basket.page.title.yourPickUpItems=Your Pick Up Items
basket.page.total=Total
basket.page.totals.delivery=Delivery:
basket.page.totals.grossTax=* Your order includes taxes of {0}.
basket.page.totals.netTax=Tax:
basket.page.totals.noNetTax=*No taxes are included in the total
basket.page.totals.savings=Savings
basket.page.quote.discounts.link=Discount
basket.page.quote.discounts.link.percent.off={0}% off
basket.page.quote.discounts.link.absolute.off={0} off
basket.page.quote.discounts.link.target.as=Target as {0}
basket.page.quote.discounts=Quote Discount:
basket.page.totals.discounts=Order Discounts:
basket.page.totals.quote.total.original=Original Subtotal:
basket.page.totals.quote.total.after.discount=New Subtotal:
basket.page.totals.subtotal=Subtotal:
basket.page.totals.total=Total
basket.page.totals.total.items={0} items
basket.page.totals.total.items.one={0} item
basket.page.totals.estimatetaxesbutton=Estimate tax
basket.page.totals.estimatedtotaltax=Estimated tax:
basket.page.totals.estimatedtotal=Estimated total:
basket.page.totals.estimatedZip=Zip code:
basket.page.totals.deliverycountry=Delivery destination:
basket.page.totals.error.wrongzipcode=Please enter a valid zip code
basket.page.totals.error.wrongcountry=Please select a country
basket.page.update=Update
basket.page.validation.message=Product(s) have been updated/removed in your cart.
basket.page.change.configuration=Change configuration
basket.pickup.product.variant={0}: {1}
basket.potential.promotions=Potential Promotions
basket.removed.from.basket.error=The app was removed from your shopping cart
basket.received.promotions=Received Promotions
basket.restoration.restorationError=Your cart could not be restored.
basket.restoration=Welcome back {0}. Some items from your previous shopping cart have been restored to your current shopping cart.
basket.restoration.delivery.changed={0} was restored to your cart, but the delivery method has changed due to low local stock.
basket.restoration.errorMsg=Your cart could not be restored.
basket.restoration.lowStock=Unfortunately a lower quantity of {0} was restored to your cart due to low stock. {3} were added to your cart, you now have {2}.
basket.restoration.noStock=Unfortunately <a href="{1}" {4}>{0}</a> could not be restored to your cart as it is out of stock. You previously had {2} in your cart.
basket.restoration.success={0}
basket.restoration.view.cart=<a href="{0}">  Click to view cart</a>.
basket.restoration.unavailable=Unfortunately {0} is no longer available. You previously had {2} in your cart.
basket.restoration.items.msg=The following items are:
basket.restoration.view.cart.btn=View Cart
basket.validation.configurationError={0} has configuration error(s) that need to be fixed before proceeding to checkout.
basket.validation.entryGroupError=One of entry groups has error(s) that need to be fixed before proceeding to checkout.
basket.validation.invalidGroup=This group is improperly configured. Please edit it.
basket.validation.lowStock=Unfortunately a lower quantity of <a href="{1}" {4}>{0}</a> was applied to your cart due to low stock. You previously had {2} in your cart, you now have {3}.
basket.validation.movedFromPOSToStore=Your items have been modified to match available stock levels. One or more pickup in store items were updated to shipping.
basket.validation.couponNotValid=The coupon applied on the cart is no longer valid and has been removed from the cart.
basket.validation.voucherNotValid=The coupon applied on the cart is no longer valid and has been removed from the cart.
basket.validation.noStock=Unfortunately <a href="{1}">{0}</a> was removed from your cart as it is out of stock. You previously had {2} in your cart.
basket.validation.unavailable=Unfortunately {0} was removed from your cart as it is no longer available.
basket.view.basket=View cart
basket.your.shopping.basket=Your Shopping Cart
basket.save.cart.info.msg=Your cart will be moved to Saved Cart list.
basket.save.cart=New Cart
basket.save.cart.name=Name
basket.save.cart.description=Description
basket.save.cart.max.chars=Characters Left
basket.save.cart.action.save=Save
basket.save.cart.action.cancel=Cancel
basket.save.cart.validation.name.notBlank=Cart name cannot be blank
basket.save.cart.validation.name.size=Cart name cannot be more than 255 characters
basket.save.cart.validation.name.charset=Cart name can only be alpha numeric
basket.save.cart.validation.description.size=Cart description cannot be more than 255 characters
basket.save.cart.on.success=Cart {0} was successfully saved
basket.save.cart.on.error=There was an exception while saving Cart {0}
basket.quick.order.error={0} Product(s) were not added to the cart, Review errors on quick orders page
text.save.cart.title=Save Cart
basket.export.csv.file=Export CSV
basket.export.cart.error=There was an exception while exporting Cart
basket.export.cart.item.sku=Sku
basket.export.cart.item.name=Name
basket.export.cart.item.price=Price
basket.export.cart.item.quantity=Quantity
basket.configure.product=Configure
basket.select.product=Select

breadcrumb.cart=Cart
breadcrumb.home=Home
breadcrumb.login=Login
breadcrumb.not.found=Page Not Found

cart.page.shop=Shop
cart.product.configuration.toolong=Value is too long
cart.checkout.error=Error proceeding to checkout.
cart.groups.edit=edit
cart.groups.remove=remove
cart.page.continue=Continue shopping
cart.page.back.to.productpage=Back to product page
cart.page.genericerror=Try again
cart.page.bycompany=by
cart.page.version=Version
cart.internal.error=Internal error - please try again later

checkout.skip.checkout=Place Order

checkout.checkout=Checkout
checkout.checkout.flow.select=Select an alternative checkout flow
checkout.checkout.multi=Checkout Multi
checkout.checkout.multi.pci=Checkout Multi with PCI
checkout.checkout.multi.pci-hop=PCI-HOP
checkout.checkout.multi.pci-sop=PCI-SOP
checkout.checkout.multi.pci-ws=PCI-Default
checkout.checkout.multi.pci.select=Select a PCI option
checkout.checkout.single=Checkout Single
checkout.deliveryAddress.notSelected=Please provide a delivery address for your order
checkout.error.authorization.failed=Your transaction did not succeed. Please try again later or contact customer support for further assistance.
checkout.cartModified=Your cart was modified. Please verify the changes and proceed to checkout one more time.
checkout.error.authorization.declinedByBank=The transaction has been declined by your bank.
payment.error.checkData=Please check the entered information.
payment.authentication.failure=Error when authenticating your payment method. Please enter your payment method details again.
checkout.invalidCartState=We are sorry but your transaction could not be completed at this time; please try again later or contact customer service at ************
checkout.error.payment.not.accepted=Your payment was declined. Please check your payment details are correct.
checkout.error.paymentethod.formentry.invalid=Please check your payment details are correct or provide a different payment method.
checkout.error.paymentethod.formentry.sop.invalid.billTo_city=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_country=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_email=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_firstName=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_lastName=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_phoneNumber=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_postalCode=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_state=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_street1=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.billTo_street2=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.card_accountNumber=The card number is not valid
checkout.error.paymentethod.formentry.sop.invalid.card_cardType=The card type is not supported
checkout.error.paymentethod.formentry.sop.invalid.card_cvNumber=The security code is not valid
checkout.error.paymentethod.formentry.sop.invalid.card_expirationMonth=The expiry month is not valid
checkout.error.paymentethod.formentry.sop.invalid.card_expirationYear=The expiry year is not valid
checkout.error.paymentethod.formentry.sop.invalid.card_issueNumber=The issue number is not valid
checkout.error.paymentethod.formentry.sop.invalid.card_startMonth=The start month is not valid
checkout.error.paymentethod.formentry.sop.invalid.card_startYear=The start year is not valid
checkout.error.paymentethod.formentry.sop.invalid.shipTo_city=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_country=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_firstName=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_lastName=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_phoneNumber=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_postalCode=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_shippingMethod=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_state=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_street1=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.invalid.shipTo_street2=This value is invalid for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_city=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_country=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_email=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_firstName=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_lastName=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_phoneNumber=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_postalCode=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_titleCode=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_state=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_street1=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.billTo_street2=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.card_accountNumber=Please enter a card number
checkout.error.paymentethod.formentry.sop.missing.card_cardType=Please select a cart type
checkout.error.paymentethod.formentry.sop.missing.card_cvNumber=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.card_expirationMonth=Please enter an expiry month
checkout.error.paymentethod.formentry.sop.missing.card_expirationYear=Please enter an expiry year
checkout.error.paymentethod.formentry.sop.missing.card_issueNumber=Please enter an issue number
checkout.error.paymentethod.formentry.sop.missing.card_startMonth=Please enter a start month
checkout.error.paymentethod.formentry.sop.missing.card_startYear=Please enter a start year
checkout.error.paymentethod.formentry.sop.missing.shipTo_city=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_country=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_firstName=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_lastName=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_phoneNumber=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_postalCode=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_shippingMethod=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_state=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_street1=Please enter a value for this field
checkout.error.paymentethod.formentry.sop.missing.shipTo_street2=Please enter a value for this field
checkout.error.tax.missing=We are sorry but your transaction could not be completed at this time; please try again later or contact customer service at ************
checkout.error.terms.not.accepted=Please accept our terms & conditions before submitting your order.
checkout.express.error.notAvailable=Express checkout is not available since it is disabled.
checkout.express.error.deliveryAddress=Express checkout is not available since you do not have a default delivery address. Please set a default delivery address in your account to use Express Checkout next time.
checkout.express.error.deliveryMode=Express checkout is not available. An error occurred setting the delivery mode.
checkout.express.error.paymentInfo=Express checkout is not available since you do not have a default payment info. Please set a default payment info in your account to use Express Checkout next time.
checkout.information.delivery.method.changed=Your delivery method has been updated based on your delivery location.
checkout.login.guestCheckout=Check Out as a Guest
checkout.login.loginAndCheckout=Log In and Check Out
checkout.login.registerAndCheckout=Register and Checkout
checkout.multi.addEditform=Please use this form to add/edit an address.
checkout.multi.address.added=Your address was created.
checkout.multi.address.updated=Your address was updated
checkout.multi.addressDetails=Address Details
checkout.multi.breadcrumb=Checkout
checkout.multi.cancel=Cancel
checkout.multi.confirmOrder=Final Review
checkout.multi.billingAddress=Billing Address
checkout.multi.billingAddress.continue=Next
checkout.multi.billingAddress.useThisAddress=Use this Address
checkout.multi.billingAddress.viewAddressBook=Address Book
checkout.multi.billingAddress.notprovided=You must provide a billing address in order to go to the next step.
checkout.multi.billingAddress.saveAddressInMyAddressBook=Save Billing Address
checkout.multi.items.to.pickup=Pick Up:
checkout.multi.next=Next &raquo;
checkout.multi.order.summary=Order Summary
checkout.multi.payment=Payment:
checkout.multi.paymentDetails.notprovided=You must provide a payment details in order to go to the next step.
checkout.multi.paymentMethod=Payment Method
checkout.multi.paymentMethod.notNeeded=You have only free apps in your cart. No payment is needed.
checkout.multi.paymentMethod.addPaymentDetails.billingAddress=Billing Address
checkout.multi.paymentMethod.addPaymentDetails.billingAddressDiffersFromDeliveryAddress=If your billing address is  different  to your delivery address, please use this form to enter your billing address
checkout.multi.paymentMethod.addPaymentDetails.enterDifferentBillingAddress=Enter a different billing address
checkout.multi.paymentMethod.addPaymentDetails.enterYourCardDetails=Please enter your card details for payment
checkout.multi.paymentMethod.addPaymentDetails.generalError=An error occurred contacting the payment provider.  Wait a few minutes and try again.  If the problem persists, please contact our sales team.
checkout.multi.paymentMethod.addPaymentDetails.header=Payment Details
checkout.multi.paymentMethod.addPaymentDetails.paymentCard=Card Details
checkout.multi.paymentMethod.addPaymentDetails.savePaymentDetailsInAccount=Save these payment details in my account
checkout.multi.paymentMethod.addPaymentDetails.useSavedCard=Use a saved card
checkout.multi.paymentMethod.addPaymentDetails.useSavedCard.description=Registered customers may select a previously saved card
checkout.multi.paymentMethod.addPaymentDetails.useThesePaymentDetails=Use these payment details
checkout.multi.paymentMethod.addPaymentDetails.validationFails=Please add correct payment details
checkout.multi.paymentMethod.continue=Next
checkout.multi.paymentMethod.createSubscription.billingAddress.noneSelectedMsg=Please enter a billing address or enter a delivery address first so that it can be used as billing address.
checkout.multi.paymentMethod.createSubscription.failedMsg=Failed to create subscription. Please check the values entered.
checkout.multi.paymentMethod.edit=Edit
checkout.multi.paymentMethod.paymentDetails.expires=Expires {0} / {1}
checkout.multi.paymentMethod.paymentDetails.noneSelected=None selected
checkout.multi.paymentMethod.savedCards.actions=Actions
checkout.multi.paymentMethod.savedCards.billingAddress=Billing Address
checkout.multi.paymentMethod.savedCards.enterNewPaymentDetails=Enter New Payment Details
checkout.multi.paymentMethod.savedCards.noExistingSavedCards=You don't have any saved cards
checkout.multi.paymentMethod.savedCards.paymentCard=Payment Card
checkout.multi.paymentMethod.savedCards.select=Select
checkout.multi.paymentMethod.savedCards.selectSavedCardOrEnterNew=Select existing payment details or enter new payment details
checkout.multi.paymentMethod.savedCards.stepHeader=3 - Select Payment Details
checkout.multi.paymentMethod.viewSavedPayments=View Saved Payments
checkout.multi.paymentMethod.seeOrderSummaryForMoreInformation=See the Order Summary area for more information.
checkout.multi.pickup.items=Pick Up #{0} - {1} Item(s)
checkout.multi.pickupInStore=Pick Up in Store
checkout.multi.inStore=In Store
checkout.multi.pickupInStore.confirm.and.continue=Simply confirm the information below and continue to the next step of checkout.
checkout.multi.saveAddress=Save Address
checkout.multi.secure.checkout=Secure Checkout
checkout.multi.shipment.items=Cart - {0} Item(s)
checkout.multi.shipment.pickup.location=Shipment/Pick Up Location
checkout.multi.sop.globalError=An error occurred processing your request. Possible action: Wait a few minutes and resend the order.
checkout.multi.sop.remove=Remove
checkout.multi.sop.savePaymentInfo=Save Payment Info
checkout.multi.sop.useMyDeliveryAddress=Use my Delivery Address
checkout.multi.sop.useThisPaymentInfo=Use this payment info
checkout.multi.summary.breadcrumb=Final Review

#Order confirmation
checkout.orderConfirmation.get.coupon=You've earned the following coupon!
checkout.orderConfirmation.coupon.code=Use coupon code: {0}
checkout.orderConfirmation.coupon.valid.from=Valid from {0}
checkout.orderConfirmation.coupon.valid.until=Valid until {0}
checkout.orderConfirmation.coupon.valid.range=Valid from {0} until {1}

checkout.orderDetails.hide=[-] Hide Order Details
checkout.orderDetails.show=[+] View Order Details
checkout.paymentMethod.createSubscription.billingAddress.noneSelected=Please enter a billing address or enter a delivery address first so that it can be used as billing address.
checkout.paymentMethod.createSubscription.failed=Failed to create subscription. Please check the values entered.
checkout.paymentMethod.noSecurityCode=Please provide security code.
checkout.paymentMethod.notSelected=Please provide details of your payment for your order
checkout.pickup.confirm.and.continue=Simply confirm the information below and continue to the next step of checkout.
checkout.pickup.continue.button=Continue
checkout.pickup.estimated.total=Estimated Total:
checkout.pickup.items.appear.later=Order items to be picked up will appear later in the checkout
checkout.pickup.items.at.one.location=All items in your order selected for pickup are currently available at these store locations:
checkout.pickup.items.available.at.one.location=YOUR PICKUP ITEMS ARE AVAILABLE AT ONE LOCATION (Does not affect delivery orders)
checkout.pickup.items.simplify.pickup.location=I would like to change my order to pickup all my items in the same store
checkout.pickup.items.to.pickup={0} item(s) to pickup
checkout.pickup.no.delivery.required=No delivery required for this order
checkout.pickup.pickup.in.store=Pickup in Store - {0}, {1}
checkout.pickup.pickup.in.store.title=PICKUP IN STORE
checkout.pickup.simplifyPickup=Simplify Pickup Location
checkout.pickup.store.destinations={0} store pickup destination(s)

checkout.items.in.cart=Items in cart
checkout.billing.address=Billing Address:

checkout.placeOrderFailed=Failed to place the order
checkout.paymentFailed=There was an error with your payment. Please try again or use a different payment method.
checkout.security.code=Security Code
checkout.summary.edit=Edit
checkout.summary.deliveryAddress=Delivery Address
checkout.summary.deliveryAddress.edit=Edit
checkout.summary.deliveryAddress.editDeliveryAddressButton=Edit Delivery Address
checkout.summary.deliveryAddress.enterDeliveryAddressButton=Edit Delivery Address
checkout.summary.deliveryAddress.header=Delivery Address
checkout.summary.deliveryAddress.noExistingAddresses=You don't have any addresses in your address book.
checkout.summary.deliveryAddress.noneSelected=None selected
checkout.summary.deliveryAddress.saveAndUseThisAddress=Save and use this address
checkout.summary.deliveryAddress.selectExistingAddress=Select Existing Address
checkout.summary.deliveryAddress.useForNewAddress=Please use this form to enter a new address
checkout.summary.deliveryAddress.useThisAddress=Use this address
checkout.summary.deliveryMode.editDeliveryMethod=Edit Delivery Method
checkout.summary.deliveryMode.header=Delivery Options
checkout.summary.deliveryMode.items.for.pickup={0} Items for Pickup in Store
checkout.summary.deliveryMode.noneSelected=None selected
checkout.summary.deliveryMode.number.of.pickup.destinations={0} Store Pickup Destination(s)
checkout.summary.deliveryMode.selectDeliveryMethod=Select Delivery Method
checkout.summary.deliveryMode.selectDeliveryMethodForOrder=Option
checkout.summary.deliveryMode.useThisDeliveryMethod=Use this delivery method
checkout.summary.paymentMethod.addPaymentDetails.billingAddress=Billing Address
checkout.summary.paymentMethod.addPaymentDetails.billingAddressDiffersFromDeliveryAddress=If your billing address is  different  to your delivery address, please use this form to enter your billing address
checkout.summary.paymentMethod.addPaymentDetails.enterDifferentBillingAddress=Enter a different billing address
checkout.summary.paymentMethod.addPaymentDetails.enterYourCardDetails=Please enter your card details for payment
checkout.summary.paymentMethod.addPaymentDetails.header=Payment Details
checkout.summary.paymentMethod.addPaymentDetails.paymentCard=Payment Card
checkout.summary.paymentMethod.addPaymentDetails.saveAndUseThesePaymentDetails=Save and use these payment details
checkout.summary.paymentMethod.addPaymentDetails.savePaymentDetailsInAccount=Save these payment details in my account
checkout.summary.paymentMethod.addPaymentDetails.useSavedCard=Use a saved card
checkout.summary.paymentMethod.addPaymentDetails.useSavedCard.description=Registered customers may select a previously saved card
checkout.summary.paymentMethod.addPaymentDetails.useThesePaymentDetails=Use these payment details
checkout.summary.paymentMethod.billingAddress.header=Billing Address:
checkout.summary.paymentMethod.editPaymentMethod=Edit Payment Method
checkout.summary.paymentMethod.header=Payment Details
checkout.summary.paymentMethod.paymentDetails.expires=Expires {0} / {1}
checkout.summary.paymentMethod.paymentDetails.noneSelected=None selected
checkout.summary.paymentMethod.savedCards.UseThisSavedCard=Use these payment details
checkout.summary.paymentMethod.savedCards.actions=Actions
checkout.summary.paymentMethod.savedCards.billingAddress=Billing Address
checkout.summary.paymentMethod.savedCards.enterNewPaymentDetails=Enter new payment details
checkout.summary.paymentMethod.savedCards.header=Select Payment Details
checkout.summary.paymentMethod.savedCards.noExistingSavedCards=You don't have any saved cards
checkout.summary.paymentMethod.savedCards.paymentCard=Payment Card
checkout.summary.paymentMethod.savedCards.selectSavedCardOrEnterNew=Select existing payment details or enter new payment details
checkout.summary.paymentMethod.securityCode=Security Code
checkout.summary.paymentMethod.securityCode.whatIsThis=(What is this?)
checkout.summary.paymentMethod.securityCode.whatIsThis.description=The last 3 digits on the signature strip on the back of the card. For American Express, it's the 4 digits just above the hologram on the front of the card.
checkout.summary.placeOrder=Place Order
checkout.summary.placeOrder.readTermsAndConditions=By placing the order, I am confirming that I have read and agree with the <a href="{0}" target="_blank" rel="noopener noreferrer">Terms & Conditions</a>
checkout.summary.placeOrder.readTermsAndConditions.close=Close
checkout.summary.placeOrder.wrongDateFormatMessage=Date format must be {0}.
checkout.summary.reviewYourOrder=Final Review
checkout.summary.reviewYourOrderMessage=Please review your order carefully!
checkout.summary.select.payment.method=Select Payment Method
checkout.summary.billingAddress=Address

consent.form.global.error=Something went wrong while saving your consent. Please manage your consent in the Consent Management page.

coupon.invalid.code.provided=The coupon code entered is not valid.
coupon.already.redeemed=The coupon code entered has been redeemed already.
coupon.order.recalculation.error=Error occurred while re-calculatinng the order.
coupon.not.active.expired=The coupon code entered is no longer valid.
coupon.already.exists.cart=A coupon of the same campaign has already been applied to the cart.
coupon.general.error=Coupon Redemption Failed.

file.size.bytes={0} Bytes
file.size.kilobytes={0} KB
file.size.megabytes={0} MB
file.size.gigabytes={0} GB

form.field.required=Required
form.global.error=Please correct the errors below.
form.select.empty=Please select

general.continue.shopping=Continue Shopping
general.back.home = Back home
general.find.a.store=Find a Store
general.mobile.store=Mobile Store
general.month.april=April
general.month.august=August
general.month.december=December
general.month.february=February
general.month.january=January
general.month.july=July
general.month.june=June
general.month.march=March
general.month.may=May
general.month.november=November
general.month.october=October
general.month.september=September
general.required=Required
general.unknown.identifier=Unknown identifier was passed
general.zoom=Enlarged view of picture
general.continue.button=Continue
general.delete.button=Delete

guest.checkPwd=Confirm Password
guest.checkout=New to {0}?
guest.checkout.existingaccount.register.error=An account with email id {0} already exists.
guest.confirm.email=Confirm Email Address
guest.description=Prefer to shop without creating an account? <br />It's quick and simple!
guest.email=Email Address
guest.pwd=Password
guest.register=Create an Account
guest.register.description=Benefit from a faster checkout for your next purchase and access your order history
order.confirmation.guest.register.description=For a Fast Checkout and Easy Access to Previous Orders
guest.required.message=Fields marked * are required
guest.register.submit=Register

header.hello=Hello
header.link.account=My Account
header.link.login=Sign in / Register
header.link.logout=Sign Out
header.mobile.link.login=Login
header.welcome=Welcome {0} ({2})

import.csv.savedCart.chooseFile=Choose file
import.csv.savedCart.fileConstraint=Maximum file size:
import.csv.savedCart.fileContent=SKU, Quantity
import.csv.savedCart.fileContentNote=The text file should list the product SKUs and quantities in the following format:
import.csv.savedCart.fileCSVRequired=Imported file must be a text file with CSV extension.
import.csv.savedCart.fileRequired=The file cannot be empty.
import.csv.savedCart.fileMaxSizeExceeded=The file is too large.
import.csv.savedCart.filesNote=The imported file will be used to create a saved cart.
import.csv.savedCart.genericError=An error occurred. Please try again later.
import.csv.savedCart.import=Import
import.csv.savedCart.noFile=No file chosen.
import.csv.savedCart.selectFile=Select a file to upload. The file must be a text file with extension CSV.
import.csv.savedCart.success=Your import is now being processed. Check <a href="{0}" style="text-decoration: underline">saved carts</a> page to see its progress.
import.csv.savedCart.title=Import Saved Cart
import.csv.savedCart.uploadStarted=Uploading file...

j_password=Password

j_username=Login

login.description=Already have an account? Sign in to retrieve your account settings.
login.email=User Account
login.error.account.not.found.subtitle=If you forgot your password, please use the Forgotten Password link.
login.error.account.not.found.title=Your username or password was incorrect.
login.error.incorrect.password.subtitle=If you forgot your password, please use the Forgotten Password link.
login.error.incorrect.password.title=Your username or password was incorrect.
login.login=Log In
login.password=Password
login.required=Required
login.optional=(optional)
login.required.message=Fields marked * are required
login.title=Login

menu.button.home=Home

mobile.basket.page.update=Update quantity
mobile.checkout.cart.viewFullCart=View Full Cart
mobile.checkout.cart.viewLess=View less
mobile.checkout.confirmOrder=Final Review
mobile.checkout.continue.button=Continue
mobile.checkout.continue.shopping=Continue Shopping
mobile.checkout.deliveryAddress=Delivery Address
mobile.checkout.deliveryAddress.selectAddressMessage=Choose a delivery address from your address book, or add a new one
mobile.checkout.deliveryAddress.use=Use
mobile.checkout.deliveryMethod=Delivery Options
mobile.checkout.edit.link=Edit
mobile.checkout.items.hide=Hide Items
mobile.checkout.items.show=Show more Items
mobile.checkout.multi.button.submit=Submit
mobile.checkout.paymentMethod=Payment Details
mobile.checkout.paymentMethod.add.card=Add New Card
mobile.checkout.paymentMethod.addOrSelect.card=Select a saved payment card from your wallet or add a new card
mobile.payment.issueNumber=Issue number (Maestro / Solo / Switch only)
mobile.multi.checkout.selectExistingCard=Choose from existing Payment Details
mobile.search.add.refinements=Refine
mobile.search.nav.clearSelections=Clear all selections
mobile.search.nav.facetValueCount={0}
mobile.storelocator.title=Store Locator

order.free=FREE
order.itemPrice=Item Price
order.order.totals=Order Totals
order.orderItems=Order Items
order.product=Product
order.product.seeDetails=See details
order.productDetails=Product Details
order.quantity=Quantity
order.total=Total
order.totals.delivery=Delivery:
order.totals.savings=Savings:
order.totals.subtotal=Subtotal:
order.totals.total=Total:

password.strength.medium=Medium
password.strength.minchartext=Minimum length is %d characters
password.strength.strong=Strong
password.strength.tooshortpwd=Too short
password.strength.verystrong=Very strong
password.strength.veryweak=Very weak
password.strength.weak=Weak

payment.cardNumber=Card number
payment.cardNumber.invalid=Please enter a valid card number
payment.cardType=Card type
payment.cardType.invalid=Please select a card type
payment.cardType.pleaseSelect=Please select a card type
payment.cvn=Card Verification Number
payment.expiryDate=Expiry date*
payment.expiryMonth.invalid=Please select expiry month of the card
payment.expiryYear.invalid=Please select expiry year of the card
payment.issueNumber=Issue number
payment.issueNumber.invalid=Only numbers are allowed for that field
payment.issueNumber.toolong=The issue number is too long.
payment.month=Month
payment.nameOnCard=Name on card
payment.nameOnCard.invalid=Please enter name on the card
payment.startDate=Start date (Maestro / Solo / Switch only)
payment.startDate.invalid=Start date must precede the expiry date
payment.year=Year

paymentMethod.billingAddress.header=Billing Address:
paymentMethod.header=Payment Details

pickup.back.to.product.page=&lt; Back to Product Page
pickup.buy.online.message=Buy your products now and pick them up later in store.
pickup.force.in.stock=IN STOCK
pickup.here.button=Pick Up Here
pickup.in.stock={0} IN STOCK
pickup.in.store=Pick Up in Store
pickup.in.store.back.to.results=Back
pickup.location.required=Please provide your location to see products available in your area.
pickup.mobile.back.to.cart.page=Back to Shopping Bag
pickup.mobile.back.to.product.page=Back to Product Page
pickup.out.of.stock=OUT OF STOCK
pickup.pagination.first=First
pickup.pagination.last=Last
pickup.pagination.next=Next
pickup.pagination.from=from&nbsp
pickup.pagination.stores=&nbspstores found
pickup.pagination.page.details={0} to {1} of {2}
pickup.pagination.previous=Previous
pickup.product.availability=Product Availability by Store Location
pickup.product.by.store.location=Product by store location
pickup.results.button=Pick Up
pickup.search.button=Find Stores
pickup.search.message=Enter a Town/City or Zip Code:

popup.cart.empty=Empty Cart
popup.cart.pickup=Pick Up
popup.cart.quantity=Quantity
popup.cart.quantity.added=Quantity Added
popup.cart.showing=Showing {0} of {1} Items
popup.cart.title=Your Shopping Cart
popup.cart.total=Total
popup.close=Close
popup.done=Done
popup.quick.view.select=Select Options
popup.cart.showall=Show All

product.average.review.rating=Products Average Rating {0} / 5
product.bookmark.and.share=Bookmark and Share
product.bundles=Buy this item in a bundle
product.close=Close
product.image.zoom.in=Click To Zoom
product.image.zoom.out=Zoom Out
product.overview=Details
product.price.from=From {0}
product.product.details=Product details
product.product.details.future.date=Estimated delivery date
product.product.details.future.nostock=This product has no future availability information.
product.product.details.future.quantity=Quantity
product.product.details.future.qty=Qty
product.product.details.future.title=Future availability
product.product.details.more=More product details
product.product.spec=Specs
product.related.products=Related Products
product.share.email=E-mail
product.share.print=Print
product.share.sendToFacebook=Send to Facebook
product.share.share=Share
product.share.tweet=Tweet this
product.share.viewMoreServices=View more services
product.start.bundle=Start Bundle
product.variants.available=available online
product.variants.colour=Colour
product.variants.in.stock=In Stock
product.variants.only.left=only {0} left online
product.variants.out.of.stock=Out of Stock
product.variants.select.size=Select a size
product.variants.select.style=Please select style first
product.variants.select.variant=Please select variant
product.variants.size=Size
product.variants.size.guide=Size guide
product.variants.type=Type
product.variants.update=update
product.volumePrices.column.price=Price each
product.volumePrices.column.qa=Quantity
product.notFound=The product could not be found.

review.alias=Your Name
review.alias.invalid=Please enter your name.
review.back=Back To Reviews
review.based.on={0} Reviews
review.based.on.one=Based on {0} review
review.comment=Review Description
review.comment.maxlength=The comment is too long.
review.comment.missing=Review comment needs to be specified.
review.confirmation.thank.you.subtitle=We try to put all reviews on the site within 24 hours.
review.confirmation.thank.you.title=Thank you for your review.
review.general.error=Please fill all mandatory review fields
review.headline=Review Title
review.headline.maxlength=The title is too long.
review.headline.missing=Review title needs to be specified.
review.no.reviews=Be the first to write a review.
review.number.of=of
review.number.reviews=Reviews
review.rating=Your Rating
review.rating.alt=stars
review.required=Fields marked* are required
review.reviews=Reviews
review.see.reviews=Show Reviews
review.show.all=Show All
review.show.less=Show Less
review.show.more=Show More
review.submit=Submit Review
review.submitted.anonymous=Anonymous
review.submitted.unknown=no name
review.submitted.by=Submitted by
review.write.review=Write Review
review.write.title=Write a Review
review.write.title.product=Write a Review for {0}
review.showCompany=Show company name in rating.
review.company.doesNotMatchSessionUser=The company of the review does not match the logged-in user.
review.exists=An employee of your company has already reviewed this app.
review.appNotOwned=You have to purchase the app in order to review it.
review.create=Create a review
review.not.reviewed=This app has not been reviewed yet.

sortfield.not.supported=Provided sort field is not supported

search.back.to.product.list=Back to product list
search.meta.description.on=on
search.meta.description.results=Search results for
search.meta.title=Search
search.mobile.no.results=No Results Found
search.mobile.page.currentPage={0} - {1} of {2} products
search.mobile.page.linkNextPage=Next &raquo;
search.mobile.page.linkPreviousPage=&laquo; Previous
search.mobile.page.searchText=You searched for "{0}"
search.mobile.page.showAllResults=Show all
search.mobile.page.sortTitle=Sort by:
search.mobile.page.totalResults={0} item(s)
search.nav.appliedFilters=Remove
search.nav.categoryNav=Shop by Category
search.nav.changeLocation=Change Location
search.nav.done.button=Done
search.nav.applied.facets=Applied Facets
search.nav.facetShowLess=less...
search.nav.facetShowLess_availableInStores=less stores...
search.nav.facetShowLess_brand=less brands...
search.nav.facetShowLess_category=less categories...
search.nav.facetShowLess_collection=less collections...
search.nav.facetShowLess_colour=less colours...
search.nav.facetShowLess_price=less prices...
search.nav.facetShowLess_size=less sizes...
search.nav.facetShowLess_style=less styles...
search.nav.facetShowMore=more...
search.nav.facetShowMore_availableInStores=more stores...
search.nav.facetShowMore_brand=more brands...
search.nav.facetShowMore_category=more categories...
search.nav.facetShowMore_collection=more collections...
search.nav.facetShowMore_colour=more colours...
search.nav.facetShowMore_price=more prices...
search.nav.facetShowMore_size=more sizes...
search.nav.facetShowMore_stores=Show More Stores
search.nav.facetShowMore_style=more styles...
search.nav.facetTitle=Shop by {0}
search.nav.facetValueCount=({0})
search.nav.refine.button=Refine
search.nav.selectRefinements.title=Select Refinements
search.nav.refinements=Refinements
search.nav.removeAttribute=Remove Attribute
search.nav.resultsForStore=Results for: {0}
search.no.results=0 items found for keyword <strong>{0}</strong>
search.page.currentPage=Page {0} of {1}
search.page.firstPage=&laquo;
search.page.lastPage=&raquo;
search.page.linkNextPage=Next Page
search.page.linkPreviousPage=Previous Page
search.page.nearbyStores=You searched for nearby stores
search.page.searchText=You searched for "{0}"
search.page.showAllResults=Show all
search.page.showPageResults=Show paginated
search.page.sortTitle=Sort by:
search.page.totalResults={0} Apps found
search.placeholder=I'm looking for
search.spellingSuggestion.prompt=Did you mean:

orderHistory.page.totalResults=of {0} Orders
orderHistory.page.totalResultsDetails={0} of {1} Orders

storeDetails.map.link=Map
storeDetails.table.address=Address
storeDetails.table.distance=Distance
storeDetails.table.distanceFromCurrentLocation={0} from Current Location
storeDetails.table.distanceFromSource={0} from {1}
storeDetails.table.email=Email
storeDetails.table.features=Features
storeDetails.table.from=from
storeDetails.table.opening=Hours
storeDetails.table.opening.closed=Closed
storeDetails.table.opening.opened=Opened
storeDetails.table.openingSpecialDays=Special Opening Times
storeDetails.table.telephone=Telephone
storeDetails.title=Store Details

storeFinder.currentPosition=Current Position
storeFinder.find.a.store=Find a Store
storeFinder.findStoresNearMe=Find stores
storeFinder.line.text=or
storeFinder.link=Store Finder
storeFinder.meta.description.results=store Locations near to
storeFinder.meta.title=Stores near
storeFinder.navigateTo=Navigate To
storeFinder.nearby.stores=Nearby Stores
storeFinder.orSearchBy=Or search by:
storeFinder.pagination.from=from
storeFinder.pagination.next=Next
storeFinder.pagination.previous=Previous
storeFinder.pagination.stores=stores found
storeFinder.postcode.town=Postcode / Town
storeFinder.search=Search
storeFinder.see.more=See more...
storeFinder.store.locator=Store Locator
storeFinder.stores.nearby=Stores Nearby
storeFinder.stores.nearto=Stores near: {0}
storeFinder.table.address=Address
storeFinder.table.distance=Distance
storeFinder.table.opening=Opening Hours
storeFinder.table.store=Store
storeFinder.table.view.map=View map
storeFinder.table.view.store=View store
storeFinder.use.this.form=Use this form to search for a store
storeFinder.viewMap=View Map

storefinder.searchterm.invalid=Please enter search term or zip code

storelocator.error.no.results.subtitle=Check that you entered a valid postcode or place name.
storelocator.error.no.results.title=No store results were found for your search criteria.
storelocator.postcode.city.search=Postcode/City Search
storelocator.query=Postcode / Town
storelocator.search.results.go=Go
storelocator.search.totalResults=Showing {0} nearest stores

system.error.link.expired.subtitle=Complete the forgotten password form again.
system.error.link.expired.title=Sorry, this link has expired
system.error.page.not.found=404 Page Not Found
system.error.page.forbidden=403 Forbidden

text.account.account=Account
text.account.addressBook=Address Book
text.account.addressBook.addAddress=Add Address
text.account.addressBook.addEditAddress=Add/Edit Address
text.account.addressBook.addEditform=Please use this form to add/edit an address
text.account.addressBook.addressDetails=Address Details
text.account.addressBook.confirmationUpdated=Your address was updated
text.account.addressBook.manageDeliveryAddresses=Manage your delivery address
text.account.addressBook.manageYourAddresses=Manage your address book
text.account.addressBook.noSavedAddresses=No Saved Addresses Found
text.account.addressBook.saveAddress=Save address
text.account.addressBook.setDefaultDeliveryAddress=Set default delivery address
text.account.addressBook.updateAddress=Update Address
text.account.addressBook.yourDefaultAddress=This is your default address
text.account.addressBook.back.btn=Back
text.account.change.email.address=Change Email Address
text.account.update.email.address=Email Address
text.account.confirmation.password.updated=Your password has been changed
text.account.order.consignment.status.cancelled=Cancelled
text.account.order.consignment.status.pickedup=Picked Up
text.account.order.consignment.status.readyforpickup=In Store Pick-Up
text.account.order.consignment.status.shipped=Shipped
text.account.order.consignment.status.processing=In Process
text.account.order.consignment.store.title=Store
text.account.order.consignment.pickUpBy=Pick Up By
text.account.order.unconsignedEntry.status.pending=Pending
text.account.order.consignment.trackingID.notavailable=Not available.
text.account.order.delivery=Delivery:
text.account.order.shipping=Shipping:
text.account.order.appliedCoupons=Applied Coupons:
text.account.order.includesTax=Your order includes {0} tax
text.account.order.netTax=Tax:
text.account.order.discount=Order Discounts:
text.account.order.orderBreadcrumb=Order {0}
text.account.order.order=Order
text.account.order.orderNumber=Your Order Number is {0}
text.account.order.orderNumberShort=Order #: {0}
text.account.order.orderDetails.orderNumber=Order # {0}
text.account.order.orderDetails.billingInformtion=Billing Information
text.account.order.orderPlaced=Placed on {0}
text.account.order.orderStatus=The order is {0}
text.account.order.orderTotals=Order Total
text.account.order.pickup.location=Pick Up Location:
text.account.order.placedBy=Placed by: {0}
text.account.order.placedByText=Placed by: Customer Service Team Member
text.account.order.receivedPromotions=Received Promotions
text.account.order.status=Status: {0}
text.account.order.status.display.cancelled=Cancelled
text.account.order.status.display.cancelling=Cancel Pending
text.account.order.status.display.completed=Completed
text.account.order.status.display.created=Created
text.account.order.status.display.error=Pending
text.account.order.status.display.Error=Pending
text.account.order.status.display.open=Open
text.account.order.status.display.processing=In Process
text.account.order.subtotal=Subtotal:
text.account.order.savings=Order Savings:
text.account.order.summary=A summary of your order is below:
text.account.order.title.deliveryItems=Delivery Items
text.account.order.title.inProgressItems=In Progress Order Items
text.account.order.title.storePickUpItems=Store Pick Up Items
text.account.order.title.details=Order Details
text.account.order.total=Total
text.account.order.total.includesTax=Total includes {0} in taxes
text.account.order.tracking=Tracking Number
text.account.order.shipto=Ship To
text.account.order.qty=Qty
text.account.order.warning.storePickUpItems=Reminder - Please pick up your items(s) soon.
text.account.order.yourOrder=Your Order
text.account.order.totalSavings=You saved {0} on your order
text.account.order.back.btn=Back
text.account.orderHistory=Order History
text.account.orderHistory.back=Back
text.account.orderHistory.actions=Actions
text.account.orderHistory.datePlaced=Date Placed
text.account.orderHistory.mobile.page.currentPage={0} of {1}
text.account.orderHistory.mobile.page.currentResults={0} - {1} of {2} orders
text.account.orderHistory.mobile.page.linkNextPage=Next &raquo;
text.account.orderHistory.mobile.page.linkPreviousPage=&laquo; Previous
text.account.orderHistory.mobile.page.sort.byDate=Date
text.account.orderHistory.mobile.page.sort.byOrderNumber=Order Number
text.account.orderHistory.mobile.page.sortTitle=Sort by:
text.account.orderHistory.mobile.page.totalResults={0} Orders found
text.account.orderHistory.emptyOrderHistory=Order History Empty
text.account.orderHistory.noOrders=No Orders Found
text.account.orderHistory.orderNumber=Order Number
text.account.orderHistory.orderStatus=Order Status
text.account.orderHistory.invoice=Invoice
text.account.orderHistory.invoice.download=Download as PDF
text.account.orderHistory.page.currentPage=Page {0} of {1}
text.account.orderHistory.page.firstPage=&laquo;
text.account.orderHistory.page.lastPage=&raquo;
text.account.orderHistory.page.linkNextPage=Next Page
text.account.orderHistory.page.linkPreviousPage=Previous Page
text.account.orderHistory.page.showAllResults=Show all
text.account.orderHistory.page.showPageResults=Show paginated
text.account.orderHistory.page.sort.byDate=Date
text.account.orderHistory.page.sort.byOrderNumber=Order Number
text.account.orderHistory.page.sortTitle=Sort by
text.account.orderHistory.page.totalResults=of {0} Orders
text.account.orderHistory.page.totalResultsCurrPage={0} of {1} Orders
text.account.orderHistory.page.totalResultsSinglePag={0} Orders
text.account.orderHistory.page.totalResultsSingleOrder=1 Order
text.account.orderHistory.total=Total
text.account.orderHistory.viewOrders=View your order history
text.account.paymentDetails=Payment Details
text.account.paymentType=Payment Type
text.account.paymentDetails.billingAddress=Billing Address
text.account.paymentDetails.managePaymentDetails=Manage your payment details
text.account.paymentDetails.noPaymentInformation=No Saved Payment Details
text.account.paymentDetails.paymentCard=Payment Card
text.account.paymentDetails.paymentCard.default=My Default Payment Card
text.account.paymentDetails.setDefaultPaymentDetails=Set default payment details
text.account.paymentDetails.delete=Delete
text.account.paymentDetails.delete.following=The following payment method will be deleted
text.account.paymentDetails.delete.popup.title=Delete Payment
text.account.profile=Profile
text.account.profile.paymentCart.removed=Payment Card removed successfully
text.account.savedCart.id=ID
text.account.savedCart.dateSaved=Date Saved
text.account.savedCart.description=Description
text.account.savedCart.name=Name
text.account.savedCart.qty=QTY
text.account.savedCart.numberOfProducts=Number of Products
text.account.savedCart.total=Total
text.account.savedCart.restore=Restore
text.account.savedCart.savedCartBreadcrumb=Saved Cart {0}
text.account.savedCart.title.details=Saved Cart Details
text.account.savedCart.backToSavedCarts=Back to Saved Carts
text.account.savedCart.edit=Edit
text.account.savedCart.edit.title=Edit Saved Cart
text.account.savedCart.delete=Delete Cart
text.account.savedCart.message=Promotions and totals reflect what existed at the time the cart was saved. Promotions and totals may change or no longer apply when the cart is restored.
text.account.savedCarts=Saved Carts
text.account.savedCarts.page.currentPage=Page {0} of {1}
text.account.savedCarts.page.firstPage=&laquo;
text.account.savedCarts.page.lastPage=&raquo;
text.account.savedCarts.page.linkNextPage=Next Page
text.account.savedCarts.page.linkPreviousPage=Previous Page
text.account.savedCarts.page.showAllResults=Show all
text.account.savedCarts.page.showPageResults=Show paginated
text.account.savedCarts.page.sort.byCode=ID
text.account.savedCarts.page.sort.byDateSaved=Date Saved
text.account.savedCarts.page.sort.byDateModified=Date Modified
text.account.savedCarts.page.sort.byName=Name
text.account.savedCarts.page.sort.byTotal=Total
text.account.savedCarts.page.sortTitle=Sort by
text.account.savedCarts.page.totalResults=of {0} Saved Carts
text.account.savedCarts.page.totalResultsCurrPage={0} of {1} Saved Carts
text.account.savedCarts.page.totalResultsSinglePag={0} Saved Carts
text.account.savedCarts.page.totalResultsSingleOrder=1 Saved Cart
text.account.savedCarts.noSavedCarts=No Saved Carts Found
text.account.saveCart.edit.success=Cart {0} was successfully updated
text.account.saveCart.edit.error=There was an exception while updating Cart {0}
text.account.consent.consentManagement=Consent Management
text.account.consent.consentManagement.general.text=To personalize your experience, we'd like your consent to receive your profile data:
text.account.consent.given=Your consent has been given
text.account.consent.withdrawn=Your consent has been withdrawn
text.account.consent.notFound=Consent not found
text.account.consent.template.notFound=Consent template not found
text.account.close=Close Account
text.account.trackOrders=Track your orders
text.account.viewOrderHistory=View order history
text.account.orderDetails.backToOrderHistory=Back To order history
text.account.yourAccount=My Account
text.address.delete=Delete
text.address.delete.popup.title=Delete Address
text.address.remove.confirm=Are you sure you want to delete this address?
text.address.remove.following=The following address will be deleted from your Address Book
text.backToMobileStore=Mobile
text.button.cancel=Cancel
text.button.menu=Menu
text.button.new=New
text.button.save=Save
text.button.showall=Show All
text.button.use=Use
text.cart=Cart
text.connect=Connect
text.copyright=&copy; 2016 hybris software
text.guest.customer=Guest
text.header.connect=Connect
text.header.language=Click here to change the language
text.header.languageandcurrency=Language and Currency
text.header.loginandaccount=Login and Account
text.header.menu=Menu
text.header.storefinder=Store Finder
text.headertext=Confirm
text.headertext.conf=Confirm
text.headertext.error=Error(s)
text.headertext.info=Info
text.headline.addaddress=Click to add an address
text.headline.addtocart=Click to add the product to cart
text.headline.bottombanner=Bottom Banner
text.headline.breadcrumbs=Breadcrumbs
text.headline.categories=Click here the menu button to get to the categories
text.headline.findstore=Find your store
text.headline.footer.navigationbar=Browse through the footer navigation bar
text.headline.homebanner=Top Banner
text.headline.login=Click here to login
text.headline.myaccount=Click here to login or get to your Account
text.headline.navigationbar=Browse through the navigation bar
text.headline.orderinfo=All information about delivery address, delivery method and payment method
text.headline.orderitems=All information about your order items
text.headline.orders=View your orders
text.headline.productcategories=Product categories
text.headline.productinfo=Click to get more information about product overview, product review or delivery method
text.headline.profile=Click here to view your profile, address book, payment details or order history
text.headline.refinements=Choose the relevance or add refinements
text.headline.register=Click here to register a new customer
text.headline.search=Here you can search for products
text.headline.sortandrefine=Sort and refine
text.headline.terms=Agree with the terms and conditions
text.help=Help
text.javascript.disabled=JavaScript is required to access the page. Please enable JavaScript.
text.label.loadingmoreresults=Loading more results...
text.label.showmoreresults=Show more results
text.link.home.label=Home
text.loadingMessage=Loading...
text.logout=Logout
text.myaccount=&laquo; My Account
text.myaccount.orderHistory=&laquo; Order History
text.paymentcard.remove.confirm=Are you sure you want to delete this payment card?
text.productreviews=Reviews
text.storefinder.desktop.page.currentPage={0} of {1}
text.storefinder.desktop.page.firstPage=&laquo;
text.storefinder.desktop.page.lastPage=&raquo;
text.storefinder.desktop.page.linkNextPage=Next
text.storefinder.desktop.page.linkPreviousPage=Previous
text.storefinder.desktop.page.showAllResults=Show all
text.storefinder.desktop.page.showPageResults=Show paginated
text.storefinder.desktop.page.sort.byName=By Name
text.storefinder.desktop.page.sortTitle=Sort by:
text.storefinder.desktop.page.totalResults={0} Stores found
text.storefinder.mobile.page.currentResults={0} - {1} of {2} stores
text.storefinder.mobile.page.currentPage={0} of {1}
text.storefinder.mobile.page.description=Please provide your location to see stores in your area or let your device find nearby stores for you
text.storefinder.mobile.page.linkNextPage=Next &raquo;
text.storefinder.mobile.page.linkPreviousPage=&laquo; Previous
text.storefinder.mobile.page.noResults=No results for your area.
text.storefinder.mobile.page.totalResults={0} Stores found
text.storefinder.responsive.page.currentPage={0} of {1}
text.storefinder.responsive.page.firstPage=&laquo;
text.storefinder.responsive.page.lastPage=&raquo;
text.storefinder.responsive.page.linkNextPage=Next
text.storefinder.responsive.page.linkPreviousPage=Previous
text.storefinder.responsive.page.showAllResults=Show all
text.storefinder.responsive.page.showPageResults=Show paginated
text.storefinder.responsive.page.sort.byName=By Name
text.storefinder.responsive.page.sortTitle=Sort by:
text.storefinder.responsive.page.totalResults=1-10 from {0} Stores found
text.stores=Stores
text.swithToMobileStore=Switch back to mobile
text.viewfullsite=View Full Site
text.expresscheckout.header=Express Checkout
text.expresscheckout.title=Benefit from a faster checkout by:
text.expresscheckout.line1=setting a default Delivery Address in your account or when you checkout
text.expresscheckout.line2=setting a default Payment Details when you checkout
text.expresscheckout.line3=using a default shipping method
text.expresscheckout.info1=By succesfully configuring your settings this way, signed users will proceed directly to the Final Review step when proceeding to checkout
text.expresscheckout.info2=Users who have not yet signed in may choose Express Checkout in the shopping cart
text.expresscheckout.info3=The express checkout feature is not available to Guest Users
text.iconCartRemove=Remove

paymentMethod.paymentDetails.expires=Expires {0} / {1}
text.page.message.underconstruction=<strong>Information:</strong>  Page Under Construction - Not Completely Functional
text.component.message.underconstruction=<strong>Information:</strong>  Component Under Construction - Not Completely Functional

#Multi-D related messages
product.view.details=View Details
product.view.more=More >
order.form=Order Form
order.form.total=Order Form Total
order.form.subtotal=Subtotal
order.form.currency=$
basket.page.viewFuture=Future Availability
basket.page.viewFuture.not.multisku=The selected code {0} is not a multidimensional product.
basket.page.viewFuture.unavailable=Future stock system is unavailable, please try again later.

product.grid.yourPrice=Your Price
product.grid.availability=Availability
product.grid.futurestock.refresh=Update Future
product.grid.futurestock.updatefuture.availability=Show future availability
product.grid.futurestock.hidefuture.availability=Hide future availability
product.grid.formDescription=Select items below and add to cart
product.grid.expand=Expand
product.grid.outOfStock=Out of Stock
product.grid.subtotalText=Subtotal
product.grid.averagePriceUnitText=Average Price / Unit
product.grid.quantityText=Quantity
product.grid.itemsText=items
product.grid.items.selected=Quantity selected:
product.grid.viewFuture=Future
product.grid.confirmQtys.message=Navigating away from this page will result in losing the data you have entered!
product.grid.future.tooltip.error=Sorry, but it is not possible to retrieve the future availability data at this time. Please try later.
product.grid.future.tooltip.delivery=Delivery
product.grid.future.tooltip.qty=QTY
product.grid.in.stock=In Stock
product.grid.readonly.qty=Qty
product.grid.selectSize=Select
product.grid.editSize=Edit

text.volumePrices=Volume prices available

text.account.savedcart.to.activecart=The following saved cart will restore as active cart
text.account.savedcart.cart.name=Cart Name
text.account.savedcart.cart.id=ID
text.account.savedcart.numberofproducts=Number of products
text.account.savedcart.retain.savedcart=Keep a copy of this cart on saved list
text.account.savedcart.save.activecart=I do not want to save items for later
text.account.savedcart.activecart.msg=The current items in the cart will be saved as:
text.account.savedcart.delete.msg=The following cart will be deleted
text.account.savedcart.delete.popuptitle=Delete Saved Cart
text.account.savedcart.restore.popuptitle=Restore Saved Cart
text.account.savedcart.restore.btn=Restore
text.restore.savedcart.error=Error while restoring saved cart
text.delete.savedcart.error=Error while deleting saved cart

saved.cart.total.number=Saved Carts ({0})
saved.quote.total.number=Quotes ({0})

text.voucher.apply.invalid.error=This coupon is not valid
text.voucher.apply.applied.success=The {0} has been applied successfully
text.voucher.apply.input.label=Coupon code
text.voucher.apply.input.placeholder=enter coupon code
text.voucher.apply.button.label=Apply
text.voucher.release.error=Error while removing the voucher {0}

#quick order
breadcrumb.quickOrder=Quick Order

text.quick.order.reset.form=Reset Form
text.quickOrder.header=Quick Order
text.quickOrder.product.not.found=Product not found
text.quickOrder.product.not.unique=Product not unique
text.quickOrder.product.not.purchaseable=Product cannot be purchased. Please enter another SKU
text.quickOrder.product.code.invalid=Invalid product code
text.quickOrder.product.quantity.invalid=Invalid quantity
text.quickOrder.product.quantity.max=Quantity reduced due to insufficient stock
text.quickOrder.form.product.exists=Sku already exists in the form
text.quickOrder.page.product=Product
text.quickOrder.page.price=Price
text.quickOrder.page.qty=Qty
text.quickOrder.page.total=Total

#quote management
text.account.quote.myquotes=My Quotes
text.account.quote.name=Name
text.account.quote.code=Quote ID
text.account.quote.status=Status
text.account.quote.date.placed=Date Placed
text.account.quote.date.updated=Date Updated
text.account.quote.total=Total
text.account.quote.page.currentPage=Page {0} of {1}
text.account.quote.page.firstPage=&laquo;
text.account.quote.page.lastPage=&raquo;
text.account.quote.page.linkNextPage=Next Page
text.account.quote.page.linkPreviousPage=Previous Page
text.account.quote.page.showAllResults=Show all
text.account.quote.page.showPageResults=Show paginated
text.account.quote.page.sort.byCode=Quote ID
text.account.quote.page.sort.byName=Name
text.account.quote.page.sort.byDate=Updated Date
text.account.quote.page.sort.byState=Status
text.account.quote.page.sortTitle=Sort by
text.account.quote.page.totalResults=of {0} Quotes
text.account.quote.page.totalResultsCurrPage={0} of {1} Quotes
text.account.quote.page.totalResultsSinglePag={0} Quotes found
text.account.quote.page.totalResultsSingleOrder=1 Quote found
text.account.quote.manage.items=Manage Items
text.account.quote.duplicate=Duplicate
text.account.quote.cancel=Cancel
text.account.quote.remove=Remove
text.account.quote.status.display.BUYER_DRAFT=Draft
text.account.quote.status.display.BUYER_SUBMITTED=Submitted
text.account.quote.status.display.BUYER_OFFER=Vendor Quote
text.account.quote.status.display.BUYER_ORDERED=Ordered
text.account.quote.status.display.SELLER_DRAFT=Draft
text.account.quote.status.display.SELLER_REQUEST=Requested
quote.accept.error=Error accepting quote.
quote.cart.empty.error=Missing or empty cart
quote.create.success=Quote created with success.
quote.reject.initiate.request=Minimum {0} subtotal to submit a quote.
quote.create.error=Error creating quote.
quote.requote.error=Error requoting.
quote.newcart.error=An error occurred processing your request. Please try to submit your request again.
quote.newcart.success=Quote {0} was successfully saved
quote.save.error=Error saving quote.
quote.submit.error=Error submitting quote.
quote.submit.success=Quote submitted with success.
quote.approve.error=Error approving quote.
quote.approve.success=Quote approved with success.
quote.reject.error=Error rejecting quote.
quote.reject.success=Quote rejected with success.
quote.create=Request a Quote
quote.cart.checkout.error=The vendor quote was modified, Please Accept & Checkout again.
quote.cart.insufficient.access.rights.error=Insufficient access rights for cart {0}.

text.account.quotes.page.totalResultsCurrPage={0} of {1} Quotes

text.quote.code=Quote ID:
text.quote.description.not.applicable=N/A
text.quote.name.label=Name
quote.name.invalid=Please enter a valid name
text.quote.description.label=Description
quote.description.invalid=Description cannot be more than 255 characters
text.quote.comment.label=Negotiation Comment
quote.comment.invalid=Comment cannot be more than 255 characters
text.quote.expiration.time.label=Expiry Date
text.quote.expiration.time.invalid=Please enter a valid expiry date
text.quote.expiration.time.warning=Note: If the expiry date is set to today or earlier, it will be set to {0} days from today.
text.quote.state.label=Status
text.quote.name.description.invalid=Please enter a valid name and description

breadcrumb.quote.edit=Edit Quote
breadcrumb.quote.view=Quote {0}

quote.state.expired=This quote has expired. Create a new quote or edit the expired quote.
quote.cart.title=This cart is being quoted
quote.view=View Quote
quote.edit=Edit Quote
quote.submit=Submit Quote
quote.approve=Approve Quote
quote.reject=Reject Quote
quote.acceptandcheckout=Accept and Checkout
quote.cancel=Cancel Quote
quote.save=Save Quote
quote.edit.done=New Cart
quote.requote=Requote
quote.state.BUYER_DRAFT=DRAFT
quote.enter.comment=Add a comment and press enter
quote.more.comments=View more comments
quote.less.comments=View less comments
quote.cart.comments=Cart Comments
quote.not.editable=Quote ID : {0} is not editable
quote.edit.locked=Quote ID : {0} is currently being edited by {1}
quote.save.cart.error=There was an internal error. Please try again.

text.quote.illegal.state.error=Please note that {0} action you tried to perform is not allowed for quote {1} with {2} status

text.quote.validity.message=This quote is valid until {0}
text.quote.submit.not.modifiable.message=Once a request for quote is submitted it cannot be modified.
text.quote.submit.confirmation.message=Are you sure you want to submit this quote?
text.quote.submit.confirmation.modal.title=Confirm Requested Quote {0}?
text.quote.checkout.confirmation.modal.title=Confirm Accept and Checkout Quote {0}?
text.quote.conteroffer.confirmation.message=Are you sure you want to offer this vendor quote?
text.quote.conteroffer.possible.checkout.message=This is a binding vendor quote. The buyer may check out immediately.
text.quote.conteroffer.confirmation.modal.title=Confirm Vendor Quote {0}?
text.quote.checkout.warning.message=Modifying the cart may result in voiding the vendor quote.
text.quote.cancel.confirmation.message=Are you sure you want to cancel this quote?
text.quote.cancel.confirmation.modal.title=Cancel Quote {0}
text.quote.yes.button.label=Yes
text.quote.no.button.label=No
text.quote.discount.rate.null.error=The discount rate is null.
text.quote.discount.type.null.error=The discount type is null.
text.quote.discount.apply.argument.invalid.error=The discount input is invalid. Please try again.
text.quote.discount.apply.calculation.error=There is an internal error for applying the discount. Please try again.
text.quote.discount.modal.title=Discount {0}
text.quote.discount.by.percentage=Discount by Percentage
text.quote.discount.by.amount=Discount by Amount
text.quote.discount.adjust.total=Adjust Total
text.quote.done.button.label=Done
text.quote.cancel.button.label=Cancel
text.quote.cancel.success=Quote {0} has been canceled
text.quote.not.cancelable=Quote {0} is not cancelable
text.quote.not.submitable=Quote cannot be submitted since total is negative
text.account.quotes.noQuotes=No Quotes
text.account.manageQuotes.breadcrumb=Quotes
text.quote.comments.label=Comments
text.account.quote.status.display.BUYER_ACCEPTED=Accepted
text.account.quote.status.display.BUYER_APPROVED=Approved
text.account.quote.status.display.BUYER_REJECTED=Rejected
text.account.quote.status.display.SELLER_SUBMITTED=Submitted
text.account.quote.status.display.SELLERAPPROVER_PENDING=Pending Approval
text.account.quote.status.display.SELLERAPPROVER_DRAFT=Draft
text.account.quote.status.display.SELLERAPPROVER_APPROVED=Approved
text.account.quote.status.display.SELLERAPPROVER_REJECTED=Rejected
text.account.quote.status.display.CANCELLED=Cancelled
text.account.quote.status.display.EXPIRED=Expired

text.account.quote.action.display.EXPIRED=Expired
text.account.quote.action.display.CREATE=Create Quote
text.account.quote.action.display.SUBMIT=Submit Quote
text.account.quote.action.display.EDIT=Edit Quote
text.account.quote.action.display.CANCEL=Cancel Quote
text.account.quote.action.display.CHECKOUT=Accept and Check Out Quote
text.account.quote.action.display.REJECT=Reject Quote
text.account.quote.action.display.VIEW=View Quote
text.account.quote.action.display.SAVE=Save Quote
text.account.quote.action.display.ORDER=Order Quote

text.quote.edit.confirmation.message=This quote has been approved, Editing this quote will prevent checkout until the new edits are approved.
text.quote.edit.confirmation.modal.title=Edit Quote Warning
text.quote.edit.warning.message=Are you sure you want to edit this approved quote?

text.quote.dateformat=MM/dd/yyyy
text.quote.dateformat.datepicker.selection=mm/dd/yy
text.quote.dateformat.datepicker.selection.hint=mm/dd/yyyy

text.quote.previous.estimated.total.display=Previous Estimated Total
text.account.configuration.display=View Configuration

registration.consent.link=To view other options or to change your settings, go to the Consent Management page in My Account.
text.consent.button.accept=Accept
text.consent.button.decline=Decline
text.consent.button.more=More
text.cookie.notification=We use cookies to ensure that we give you the best experience on our website. If you continue, you agree with our policy statement.
text.cookie.notification.accept=Ã

text.integrator.user.id=User:
cart.error.message=Oooups, something went wrong. We are sorry!

checkout.multi.billingAddress.breadcrumb=Billing address
checkout.multi.deliveryMethod.breadcrumb=Options
checkout.multi.paymentMethod.breadcrumb=Payment method

text.product.details.add.app=Add app to cart

checkout.addtocart.app.invalid=The app {0} can not be purchased.
checkout.addtocart.appversion.invalid=App version with code {0} can not be purchased.

text.setDefault=Set as default
text.default=Default

text.account.profile.paymentInfo.disableFailed=Payment info could not be deleted. Please try again later or contact customer support.
text.account.profile.paymentInfo.settingPreferenceFailed=The payment method could not be set as preference. Please try again later or contact customer support.

product.details.license.full=Purchase
product.details.license.evaluation=Trial
product.details.license.subscription=Subscription
product.details.license.tool=Free Azena Tool

flyout.menu.header.text=Logged in as

validation.entity.notfound=Entity with given identifier was not found.
validation.resource.forbidden=Access to resource is not allowed.
validation.input.error=Input not valid.

# DUAL USE RELATED
dual.use.info.text=The information contained in this page pertains to a dual use product controlled for export by the Export Control laws. Diversion contrary to applicable Export Control laws is prohibited. Download may be delayed due to authorization requirements.

licenseactivation.generic=Error when activating license.
licenseactivation.notPurchasable=License activation failed. License is not purchasable.
licenseactivation.notSupported=License activation failed. License type is not supported.

companyprofile.notFound=Company profile not found.

error.title=Error
error.notfound.title=Page not found
error.notfound.message=This page does not exist.
error.servererror.title=Service unavailable
error.servererror.message=The service is not available at this moment.
error.message.contactus.text=We are sorry! If you need further help, please
error.message.contactus.linktext=contact us

footer.termsAndConditions=Terms and Conditions
footer.privacyPolicy=Privacy Policy
footer.legalNote=Legal Note
footer.imprint=Corporate Information
footer.reportInfringement=Report Infringement
footer.copyright=Copyright Â© 2021 Bosch Digital Commerce GmbH. All rights reserved
