package com.sast.rest.api.order.controller.admin;

import com.sast.rest.api.order.controller.dto.CancelOrderResponse;
import com.sast.rest.api.order.facade.admin.OrderFacade;
import com.sast.rest.api.support.BaseControllerTest;
import de.hybris.bootstrap.annotations.UnitTest;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.security.Principal;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class OrderControllerUnitTest extends BaseControllerTest {

    @Mock
    private OrderFacade orderFacade;

    @InjectMocks
    private OrderController orderController;

    @Mock
    private Principal principal;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(orderController).build();
    }

    @Test
    @SneakyThrows
    public void testCancelOrder() {
        final String orderId = "order_123";
        when(orderFacade.cancelOrder(orderId, principal)).thenReturn(new CancelOrderResponse(orderId));

        mockMvc.perform(post("/v1/admin/orders/{orderId}/cancel", orderId)
                .principal(principal)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.orderId").value(orderId));
    }
}
