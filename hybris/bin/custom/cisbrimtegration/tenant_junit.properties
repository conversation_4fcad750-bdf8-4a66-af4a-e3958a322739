solace.product.create.queue=iotstore-junit/product/create/v1
solace.product.update.queue=iotstore-junit/product/update/v1
solace.order.create.queue=iotstore-junit/order/create/v1
solace.order.update.queue=iotstore-junit/order/updated/v1
solace.order.reject.queue=iotstore-junit/order/reject/v1
solace.invoice.update.queue=iotstore-junit/invoice/created/v1
solace.invoice.paid.queue=iotstore-junit/invoice/paidstatusupdated/v1
solace.invoice.paymentfailed.queue=iotstore-junit/invoice/paymentfailed/v1
solace.contract.update.queue=iotstore-junit/contract/update/v1
solace.transactionbasedprice.updated.queue=iotstore-junit/transactionbasedprice/updated/v1

