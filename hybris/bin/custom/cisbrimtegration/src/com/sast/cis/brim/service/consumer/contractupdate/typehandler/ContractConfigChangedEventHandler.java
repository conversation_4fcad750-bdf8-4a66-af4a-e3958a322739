package com.sast.cis.brim.service.consumer.contractupdate.typehandler;

import com.sast.cis.brim.service.consumer.contractupdate.ContractUpdateEventType;
import com.sast.cis.brim.service.consumer.contractupdate.ContractUpdateEventTypeHandler;
import com.sast.cis.brim.service.consumer.contractupdate.ContractUpdateMessageHandler;
import com.sast.cis.core.billingintegration.dto.subscription.ContractChangeData;
import com.sast.cis.core.billingintegration.events.ContractConfigChangedResponseEvent;
import com.sast.cis.core.billingintegration.publisher.PartnerExportResponsePublisher;
import org.springframework.stereotype.Component;

@Component
public class ContractConfigChangedEventHandler implements ContractUpdateEventTypeHandler {
    private final PartnerExportResponsePublisher partnerExportResponsePublisher;

    public ContractConfigChangedEventHandler(
        final PartnerExportResponsePublisher partnerExportResponsePublisher,
        final ContractUpdateMessageHandler contractUpdateEventProcessor) {
        this.partnerExportResponsePublisher = partnerExportResponsePublisher;
        contractUpdateEventProcessor.registerHandlerForType(this, ContractUpdateEventType.CONFIG_CHANGE);
    }

    @Override
    public void handleEvent(ContractChangeData eventPayload) {
        partnerExportResponsePublisher.publishContractConfigChangedResponse(
                new ContractConfigChangedResponseEvent(eventPayload));

    }
}
