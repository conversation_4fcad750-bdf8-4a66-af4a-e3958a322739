package com.sast.cis.brim.converter;

import com.sast.cis.core.billingintegration.dto.CreditNoteCreatedData;
import com.securityandsafetythings.billing.brim.model.invoice.v1.InvoicePdfNotification;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;

@Component
@RequiredArgsConstructor
public class CreditNoteCreatedDataConverter implements BrimConverter<InvoicePdfNotification, CreditNoteCreatedData> {

    private final InvoiceDocumentDataConverter invoiceDocumentDataConverter;

    @Override
    public CreditNoteCreatedData convert(final @Nonnull InvoicePdfNotification invoicePdfNotification) {
        checkSource(invoicePdfNotification);

        final CreditNoteCreatedData creditNoteCreatedData = new CreditNoteCreatedData();
        invoiceDocumentDataConverter.convert(invoicePdfNotification, creditNoteCreatedData);
        creditNoteCreatedData.setOriginalInvoice(invoicePdfNotification.getOriginalInv());
        return creditNoteCreatedData;
    }
}
