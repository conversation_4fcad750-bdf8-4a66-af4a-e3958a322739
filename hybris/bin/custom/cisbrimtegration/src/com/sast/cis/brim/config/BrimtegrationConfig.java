package com.sast.cis.brim.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.cis.brim.converter.BrimConverter;
import com.sast.cis.brim.enums.BrimEventType;
import com.sast.cis.brim.http.BrimProductUpdateClient;
import com.sast.cis.brim.service.BrimProductExportFactory;
import com.sast.cis.brim.service.BrimProductSyncExport;
import com.sast.cis.brim.service.consumer.BrimEventProcessor;
import com.sast.cis.core.billingintegration.dto.ProductExportData;
import com.sast.cis.core.billingintegration.listener.ProductUpdateListener;
import com.sast.cis.core.billingintegration.request.ProductExport;
import com.sast.cis.core.billingintegration.request.ProductExportFactory;
import com.sast.cis.core.model.AppLicenseModel;
import com.securityandsafetythings.billing.brim.config.BrimObjectMapper;
import com.securityandsafetythings.billing.brim.model.product.v1.ProductData;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
@EnableRetry
public class BrimtegrationConfig {
    @Bean
    public ObjectMapper brimObjectMapper() {
        return new BrimObjectMapper();
    }

    @Bean
    public Map<BrimEventType, BrimEventProcessor> brimEventProcessors(List<BrimEventProcessor> eventProcessorList) {
        Set<BrimEventType> processableEventTypes = eventProcessorList.stream()
                .map(BrimEventProcessor::getProcessableEventType)
                .collect(Collectors.toSet());
        if (processableEventTypes.size() != eventProcessorList.size()) {
            throw new IllegalStateException(
                    String.format("Possible duplicate Mapping of processable event types. " +
                            "Processable event types: %s, Event processors: %s", processableEventTypes, eventProcessorList));
        }
        return eventProcessorList.stream()
                .collect(Collectors.toMap(BrimEventProcessor::getProcessableEventType, Function.identity()));
    }

    @Bean
    public ProductExportFactory productExportFactory(ProductExport productExport,
        ProductExport productSyncExport, ProductExport expiredPriceDiscardingBrimAsyncProductExport) {
        return new BrimProductExportFactory(productSyncExport, productExport, expiredPriceDiscardingBrimAsyncProductExport);
    }
}
