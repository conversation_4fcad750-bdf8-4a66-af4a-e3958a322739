package com.sast.cis.brim.service.consumer;

import com.sast.cis.brim.enums.BrimEventStatus;
import com.sast.cis.brim.enums.BrimEventType;
import com.sast.cis.brim.model.BrimEventModel;
import com.sast.cis.core.util.Base58UUIDCodeGenerator;
import de.hybris.platform.servicelayer.model.ModelService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

import java.util.UUID;

import static java.util.Optional.ofNullable;

@Service
@Slf4j
@RequiredArgsConstructor
public class BrimEventPersistenceService {
    private static final String EVENT_CODE_PREFIX = "BRIMEVENT";
    private static final String JMS_DESTINATION_KEY = "jms_destination";

    private final ModelService modelService;

    public BrimEventModel saveMessage(@NonNull Message<String> message, @NonNull BrimEventType eventType) {
        MessageHeaders headers = message.getHeaders();
        String jmsEventId = ofNullable(headers.getId()).map(UUID::toString).orElse(null);
        BrimEventModel newEvent = modelService.create(BrimEventModel.class);
        newEvent.setCode(Base58UUIDCodeGenerator.generateCode(EVENT_CODE_PREFIX));
        newEvent.setJmsEventId(jmsEventId);
        newEvent.setOriginalJmsDestination(headers.getOrDefault(JMS_DESTINATION_KEY, "").toString());
        newEvent.setEventType(eventType);
        newEvent.setStatus(BrimEventStatus.NEW);
        newEvent.setMessagePayload(message.getPayload());
        modelService.save(newEvent);
        LOG.info("Stored BRIM event with code={}, type={}, jmsEventId={}",
            newEvent.getCode(), newEvent.getEventType(), newEvent.getJmsEventId());
        return newEvent;
    }

    public void setEventStatus(@NonNull BrimEventModel event, @NonNull BrimEventStatus status) {
        event.setStatus(status);
        modelService.save(event);
        LOG.info("Status of BRIM event with code={}, jmsEventId={} set to {}",
            event.getCode(), event.getJmsEventId(), event.getStatus());
    }
}
