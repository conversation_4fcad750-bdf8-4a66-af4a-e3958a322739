package com.sast.cis.brim.service.consumer;

import com.sast.cis.brim.enums.BrimEventType;
import com.sast.cis.brim.exception.BrimInvalidMessageException;
import com.sast.cis.brim.model.BrimEventModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class BrimJmsMessageConsumer {
    private final BrimEventPersistenceService brimEventPersistenceService;
    private final BrimEventProcessingService brimEventProcessingService;

    public void consume(@NonNull Message<String> message, @NonNull BrimEventType eventType) {
        if (StringUtils.isEmpty(message.getPayload())) {
            throw new BrimInvalidMessageException(String.format("A message with empty payload was received in the solace order consumer, message=%s", message));
        }
        LOG.info("Solace message headers: {}", message.getHeaders());
        LOG.info("Solace message payload: {}", message.getPayload());
        BrimEventModel brimEvent = brimEventPersistenceService.saveMessage(message, eventType);

        brimEventProcessingService.process(brimEvent);
    }
}
