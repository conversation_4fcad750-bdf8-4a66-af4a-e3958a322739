package com.sast.cis.brim.service.consumer;

import com.sast.cis.brim.config.SolaceConfig;
import com.sast.cis.brim.enums.BrimEventType;
import lombok.RequiredArgsConstructor;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.messaging.Message;

@RequiredArgsConstructor
public class SolaceOrderCreateConsumer {
    private final BrimJmsMessageConsumer brimJmsMessageConsumer;

    @JmsListener(destination = SolaceConfig.SOLACE_ORDER_CREATE_QUEUE_PROPERTY, containerFactory = "brimListenerContainerFactory")
    public void handle(Message<String> message) {
        brimJmsMessageConsumer.consume(message, BrimEventType.ORDER_CREATED);
    }
}
