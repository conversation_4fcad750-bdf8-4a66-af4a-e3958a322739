package com.sast.cis.brim.service.consumer.order;

import com.sast.cis.brim.converter.BrimConverter;
import com.sast.cis.brim.service.consumer.BrimMessageHandler;
import com.sast.cis.core.billingintegration.dto.OrderExportData;
import com.sast.cis.core.billingintegration.events.OrderResponseEvent;
import com.sast.cis.core.billingintegration.events.OrderUpdateResponseEvent;
import com.sast.cis.core.billingintegration.publisher.PartnerExportResponsePublisher;
import com.securityandsafetythings.billing.brim.model.ordercontract.v1.ResponseObject;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class OrderUpdateMessageHandler implements BrimMessageHandler<ResponseObject> {
    private final PartnerExportResponsePublisher partnerExportResponsePublisher;
    private final BrimConverter<ResponseObject, OrderExportData> orderExportDataConverter;

    @Override
    public void handleMessage(@NonNull final ResponseObject message) {
        partnerExportResponsePublisher.publishOrderUpdateResponse(createOrderResponseEvent(message));
    }

    private OrderUpdateResponseEvent createOrderResponseEvent(final ResponseObject responseObject) {
        final OrderExportData orderExportData = orderExportDataConverter.convert(responseObject);
        return new OrderUpdateResponseEvent(orderExportData);
    }
}
