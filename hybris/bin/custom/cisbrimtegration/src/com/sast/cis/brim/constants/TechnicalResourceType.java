package com.sast.cis.brim.constants;

public enum TechnicalResourceType {
    ABA_ROUTING_NUMBER("P0", "routingNumber"),
    PAYMENT_METHOD("P1", "paymentMethod"),
    RECURRING_REFERENCE("P2", "recurringReference"),
    PSP_REFERENCE("P3", "pspReference"),
    SERVICE_PROVIDER_PSP_REFERENCE("P4", "serviceProviderPspReference"),
    BANK_ACCOUNT_NUMBER("P5", "iban"),
    BIC("P6", "bic"),
    BANK_NAME("P7", "bankName"),
    PSP_CUSTOMER_ID("P8", null),
    PSP_NAME("P9", null),
    CUSTOMER_PROJECT_NAME("YP", null),
    CUSTOMER_REFERENCE_NUMBER("YR", null),
    PLATFORM_COMMISSION_ABSOLUTE("CA", null),
    PLATFORM_COMMISSION_RELATIVE("CR", null),
    PSP_SEQUENCE_REFERENCE("SR", null),
    CUSTOM_PRICE_TEXT("CT", null),
    ACCOUNT_HOLDER_NAME("PH", null),
    DATE_OF_SIGNATURE("PG", null),
    CIAM_NUMBER("CI", "ciamNumber"),
    PAYMENT_MANDATE("PM", null);


    private final String resourceType;
    private final String alternateResourceType;

    TechnicalResourceType(String resourceType, String alternateResourceType) {
        this.resourceType = resourceType;
        this.alternateResourceType = alternateResourceType;
    }

    public String getResourceType() {
        return resourceType;
    }

    public String getAlternateResourceType() {
        return alternateResourceType;
    }
}
