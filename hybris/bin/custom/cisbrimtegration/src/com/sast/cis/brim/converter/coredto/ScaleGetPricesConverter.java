package com.sast.cis.brim.converter.coredto;

import com.google.common.base.Preconditions;
import com.sast.cis.core.billingintegration.dto.Price;
import com.sast.cis.core.billingintegration.dto.PriceRecurrence;
import com.securityandsafetythings.billing.brim.model.product.v1.*;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ScaleGetPricesConverter implements PriceConverter<ScaleGet> {
    private static final LocalDateTime DEFAULT_START_DATE = LocalDate.now().atTime(1, 0);
    private static final LocalDateTime DEFAULT_END_DATE = LocalDate.of(9999, 12, 31).atTime(LocalTime.MAX);
    private static final int MIN_QUANTITY_INITIAL_VALUE = BigDecimal.ONE.intValue();

    /*
    This is a little bit tricky because the scale versions provided by BRIM only contain a start time. We need to determine
    end times for each ScaleVersion from the ordered collection of scale versions, as the end time of any given scale version
    is either right before the start date of the *next* ScaleVersion or eternity (9999-12-31).
    The start time of first scale version will always remain blank.
     */
    @Override
    public List<Price> convert(ScaleGet scaleGet, PriceRecurrence priceRecurrence) {
        validateScaleGet(scaleGet);
        List<ScaleVersion> scaleVersions = scaleGet.getVersions();
        if (CollectionUtils.isEmpty(scaleVersions)) {
            return Collections.emptyList();
        }

        LocalDateTime dateFrom = getEarliestDateFrom(scaleVersions);
        scaleVersions.forEach(scaleVersion -> {
            if (scaleVersion.getDateFrom() == null) {
                scaleVersion.setDateFrom(dateFrom.minusNanos(1L));
            }
        });

        List<ScaleVersion> sortedScaleVersions = scaleVersions.stream()
            .sorted(Comparator.comparing(ScaleVersion::getDateFrom))
            .collect(Collectors.toList());

        List<Price> scalePrices = new LinkedList<>();
        for (int i = 0; i < sortedScaleVersions.size(); i++) {
            ScaleVersion currentScaleVersion = sortedScaleVersions.get(i);
            LocalDateTime validToDate = DEFAULT_END_DATE;
            if (i < sortedScaleVersions.size() - 1) {
                ScaleVersion nextScaleVersion = sortedScaleVersions.get(i + 1);
                validToDate = nextScaleVersion.getDateFrom().minusNanos(1L);
            }
            List<Price> pricesForScaleVersion = createPricesFromScaleVersion(currentScaleVersion, priceRecurrence, validToDate);
            scalePrices.addAll(pricesForScaleVersion);
        }
        return scalePrices;
    }

    private LocalDateTime getEarliestDateFrom(List<ScaleVersion> scaleVersions) {
        if (CollectionUtils.emptyIfNull(scaleVersions).stream().anyMatch(scaleVersion -> scaleVersion.getDateFrom() != null)) {
            Optional<ScaleVersion> oldestScaleVersion = CollectionUtils.emptyIfNull(scaleVersions).stream()
                .filter(scaleVersion -> scaleVersion.getDateFrom() != null)
                .min(Comparator.comparing(ScaleVersion::getDateFrom));
            return oldestScaleVersion.orElseThrow().getDateFrom();
        }
        //the nano second will be removed for the first scale version
        return DEFAULT_START_DATE.plusNanos(1L);
    }

    /*
    Within each scale version, we need to transform the maxQuantity for each row into minQuantity (as we expect it for hybris).
    minQuantity for each returned Price is either 0 for the base price or maxQuantity of previous row plus 1.
     */
    private List<Price> createPricesFromScaleVersion(ScaleVersion scaleVersion, PriceRecurrence priceRecurrence,
        LocalDateTime validToDate) {
        LocalDateTime validFromDate = scaleVersion.getDateFrom();
        Map<Currency, List<SortableScaleRow>> scaleRowPerCurrency = new HashMap<>();
        for (RangeTableRow rangeTableRow : CollectionUtils.emptyIfNull(scaleVersion.getRows())) {
            SortableScaleRow scaleRow = convertRangeTableRow(rangeTableRow);
            scaleRowPerCurrency.computeIfAbsent(scaleRow.getCurrency(), k -> new LinkedList<>()).add(scaleRow);
        }

        List<Price> prices = new ArrayList<>(scaleVersion.getRows().size());
        for (List<SortableScaleRow> scaleRowsForCurrency : scaleRowPerCurrency.values()) {
            prices.addAll(createPricesFromCurrencyScaleRows(scaleRowsForCurrency, priceRecurrence, validFromDate, validToDate));
        }
        return prices;
    }

    private List<Price> createPricesFromCurrencyScaleRows(List<SortableScaleRow> currencyScaleRows, PriceRecurrence priceRecurrence,
        LocalDateTime validFromDate, LocalDateTime validToDate) {
        List<SortableScaleRow> sortedScaleRows = CollectionUtils.emptyIfNull(currencyScaleRows).stream()
            .map(this::validateScaleRow)
            .sorted(Comparator.comparing(SortableScaleRow::getValueTo))
            .collect(Collectors.toList());

        List<Price> prices = new ArrayList<>(sortedScaleRows.size());
        for (int i = sortedScaleRows.size() - 1; i >= 0; i--) {
            SortableScaleRow currentScaleRow = sortedScaleRows.get(i);
            int minQuantity = MIN_QUANTITY_INITIAL_VALUE;
            if (i > 0) {
                SortableScaleRow nextScaleRow = sortedScaleRows.get(i - 1);
                minQuantity = nextScaleRow.getValueTo().intValue() + 1;
            }
            prices.add(createPrice(currentScaleRow, priceRecurrence, minQuantity, validFromDate, validToDate));
        }
        return prices;
    }

    private SortableScaleRow validateScaleRow(SortableScaleRow sortableScaleRow) {
        Preconditions.checkArgument(sortableScaleRow.getValueTo() != null, "Given scale row upper quantity boundary");
        return sortableScaleRow;
    }

    private void validateScaleGet(ScaleGet scaleGet) {
        Preconditions.checkArgument(scaleGet.getRangeTableClassId() != null,
            "Response contains range table without range table class ID.");
        Preconditions.checkArgument(RangeTable.SAST_VOLUME_DISCOUNT_V3.equals(scaleGet.getRangeTableClassId()),
            "Response contains range table with unknown class ID '%s'.", scaleGet.getRangeTableClassId());
    }

    private Price createPrice(SortableScaleRow sortableScaleRow, PriceRecurrence priceRecurrence, int minQuantity,
        LocalDateTime validFromDate, LocalDateTime validToDate) {
        return Price.builder()
            .amount(convertAmount(sortableScaleRow))
            .currency(sortableScaleRow.getCurrency())
            .recurrence(priceRecurrence)
            .minQuantity(minQuantity)
            .scaledPriceDiscount(sortableScaleRow.getDiscount().intValue())
            .validFrom(validFromDate)
            .validTo(validToDate)
            .build();
    }

    private BigDecimal convertAmount(SortableScaleRow sortableScaleRow) {
        if (sortableScaleRow.getPrice() == null) {
            return BigDecimal.ZERO;
        }
        return sortableScaleRow.getPrice();
    }

    /*
    Converts to intermediate representation of a RangeTableRow that is a bit easier to handle
     */
    private SortableScaleRow convertRangeTableRow(RangeTableRow rangeTableRow) {
        SortableScaleRow.SortableScaleRowBuilder builder = SortableScaleRow.builder();
        builder.valueTo(rangeTableRow.getValueTo());
        for (RangeTableColumn rangeTableColumn : CollectionUtils.emptyIfNull(rangeTableRow.getColumns())) {
            switch (rangeTableColumn.getName()) {
                case PRICE:
                    builder.price(rangeTableColumn.getValueNumber());
                    break;
                case CURRENCY:
                    builder.currency(Currency.getInstance(rangeTableColumn.getValueString()));
                    break;
                case DISCOUNT:
                    builder.discount(rangeTableColumn.getValueNumber() != null ? rangeTableColumn.getValueNumber() : BigDecimal.ZERO);
                    break;
                default:
                    LOG.warn("Received unhandled column name '{}'", rangeTableColumn.getName());
            }
        }

        return builder.build();
    }

    @Builder
    @Getter
    private static class SortableScaleRow {
        private final BigDecimal valueTo;
        private final BigDecimal discount;
        private final BigDecimal price;
        private final Currency currency;
    }
}
