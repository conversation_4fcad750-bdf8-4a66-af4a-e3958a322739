package com.sast.cis.brim.config.messagerouting;

import com.sast.cis.brim.config.SolaceConfig;
import com.sast.cis.brim.exception.SolaceTopicCreationException;
import com.solacesystems.jcsmp.JCSMPException;
import com.solacesystems.jcsmp.JCSMPSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;

import static java.util.Set.of;

@Slf4j
@RequiredArgsConstructor
public class SolaceTopicSetup {

    private final SolaceConfig solaceConfig;
    private final SolaceUtil solaceUtil;

    @PostConstruct
    public void setupSolaceTopics() {
        JCSMPSession jcsmpSession = null;
        try {
            jcsmpSession = solaceUtil.createJcsmpSession();

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueOrderCreate(),
                of(solaceConfig.getTopicOrderCreateResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueOrderReject(),
                of(solaceConfig.getTopicOrderRejectResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueOrderUpdate(),
                of(solaceConfig.getTopicOrderUpdateResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueProductCreate(),
                of(solaceConfig.getTopicProductCreateResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueProductUpdate(),
                of(solaceConfig.getTopicProductUpdateResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueInvoiceUpdate(),
                of(solaceConfig.getTopicInvoiceUpdateResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueContractUpdate(),
                of(solaceConfig.getTopicContractUpdateResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueInvoicePaid(),
                of(solaceConfig.getTopicInvoicePaidResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueueInvoicePaymentfailed(),
                of(solaceConfig.getTopicInvoicePaymentfailedResponse()));

            solaceUtil.createTopicSubscribedQueue(jcsmpSession, solaceConfig.getQueuePriceUpdate(),
                of(solaceConfig.getTopicPriceUpdateResponse()));

        } catch (JCSMPException | JMSException e) {
            LOG.error("ALERT Could not re-create topics and queues, solace might be down",
                      new SolaceTopicCreationException("Error during message broker configuration", e));
        } finally {
            if (jcsmpSession != null) {
                jcsmpSession.closeSession();
            }
        }
    }

}
