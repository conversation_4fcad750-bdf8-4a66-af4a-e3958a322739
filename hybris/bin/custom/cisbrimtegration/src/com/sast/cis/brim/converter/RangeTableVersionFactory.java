package com.sast.cis.brim.converter;

import com.google.common.base.Preconditions;
import com.sast.cis.brim.exception.BrimProductUpdateException;
import com.sast.cis.core.CisTimeService;
import com.sast.cis.core.model.PriceDraftModel;
import com.securityandsafetythings.billing.brim.model.product.v1.RangeTableColumn;
import com.securityandsafetythings.billing.brim.model.product.v1.RangeTableColumnName;
import com.securityandsafetythings.billing.brim.model.product.v1.RangeTableRow;
import com.securityandsafetythings.billing.brim.model.product.v1.ScaleVersion;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class RangeTableVersionFactory {
    private final CisTimeService cisTimeService;

    private static final BigDecimal MAX_VALUE_TO = new BigDecimal("9999999999999999999999.999999");

    public RangeTableVersionFactory(CisTimeService cisTimeService) {
        this.cisTimeService = cisTimeService;
    }

    public ScaleVersion createVersion(Set<PriceDraftModel> priceDrafts) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(priceDrafts), "Given an empty collection of price drafts.");
        Set<Date> startDates = priceDrafts.stream().map(PriceDraftModel::getValidFrom).collect(Collectors.toSet());
        if (startDates.size() != 1) {
            throw new BrimProductUpdateException("Price drafts cannot have different start dates");
        }
        return new ScaleVersion()
            .setRows(createRangeTableRows(priceDrafts))
            .setDateFrom(cisTimeService.convertToLocalDateTime(getStartDate(startDates)));
    }

    public List<RangeTableRow> createRangeTableRows(Set<PriceDraftModel> priceDrafts) {
        Map<CurrencyModel, List<PriceDraftModel>> priceDraftsByCurrency = CollectionUtils.emptyIfNull(priceDrafts).stream()
            .collect(Collectors.groupingBy(PriceDraftModel::getCurrency, HashMap::new, Collectors.toList()));

        List<RangeTableRow> allRangeTableRows = new LinkedList<>();
        for (List<PriceDraftModel> priceDraftsForCurrency : priceDraftsByCurrency.values()) {
            allRangeTableRows.addAll(getRangeTableRows(priceDraftsForCurrency));
        }
        return allRangeTableRows;
    }

    private Date getStartDate(Set<Date> dates) {
        return dates.stream().findFirst().orElseThrow(() -> new BrimProductUpdateException("Price drafts cannot have different start days"));
    }

    /*
    Transforms a given list of Price drafts into a list of RangeTableRows. The tricky part here is that we store (as hybris does)
    the lower quantity boundary for a price while brim expects the upper boundary of a given price.
     */
    private List<RangeTableRow> getRangeTableRows(List<PriceDraftModel> priceDrafts) {

        PriceDraftModel[] sortedDrafts = priceDrafts.stream()
            .sorted(Comparator.comparing(PriceDraftModel::getMinQuantity))
            .toArray(PriceDraftModel[]::new);

        RangeTableRow[] rangeTableRows = new RangeTableRow[sortedDrafts.length];

        for (int i = 0; i < sortedDrafts.length; i++) {
            PriceDraftModel currentPriceDraft = sortedDrafts[i];
            if (i > 0) { // adjust previous entry
                RangeTableRow previousRangeTableRow = rangeTableRows[i - 1];
                previousRangeTableRow.setValueTo(BigDecimal.valueOf(currentPriceDraft.getMinQuantity() - 1L));
            }
            rangeTableRows[i] = createRangeTableRow(currentPriceDraft);
        }

        return Arrays.asList(rangeTableRows);
    }

    private RangeTableRow createRangeTableRow(PriceDraftModel priceDraft) {
        return new RangeTableRow()
            .setColumns(List.of(
                new RangeTableColumn().setName(RangeTableColumnName.DISCOUNT)
                    .setValueNumber(BigDecimal.valueOf(priceDraft.getScaledPriceDiscount())),
                new RangeTableColumn().setName(RangeTableColumnName.PRICE)
                    .setValueNumber(BigDecimal.valueOf(priceDraft.getAmount())),
                new RangeTableColumn().setName(RangeTableColumnName.CURRENCY)
                    .setInputColumn(true)
                    .setValueString(priceDraft.getCurrency().getIsocode())
            ))
            .setValueTo(MAX_VALUE_TO);
    }
}
