package com.sast.cis.brim.converter.productupdate.rate;

import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.PendingProductInfoModel;
import com.sast.cis.core.model.PriceDraftModel;
import com.sast.cis.core.model.RuntimeModel;
import com.sast.cis.core.runtime.LicenseRuntimeService;
import com.sast.cis.core.service.company.IotCompanyService;
import com.securityandsafetythings.billing.brim.model.product.v1.*;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.sast.cis.core.enums.LicenseType.FULL;
import static com.sast.cis.core.enums.LicenseType.SUBSCRIPTION;
import static com.sast.cis.core.service.AppLicenseService.isAAProduct;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

@Component
@RequiredArgsConstructor
public class RateGetFactory {

    private final PriceGetFactory priceGetFactory;
    private final ScaleGetFactory scaleGetFactory;
    private final LicenseRuntimeService licenseRuntimeService;
    private final IotCompanyService iotCompanyService;

    public RateGet createRate(@NonNull final AppLicenseModel appLicense) {
        final PendingProductInfoModel pendingProductInfo = appLicense.getPendingProductInfo();
        if (pendingProductInfo == null || (isEmpty(pendingProductInfo.getPrices())) && !pendingProductInfo.isUseLatestChargePlan()) {
            throw new IllegalStateException(
                    "License with code '%s' not ready for update. No pending product info or no prices".formatted(appLicense.getCode())
            );
        }

        final LicenseType licenseType = appLicense.getLicenseType();
        if (SUBSCRIPTION.equals(licenseType)) {
            return createRateForSubscriptionLicense(pendingProductInfo, appLicense);
        } else if (FULL.equals(licenseType)) {
            return createRateForFullLicense(appLicense);
        } else {
            throw new IllegalStateException(
                    "Could not create a rate for '%s'. License is not a FULL or SUBSCRIPTION license".formatted(appLicense.getCode())
            );
        }
    }

    private RateGet createRateForSubscriptionLicense(final PendingProductInfoModel pendingProductInfo, final AppLicenseModel appLicense) {
        final RecurringGet recurring = new RecurringGet()
                .setValid(true)
                .setProrated(false)
                .setFrequency(RecurringFrequency.YEARLY)
                .setPaymentType(PaymentType.PREPAID);
        if (pendingProductInfo.isUseLatestChargePlan()) {
            recurring.setNewChargePlan(Boolean.TRUE);
        }

        if (CollectionUtils.isNotEmpty(pendingProductInfo.getPrices())) {
            final List<PriceGet> basePrices = createBasePrices(pendingProductInfo.getPrices());
            if (isAAProduct(appLicense) && !iotCompanyService.isBrokerModel(appLicense)) {
                basePrices.forEach(price -> {
                    price.setType(PriceFeeType.VARIANT);
                    price.setVariantKey(VariantKey.VARIANT_1Y);
                });
                recurring.setFrequency(RecurringFrequency.VARIANT);
            } else {
                basePrices.forEach(price -> price.setType(PriceFeeType.YEARLY_FEE));
            }
            recurring.setPrices(basePrices);
        }

        return new RateGet().setRecurring(recurring);
    }


    private RateGet createRateForFullLicense(final AppLicenseModel appLicense) {
        final PendingProductInfoModel pendingProductInfo = appLicense.getPendingProductInfo();
        boolean licenseWithLimitedFixedRuntime = licenseRuntimeService.isLicenseWithLimitedFixedRuntime(appLicense);
        if (!licenseWithLimitedFixedRuntime) {
            return getOneTimeRate(pendingProductInfo);
        }

        VariantKey variantKeyForRuntime = getVariantKeyForRuntime(appLicense.getRuntime());
        return createVariantRate(pendingProductInfo, variantKeyForRuntime);
    }

    private RateGet getOneTimeRate(final PendingProductInfoModel pendingProductInfo) {
        OneTimeGet oneTime = new OneTimeGet()
                .setValid(true)
                .setProrated(false)
                .setPaymentType(PaymentType.PREPAID);

        if (pendingProductInfo.isUseLatestChargePlan()) { // done this way to avoid setting the attribute at all if it is false
            oneTime.setNewChargePlan(Boolean.TRUE);
        }

        if (CollectionUtils.isNotEmpty(pendingProductInfo.getPrices())) {
            final var priceDrafts = pendingProductInfo.getPrices();
            oneTime
                    .setPrices(createBasePrices(priceDrafts))
                    .setScalePrices(scaleGetFactory.createScalePrices(priceDrafts));
        }

        return new RateGet().setOneTime(oneTime);
    }

    private RateGet createVariantRate(final PendingProductInfoModel pendingProductInfo, final VariantKey variantKey) {
        RecurringGet recurring = new RecurringGet()
                .setValid(true)
                .setProrated(false)
                .setPaymentType(PaymentType.PREPAID)
                .setFrequency(RecurringFrequency.VARIANT);

        if (pendingProductInfo.isUseLatestChargePlan()) {
            recurring.setNewChargePlan(Boolean.TRUE);
        }

        if (CollectionUtils.isNotEmpty(pendingProductInfo.getPrices())) {
            final List<PriceGet> basePrices = createBasePrices(pendingProductInfo.getPrices());
            basePrices.forEach(price -> {
                price.setType(PriceFeeType.VARIANT);
                price.setVariantKey(variantKey);
            });
            recurring.setPrices(basePrices);
        }

        return new RateGet().setRecurring(recurring);
    }

    private VariantKey getVariantKeyForRuntime(final RuntimeModel runtime) {
        return Arrays.stream(VariantKey.values())
                .filter(variantKeyValue -> Objects.equals(variantKeyValue.name(), runtime.getBrimVariantKey()))
                .findFirst()
                .orElseThrow(
                        () -> new IllegalStateException("Unknown variant key in runtime '%s'".formatted(runtime.getBrimVariantKey()))
                );
    }

    private List<PriceGet> createBasePrices(final Set<PriceDraftModel> basePriceDrafts) {
        return priceGetFactory.createPrices(basePriceDrafts);
    }
}
