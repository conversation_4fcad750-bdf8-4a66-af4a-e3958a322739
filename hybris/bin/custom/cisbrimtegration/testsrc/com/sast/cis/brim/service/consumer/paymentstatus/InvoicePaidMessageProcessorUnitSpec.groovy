package com.sast.cis.brim.service.consumer.paymentstatus

import com.sast.cis.brim.service.consumer.AbstractBrimEventProcessor
import com.sast.cis.brim.service.consumer.AbstractBrimMessageProcessorUnitSpec
import com.securityandsafetythings.billing.brim.model.paymentstatus.PaymentStatusNotification
import de.hybris.bootstrap.annotations.UnitTest

@UnitTest
class InvoicePaidMessageProcessorUnitSpec extends AbstractBrimMessageProcessorUnitSpec<PaymentStatusNotification> {

    @Override
    AbstractBrimEventProcessor<PaymentStatusNotification> getProcessor() {
        return new InvoicePaidEventProcessor(brimObjectMapper, messageValidator, messageFilter, messageHandler)
    }

    @Override
    PaymentStatusNotification getMockMessageObject() {
        return Mock(PaymentStatusNotification)
    }
}
