package com.sast.cis.brim.converter.productexport.rate

import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.enums.StoreEnum
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.RuntimeModel
import com.sast.cis.core.runtime.LicenseRuntimeService
import com.sast.cis.core.service.company.IotCompanyService
import com.securityandsafetythings.billing.brim.model.product.v1.*
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.store.BaseStoreModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test
import spock.lang.Unroll

@UnitTest
class RateFactoryUnitSpec extends JUnitPlatformSpecification {

    private DefaultPriceFactory priceFactory = Mock()
    private ScaleFactory scaleFactory = Mock()
    private AppModel appModel = Mock()
    private IoTCompanyModel ioTCompanyModel = Mock()
    private BaseStoreModel baseStoreModel = Mock()
    private LicenseRuntimeService licenseRuntimeService = Mock()
    private IotCompanyService iotCompanyService = Mock()

    private RateFactory rateFactory

    private AppLicenseModel appLicense = Mock()
    private RuntimeModel runtime = Mock()

    private List<Price> prices = [Mock(Price), Mock(Price)]
    private Scale scale = Mock()

    void setup() {
        rateFactory = new RateFactory(priceFactory, scaleFactory, licenseRuntimeService, iotCompanyService)
        priceFactory.createPrices(appLicense) >> prices
        scaleFactory.createScale(appLicense) >> scale

        appLicense.getBaseProduct() >> appModel
        appModel.getCompany() >> ioTCompanyModel
        ioTCompanyModel.getStore() >> baseStoreModel
    }

    @Test
    def "given subscription license then create recurring rate"() {
        given:
        appLicense.getLicenseType() >> LicenseType.SUBSCRIPTION
        baseStoreModel.getUid() >> StoreEnum.AASTORE.getCode()
        iotCompanyService.isBrokerModel(appLicense) >> false

        when:
        def rate = rateFactory.createRate(appLicense)

        then:
        with(rate) {
            verifyAll {
                oneTime == null
                recurring != null
                recurring.prices == prices
                recurring.paymentType == PaymentType.PREPAID
                recurring.frequency == RecurringFrequency.VARIANT
                recurring.valid
                !recurring.prorated
            }
        }
        prices.each { price ->
            1 * price.setType(PriceFeeType.VARIANT)
        }
    }

    @Test
    def "given subscription license then create recurring rate for broker model"() {
        given:
        appLicense.getLicenseType() >> LicenseType.SUBSCRIPTION
        baseStoreModel.getUid() >> StoreEnum.AASTORE.getCode()
        iotCompanyService.isBrokerModel(appLicense) >> true

        when:
        def rate = rateFactory.createRate(appLicense)

        then:
        with(rate) {
            verifyAll {
                oneTime == null
                recurring != null
                recurring.prices == prices
                recurring.paymentType == PaymentType.PREPAID
                recurring.frequency == RecurringFrequency.YEARLY
                recurring.valid
                !recurring.prorated
            }
        }
        prices.each { price ->
            1 * price.setType(PriceFeeType.YEARLY_FEE)
        }
    }

    @Test
    def "given subscription for azena app license then create recurring rate"() {
        given:
        appLicense.getLicenseType() >> LicenseType.SUBSCRIPTION
        baseStoreModel.getUid() >> StoreEnum.IOTSTORE.getCode()

        when:
        def rate = rateFactory.createRate(appLicense)

        then:
        with(rate) {
            verifyAll {
                oneTime == null
                recurring != null
                recurring.prices == prices
                recurring.paymentType == PaymentType.PREPAID
                recurring.frequency == RecurringFrequency.YEARLY
                recurring.valid
                !recurring.prorated
            }
        }
        prices.each { price ->
            1 * price.setType(PriceFeeType.YEARLY_FEE)
        }
    }

    @Test
    def "given full license with unlimited fixed runtime then create one time rate"() {
        given:
        appLicense.getLicenseType() >> LicenseType.FULL
        licenseRuntimeService.isLicenseWithLimitedFixedRuntime(appLicense) >> false
        baseStoreModel.getUid() >> StoreEnum.AASTORE.getCode()

        when:
        def rate = rateFactory.createRate(appLicense)

        then:
        with(rate) {
            verifyAll {
                recurring == null
                oneTime != null
            }
        }
    }

    @Test
    def "given full license with limited runtime and brim variant key then create variant rate"() {
        given:
        licenseRuntimeService.isLicenseWithLimitedFixedRuntime(appLicense) >> true
        appLicense.getLicenseType() >> LicenseType.FULL
        appLicense.getRuntime() >> runtime
        runtime.getBrimVariantKey() >> VariantKey.VARIANT_3Y.name()
        baseStoreModel.getUid() >> StoreEnum.AASTORE.getCode()

        when:
        def rate = rateFactory.createRate(appLicense)

        then:
        with(rate) {
            verifyAll {
                oneTime == null
                recurring != null
                recurring.prices == prices
                recurring.paymentType == PaymentType.PREPAID
                recurring.frequency == RecurringFrequency.VARIANT
                recurring.valid
                !recurring.prorated
            }
        }
        prices.each { price ->
            1 * price.setType(PriceFeeType.VARIANT)
            1 * price.setVariantKey(VariantKey.VARIANT_3Y)
        }
    }

    @Test
    def "given full license with runtime and unknown brim variant key then throw exception"() {
        given:
        licenseRuntimeService.isLicenseWithLimitedFixedRuntime(appLicense) >> true
        appLicense.getLicenseType() >> LicenseType.FULL
        appLicense.getRuntime() >> runtime
        runtime.getBrimVariantKey() >> "unknown"
        baseStoreModel.getUid() >> StoreEnum.AASTORE.getCode()

        when:
        rateFactory.createRate(appLicense)

        then:
        def exception = thrown(IllegalStateException)
        exception.message == "Unknown variant key in runtime 'unknown'"
    }

    @Test
    @Unroll
    def 'given license of unsupported type #givenLicenseType, createRate throws exception'() {
        given:
        appLicense.getCode() >> 'app_001'
        appLicense.getLicenseType() >> givenLicenseType
        baseStoreModel.getUid() >> StoreEnum.AASTORE.getCode()

        when:
        rateFactory.createRate(appLicense)

        then:
        def exception = thrown(IllegalStateException)
        exception.message == "Could not create a rate for license 'app_001'"

        where:
        givenLicenseType       || _
        LicenseType.EVALUATION || _
        LicenseType.TOOL       || _
    }
}
