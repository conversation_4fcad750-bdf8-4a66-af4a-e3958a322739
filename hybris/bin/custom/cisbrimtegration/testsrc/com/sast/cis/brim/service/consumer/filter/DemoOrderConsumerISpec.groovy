package com.sast.cis.brim.service.consumer.filter

import com.sast.cis.brim.config.ConsumerFilterConfig
import com.sast.cis.brim.config.SolaceFilterEnabledConfig
import com.sast.cis.brim.converter.ContractItemDataConverter
import com.sast.cis.brim.converter.orderexport.OrderExportDataConverter
import com.sast.cis.brim.converter.orderexport.OrderLogConverter
import com.sast.cis.brim.converter.sellercontract.SellerContractResponseConverter
import com.sast.cis.brim.model.BrimEventModel
import com.sast.cis.brim.service.consumer.order.OrderEventProcessor
import com.sast.cis.brim.service.consumer.order.OrderMessageFilter
import com.sast.cis.brim.service.consumer.order.OrderMessageHandler
import com.sast.cis.brim.service.consumer.order.OrderMessageValidator
import com.sast.cis.brim.service.filter.OrderConsumerFilter
import com.sast.cis.brim.service.sellercontract.BrimSellerContractService
import com.sast.cis.core.billingintegration.publisher.PartnerExportResponsePublisher
import com.sast.cis.core.config.SpringProfileConfig
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.event.EventService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.brim.model.BrimEventBuilder
import org.junit.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.core.io.Resource
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource

@ContextConfiguration(classes = [
        ConsumerContextConfig.class,
        SpringProfileConfig.class,
        SolaceFilterEnabledConfig.class,
        PartnerExportResponsePublisher.class,
        ConsumerFilterConfig.class,
        OrderExportDataConverter.class,
        SellerContractResponseConverter.class,
        OrderLogConverter.class,
        ContractItemDataConverter.class,
        OrderMessageValidator.class,
        OrderEventProcessor.class,
        OrderMessageFilter.class,
        OrderMessageHandler.class
])
@ActiveProfiles("demo")
@TestPropertySource(value = "classpath:/test/project-demo.properties")
@IntegrationTest
class DemoOrderConsumerISpec extends JUnitPlatformSpecification {
    @Autowired
    private OrderEventProcessor orderEventProcessor

    @Autowired
    private ApplicationContext applicationContext

    @Autowired
    private EventService eventService

    @Autowired
    private OrderConsumerFilter orderConsumerFilter

    @Autowired
    private BrimSellerContractService brimSellerContractService

    @Test
    def 'order response on demo environment filters wrong ids'() {
        given:
        def givenOrderResponseOnDemo = getBrimEventFromFile("orderCreateResponse_error_wrongId.json")

        when:
        orderEventProcessor.process(givenOrderResponseOnDemo)

        then:
        0 * eventService.publishEvent(_)

    }

    @Test
    def 'order response for dev on demo environment is filtered'() {
        given:
        def givenOrderResponseOnDemo = getBrimEventFromFile("orderCreateResponse_dev.json")

        when:
        orderEventProcessor.process(givenOrderResponseOnDemo)

        then:
        0 * eventService.publishEvent(_)

    }

    @Test
    def 'order response for demo on demo environment is not filtered'() {
        given:
        def givenOrderResponseOnDemo = getBrimEventFromFile("orderCreateResponse_demo.json")

        when:
        orderEventProcessor.process(givenOrderResponseOnDemo)

        then:
        1 * eventService.publishEvent(_)

    }

    @Test
    def 'seller contract response for dev on demo environment is filtered'() {
        given:
        def givenOrderResponseOnDemo = getBrimEventFromFile("sellerContractResponse_dev.json")

        when:
        orderEventProcessor.process(givenOrderResponseOnDemo)

        then:
        0 * eventService.publishEvent(_)

    }


    @Test
    def 'seller contract response for demo on demo environment is not filtered'() {
        given:
        def givenOrderResponseOnDemo = getBrimEventFromFile("sellerContractResponse_demo.json")

        when:
        1 * brimSellerContractService.handleSellerContractResponse(_) >> true
        orderEventProcessor.process(givenOrderResponseOnDemo)

        then:
        0 * eventService.publishEvent(_)

    }


    private BrimEventModel getBrimEventFromFile(String filename) {
        Resource resource = applicationContext.getResource("classpath:test/filter/${filename}")
        BrimEventBuilder.generate().withMessagePayload(resource.getInputStream().getText()).buildInstance()
    }
}
