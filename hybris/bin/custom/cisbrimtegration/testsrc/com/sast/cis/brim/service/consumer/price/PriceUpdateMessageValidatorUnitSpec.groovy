package com.sast.cis.brim.service.consumer.price

import com.sast.cis.brim.exception.BrimInvalidMessageException
import com.sast.cis.test.utils.TestLogListener
import com.securityandsafetythings.billing.brim.model.enums.LogCode
import com.securityandsafetythings.billing.brim.model.product.v1.Log
import com.securityandsafetythings.billing.brim.model.product.v1.transbasedprice.PriceCodeValue
import com.securityandsafetythings.billing.brim.model.product.v1.transbasedprice.TransactionBasedPrice
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import de.hybris.platform.util.logging.HybrisLogger
import org.apache.log4j.Level
import org.junit.Test
import spock.lang.Unroll

import static org.hamcrest.Matchers.containsString
import static org.hamcrest.Matchers.startsWith
import static spock.util.matcher.HamcrestSupport.that

@UnitTest
class PriceUpdateMessageValidatorUnitSpec extends JUnitPlatformSpecification {
    TestLogListener testLogListener

    private PriceUpdateMessageValidator priceUpdateMessageValidator

    def setup() {
        testLogListener = new TestLogListener()
        HybrisLogger.addListener(testLogListener)

        priceUpdateMessageValidator = new PriceUpdateMessageValidator()
    }

    def cleanup() {
        HybrisLogger.removeListener(testLogListener)
    }

    @Test
    void 'valid message passes validation'() {
        given:
        def validTransactionBasedPrice = TransactionBasedPrice.builder()
                .mappingTableId('lalala')
                .build()
        when:
        priceUpdateMessageValidator.validate(validTransactionBasedPrice)

        then:
        noExceptionThrown()
    }

    @Test
    void 'message with error log causes alert logging'() {
        given:

        def messageWithErrors = TransactionBasedPrice.builder()
                .mappingTableId('lalala')
                .log([
                        new Log().setCode(LogCode.ERROR).setDetail('This is the first error'),
                        new Log().setCode(LogCode.ERROR).setDetail('This is the second error')
                ]).build()

        when:
        priceUpdateMessageValidator.validate(messageWithErrors)
        def actualLogs = testLogListener.getLog()

        then:
        noExceptionThrown()
        actualLogs.size() == 2

        expect:
        assert actualLogs.any { it.getLevel() == Level.ERROR }
    }

    @Test
    void 'message with error log and price codes causes alert logging containing price codes'() {
        given:

        def messageWithErrors = TransactionBasedPrice.builder()
                .mappingTableId('lalala')
                .priceCodeValues(List.of(
                        PriceCodeValue.builder().code('firstCode').build(),
                        PriceCodeValue.builder().build(),
                        PriceCodeValue.builder().code('secondCode').build()
                ))
                .log([
                        new Log().setCode(LogCode.ERROR).setDetail('This is the first error'),
                        new Log().setCode(LogCode.ERROR).setDetail('This is the second error')
                ]).build()

        when:
        priceUpdateMessageValidator.validate(messageWithErrors)
        def actualLogs = testLogListener.getLog()

        then:
        noExceptionThrown()
        actualLogs.size() == 2

        expect:
        assert actualLogs.any { it.getLevel() == Level.ERROR }
        that actualLogs.find({ it.getLevel() == Level.ERROR }).message as String, containsString('firstCode')
        that actualLogs.find({ it.getLevel() == Level.ERROR }).message as String, containsString('secondCode')
    }

    @Test
    void 'null message throws IllegalArgumentException'() {
        when:
        priceUpdateMessageValidator.validate(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    @Unroll
    void 'message with mapping table id #actualMappingTableId throws BrimInvalidMessageException'() {
        given:
        def validTransactionBasedPrice = TransactionBasedPrice.builder()
                .mappingTableId(actualMappingTableId)
                .build()
        when:
        priceUpdateMessageValidator.validate(validTransactionBasedPrice)

        then:
        thrown(BrimInvalidMessageException)

        where:
        actualMappingTableId << [null, '']
    }
}