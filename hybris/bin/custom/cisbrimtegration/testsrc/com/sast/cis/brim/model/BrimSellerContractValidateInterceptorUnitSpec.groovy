package com.sast.cis.brim.model

import com.sast.cis.core.model.IoTCompanyModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.interceptor.InterceptorException
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class BrimSellerContractValidateInterceptorUnitSpec extends JUnitPlatformSpecification {
    private BrimSellerContractValidateInterceptor brimSellerContractValidateInterceptor

    private BrimSellerContractModel mockSellerContract = Mock(BrimSellerContractModel)
    private IoTCompanyModel mockCompany1 = Mock(IoTCompanyModel)
    private IoTCompanyModel mockCompany2 = Mock(IoTCompanyModel)

    def setup() {
        brimSellerContractValidateInterceptor = new BrimSellerContractValidateInterceptor()

        mockSellerContract.getCode() >> 'Some code'
        mockSellerContract.getCompany() >> Set.of()
    }

    @Test
    def 'if no company is attached to seller contract, validation passes'() {
        when:
        brimSellerContractValidateInterceptor.onValidate(mockSellerContract, null)

        then:
        noExceptionThrown()
    }

    @Test
    def 'if one company is attached to seller contract, validation passes'() {
        when:
        brimSellerContractValidateInterceptor.onValidate(mockSellerContract, null)

        then:
        mockSellerContract.getCompany() >> Set.of(mockCompany1)
        noExceptionThrown()
    }

    @Test
    def 'if two companies are attached to seller contract, validation fails'() {
        when:
        brimSellerContractValidateInterceptor.onValidate(mockSellerContract, null)

        then:
        mockSellerContract.getCompany() >> Set.of(mockCompany1, mockCompany2)
        thrown(InterceptorException)
    }
}
