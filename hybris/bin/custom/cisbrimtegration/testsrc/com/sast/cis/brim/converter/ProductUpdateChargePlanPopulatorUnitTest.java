package com.sast.cis.brim.converter;

import com.sast.cis.brim.converter.productupdate.rate.RateGetFactory;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.PendingProductInfoModel;
import com.sast.cis.core.model.PriceDraftModel;
import com.securityandsafetythings.billing.brim.model.product.v1.ChargeGet;
import com.securityandsafetythings.billing.brim.model.product.v1.ProductData;
import com.securityandsafetythings.billing.brim.model.product.v1.RateGet;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class ProductUpdateChargePlanPopulatorUnitTest {

    @Mock
    private RateGetFactory rateGetFactory;

    @InjectMocks
    private ProductUpdateChargePlanPopulator productUpdateChargePlanPopulator;

    @Mock
    private AppLicenseModel appLicense;

    @Mock
    private PendingProductInfoModel pendingProductInfo;

    @Mock
    private PriceDraftModel priceDraft;

    @Mock
    private RateGet rateGet;

    @Before
    public void setup() {
        when(appLicense.getPendingProductInfo()).thenReturn(pendingProductInfo);
        when(pendingProductInfo.getPrices()).thenReturn(Set.of(priceDraft));

        when(rateGetFactory.createRate(appLicense)).thenReturn(rateGet);
    }

    @Test
    public void testPopulate() {
        final ProductData productData = new ProductData();
        productUpdateChargePlanPopulator.populate(appLicense, productData);

        assertThat(productData.getChargePlan()).isEqualTo(new ChargeGet().setRate(rateGet));
        verify(rateGetFactory).createRate(appLicense);
    }

    @Test
    public void givenAppLicenseWithNoPendingProductInfo_whenPopulate_thenSkipRatePopulation() {
        when(appLicense.getPendingProductInfo()).thenReturn(null);

        final ProductData productData = new ProductData();
        productUpdateChargePlanPopulator.populate(appLicense, productData);

        assertThat(productData.getChargePlan()).isNull();
        verify(rateGetFactory, never()).createRate(appLicense);
    }

    @Test
    public void givenPendingProductInfoWithNoPrices_whenPopulate_thenSkipRatePopulation() {
        when(pendingProductInfo.getPrices()).thenReturn(Set.of());

        final ProductData productData = new ProductData();
        productUpdateChargePlanPopulator.populate(appLicense, productData);

        assertThat(productData.getChargePlan()).isNull();
        verify(rateGetFactory, never()).createRate(appLicense);
    }

    @Test
    public void givenPendingProductInfoWithNoPricesButNewChargePlanFlag_whenPopulate_thenPopulate() {
        when(pendingProductInfo.getPrices()).thenReturn(Set.of());
        when(pendingProductInfo.isUseLatestChargePlan()).thenReturn(true);

        final ProductData productData = new ProductData();
        productUpdateChargePlanPopulator.populate(appLicense, productData);

        assertThat(productData.getChargePlan()).isEqualTo(new ChargeGet().setRate(rateGet));
        verify(rateGetFactory).createRate(appLicense);
    }
}
