package com.sast.cis.brim.service.consumer.subscription

import com.sast.cis.brim.service.consumer.SolaceContractUpdateEventConsumer
import com.sast.cis.core.billingintegration.dto.ContractItemData
import com.sast.cis.core.billingintegration.dto.Timeframe
import com.sast.cis.core.billingintegration.dto.subscription.ContractChangeData
import com.sast.cis.core.billingintegration.dto.subscription.ContractUpdateResult
import com.sast.cis.core.billingintegration.dto.subscription.PaymentInformation
import com.sast.cis.core.billingintegration.events.ContractCancelResponseEvent
import com.sast.cis.core.billingintegration.events.ContractCancelRevocationResponseEvent
import com.sast.cis.core.billingintegration.events.ContractConfigChangedResponseEvent
import com.sast.cis.core.billingintegration.events.ContractPaymentChangeResponseEvent
import com.sast.cis.core.dao.CatalogVersion
import com.sast.cis.core.enums.BillingSystemStatus
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.SubscriptionContractModel
import com.sast.cis.test.utils.SampleDataCreator
import com.sast.cis.test.utils.SingleHybrisEventTestListener
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.enums.OrderStatus
import de.hybris.platform.core.model.order.OrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.core.model.product.ProductModel
import de.hybris.platform.servicelayer.ServicelayerSpockSpecification
import de.hybris.platform.servicelayer.cluster.ClusterService
import de.hybris.platform.servicelayer.event.EventService
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.tenant.TenantService
import de.hybris.platform.testframework.RunListeners
import de.hybris.platform.testframework.runlistener.ItemCreationListener
import org.junit.Test
import org.springframework.messaging.Message
import org.springframework.messaging.support.MessageBuilder

import javax.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.Month
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException

@IntegrationTest
@RunListeners([ItemCreationListener])
class SolaceContractUpdateConsumerISpec extends ServicelayerSpockSpecification {
    private static final String EXTERNAL_CONTRACT_REFERENCE = 'VZdXvd42RjOeqkDq1i0RIg'
    private static final String EXTERNAL_REFERENCE = '00343000'
    private static final String PROVIDER_ID = '5056559'
    private static final LocalDate START_DATE = LocalDate.of(2021, Month.FEBRUARY, 11)
    private static final LocalDateTime CONTRACT_START = LocalDateTime.of(START_DATE, LocalTime.of(0, 0, 0))
    private static final LocalDateTime START_OF_TIME_SLICE = LocalDateTime.of(START_DATE, LocalTime.of(6, 0, 0))
    private static final LocalDateTime CONTRACT_END = LocalDateTime.of(LocalDate.of(2022, Month.FEBRUARY, 10), LocalTime.of(22, 59, 59))
    private static final String CONTRACT_ID = "29147"
    private static final String SOLD_TO_PARTY = '**********'
    private static final String PRODUCT_ID = 'A_00177002_SUB'
    private static final String APP_CODE = 'A_00177002'
    private static final String TZ_CET = "CET"

    @Resource
    private TenantService tenantService

    @Resource
    private ClusterService clusterService

    @Resource
    private EventService eventService

    @Resource
    private SolaceContractUpdateEventConsumer contractUpdateEventConsumer

    @Resource
    private ModelService modelService

    @Resource
    private CommonI18NService commonI18NService

    private SampleDataCreator sampleDataCreator = new SampleDataCreator()
    private SingleHybrisEventTestListener<ContractCancelResponseEvent> contractCancelResponseListener
    private SingleHybrisEventTestListener<ContractCancelRevocationResponseEvent> contractCancelRevocationResponseListener
    private SingleHybrisEventTestListener<ContractPaymentChangeResponseEvent> contractPaymentChangeResponseListener
    private SingleHybrisEventTestListener<ContractConfigChangedResponseEvent> contractConfigChangedResponseListener
    private ProductModel product
    private AppLicenseModel license
    private OrderModel order
    private SubscriptionContractModel subscription

    def setup() {
        product = sampleDataCreator.createApp(APP_CODE, "sample.package", CatalogVersion.STAGED)
        license = sampleDataCreator.createSubscriptionAppLicense(product)
        order = sampleDataCreator.createOrder(OrderStatus.CHECKED_VALID)
        OrderEntryModel orderEntry = sampleDataCreator.createOrderEntry(order, license, 1)
        def terminationRule = sampleDataCreator.createTerminationRule(UUID.randomUUID().toString())

        subscription = new SubscriptionContractModel();
        subscription.setCancelIdempotencyKey("3de0cf1e-7c47-4691-93dd-b6fb08706802");
        subscription.setBillingSystemStatus(BillingSystemStatus.NEW)
        subscription.setOrderEntry(orderEntry)
        subscription.setCode(EXTERNAL_CONTRACT_REFERENCE)
        subscription.setContractTerminationRule(terminationRule)

        modelService.saveAll(terminationRule, subscription)

        contractCancelResponseListener = new SingleHybrisEventTestListener<ContractCancelResponseEvent>() {}
        eventService.registerEventListener(contractCancelResponseListener)

        contractCancelRevocationResponseListener = new SingleHybrisEventTestListener<ContractCancelRevocationResponseEvent>() {}
        eventService.registerEventListener(contractCancelRevocationResponseListener)

        contractPaymentChangeResponseListener = new SingleHybrisEventTestListener<ContractPaymentChangeResponseEvent>() {}
        eventService.registerEventListener(contractPaymentChangeResponseListener)

        contractConfigChangedResponseListener = new SingleHybrisEventTestListener<ContractConfigChangedResponseEvent>() {}
        eventService.registerEventListener(contractConfigChangedResponseListener)
    }

    def cleanup() {
        modelService.remove(subscription)
        eventService.unregisterEventListener(contractCancelResponseListener)
        eventService.unregisterEventListener(contractCancelRevocationResponseListener)
        eventService.unregisterEventListener(contractPaymentChangeResponseListener)
        eventService.unregisterEventListener(contractConfigChangedResponseListener)

        contractCancelResponseListener.reset()
        contractCancelRevocationResponseListener.reset()
        contractPaymentChangeResponseListener.reset()
        contractConfigChangedResponseListener.reset()
    }

    @Test
    def 'subscription cancellation succeeds'() {
        given:
        def contractCancelResponse = getMessageFromFile('subscription/contractCancelResponse.json')

        when:
        contractUpdateEventConsumer.handle(contractCancelResponse)
        def lastEvent = contractCancelResponseListener.fetchEvent().get(10L, TimeUnit.SECONDS)
        def contractCancel = lastEvent.contractChangeData

        then:
        lastEvent != null
        contractCancel == getExpectedContractChangeData([])

        verifyAll(subscription) {
            endDate
            billingSystemStatus == BillingSystemStatus.IN_SYNC
        }

    }

    @Test
    def 'subscription cancellation revocation succeeds'() {
        given:
        def contractCancelResponse = getMessageFromFile('subscription/contractCancellationRevocationResponse.json')

        when:
        contractUpdateEventConsumer.handle(contractCancelResponse)
        def lastEvent = contractCancelRevocationResponseListener.fetchEvent().get(10L, TimeUnit.SECONDS)
        def contractChangeData = lastEvent.contractChangeData

        then:
        lastEvent != null
        with(contractChangeData) {
            verifyAll {
                contractChangeData.billingOrderId == PROVIDER_ID
                contractChangeData.id == EXTERNAL_REFERENCE
                contractChangeData.contractUpdateResult == ContractUpdateResult.COMPLETED
                contractChangeData.contractItemData.each { contractItem ->
                    contractItem.timeframe.contractStart == CONTRACT_START
                    !contractItem.timeframe.contractEnd
                }
            }
        }
        with(subscription) {
            verifyAll {
                !subscription.cancelledDate
                !subscription.endDate
                subscription.billingSystemStatus == BillingSystemStatus.IN_SYNC
            }
        }
    }

    @Test
    def 'subscription cancellation response with error logs is consumed'() {
        given:
        def contractCancelResponse = getMessageFromFile('subscription/contractCancelResponse_error.json')
        def expectedContractCancelData = ContractChangeData.builder()
                .billingOrderId(PROVIDER_ID)
                .id(EXTERNAL_REFERENCE)
                .contractUpdateResult(ContractUpdateResult.COMPLETED)
                .contractItemData(List.of(ContractItemData.builder()
                        .externalContractReference(EXTERNAL_CONTRACT_REFERENCE)
                        .configuration([:] as Map)
                        .status('')
                        .paymentInformation(PaymentInformation.builder().build())
                        .build()))
                .errors(["Error Brim does not like your message"])
                .build()

        when:
        contractUpdateEventConsumer.handle(contractCancelResponse)
        def lastEvent = contractCancelResponseListener.fetchEvent().get(10L, TimeUnit.SECONDS)
        def contractCancel = lastEvent.contractChangeData

        then:
        lastEvent != null
        contractCancel == expectedContractCancelData
    }

    @Test
    def 'contract not cancel process response is ignored'() {
        given:
        def contractUpdateResponse = getMessageFromFile('subscription/contractCancelResponse_nonCancelProcess.json')

        when:
        contractUpdateEventConsumer.handle(contractUpdateResponse)
        def lastEvent = contractCancelResponseListener.fetchEvent().get(5L, TimeUnit.SECONDS)

        then:
        thrown(TimeoutException.class)
    }

    @Test
    def 'contract payment update succeeds'() {
        given:
        def paymentUpdateResponse = getMessageFromFile('subscription/contractUpdatePaymentResponse.json')

        when:
        contractUpdateEventConsumer.handle(paymentUpdateResponse)
        def lastEvent = contractPaymentChangeResponseListener.fetchEvent().get(10L, TimeUnit.SECONDS)
        def contractChangeData = lastEvent.contractChangeData

        then:
        lastEvent != null
        verifyAll(contractChangeData) {
            billingOrderId == PROVIDER_ID
            id == EXTERNAL_REFERENCE
            contractUpdateResult == ContractUpdateResult.COMPLETED
            verifyAll(contractItemData[0]) {
                paymentInformation.paymentProvider() == PaymentProvider.DPG
            }
        }
    }

    @Test
    def 'contract config change succeeds'() {
        given:
        def changeConfigResponse = getMessageFromFile('subscription/contractChangeConfigResponse.json')

        when:
        contractUpdateEventConsumer.handle(changeConfigResponse)
        def lastEvent = contractConfigChangedResponseListener.fetchEvent().get(10L, TimeUnit.SECONDS)
        def contractChangeData = lastEvent.contractChangeData

        then:
        lastEvent != null
        verifyAll(contractChangeData) {
            billingOrderId == PROVIDER_ID
            id == EXTERNAL_REFERENCE
            contractUpdateResult == ContractUpdateResult.COMPLETED
            verifyAll(contractItemData[0]) {
                configuration == [
                    CURRENCY: 'EUR',
                    PART_ID: '0',
                    PROD_ACTIV: PRODUCT_ID,
                    QUANTITY: '1.0',
                    SHARE: '100.0',
                    PRICE_CODE_ID: 'Herbert'
                ] as Map
            }
        }
    }

    private Message<String> getMessageFromFile(String filename) {
        def resource = getApplicationContext().getResource("classpath:test/${filename}")
        MessageBuilder.withPayload(resource.getInputStream().getText()).build()
    }

    ContractChangeData getExpectedContractChangeData(List<String> errors) {
        ContractChangeData.builder()
                .billingOrderId(PROVIDER_ID)
                .id(EXTERNAL_REFERENCE)
                .contractUpdateResult(ContractUpdateResult.COMPLETED)
                .contractItemData(List.of(getContractItemData(errors)))
                .errors(errors)
                .build()
    }

    private ContractItemData getContractItemData(List<String> errors) {
        def contractItemDataBuilder = ContractItemData.builder()
                .externalContractReference(EXTERNAL_CONTRACT_REFERENCE)
                .configuration([
                        CURRENCY: 'EUR',
                        PART_ID: '0',
                        PROD_ACTIV: PRODUCT_ID,
                        QUANTITY: '1.0',
                        SHARE: '100.0',
                ] as Map)
                .status("")

        if (!errors.isEmpty()) {
            return contractItemDataBuilder
                    .paymentInformation(PaymentInformation.builder().build())
                    .build()
        }
        contractItemDataBuilder
                .contractId(CONTRACT_ID)
                .soldToParty(SOLD_TO_PARTY)
                .productId(PRODUCT_ID)
                .timeframe(getExpectedTimeframe())
                .paymentInformation(PaymentInformation.builder()
                        .pspPaymentTransactionReference('src_1IJbc8F52mxfPmP1mMXgyKF6')
                        .build())
                .build()
    }

    private Timeframe getExpectedTimeframe() {
        Timeframe.builder()
                .startOfTimeSlice(START_OF_TIME_SLICE)
                .timezone(TZ_CET)
                .contractStart(CONTRACT_START)
                .contractEnd(CONTRACT_END)
                .build()
    }
}
