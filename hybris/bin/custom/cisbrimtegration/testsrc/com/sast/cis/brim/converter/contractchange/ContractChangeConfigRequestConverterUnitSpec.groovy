package com.sast.cis.brim.converter.contractchange

import com.sast.cis.core.CisTimeService
import com.sast.cis.core.addon.ThlAddon
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.SubscriptionContractModel
import com.securityandsafetythings.billing.brim.model.ordercontract.v1.*
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.OrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.core.model.product.ProductModel
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

import java.time.LocalDate
import java.time.LocalDateTime

import static com.sast.cis.brim.constants.CisbrimtegrationConstants.*

@UnitTest
class ContractChangeConfigRequestConverterUnitSpec extends JUnitPlatformSpecification {
    private static final String PRICE_CODE_KEY = 'PRICE_CODE_ID'
    private static final String BRIM_ID = 'ContractBrimID'
    private static final UUID IDEMPOTENCY_KEY = UUID.randomUUID()
    private static final String PRICE_CODE_ID = 'Herbert5000'
    private static final String APPLICENSE_CODE = 'licensecode'
    private static final String THL_APPLICENSE_CODE = 'thllicensecode'
    private static final String THL_USERGROUP = 'thlusergroup'
    private static final String THL_USERGROUP_DISCOUNT = '8.5'
    private static final String BUYER_COMPANY_UID = 'buyercompany'

    private CisTimeService cisTimeService = Mock(CisTimeService)
    private ThlAddon thlAddon = Mock(ThlAddon)
    private ContractChangeConfigRequestConverter contractChangeConfigRequestConverter

    def setup() {
        contractChangeConfigRequestConverter = new ContractChangeConfigRequestConverter(cisTimeService, thlAddon)
        cisTimeService.convertToLocalDateTime(_ as Date) >> LocalDate.now().atTime(0, 0, 0)
        thlAddon.isMasterProductInCart(_ as OrderModel) >> Optional.empty()
    }

    @Test
    void 'given a valid subscription with a price code, it is converted and sent'() {
        given:
        def givenSubscription = validSubscription

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        actualContractChangeRequest == validExpectedRequest
    }

    @Test
    void 'given a valid subscription with null price code, the sent price code is an empty string'() {
        given:
        def givenSubscription = validSubscription
        givenSubscription.setBillingPriceCode(null)
        def expectedContractChangeRequest = validExpectedRequest
        expectedContractChangeRequest.setProcessAtributes(
                List.of(new ProcessAtribute().setName(PRICE_CODE_KEY).setValue("")))

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        actualContractChangeRequest == expectedContractChangeRequest
    }

    @Test
    void 'given a subscription without idempotency key, an exception is thrown'() {
        given:
        def givenSubscription = validSubscription
        givenSubscription.setConfigChangeIdempotencyKey(null)

        when:
        contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        thrown(IllegalStateException)
    }

    @Test
    void 'given a null subscription, an exception is thrown'() {
        when:
        contractChangeConfigRequestConverter.convert(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    void 'given a valid subscription with thl discount, it is converted and sent'() {
        given:
        def givenSubscription = validThlSubscription

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        thlAddon.isMasterProductInCart(_ as OrderModel) >> Optional.of(getValidThlOrderEntry())
        actualContractChangeRequest == validThlExpectedRequest
    }

    @Test
    void 'given a valid subscription with thl discount but no order entry, does not add discount configuration'() {
        given:
        def givenSubscription = validThlSubscription
        givenSubscription.setOrderEntry(null)

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        thrown(IllegalStateException)
    }

    @Test
    void 'given a valid subscription with no usergroup, throws exception'() {
        given:
        def givenCompany = getValidCompany()
        def givenSubscription = validThlSubscription
        givenCompany.setAaCustomerGroup()
        givenSubscription.getOrderEntry().getOrder().setCompany(givenCompany)

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        thlAddon.isMasterProductInCart(_ as OrderModel) >> Optional.of(getValidThlOrderEntry())
        thrown(IllegalStateException)
    }

    @Test
    void 'given a valid subscription with orderentry with no order, throws exception'() {
        given:
        def givenOrderEntry = getValidOrderEntry()
        def givenSubscription = validThlSubscription
        givenOrderEntry.setOrder(null)
        givenSubscription.setOrderEntry(givenOrderEntry)

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        thrown(IllegalStateException)
    }

    @Test
    void 'given a valid thl subscription with applicense without thl discount, discount configuration is not set'() {
        given:
        def givenAppLicense = getValidThlAppLicense()
        def givenSubscription = validThlSubscription
        givenAppLicense.setThlGroupDiscount(null)
        givenSubscription.getOrderEntry().setProduct(givenAppLicense)

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        actualContractChangeRequest == validExpectedRequest
    }

    @Test
    void 'given a valid thl subscription with new master without thl, discount configuration is set to null'() {
        given:
        def givenAppLicense = getValidThlAppLicense()
        def givenSubscription = validThlSubscription
        def givenOrderEntry = validThlOrderEntry
        givenOrderEntry.setAddOnUg("IDW000")
        givenOrderEntry.setProduct(givenAppLicense)
        givenSubscription.setOrderEntry(givenOrderEntry)

        when:
        def actualContractChangeRequest = contractChangeConfigRequestConverter.convert(givenSubscription)

        then:
        thlAddon.isMasterProductInCart(_ as OrderModel) >> Optional.of(getValidThlOrderEntry())
        actualContractChangeRequest == validThlNewExpectedRequest
    }

    private SubscriptionContractModel getValidSubscription() {
        SubscriptionContractModel subscription = new SubscriptionContractModel()
        subscription.setCode(BRIM_ID)
        subscription.setBillingPriceCode(PRICE_CODE_ID)
        subscription.setConfigChangeIdempotencyKey(IDEMPOTENCY_KEY.toString())
        subscription.setOrderEntry(getValidOrderEntry())
        return subscription
    }

    private SubscriptionContractModel getValidThlSubscription() {
        SubscriptionContractModel subscription = new SubscriptionContractModel()
        subscription.setCode(BRIM_ID)
        subscription.setBillingPriceCode(PRICE_CODE_ID)
        subscription.setConfigChangeIdempotencyKey(IDEMPOTENCY_KEY.toString())
        subscription.setOrderEntry(getValidThlOrderEntry())
        subscription.setRevokeCancelIdempotencyKey(null)
        return subscription
    }

    private OrderModel getValidOrder() {
        OrderModel order = new OrderModel()
        order.setCompany(getValidCompany())
        return order
    }

    private IoTCompanyModel getValidCompany() {
        IoTCompanyModel company = new IoTCompanyModel()
        company.setUid(BUYER_COMPANY_UID)
        company.setAaCustomerGroup(getValidUserGroup())
        return company
    }

    private UserGroupModel getValidUserGroup() {
        UserGroupModel userGroup = new UserGroupModel()
        userGroup.setUid(THL_USERGROUP)
        return userGroup
    }

    private OrderEntryModel getValidOrderEntry() {
        OrderEntryModel orderEntry = new OrderEntryModel()
        orderEntry.setProduct(getValidAppLicense())
        orderEntry.setOrder(getValidOrder())
        return orderEntry
    }

    private OrderEntryModel getValidThlOrderEntry() {
        OrderEntryModel orderEntry = getValidOrderEntry()
        orderEntry.setProduct(getValidThlAppLicense())
        orderEntry.setOrder(getValidOrder())
        return orderEntry
    }

    private AppLicenseModel getValidAppLicense() {
        AppLicenseModel appLicense = new AppLicenseModel()
        appLicense.setCode(APPLICENSE_CODE)
        return appLicense
    }

    private AppLicenseModel getValidThlAppLicense() {
        AppLicenseModel appLicense = getValidAppLicense()
        appLicense.setThlGroupDiscount(Map.of(THL_USERGROUP, THL_USERGROUP_DISCOUNT))
        appLicense.setAddonThl(THL_APPLICENSE_CODE)
        return appLicense
    }

    private ContractChangeRequest getValidExpectedRequest() {
        new ContractChangeRequest()
                .setMessageHeader(new MessageHeader()
                        .setIdempotencyKey(IDEMPOTENCY_KEY)
                        .setMode(MessageMode.ASYNC)
                        .setLeadingCustomerFacingSystem('SAST'))
                .setExternalContractReference(BRIM_ID)
                .setProcessAtributes(List.of(new ProcessAtribute().setName(PRICE_CODE_KEY).setValue(PRICE_CODE_ID)))
    }

    private ContractChangeRequest getValidThlExpectedRequest() {
        getValidExpectedRequest()
                .setServiceIdentifier(new ServiceIdentifier().setKey("LK").setValue(BRIM_ID))
                .setProcessAtributes(
                        List.of(
                                new ProcessAtribute().setName(DISC_MONTH).setValue(DISC_MONTH_DEFAULT_VALUE),
                                new ProcessAtribute().setName(FREEMIUM_FLAG).setValue(FREEMIUM_FLAG_VALUE),
                                new ProcessAtribute().setName(DISC_PERCENT).setValue(THL_USERGROUP_DISCOUNT),
                                new ProcessAtribute().setName(PRICE_CODE_KEY).setValue(PRICE_CODE_ID),
                        ))
                .setActivationDate(LocalDate.now().atTime(0, 0, 0))
                .setTimezone(TimeZone.getTimeZone("UTC"))
    }

    private ContractChangeRequest getValidThlNewExpectedRequest() {
        getValidExpectedRequest()
                .setServiceIdentifier(new ServiceIdentifier().setKey("LK").setValue(BRIM_ID))
                .setProcessAtributes(
                        List.of(
                                new ProcessAtribute().setName(DISC_MONTH).setValue(null),
                                new ProcessAtribute().setName(FREEMIUM_FLAG).setValue(null),
                                new ProcessAtribute().setName(DISC_PERCENT).setValue(null),
                                new ProcessAtribute().setName(PRICE_CODE_KEY).setValue(PRICE_CODE_ID),
                        ))
                .setActivationDate(LocalDate.now().atTime(0, 0, 0))
                .setTimezone(TimeZone.getTimeZone("UTC"))
    }
}
