package com.sast.cis.brim.service.consumer.invoice

import com.sast.cis.brim.exception.BrimInvalidMessageException
import com.securityandsafetythings.billing.brim.model.invoice.v1.CategoryType
import com.securityandsafetythings.billing.brim.model.invoice.v1.InvoicePdfNotification
import com.securityandsafetythings.billing.brim.model.invoice.v1.OrderItem
import de.hybris.bootstrap.annotations.UnitTest
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

import java.time.LocalDate

@UnitTest
class InvoiceMessageValidatorUnitSpec extends JUnitPlatformSpecification {
    InvoiceMessageValidator invoicePdfNotificationValidator

    def setup() {
        invoicePdfNotificationValidator = new InvoiceMessageValidator()
    }

    @Test
    def 'fully populated object passes validation'() {
        when:
        invoicePdfNotificationValidator.validate(fullInvoicePdfNotification)

        then:
        noExceptionThrown()
    }

    @Test
    def 'null object does not pass validation'() {
        when:
        invoicePdfNotificationValidator.validate(null)

        then:
        thrown(IllegalArgumentException)
    }

    @Test
    def 'object with missing mandatory fields do not pass validation'() {
        when: 'missing orders'
        invoicePdfNotificationValidator.validate(fullInvoicePdfNotification.setExternalOrders(null))
        then:
        thrown(BrimInvalidMessageException)

        when: 'empty orders'
        invoicePdfNotificationValidator.validate(fullInvoicePdfNotification.setExternalOrders(List.of()))
        then:
        thrown(BrimInvalidMessageException)

        when: 'missing invoiceNumber'
        invoicePdfNotificationValidator.validate(fullInvoicePdfNotification.setInvoiceNumber(null))
        then:
        thrown(BrimInvalidMessageException)

        when: 'missing categoryType'
        invoicePdfNotificationValidator.validate(fullInvoicePdfNotification.setCategoryType(null))
        then:
        thrown(BrimInvalidMessageException)
    }



    @Test
    def 'object with missing optional fields passes validation'() {
        given:
        def invoicePdfNotification = fullInvoicePdfNotification
            .setInvoiceDate(null)
            .setCreatedAt(null)
            .setPayerId(null)

        when:
        invoicePdfNotificationValidator.validate(invoicePdfNotification)

        then:
        noExceptionThrown()
    }

    def getFullInvoicePdfNotification() {
        new InvoicePdfNotification()
            .setCategoryType(CategoryType.invoice)
            .setInvoiceNumber("I1234")
            .setInvoiceDate(LocalDate.now())
            .setExternalOrders(List.of(new OrderItem().setId("A1234")))
            .setCreatedAt(LocalDate.now())
            .setPayerId("01234")
    }
}
