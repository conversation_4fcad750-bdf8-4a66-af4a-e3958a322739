package com.sast.cis.brim.service.consumer

import com.sast.cis.brim.enums.BrimEventType
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test
import org.springframework.messaging.Message

@UnitTest
class SolaceProductCreateConsumerUnitSpec extends JUnitPlatformSpecification {
    BrimJmsMessageConsumer brimMessageConsumer = Mock()

    SolaceProductCreateConsumer solaceProductCreateConsumer

    String dummyPayload = new String("This is a paid invoice json message")
    Message<String> mockMessage

    def setup() {
        solaceProductCreateConsumer = new SolaceProductCreateConsumer(brimMessageConsumer)
        mockMessage = Mock(Message)
        mockMessage.getPayload() >> dummyPayload

    }

    @Test
    def 'message is passed to processor'() {
        when:
        solaceProductCreateConsumer.handle(mockMessage)

        then:
        1 * brimMessageConsumer.consume(mockMessage, BrimEventType.PRODUCT_CREATED)
    }

    @Test
    def 'exception thrown by message processor is not caught'() {
        when:
        solaceProductCreateConsumer.handle(mockMessage)

        then:
        1 * brimMessageConsumer.consume(mockMessage, BrimEventType.PRODUCT_CREATED) >> { throw new RuntimeException() }
        thrown(RuntimeException)
    }
}
