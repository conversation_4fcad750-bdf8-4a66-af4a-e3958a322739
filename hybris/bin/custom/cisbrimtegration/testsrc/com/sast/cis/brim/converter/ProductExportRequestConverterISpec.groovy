package com.sast.cis.brim.converter

import com.sast.cis.core.dao.CatalogVersion
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.RuntimeModel
import com.sast.cis.core.service.customer.developer.DeveloperService
import com.sast.cis.test.utils.SampleDataCreator
import com.securityandsafetythings.billing.brim.model.product.v1.*
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import generated.com.sast.cis.brim.model.BrimSellerInfoBuilder
import org.junit.Test

import javax.annotation.Resource

import static com.sast.cis.core.dao.CatalogVersion.STAGED
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_DEVELOPER_UID
import static com.sast.cis.test.utils.TestDataConstants.AA_PRODUCT_CATALOG
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED

@IntegrationTest
class ProductExportRequestConverterISpec extends ServicelayerTransactionalSpockSpecification {

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Resource
    private ProductExportRequestConverter productExportRequestConverter

    @Resource
    private ModelService modelService

    @Resource
    private DeveloperService developerService

    @Resource
    private FlexibleSearchService flexibleSearchService

    private AppModel aaApp
    private AppModel azenaApp

    private AppLicenseModel fullAaAppLicense
    private AppLicenseModel subscriptionAaAppLicense

    private AppLicenseModel fullAzenaAppLicense
    private AppLicenseModel subscriptionAzenaAppLicense

    private RuntimeModel fullUnlimited
    private RuntimeModel subsUnlimited
    private RuntimeModel full3Years

    void setup() {
        aaApp = createAaApp()
        fullAaAppLicense = sampleDataCreator.createFullAppLicense(aaApp)
        subscriptionAaAppLicense = sampleDataCreator.createSubscriptionAppLicense(aaApp)

        fullUnlimited = findRuntime("runtime_full_unlimited")
        subsUnlimited = findRuntime("runtime_subs_unlimited")
        full3Years = findRuntime("runtime_full_3y")

        fullAaAppLicense.setRuntime(fullUnlimited)
        subscriptionAaAppLicense.setRuntime(subsUnlimited)
        modelService.saveAll(fullAaAppLicense, subscriptionAaAppLicense)

        azenaApp = createAzenaApp()
        fullAzenaAppLicense = sampleDataCreator.createFullAppLicense(azenaApp)
        subscriptionAzenaAppLicense = sampleDataCreator.createSubscriptionAppLicense(azenaApp)
    }

    @Test
    def 'given Azena FULL license when convert then return product with fixed period termination rule and one time rate'() {
        when:
        def result = productExportRequestConverter.convert(fullAzenaAppLicense)

        then:
        result.terminationRule.fixedPeriod == new Duration().setUnit(DurationUnit.DAY).setValue(BigDecimal.ONE)
        !result.terminationRule.initialPeriod
        !result.terminationRule.followUpPeriod
        !result.terminationRule.noticePeriod

        result.chargePlan.rate.oneTime
        result.chargePlan.rate.oneTime.prices.each { price ->
            price.type == PriceFeeType.ONE_TIME_FEE
            !price.variantKey
        }
        !result.chargePlan.rate.recurring
    }

    @Test
    def 'given Azena Subscription license when convert then return product with termination rule and recurring rate'() {
        when:
        def result = productExportRequestConverter.convert(subscriptionAzenaAppLicense)

        then:
        result.terminationRule.initialPeriod == new Duration().setUnit(DurationUnit.YEAR).setValue(BigDecimal.ONE)
        result.terminationRule.followUpPeriod == new Duration().setUnit(DurationUnit.YEAR).setValue(BigDecimal.ONE)
        result.terminationRule.noticePeriod == new Duration().setUnit(DurationUnit.DAY).setValue(BigDecimal.valueOf(30))
        !result.terminationRule.fixedPeriod

        result.chargePlan.rate.recurring
        result.chargePlan.rate.recurring.frequency == RecurringFrequency.YEARLY
        !result.chargePlan.rate.oneTime
    }

    @Test
    def 'given AA Subscription license when convert then return product with period termination rule and recurring rate'() {
        when:
        def result = productExportRequestConverter.convert(subscriptionAaAppLicense)

        then:
        result.terminationRule.initialPeriod == new Duration().setUnit(DurationUnit.YEAR).setValue(BigDecimal.ONE)
        result.terminationRule.followUpPeriod == new Duration().setUnit(DurationUnit.YEAR).setValue(BigDecimal.ONE)
        result.terminationRule.noticePeriod == new Duration().setUnit(DurationUnit.DAY).setValue(BigDecimal.valueOf(56))
        !result.terminationRule.fixedPeriod

        result.chargePlan.rate.recurring
        result.chargePlan.rate.recurring.frequency == RecurringFrequency.YEARLY
        !result.chargePlan.rate.oneTime
    }

    @Test
    def 'given AA Full license with variant runtime when convert then return product with period termination rule and variant rate'() {
        given:
        fullAaAppLicense.setRuntime(full3Years)
        modelService.save(fullAaAppLicense)

        when:
        def result = productExportRequestConverter.convert(fullAaAppLicense)

        then:
        result.terminationRule.fixedPeriod == new Duration().setUnit(DurationUnit.YEAR).setValue(BigDecimal.valueOf(3))

        result.chargePlan.rate.recurring
        result.chargePlan.rate.recurring
        result.chargePlan.rate.recurring.frequency == RecurringFrequency.VARIANT
        result.chargePlan.rate.recurring.prices.each { price ->
            price.type == PriceFeeType.VARIANT
            price.variantKey == VariantKey.VARIANT_3Y
        }
        !result.chargePlan.rate.oneTime
    }

    @Test
    def 'given AA Full license with non variant runtime when convert then return product with period termination rule and one time rate'() {
        when:
        def result = productExportRequestConverter.convert(fullAaAppLicense)

        then:
        result.terminationRule.fixedPeriod == new Duration().setUnit(DurationUnit.DAY).setValue(BigDecimal.valueOf(1))

        result.chargePlan.rate.oneTime
        result.chargePlan.rate.oneTime.prices.each { price ->
            price.type == PriceFeeType.ONE_TIME_FEE
            !price.variantKey
        }
        !result.chargePlan.rate.recurring
    }

    @Test
    def 'given Azena license when convert then return product expected profitCenter and categoryId'() {
        when:
        def result = productExportRequestConverter.convert(fullAzenaAppLicense)

        then:
        result.categoryId == "SAST"
        result.profitCenter == "P00790"
    }

    @Test
    def 'given Azena license when convert then return product with expected channel'() {
        when:
        def result = productExportRequestConverter.convert(fullAzenaAppLicense)

        then:
        result.channel == "SAST"
    }

    @Test
    def 'given AA license when convert then return product expected profitCenter and categoryId'() {
        given:
        modelService.save(fullAaAppLicense)

        when:
        def result = productExportRequestConverter.convert(fullAaAppLicense)

        then:
        result.categoryId == "SAST_AA_ESI_AT"
        result.profitCenter == "A07810"
    }

    @Test
    def 'given AA license when convert then return product with expected channel'() {
        given:
        modelService.save(fullAaAppLicense)

        when:
        def result = productExportRequestConverter.convert(fullAaAppLicense)

        then:
        result.channel == "SAST"
    }

    @Test
    def 'given company with custom seller brim info when convert then return product with seller info channel'() {
        given:
        aaApp.company.brimSellerInfo = BrimSellerInfoBuilder.generate().withChannel("DE21").buildIntegrationInstance()
        modelService.save(fullAaAppLicense)

        when:
        def result = productExportRequestConverter.convert(fullAaAppLicense)

        then:
        result.channel == "DE21"
    }

    @Test
    def 'given AA license when convert then use brimName as product name'() {
        given:
        def brimNameEn = "BRIM Name EN"
        def brimNameDe = "BRIM Name DE"
        fullAaAppLicense.setBrimName(brimNameEn, Locale.ENGLISH)
        fullAaAppLicense.setBrimName(brimNameDe, Locale.GERMAN)
        modelService.save(fullAaAppLicense)

        when:
        def result = productExportRequestConverter.convert(fullAaAppLicense)

        then:
        def names = result.getName()
        names.size() == 2
        names.contains(new Name().setDescription(brimNameEn).setLanguage(IsoLanguage.of("en")))
        names.contains(new Name().setDescription(brimNameDe).setLanguage(IsoLanguage.of("de")))
    }

    @Test
    def 'given AA license with no brim name for language when convert then use english as default'() {
        given:
        def brimNameEn = "BRIM Name EN"
        fullAaAppLicense.setBrimName(brimNameEn, Locale.ENGLISH)
        modelService.save(fullAaAppLicense)

        when:
        def result = productExportRequestConverter.convert(fullAaAppLicense)

        then:
        def names = result.getName()
        names.size() == 2
        names.contains(new Name().setDescription(brimNameEn).setLanguage(IsoLanguage.of("en")))
        names.contains(new Name().setDescription(brimNameEn).setLanguage(IsoLanguage.of("de")))
    }

    @Test
    def 'given Azena license with brim name when convert then use app name as product name'() {
        given:
        def brimName = "BRIM Name EN"
        fullAzenaAppLicense.setBrimName(brimName)
        modelService.save(fullAzenaAppLicense)

        when:
        def result = productExportRequestConverter.convert(fullAzenaAppLicense)

        then:
        def names = result.getName()
        names.size() == 2
        names.contains(new Name().setDescription(azenaApp.getName(Locale.ENGLISH)).setLanguage(IsoLanguage.of("en")))
        names.contains(new Name().setDescription(azenaApp.getName(Locale.GERMAN)).setLanguage(IsoLanguage.of("de")))
    }

    AppModel createAaApp() {
        def developer = developerService.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID)
        sampleDataCreator.createApp("AA_1234", "aa.ProductExportRequestConverterISpec", developer, AA_PRODUCT_CATALOG, STAGED, APPROVED)
    }

    AppModel createAzenaApp() {
        sampleDataCreator.createApp("A_1234", "azena.ProductExportRequestConverterISpec", CatalogVersion.ONLINE)
    }

    RuntimeModel findRuntime(String runtimeCode) {
        def runtime = new RuntimeModel();
        runtime.setCode(runtimeCode);
        flexibleSearchService.getModelByExample(runtime);
    }
}
