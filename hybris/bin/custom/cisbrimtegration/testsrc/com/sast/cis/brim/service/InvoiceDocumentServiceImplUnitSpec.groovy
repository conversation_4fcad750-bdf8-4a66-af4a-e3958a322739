package com.sast.cis.brim.service

import com.sast.cis.brim.http.BrimInvoiceClient
import com.securityandsafetythings.billing.brim.model.invoice.v1.CategoryType
import com.securityandsafetythings.billing.brim.model.invoice.v1.InvoicePdfNotification
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.catalog.model.CatalogUnawareMediaModel
import de.hybris.platform.core.model.media.MediaFolderModel
import de.hybris.platform.servicelayer.exceptions.ModelSavingException
import de.hybris.platform.servicelayer.media.MediaService
import de.hybris.platform.servicelayer.model.ModelService
import org.junit.Test
import org.springframework.http.MediaType
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

@UnitTest
class InvoiceDocumentServiceImplUnitSpec extends JUnitPlatformSpecification {
    private static final String INVOICE_ID = "I12345"

    BrimInvoiceClient mockInvoiceClient
    ModelService mockModelService
    MediaService mockMediaService

    InvoiceDocumentService invoiceDocumentService

    InputStream mockInputStream
    CatalogUnawareMediaModel mockMediaModel
    MediaFolderModel mockMediaFolder

    def setup() {
        mockInvoiceClient = Mock(BrimInvoiceClient)
        mockModelService = Mock(ModelService)
        mockMediaService = Mock(MediaService)
        mockInputStream = Mock(InputStream)
        mockMediaModel = Mock(CatalogUnawareMediaModel)

        invoiceDocumentService = new InvoiceDocumentServiceImpl(mockInvoiceClient, mockModelService, mockMediaService)

        mockInvoiceClient.getInvoicePdf(INVOICE_ID) >> mockInputStream
        mockModelService.create(CatalogUnawareMediaModel.class) >> mockMediaModel
        mockMediaService.getFolder(_ as String) >> mockMediaFolder
    }

    @Test
    @Unroll
    def 'should create media for type #givenCategoryType'() {
        given:
        def givenPdfNotification = new InvoicePdfNotification()
                .setInvoiceNumber(INVOICE_ID)
                .setCategoryType(givenCategoryType)

        def givenExpectedCodePrefix = "${expectedTypePrefix}_${INVOICE_ID}_"

        when:
        def actualMedia = invoiceDocumentService.saveDocument(givenPdfNotification)

        then:
        1 * mockMediaService.getFolder(expectedFolder) >> mockMediaFolder
        actualMedia.is(mockMediaModel)
        1 * mockMediaModel.setFolder(mockMediaFolder)
        1 * mockMediaModel.setMime(MediaType.APPLICATION_PDF_VALUE)
        1 * mockMediaModel.setUsed(true)
        1 * mockMediaModel.setCode({it.startsWith(givenExpectedCodePrefix)})
        1 * mockMediaModel.setRealFileName({it.startsWith(givenExpectedCodePrefix)})
        1 * mockMediaService.setStreamForMedia(mockMediaModel, mockInputStream)
        1 * mockModelService.save(mockMediaModel)

        where:
        givenCategoryType                   || expectedFolder           | expectedTypePrefix
        CategoryType.invoice                || 'invoices'               | 'invoice'
        CategoryType.selfBillingDocument    || 'self-billing-invoices'  | 'self-billing-invoice'
    }

    @Test
    def 'propagates ModelSavingException'() {
        given:
        def givenPdfNotification = new InvoicePdfNotification()
                .setInvoiceNumber(INVOICE_ID)
                .setCategoryType(CategoryType.invoice)

        when:
        invoiceDocumentService.saveDocument(givenPdfNotification)

        then:
        mockModelService.save(mockMediaModel) >> { throw new ModelSavingException('Something went wrong.') }
        thrown(ModelSavingException)
    }
}
