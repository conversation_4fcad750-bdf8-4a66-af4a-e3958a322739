package com.sast.cis.brim.model

import com.sast.cis.core.model.PspSellerAccountModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.interceptor.InterceptorContext
import de.hybris.platform.servicelayer.interceptor.InterceptorException
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification

@UnitTest
class PspSellerAccountValidateInterceptorUnitSpec extends JUnitPlatformSpecification {
    private static final String PSP_ACCOUNT_ID = 'amazingPspAccountId'

    private PspSellerAccountModel pspSellerAccount = Mock(PspSellerAccountModel)
    private BrimSellerContractModel brimSellerContract = Mock(BrimSellerContractModel)
    private BrimSellerContractModel anotherBrimSellerContract = Mock(BrimSellerContractModel)
    private InterceptorContext interceptorContext = Mock(InterceptorContext)

    private PspSellerAccountValidateInterceptor pspSellerAccountValidateInterceptor

    def setup() {
        pspSellerAccountValidateInterceptor = new PspSellerAccountValidateInterceptor()
        pspSellerAccount.getAccountId() >> PSP_ACCOUNT_ID
    }

    @Test
    def 'account passes validation if no seller contract is attached'() {
        given:
        pspSellerAccount.getBrimSellerContract() >> Set.of()

        when:
        pspSellerAccountValidateInterceptor.onValidate(pspSellerAccount, interceptorContext)

        then:
        noExceptionThrown()
    }

    @Test
    def 'account passes validation if one seller contract is attached'() {
        given:
        pspSellerAccount.getBrimSellerContract() >> Set.of(brimSellerContract)

        when:
        pspSellerAccountValidateInterceptor.onValidate(pspSellerAccount, interceptorContext)

        then:
        noExceptionThrown()
    }

    @Test
    def 'account fails validation if two seller contracts are attached'() {
        given:
        pspSellerAccount.getBrimSellerContract() >> Set.of(brimSellerContract, anotherBrimSellerContract)

        when:
        pspSellerAccountValidateInterceptor.onValidate(pspSellerAccount, interceptorContext)

        then:
        def actualException = thrown(InterceptorException)
        actualException.message.contains(PSP_ACCOUNT_ID)
    }
}
