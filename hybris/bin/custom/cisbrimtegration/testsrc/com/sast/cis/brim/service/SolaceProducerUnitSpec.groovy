package com.sast.cis.brim.service

import com.sast.cis.brim.exception.SolaceProducerException
import com.securityandsafetythings.billing.brim.model.product.v1.Product
import de.hybris.bootstrap.annotations.UnitTest
import org.junit.Test
import org.springframework.jms.core.JmsTemplate
import org.springframework.jms.core.MessagePostProcessor
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

@UnitTest
class SolaceProducerUnitSpec extends JUnitPlatformSpecification {

    JmsTemplate jmsTemplate = Mock(JmsTemplate)
    SolaceProducer solaceProducer

    def setup() {
        solaceProducer = new SolaceProducer(jmsTemplate)
    }


    @Unroll
    @Test
    def "test send message validation"() {
        given:
        jmsTemplate.convertAndSend(_ as String, _ as Object, _ as MessagePostProcessor) >> {}

        when:
        solaceProducer.sendMessage(msg, topic)

        then:
        thrown(SolaceProducerException)

        where:
        msg           | topic
        null          | "test"
        new Product() | null
    }

    @Test
    def "test send message fail"() {
        given:
        jmsTemplate.convertAndSend(_ as String, _ as Object, _ as MessagePostProcessor) >> { throw new RuntimeException() }

        when:
        solaceProducer.sendMessage(new Product(), "topic")

        then:
        thrown(RuntimeException)
    }


    @Test
    def "test send message "() {
        given:
        jmsTemplate.convertAndSend(_ as String, _ as Object, _ as MessagePostProcessor) >> {}

        when:
        solaceProducer.sendMessage(new Product(), "test")

        then:
        noExceptionThrown()

    }

}
