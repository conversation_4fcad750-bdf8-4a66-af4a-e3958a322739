package com.sast.cis.brim.service.consumer.paymentstatus

import com.sast.cis.brim.exception.BrimInvalidMessageException
import com.securityandsafetythings.billing.brim.model.paymentstatus.PaymentStatusNotification
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

@UnitTest
class InvoicePaidMessageValidatorUnitSpec extends JUnitPlatformSpecification {
    InvoicePaidMessageValidator paymentstatusMessageValidator

    def setup() {
        paymentstatusMessageValidator = new InvoicePaidMessageValidator()
    }

    @Test
    void 'valid message passes validation'() {
        when:
        paymentstatusMessageValidator.validate(validNotification)

        then:
        noExceptionThrown()
    }

    @Test
    void 'message without sellerInvoiceDocumentNumber fails validation'() {
        given:
        def invalidNotification = validNotification.setSellerInvoiceDocumentNumber(null)

        when:
        paymentstatusMessageValidator.validate(invalidNotification)

        then:
        thrown(BrimInvalidMessageException)
    }

    @Test
    void 'message without buyerInvoiceDocumentNumber fails validation'() {
        given:
        def invalidNotification = validNotification.setBuyerInvoiceDocumentNumber(null)

        when:
        paymentstatusMessageValidator.validate(invalidNotification)

        then:
        thrown(BrimInvalidMessageException)
    }

    @Test
    void 'message without pspAuthorizationReference fails validation'() {
        given:
        def invalidNotification = validNotification.setPspAuthorizationReference(null)

        when:
        paymentstatusMessageValidator.validate(invalidNotification)

        then:
        thrown(BrimInvalidMessageException)
    }

    @Test
    void 'message without pspCaptureReference fails validation'() {
        given:
        def invalidNotification = validNotification.setPspCaptureReference(null)

        when:
        paymentstatusMessageValidator.validate(invalidNotification)

        then:
        thrown(BrimInvalidMessageException)
    }

    @Test
    void 'message without pspPaymentReference fails validation'() {
        given:
        def invalidNotification = validNotification.setPspPaymentReference(null)

        when:
        paymentstatusMessageValidator.validate(invalidNotification)

        then:
        thrown(BrimInvalidMessageException)
    }

    @Test
    void 'null message throws IllegalArgumentException'() {
        when:
        paymentstatusMessageValidator.validate(null)

        then:
        thrown(IllegalArgumentException)
    }

    PaymentStatusNotification getValidNotification() {
        new PaymentStatusNotification()
                .setSellerInvoiceDocumentNumber('sellDocNo')
                .setBuyerInvoiceDocumentNumber('buyDocNo')
                .setPspAuthorizationReference('pspAuthRef')
                .setPspCaptureReference('pspCapRef')
                .setPspPaymentReference('pspPayRef')
    }
}
