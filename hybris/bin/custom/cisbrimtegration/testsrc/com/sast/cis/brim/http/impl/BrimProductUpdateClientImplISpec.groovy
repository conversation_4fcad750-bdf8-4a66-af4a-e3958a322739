package com.sast.cis.brim.http.impl

import com.sast.cis.brim.exception.BrimProductUpdateException
import com.sast.cis.test.utils.BrimulatorRule
import com.securityandsafetythings.billing.brim.model.product.v1.*
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerSpockSpecification
import de.hybris.platform.testframework.RunListeners
import de.hybris.platform.testframework.runlistener.ItemCreationListener
import org.junit.Rule
import org.junit.Test

import javax.annotation.Resource

@IntegrationTest
@RunListeners([ItemCreationListener])
class BrimProductUpdateClientImplISpec extends ServicelayerSpockSpecification {

    private static final String PRODUCT_CODE = "TestProduct_BRIM"
    private static final String SAST = "SAST"
    private static final String CHANNEL = "SAST"
    private static final String NAME_EN = "enName"
    private static final String NAME_DE = "deName"
    private static final String EUR = "EUR"
    private static final String USD = "USD"
    private static final String PRICE_EUR_STRING = "123.45"
    private static final String PRICE_USD_STRING = "234.56"

    @Resource
    private BrimProductUpdateClientImpl brimProductUpdateClientImpl

    @Rule
    private BrimulatorRule brimulatorRule = new BrimulatorRule()


    @Test
    def 'should update product'() {
        when:
        def product = brimProductUpdateClientImpl.updateProduct(createProductData())

        then:
        assert product.id == PRODUCT_CODE
        assert product.name.size() == 2
        assert product.channel == CHANNEL
        assert product.chargePlan.rate.oneTime.paymentType == PaymentType.POSTPAID
        assert product.chargePlan.rate.oneTime.prices.size() == 2
    }

    @Test
    def 'should update product fails due to response code'() {
        when:
        brimulatorRule.prepareProductUpdateSkipResponse(PRODUCT_CODE)
        brimProductUpdateClientImpl.updateProduct(createProductData())

        then:
        thrown(BrimProductUpdateException)
    }


    ProductData createProductData() {
        new ProductData().setId(PRODUCT_CODE)
                .setChargePlan(getChargePlan()).setName(List.of(
                new Name().setLanguage(IsoLanguage.of("EN")).setDescription(NAME_EN),
                new Name().setLanguage(IsoLanguage.of("DE")).setDescription(NAME_DE)))
                .setMessageHeader(new MessageHeader().setMode(MessageMode.ASYNC)
                        .setSenderBusinessSystemId(SAST)
                        .setLeadingCustomerFacingSystem(SAST))
                .setChannel(CHANNEL)

    }

    ChargeGet getChargePlan() {
        new ChargeGet().setRate(new RateGet().setOneTime(new OneTimeGet().setValid(true).setPaymentType(PaymentType.POSTPAID)
                .setPrices(List.of(
                        new PriceGet().setAmount(PRICE_EUR_STRING).setCurrency(Currency.getInstance(EUR)),
                        new PriceGet().setAmount(PRICE_USD_STRING).setCurrency(Currency.getInstance(USD))))))
    }
}
