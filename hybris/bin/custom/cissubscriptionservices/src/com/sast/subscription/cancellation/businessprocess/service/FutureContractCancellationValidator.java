package com.sast.subscription.cancellation.businessprocess.service;

import com.sast.cis.core.model.BuyerContractModel;
import com.sast.subscription.cancellation.businessprocess.exception.FutureContractCancellationException;
import com.sast.subscription.model.FutureContractCancellationBusinessProcessModel;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.time.TimeService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.sast.cis.core.enums.BillingSystemStatus.CANCEL_PENDING;
import static de.hybris.platform.core.enums.OrderStatus.OPEN;
import static org.apache.commons.collections4.ListUtils.emptyIfNull;

/**
 * Validates future contract cancellation at various stages of the process.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class FutureContractCancellationValidator {

    private final FutureContractCancellationBusinessProcessService businessProcessService;
    private final TimeService timeService;

    /**
     * Validates the contract during the business process.
     *
     * @param contract the contract to validate
     */
    public void validateInBusinessProcess(@NonNull final BuyerContractModel contract) {
        LOG.info("Validating contract with code={} for cancellation BP", contract.getCode());
        validateGeneralContractConditions(contract);
        validateParentOrderStatus(contract);
        validateSiblingContractsPendingCancellation(contract);
    }

    /**
     * Validates the contract before initiating the cancellation process.
     *
     * @param contract the contract to validate
     */
    public void validateOnInitiation(@NonNull final BuyerContractModel contract) {
        LOG.info("Validating contract with code={} for initiating cancellation", contract.getCode());
        validateGeneralContractConditions(contract);
        validateParentOrderStatus(contract);
        validateSiblingContractsPendingCancellation(contract);
        validateConcurrentCancellationBpsForParentOrder(contract);

        if (CANCEL_PENDING.equals(contract.getBillingSystemStatus())) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(), "Contract cancellation is already in progress."
            );
        }
    }

    /**
     * Validates the contract before marking it as cancelled.
     *
     * @param contract the contract to validate
     */
    public void validateContractForCancelledState(@NonNull final BuyerContractModel contract) {
        LOG.info("Validating contract with code={} for finalizing cancellation", contract.getCode());
        validateGeneralContractConditions(contract);

        if (!CANCEL_PENDING.equals(contract.getBillingSystemStatus())) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(),
                "Contract is not pending cancellation. Current status: '%s'".formatted(contract.getBillingSystemStatus())
            );
        }
    }

    private void validateGeneralContractConditions(final BuyerContractModel contract) {
        if (contract.getCancelledDate() != null) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(), "Contract is already cancelled. Cancellation date: '%s'".formatted(contract.getCancelledDate())
            );
        }

        if (contract.getEndDate() != null) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(), "Contract has already ended. End date: '%s'".formatted(contract.getEndDate())
            );
        }

        if (!contract.getStartDate().after(timeService.getCurrentTime())) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(), "Contract is not a future contract. Start date: '%s'".formatted(contract.getStartDate())
            );
        }
    }

    private void validateParentOrderStatus(final BuyerContractModel contract) {
        final OrderModel parentOrder = getParentOrder(contract);
        if (!OPEN.equals(parentOrder.getStatus())) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(), "Parent order is not OPEN. Current status: '%s'".formatted(parentOrder.getStatus())
            );
        }
    }

    private void validateSiblingContractsPendingCancellation(final BuyerContractModel contract) {
        final OrderModel parentOrder = getParentOrder(contract);
        final List<BuyerContractModel> pendingContracts = getContractsPendingCancellation(parentOrder)
            .stream()
            .filter(sibling -> !Objects.equals(sibling, contract))
            .toList();

        if (!pendingContracts.isEmpty()) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(),
                "Sibling contracts pending cancellation '%s'".formatted(pendingContracts.stream().map(BuyerContractModel::getCode).toList())
            );
        }
    }

    private void validateConcurrentCancellationBpsForParentOrder(final BuyerContractModel contract) {
        final OrderModel parentOrder = getParentOrder(contract);
        final List<FutureContractCancellationBusinessProcessModel> runningBps =
            businessProcessService.findRunningCancellationBpsForOrder(parentOrder);
        if (!runningBps.isEmpty()) {
            throw FutureContractCancellationException.forContract(
                contract.getCode(),
                "Found running Cancellation BPs for parent order '%s'.".formatted(parentOrder.getCode())
            );
        }
    }

    private List<BuyerContractModel> getContractsPendingCancellation(final OrderModel order) {
        return emptyIfNull(order.getEntries()).stream()
            .flatMap(entry -> entry.getBuyerContracts().stream())
            .filter(this::isPendingCancellation)
            .toList();
    }

    private OrderModel getParentOrder(final BuyerContractModel contract) {
        return ((OrderEntryModel) contract.getOrderEntry()).getOrder();
    }

    private boolean isPendingCancellation(final BuyerContractModel contract) {
        return CANCEL_PENDING.equals(contract.getBillingSystemStatus());
    }
}
