package com.sast.subscription.cancellation.businessprocess.service

import com.sast.cis.core.model.SubscriptionContractModel
import com.sast.subscription.cancellation.businessprocess.dao.FutureContractCancellationBusinessProcessDao
import com.sast.subscription.model.ContractCancellationInfoModel
import com.sast.subscription.model.FutureContractCancellationBusinessProcessModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.OrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.processengine.BusinessProcessEvent
import de.hybris.platform.processengine.BusinessProcessService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.time.TimeService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

import static de.hybris.platform.processengine.enums.ProcessState.*

@UnitTest
class FutureContractCancellationBusinessProcessServiceUnitSpec extends JUnitPlatformSpecification {

    private BusinessProcessService businessProcessService = Mock()
    private FutureContractCancellationBusinessProcessDao futureContractCancellationBusinessProcessDao = Mock()
    private ModelService modelService = Mock()
    private TimeService timeService = Mock()

    private FutureContractCancellationBusinessProcessService cancellationBusinessProcessService

    private FutureContractCancellationBusinessProcessModel cancellationBp = Mock()
    private ContractCancellationInfoModel cancellationInfo = Mock()
    private OrderModel order = Mock()
    private OrderEntryModel orderEntry = Mock()
    private SubscriptionContractModel subscriptionContract = Mock()

    private final String orderCode = "orderCode"
    private final String cancellationBpCode = "cancellationBpCode"
    private final String contractCode = "contractCode"

    void setup() {
        cancellationBusinessProcessService = new FutureContractCancellationBusinessProcessService(
                businessProcessService,
                futureContractCancellationBusinessProcessDao,
                modelService,
                timeService
        )

        order.getCode() >> orderCode
        order.getEntries() >> [orderEntry]
        orderEntry.getOrder() >> order
        orderEntry.getBuyerContracts() >> [subscriptionContract]

        subscriptionContract.getCode() >> contractCode
        subscriptionContract.getOrderEntry() >> orderEntry

        cancellationInfo.getContractToCancel() >> subscriptionContract

        cancellationBp.getCode() >> cancellationBpCode
        cancellationBp.getProcessState() >> WAITING
        cancellationBp.getCancellationInfo() >> cancellationInfo


        futureContractCancellationBusinessProcessDao.findProcessesByOrder(order) >> [cancellationBp]
    }

    def "given BP in WAITING state when find waiting cancellation business process for parent order then return contract BP"() {
        when:
        def ongoingCancellationBp = cancellationBusinessProcessService.findWaitingCancellationBpForOrder(order)

        then:
        ongoingCancellationBp.isPresent()
        ongoingCancellationBp.get() == cancellationBp
    }

    def "given order with multiple cancellation BPs when find then return BP in waiting state"() {
        given:
        def secondCancellationBp = Mock(FutureContractCancellationBusinessProcessModel)
        secondCancellationBp.getProcessState() >> SUCCEEDED

        when:
        def result = cancellationBusinessProcessService.findWaitingCancellationBpForOrder(order)

        then:
        futureContractCancellationBusinessProcessDao.findProcessesByOrder(order) >> [cancellationBp, secondCancellationBp]
        result.isPresent()
        result.get() == cancellationBp
    }

    def "given cancellation business process not in waiting state when find then return empty"() {
        when:
        def ongoingCancellationBp = cancellationBusinessProcessService.findWaitingCancellationBpForOrder(order)

        then:
        cancellationBp.getProcessState() >> SUCCEEDED
        1 * futureContractCancellationBusinessProcessDao.findProcessesByOrder(order) >> [cancellationBp]
        ongoingCancellationBp.isEmpty()
    }

    def "given multiple BPs in waiting state when find then throw exception"() {
        given:
        def secondCancellationBp = Mock(FutureContractCancellationBusinessProcessModel)
        secondCancellationBp.getProcessState() >> WAITING

        when:
        cancellationBusinessProcessService.findWaitingCancellationBpForOrder(order)

        then:
        futureContractCancellationBusinessProcessDao.findProcessesByOrder(order) >> [cancellationBp, secondCancellationBp]
        def ex = thrown(IllegalStateException)
        ex.message.startsWith("Multiple ongoing cancellation business processes found for order")
    }

    def "should trigger brim order rejection response event"() {
        when:
        cancellationBusinessProcessService.triggerBrimOrderRejectionResponseEvent(cancellationBp)

        then:
        1 * businessProcessService.triggerEvent(_ as BusinessProcessEvent) >> { arguments ->
            def event = arguments[0] as BusinessProcessEvent
            event.choice == "success"
            event.event.endsWith("brim-order-rejection-response-event")
        }
    }

    def "given BP not in waiting state when trigger order rejection response event then throw exception"() {
        when:
        cancellationBusinessProcessService.triggerBrimOrderRejectionResponseEvent(cancellationBp)

        then:
        cancellationBp.getProcessState() >> SUCCEEDED
        def ex = thrown(IllegalStateException)
        ex.message == "ALERT: Business process with code '${cancellationBpCode}' is not in waiting state."
    }

    def "should trigger brim order reexport response event"() {
        when:
        cancellationBusinessProcessService.triggerBrimOrderReexportResponseEvent(cancellationBp)

        then:
        1 * businessProcessService.triggerEvent(_ as BusinessProcessEvent) >> { arguments ->
            def event = arguments[0] as BusinessProcessEvent
            event.choice == "success"
            event.event.endsWith("brim-order-reexport-response-event")
        }
    }

    def "given BP not in waiting state when trigger order reexport response event event then throw exception"() {
        when:
        cancellationBusinessProcessService.triggerBrimOrderReexportResponseEvent(cancellationBp)

        then:
        cancellationBp.getProcessState() >> SUCCEEDED
        def ex = thrown(IllegalStateException)
        ex.message == "ALERT: Business process with code '${cancellationBpCode}' is not in waiting state."
    }

    @Unroll
    def "given BP in #givenBpState state when find running cancellation business process for order then return #shouldContainBp"() {
        when:
        def result = cancellationBusinessProcessService.findRunningCancellationBpsForOrder(order)

        then:
        cancellationBp.getProcessState() >> givenBpState
        shouldContainBp ? result.contains(cancellationBp) : !result.contains(cancellationBp)

        where:
        givenBpState | shouldContainBp
        WAITING      | true
        RUNNING      | true
        ERROR        | true
        CREATED      | false
        FAILED       | false
        SUCCEEDED    | false
    }

    def "when prepare bp context then populate parent order and contract code"() {
        when:
        cancellationBusinessProcessService.prepareContractContext(cancellationBp)

        then:
        cancellationBp.getProcessState() >> SUCCEEDED
        1 * cancellationInfo.setParentOrder(order)
        1 * cancellationInfo.setContractCode(contractCode)
        1 * modelService.save(cancellationInfo)
    }
}
