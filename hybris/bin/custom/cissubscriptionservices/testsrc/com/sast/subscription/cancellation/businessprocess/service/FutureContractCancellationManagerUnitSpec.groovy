package com.sast.subscription.cancellation.businessprocess.service

import com.sast.cis.core.model.SubscriptionContractModel
import com.sast.subscription.cancellation.businessprocess.data.CancellationReasonInfo
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.time.TimeService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.SubscriptionContractBuilder
import org.assertj.core.util.DateUtil

import static com.sast.cis.core.enums.BillingSystemStatus.CANCEL_PENDING
import static com.sast.cis.core.enums.BillingSystemStatus.CANCELLED_BEFORE_EXPORT
import static org.assertj.core.util.DateUtil.tomorrow
import static org.assertj.core.util.DateUtil.yesterday

@UnitTest
class FutureContractCancellationManagerUnitSpec extends JUnitPlatformSpecification {

    private FutureContractCancellationValidator futureContractCancellationValidator = Mock()

    private FutureContractCancellationBusinessProcessService futureContractCancellationBusinessProcessService = Mock()

    private ModelService modelService = Mock()

    private TimeService timeService = Mock()

    private FutureContractCancellationManager futureContractCancellationManager

    private SubscriptionContractModel contract

    private CancellationReasonInfo cancellationReasonInfo

    private Date currentTime = DateUtil.now()

    void setup() {
        futureContractCancellationManager = new FutureContractCancellationManager(
                futureContractCancellationValidator,
                futureContractCancellationBusinessProcessService,
                modelService,
                timeService
        )

        contract = SubscriptionContractBuilder.generate()
                .withStartDate(tomorrow())
                .buildInstance()

        cancellationReasonInfo = new CancellationReasonInfo("Reason", "Extension")

        timeService.getCurrentTime() >> currentTime
    }

    def "given contract when initiate cancellation then start bp and set status to CANCEL_PENDING"() {
        when:
        futureContractCancellationManager.initiateFutureContractCancellation(contract, cancellationReasonInfo)

        then:
        contract.billingSystemStatus == CANCEL_PENDING
        contract.cancelIdempotencyKey
        contract.cancellationReason == cancellationReasonInfo.cancellationReason()
        contract.cancellationReasonExtension == cancellationReasonInfo.cancellationReasonExtension()
        1 * futureContractCancellationBusinessProcessService.startCancellationBusinessProcess(contract)
    }

    def "given contract when mark contract as cancelled then set status to CANCELLED_BEFORE_EXPORT and populate end and cancelled dates"() {
        given:
        contract.billingSystemStatus = CANCEL_PENDING

        when:
        futureContractCancellationManager.markFutureContractAsCancelled(contract)

        then:
        contract.billingSystemStatus == CANCELLED_BEFORE_EXPORT
        contract.cancelledDate == currentTime
        contract.endDate == currentTime
    }

    def "given contract with future start date when check for cancellation eligibility then return true"() {
        given:
        contract.startDate = tomorrow()

        when:
        def result = futureContractCancellationManager.isEligibleForFutureContractCancellation(contract)

        then:
        result
    }

    def "given contract with past start date when check for cancellation eligibility then return false"() {
        given:
        contract.startDate = yesterday()

        when:
        def result = futureContractCancellationManager.isEligibleForFutureContractCancellation(contract)

        then:
        !result
    }
}
