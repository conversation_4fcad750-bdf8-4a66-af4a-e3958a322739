<?xml version="1.0" encoding="ISO-8859-1"?>

<items xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:noNamespaceSchemaLocation="items.xsd">
    <!-- NOTE: typecodes for this extension should start at 23100 -->
    <itemtypes>

        <itemtype code="CartDisplayComponent" extends="SimpleCMSComponent"
                  jaloclass="com.sast.cis.shop.cms.jalo.CartDisplayComponent"
                  generate="true" autocreate="true">
            <description>A component that displays cart information</description>
        </itemtype>

        <itemtype code="HeaderNavigationNode" extends="CMSNavigationNode"
                  jaloclass="com.sast.cis.shop.cms.jalo.HeaderNavigationNode"
                  autocreate="true" generate="true">
            <attributes>
                <attribute qualifier="active" type="boolean">
                    <defaultvalue>Boolean.FALSE</defaultvalue>
                    <persistence type="property" />
                    <modifiers optional="false" />
                </attribute>
            </attributes>
        </itemtype>

        <itemtype code="ProductDetailsHeaderComponent" extends="SimpleCMSComponent"
                  jaloclass="com.sast.cis.shop.cms.jalo.ProductDetailsHeaderComponent"
                  autocreate="true" generate="true">
            <description>CMS Component for product details header</description>
        </itemtype>

        <itemtype code="ProductDetailsStickyFooterComponent" extends="SimpleCMSComponent"
                  jaloclass="com.sast.cis.shop.cms.jalo.ProductDetailsStickyFooterComponent"
                  autocreate="true" generate="true">
            <description>CMS Component for product details sticky footer</description>
        </itemtype>

    </itemtypes>
</items>
