<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--
 [y] hybris Platform

 Copyright (c) 2017 SAP SE or an SAP affiliate company.  All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
--><!--
 All hybris buildcallbacks.xml macrodefinitions:
 
 Build/Documentation
 
	 before/after ant macro "clean"
		 <macrodef name="cisshopcms_before_clean"/>
		 <macrodef name="cisshopcms_after_clean"/>
	 
	 before/after ant macro "build"
		 <macrodef name="cisshopcms_before_build"/>
		 <macrodef name="cisshopcms_after_build"/>
		 
	 before/after ant macro "compile_core" - the core module of the extension
		 <macrodef name="cisshopcms_before_compile_core">
		 <macrodef name="cisshopcms_after_compile_core">
	 
	 before/after ant macro "compile_web" - the web module of the extension
		 <macrodef name="cisshopcms_before_compile_web" />
		 <macrodef name="cisshopcms_after_compile_web" />
	 
	 before/after ant macro "compile_hmc" - the hmc module of the extension
		 <macrodef name="cisshopcms_before_compile_hmc" />
		 <macrodef name="cisshopcms_after_compile_hmc" />
 
 Preparing extension
 
	 will be called in the beginning of the ant call and only once (also when using multiple 
	 ant targets e.g. ant build yunittest)	
		 <macrodef name="cisshopcms_only_once_prepare"/>
 
 Creating ear module/production
 
	 before/after ant macro "ear"
		 <macrodef name="cisshopcms_before_ear"/>
		 <macrodef name="cisshopcms_after_ear"/>

	 before/after ant macro "production" - for hybris server only
		 <macrodef name="cisshopcms_before_production" />
		 <macrodef name="cisshopcms_after_production" />
 
 JUnit Test
 
	 before/after ant macro "yunitinit" 
		 <macrodef name="cisshopcms_before_yunitinit" />
		 <macrodef name="cisshopcms_after_yunitinit" />
	 
	 before/after ant macro "yunit"
		 <macrodef name="cisshopcms_before_yunit" />
		 <macrodef name="cisshopcms_after_yunit" /> 
		 
 Distribution package
 
 	 before/after ant macro "dist" - internal target; only for use when platform is available in source code
		 <macrodef name="cisshopcms_after_dist"/>
		 <macrodef name="cisshopcms_before_dist"/>
	 
	 before/after ant macro "dist_copy" - internal target; only for use when platform is available in source code
		 <macrodef name="cisshopcms_before_dist_copy"/>
		 <macrodef name="cisshopcms_after_dist_copy"/>
		  
 	 With these filters you can override the default extension filters defined in platform/resources/ant/dist/filtersets.xml
 	  	 <patternset id="extension.cisshopcms.binary.filter">
 	  	  	 <patternset refid="extension.filter" />
 	  	  	 <exclude name="**/*-source.jar" />
 	  	 </patternset>
 	  	 <patternset id="extension.cisshopcms.source.filter">
 	  	  	 <exclude name="**/bin/**" />
 	  	 </patternset>
     With this filter you can decide what should be excluded from development zip.
 	  	 <patternset id="extension.cisshopcms.devzip.filter">
 	  	 Include all files from extension.source.filter.
 	  	     <patternset refid="extension.source.filter" />
         Exclude unwanted files.
 	  	  	 <exclude name="lib/exclude-me.jar" />
 	  	 </patternset>
 
--><project name="cisshopcms_buildcallbacks">

	<!-- 
	    Called whenever 'ant ear' is used. this callback can be used to modify the content of the ear file
	
	     ${ear.path}: 			path to ear
	 -->
	<macrodef name="cisshopcms_before_ear">
		<sequential>

			<!-- you can do anything before the EAR file is being packed -->

		</sequential>
	</macrodef>

</project>
