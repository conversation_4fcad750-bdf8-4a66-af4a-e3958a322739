package com.sast.cis.migration.setup;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.hash.HashCode;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import com.google.common.io.Files;
import com.sast.cis.migration.constants.CismigrationConstants;
import com.sast.cis.migration.enums.HashType;
import com.sast.cis.migration.model.MigrationFileModel;
import de.hybris.platform.commerceservices.setup.AbstractSystemSetup;
import de.hybris.platform.core.initialization.SystemSetup;
import de.hybris.platform.core.initialization.SystemSetupContext;
import de.hybris.platform.core.initialization.SystemSetupParameter;
import de.hybris.platform.core.initialization.SystemSetupParameterMethod;
import de.hybris.platform.scripting.engine.ScriptExecutable;
import de.hybris.platform.scripting.engine.ScriptExecutionResult;
import de.hybris.platform.scripting.engine.ScriptingLanguagesService;
import de.hybris.platform.scripting.engine.exception.ScriptExecutionException;
import de.hybris.platform.servicelayer.cronjob.CronJobService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.io.UncheckedIOException;
import java.util.Collection;
import java.util.List;

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG;

@SystemSetup(extension = CismigrationConstants.EXTENSIONNAME)
public class CismigrationSystemSetup extends AbstractSystemSetup {

    private static final Logger LOG = LoggerFactory.getLogger(CismigrationSystemSetup.class);

    private static final String SYNC_CONTENT_CATALOGS = "syncContentCatalogs";
    private static final String SYNC_PRODUCT_CATALOG = "syncProductCatalog";

    private static final String SYNC_AA_CONTENT_CATALOGS = "syncAAContentCatalogs";
    private static final String SYNC_AA_PRODUCT_CATALOG = "syncAAProductCatalog";
    private static final String SYNC_AA_PRODUCT_CATALOG_v2 = "syncAAProductCatalogV2";

    private static final String FULL_INDEX_CRON_JOB = "full-index-cronJob";
    private static final String FULL_AA_INDEX_CRON_JOB = "full-aa-index-cronJob";

    @Resource
    private CronJobService cronJobService;

    @Resource
    private FlexibleSearchService flexibleSearchService;

    @Resource
    private ModelService modelService;

    @Resource
    private ScriptingLanguagesService scriptingLanguagesService;

    @SystemSetup(type = SystemSetup.Type.PROJECT, process = SystemSetup.Process.ALL)
    public void createProjectData(final SystemSetupContext context) {
        File impexDirectory = new File(getImpexRoot()).getParentFile();
        importImpexFilesAndSyncContentCatalogs(context, impexDirectory);
    }

    @VisibleForTesting
    protected void executeGroovyScript(File file) {
        String classPath = "classpath:/" + getRelativePath(file);
        ScriptExecutable executable = scriptingLanguagesService.getExecutableByURI(classPath);
        final ScriptExecutionResult result = executable.execute();
        if (!result.isSuccessful()) {
            LOG.error("!!!Failed to execute groovy script, filename:{}", file.getName());
            throw new ScriptExecutionException("!!!Failed to execute groovy script, filename:" + file.getName());
        } else {
            StringBuffer buffer = ((StringWriter) result.getErrorWriter()).getBuffer();
            if (buffer.length() > 0) {
                LOG.error("!!!Failed to execute groovy script, filename:{}, errorMsg:{}", file.getName(), buffer);
                throw new ScriptExecutionException("!!!Failed to execute groovy script, filename:" + file.getName() + ", errorMsg:"+ buffer);
            }
        }
    }

    @VisibleForTesting
    protected String getImpexRoot() {
        return getClass().getResource("/impex/release_0_18").getPath();
    }

    private void importImpexFilesAndSyncContentCatalogs(SystemSetupContext context, File directory) {
        Collection<File> files = FileUtils.listFiles(directory, new String[] { "impex", "groovy" }, true);
        files.stream().sorted().forEach(f -> checkAndImportImpexFile(context, f));

        syncCisCatalogs(context);
        syncAaCatalogs(context);
    }

    private void syncCisCatalogs(final SystemSetupContext context) {
        if (getBooleanSystemSetupParameter(context, SYNC_CONTENT_CATALOGS)) {
            executeCatalogSyncJob(context, "integratorContentCatalog");
        }

        if (getBooleanSystemSetupParameter(context, SYNC_PRODUCT_CATALOG)) {
            executeCatalogSyncJob(context, CIS_PRODUCT_CATALOG);
            cronJobService.performCronJob(cronJobService.getCronJob(FULL_INDEX_CRON_JOB), true);
        }
    }

    private void syncAaCatalogs(final SystemSetupContext context) {
        if (getBooleanSystemSetupParameter(context, SYNC_AA_CONTENT_CATALOGS)) {
            executeCatalogSyncJob(context, "aaContentCatalog");
        }

        if (getBooleanSystemSetupParameter(context, SYNC_AA_PRODUCT_CATALOG)) {
            executeCatalogSyncJob(context, "aaProductCatalog");
            cronJobService.performCronJob(cronJobService.getCronJob(FULL_AA_INDEX_CRON_JOB), true);
        }

        if (getBooleanSystemSetupParameter(context, SYNC_AA_PRODUCT_CATALOG_v2)) {
            executeCatalogSyncJob(context, "aav2ProductCatalog");
            cronJobService.performCronJob(cronJobService.getCronJob(FULL_AA_INDEX_CRON_JOB), true);
        }
    }

    private void checkAndImportImpexFile(SystemSetupContext context, File file) {
        String hashCode = computeHashCode(file);
        String release = getRelease(file);
        String filename = file.getName();

        List<MigrationFileModel> migrationFiles = getMigrationFile(release, filename);

        if (migrationFiles.isEmpty()) {
            if (isHashAlreadyPresent(hashCode)) {
                LOG.error("File with filename={} has hashCode={} which is already present in the DB. Skipping import.", filename, hashCode);
                return;
            }
            if (filename.endsWith(".groovy")) {
                executeGroovyScript(file);
            } else {
                importImpexFile(context, getRelativePath(file));
            }
            persistNewMigration(hashCode, filename, release);
        } else {
            doAdditionalChecks(hashCode, release, filename, migrationFiles);
            LOG.info("Skipping migration file {} because it has already been imported", file.getPath());
        }
    }

    private boolean isHashAlreadyPresent(String hashCode) {
        MigrationFileModel migrationFile = new MigrationFileModel();
        migrationFile.setHash(hashCode);
        List<MigrationFileModel> migrationFiles = flexibleSearchService.getModelsByExample(migrationFile);
        return !migrationFiles.isEmpty();
    }

    private List<MigrationFileModel> getMigrationFile(String release, String filename) {
        MigrationFileModel migrationFile = new MigrationFileModel();
        migrationFile.setFileName(filename);
        migrationFile.setRelease(release);
        return flexibleSearchService.getModelsByExample(migrationFile);
    }

    private void doAdditionalChecks(String hashCode, String release, String filename, List<MigrationFileModel> modelsByExample) {
        if (modelsByExample.size() != 1) {
            LOG.error("Found too many potential files for filename={} and release={}", filename, release);
        }

        String persistedHashCode = modelsByExample.get(0).getHash();
        if (!persistedHashCode.equals(hashCode)) {
            LOG.error("Hash code of current version does not match with version in database for filename={} and release={}", filename,
                release);
        }
    }

    private void persistNewMigration(String hashCode, String filename, String release) {
        LOG.info("Persisting new migration with filename={} for release={}", filename, release);
        MigrationFileModel migrationFile = modelService.create(MigrationFileModel.class);
        migrationFile.setFileName(filename);
        migrationFile.setHash(hashCode);
        migrationFile.setRelease(release);
        migrationFile.setHashType(HashType.SHA256);
        modelService.save(migrationFile);
    }

    private String getRelativePath(File file) {
        return file.getAbsolutePath().split("/resources")[1];
    }

    private String getRelease(File file) {
        String manualImpexFolder = file.getAbsolutePath().split("/impex")[1];
        String release = manualImpexFolder.split("/")[1];
        if (!release.contains("release")) {
            LOG.error("Folder schema does not match anymore.");
        }
        return release;
    }

    private String computeHashCode(File file) {
        HashFunction hashFunction = Hashing.sha256();
        try {
            HashCode hash = Files.asByteSource(file).hash(hashFunction);
            return hash.toString();
        } catch (IOException e) {
            LOG.error("FAILED", e);
            throw new UncheckedIOException(e);
        }
    }

    @Override
    @SystemSetupParameterMethod
    public List<SystemSetupParameter> getInitializationOptions() {
        return ImmutableList.of(
            createBooleanSystemSetupParameter(SYNC_CONTENT_CATALOGS, "Synchronize Content Catalogs", true),
            createBooleanSystemSetupParameter(SYNC_PRODUCT_CATALOG, "Synchronize Product Catalog and Run Full Solr", true),
            createBooleanSystemSetupParameter(SYNC_AA_CONTENT_CATALOGS, "Synchronize AA Content Catalogs", true),
            createBooleanSystemSetupParameter(SYNC_AA_PRODUCT_CATALOG, "Synchronize AA Product Catalog and Run Full Solr", true)
        );
    }

    @VisibleForTesting
    void setFlexibleSearchService(FlexibleSearchService flexibleSearchService) {
        this.flexibleSearchService = flexibleSearchService;
    }

    @VisibleForTesting
    void setModelService(ModelService modelService) {
        this.modelService = modelService;
    }
}
