package groovy_manual.brim

import com.sast.cis.core.billingintegration.request.OrderExport
import com.sast.cis.core.dao.CisOrderDao
import de.hybris.platform.servicelayer.model.ModelService

/*
Reject an order to BRIM.
 */

class RejectOderInBrim {
    CisOrderDao cisOrderDao
    OrderExport orderExport
    ModelService modelService

    def rejectOrder(String orderId) {
        println "Trying to reject order ${orderId} in brim."

        def order = cisOrderDao.findOrderForCode(orderId)
                .orElseThrow({ new RuntimeException("Could not find order with ID ${orderId}") })
        
        orderExport.rejectOrder(order)
        println "Order ${order.getCode()} rejection request sent to brim with idempotency key ${order.getIdempotencyKey()}."
    }

    def run() {
        // Set the order id here.
        String ORDER_ID = "01307000"


        rejectOrder(ORDER_ID)
    }
}

new RejectOderInBrim(cisOrderDao: cisOrderDao, orderExport: orderExport, modelService: modelService).run()
