package groovy_manual.release_1_34

import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.payment.boschtransfer.model.BoschSellerAccountModel
import com.sast.cis.payment.boschtransfer.model.BoschSepaCollectionAccountModel
import de.hybris.platform.servicelayer.model.ModelService

class CreateBoschTransferSellerAccounts {
    private IotCompanyService iotCompanyService
    private ModelService modelService

    private createSeller(SellerAccountData sellerAccountData) {
        IoTCompanyModel company = iotCompanyService.getCompanyByUid(sellerAccountData.companyId).orElseThrow()

        if (!company.getPspSellerAccounts().findAll {it instanceof BoschSellerAccountModel}.isEmpty()) {
            System.out.println("Company ${company.uid} (${company.name} - ${company.country.isocode}) already has BOSCH_TRANSFER seller account")
            return
        }

        BoschSepaCollectionAccountModel sepaCollectionAccount = modelService.create(BoschSepaCollectionAccountModel.class)
        sepaCollectionAccount.setIban(sellerAccountData.iban)
        sepaCollectionAccount.setBic(sellerAccountData.bic)
        sepaCollectionAccount.setBankName(sellerAccountData.bankName)
        modelService.save(sepaCollectionAccount)

        BoschSellerAccountModel sellerAccount = modelService.create(BoschSellerAccountModel.class);
        sellerAccount.setCompany(company);
        sellerAccount.setSepaCollectionAccount(sepaCollectionAccount)
        sellerAccount.setStatus(PspSellerAccountStatus.ACTIVE)
        modelService.save(sellerAccount)

        System.out.println("Company ${company.uid} (${company.name} - ${company.country.isocode}) has new seller account ${sellerAccount.accountId}")
    }

    private class SellerAccountData {
        private final String companyId
        private final String iban
        private final String bic
        private final String bankName

        SellerAccountData(String companyId, String iban, String bic, String bankName) {
            this.companyId = companyId
            this.iban = iban
            this.bic = bic
            this.bankName = bankName
        }
    }

    def run() {
        [
                new SellerAccountData('18231ebc-ce34-4a34-b51e-a17806cb18ae', '**********************', 'COBADEFFXXX', 'Commerzbank Gütersloh'),  // DE
        ].each { createSeller(it) }
    }
}

new CreateBoschTransferSellerAccounts(
        iotCompanyService: iotCompanyService,
        modelService: modelService
).run()
