package groovy_manual.release_1_38

import com.sast.cis.core.model.AppLicenseModel
import de.hybris.platform.core.model.order.AbstractOrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import groovy.util.logging.Slf4j

@Slf4j
class FindOrdersForProductsWithInactivePrices {
    FlexibleSearchService flexibleSearchService

    private List<OrderModel> getAaOrders() {
        FlexibleSearchQuery query = new FlexibleSearchQuery(
                "SELECT {om.pk} FROM { Order as om" +
                        " JOIN BaseStore as store on {om.store} = {store.pk}}" +
                        " WHERE {store.uid} = 'aastore'")
        query.setResultClassList(List.of(OrderModel.class))
        return flexibleSearchService.search(query).getResult() as List<OrderModel>
    }

    private void checkOrder(OrderModel order) {
        def buyerCompany = order.company
        def group = buyerCompany.aaCustomerGroup
        if (group.uid == 'IDW000') {
            return
        }
        for (AbstractOrderEntryModel orderEntry : order.entries) {
            def product = (AppLicenseModel) orderEntry.product
            def ugPrice = product.currentlyValidPrices.find { it.ug == group.userPriceGroup }
            if (ugPrice == null) {
                println("No active price found for order=${order.code}, product=${product.code} and group=${group.uid}")
            }
        }
    }

    def run() {
        def orders = getAaOrders()
        orders.forEach { checkOrder(it) }
    }
}

new FindOrdersForProductsWithInactivePrices(
        flexibleSearchService: flexibleSearchService
).run()
