package groovy_manual.release_0_88

import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.model.AppLicenseModel
import de.hybris.platform.core.enums.PaymentStatus
import de.hybris.platform.core.model.order.OrderEntryModel
import de.hybris.platform.core.model.order.OrderModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import groovy.util.logging.Slf4j

@Slf4j
class MigrateSubscriptionOrdersPaymentStatus {

    FlexibleSearchService flexibleSearchService
    ModelService modelService

    boolean dryRun

    def migrateOrdersStatus(int batchSize) {
        FlexibleSearchQuery query = new FlexibleSearchQuery(
                "SELECT DISTINCT { order.${OrderModel.PK} } " +
                        "FROM { " +
                        "${OrderModel._TYPECODE} AS order " +
                        "JOIN ${PaymentStatus._TYPECODE} AS status " +
                        " ON { order:${OrderModel.PAYMENTSTATUS} } = { status:pk } " +
                        "JOIN ${OrderEntryModel._TYPECODE} AS orderEntry " +
                        " ON  { order.${OrderModel.PK} } = { orderEntry.${OrderEntryModel.ORDER} } " +
                        "JOIN ${AppLicenseModel._TYPECODE} AS appLicense " +
                        " ON  { orderEntry.${OrderEntryModel.PRODUCT} } = { appLicense.${AppLicenseModel.PK} } " +
                        "JOIN ${LicenseType._TYPECODE} AS licenseType " +
                        " ON  { appLicense.${AppLicenseModel.LICENSETYPE} } = { licenseType:pk } " +
                        "} " +
                        "WHERE { licenseType:code } = '${LicenseType.SUBSCRIPTION}' " +
                        " AND { status:code } = '${PaymentStatus.PAID}' " +
                        "ORDER BY { order.${OrderModel.PK} } DESC"
        )
        query.setCount(batchSize)
        query.setResultClassList(List.of(OrderModel.class))
        List<OrderModel> subscriptionOrders = flexibleSearchService.search(query).getResult()
        log.info("Migrating {} Orders", subscriptionOrders.size())
        for (OrderModel order : subscriptionOrders) {
            log.info("Migration Order {}", order.code)
            if (!dryRun) {
                order.setPaymentStatus(PaymentStatus.PAID_RECURRING)
                order.setModifiedtime(new Date())
                modelService.save(order)
            }
        }
    }

    def run() {
        def batchSize = 10
        migrateOrdersStatus(batchSize)
    }
}

boolean dryRun = true

new MigrateSubscriptionOrdersPaymentStatus(
        flexibleSearchService: flexibleSearchService,
        modelService: modelService,
        dryRun: dryRun
).run()
