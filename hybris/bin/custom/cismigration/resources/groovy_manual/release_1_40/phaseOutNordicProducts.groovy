package groovy_manual.release_1_40

import com.sast.cis.core.enums.LicenseAvailabilityStatus
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.service.AppLicenseService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import groovy.util.logging.Slf4j
/**
 * RUN CATALOG SYNC AFTER RUNNING THIS SCRIPT!
 */

@Slf4j
class PhaseOutNordicProducts {

    private static final String AAV2_STAGED_CATALOG_ID = "8796158624345" // aav2 staged catalog

    // This list is consists product codes from the file attached to the EB-17245 Jira ticket
    private static final List<String> PHASED_OUT_PRODUCTS = List.of(
            "AA2_0451687P15130",
            "AA2_0451987P12140",
            "AA2_0451987P12255",
            "AA2_0451987P12263",
            "AA2_0451987P12280",
            "AA2_0451987P12402",
            "AA2_0451987P12913",
            "AA2_0451987P12973",
            "AA2_0451987P12988",
            "AA2_0451987P12993",
            "AA2_0461687P15130",
            "AA2_0461987P12140",
            "AA2_0461987P12255",
            "AA2_0461987P12263",
            "AA2_0461987P12280",
            "AA2_0461987P12402",
            "AA2_0461987P12913",
            "AA2_0461987P12973",
            "AA2_0461987P12988",
            "AA2_0461987P12993",
            "AA2_0471687P15130",
            "AA2_0471987P12140",
            "AA2_0471987P12255",
            "AA2_0471987P12263",
            "AA2_0471987P12280",
            "AA2_0471987P12402",
            "AA2_0471987P12913",
            "AA2_0471987P12973",
            "AA2_0471987P12988",
            "AA2_0471987P12993",
            "AA2_3541687P15130",
            "AA2_3541987P12140",
            "AA2_3541987P12255",
            "AA2_3541987P12263",
            "AA2_3541987P12280",
            "AA2_3541987P12402",
            "AA2_3541987P12913",
            "AA2_3541987P12973",
            "AA2_3541987P12988",
            "AA2_3541987P12993",
            "AA2_3581687P15130",
            "AA2_3581987P12140",
            "AA2_3581987P12255",
            "AA2_3581987P12263",
            "AA2_3581987P12280",
            "AA2_3581987P12402",
            "AA2_3581987P12822",
            "AA2_3581987P12823",
            "AA2_3581987P12843",
            "AA2_3581987P12846",
            "AA2_3581987P12913",
            "AA2_3581987P12973",
            "AA2_3581987P12988",
            "AA2_3581987P13523",
            "AA2_3701687P15130",
            "AA2_3701987729274",
            "AA2_3701987P12140",
            "AA2_3701987P12255",
            "AA2_3701987P12263",
            "AA2_3701987P12280",
            "AA2_3701987P12294",
            "AA2_3701987P12402",
            "AA2_3701987P12412",
            "AA2_3701987P12822",
            "AA2_3701987P12823",
            "AA2_3701987P12843",
            "AA2_3701987P12846",
            "AA2_3701987P12913",
            "AA2_3701987P12973",
            "AA2_3701987P12988",
            "AA2_3701987P12993",
            "AA2_3711687P15130",
            "AA2_3711987729274",
            "AA2_3711987P12140",
            "AA2_3711987P12255",
            "AA2_3711987P12263",
            "AA2_3711987P12280",
            "AA2_3711987P12294",
            "AA2_3711987P12402",
            "AA2_3711987P12412",
            "AA2_3711987P12822",
            "AA2_3711987P12823",
            "AA2_3711987P12843",
            "AA2_3711987P12846",
            "AA2_3711987P12913",
            "AA2_3711987P12973",
            "AA2_3711987P12988",
            "AA2_3711987P12993",
            "AA2_3721687P15130",
            "AA2_3721987729274",
            "AA2_3721987P12140",
            "AA2_3721987P12255",
            "AA2_3721987P12263",
            "AA2_3721987P12280",
            "AA2_3721987P12294",
            "AA2_3721987P12402",
            "AA2_3721987P12412",
            "AA2_3721987P12822",
            "AA2_3721987P12823",
            "AA2_3721987P12843",
            "AA2_3721987P12846",
            "AA2_3721987P12913",
            "AA2_3721987P12973",
            "AA2_3721987P12988",
            "AA2_3721987P12993")


    ModelService modelService
    AppLicenseService appLicenseService
    FlexibleSearchService flexibleSearchService
    boolean dryRun

    private List<AppLicenseModel> getAppLicensesForCode(String appCode) {
        FlexibleSearchQuery query = new FlexibleSearchQuery(
                "SELECT {al.pk} FROM {AppLicense as al} WHERE {al.code} = \"" + appCode + "\""
        + " AND {al.catalogVersion} = '" + AAV2_STAGED_CATALOG_ID + "'")
        query.setResultClassList(List.of(AppLicenseModel.class))
        return flexibleSearchService.search(query).getResult() as List<AppLicenseModel>
    }



    def run() {
        log.info("Running phaseOutNordicProducts, dryRun={}", dryRun)

        for (String productCode : PHASED_OUT_PRODUCTS) {
            System.out.println("Attempting to phase out product with code=" + productCode)
            if (!dryRun) {
                try {
                    getAppLicensesForCode(productCode).forEach { appLicense ->
                        appLicense.setAvailabilityStatus(LicenseAvailabilityStatus.UNPUBLISHED)
                        modelService.save(appLicense)
                    }

                } catch (Exception e) {
                    System.out.println("Could not find app license for product code=" + productCode)
                    continue
                }
            }
        }
    }
}

boolean dryRun = false

new PhaseOutNordicProducts(
        modelService: modelService,
        appLicenseService: appLicenseService,
        flexibleSearchService: flexibleSearchService,
        dryRun: dryRun).run()