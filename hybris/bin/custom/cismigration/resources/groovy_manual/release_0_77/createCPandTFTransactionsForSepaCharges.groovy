package groovy_manual.release_0_77

import com.sast.cis.core.dao.CisOrderDao
import com.sast.cis.core.dao.PaymentTransactionDao
import com.sast.cis.payment.stripe.client.StripeCustomClient;
import com.sast.cis.payment.stripe.service.StripePaymentTransactionService;

import de.hybris.platform.payment.model.PaymentTransactionModel;
import de.hybris.platform.core.model.order.OrderModel;

import com.stripe.model.Charge;

import groovy.util.logging.Slf4j
import java.util.Optional;

/**
 * Creates Capture and Transfer transactions for Stripe charges that could not be successfully processed, due to them having the same Source.
 * See https://jira.sastdev.net/browse/EB-6658 for details
 *
 */
@Slf4j
class CreateTransactionsForCharges {

    CisOrderDao cisOrderDao

    // To get Expanded Charge
    StripeCustomClient stripeCustomClient

    // Find Transactions with requestToken = chargeId
    PaymentTransactionDao paymentTransactionDao

    StripePaymentTransactionService stripePaymentTransactionService;

    def chargesToOrders = [
            'py_1JXKuICJ1CohXfXf8ILIrODc' : '00121002', // Order pk: 8800058048557
            'py_1JXKs1CJ1CohXfXfQpwcKeIP' : '00120027', // Order pk: 8800026099757
            'py_1JXKqfCJ1CohXfXf5dHhaiXh' : '00119005', // Order pk: 8799992610861
            'py_1JX3vbCJ1CohXfXfxLXmU0ki' : '00118006', // Order pk: 8799959875629
            'py_1JX3rWCJ1CohXfXf82wkhGGD' : '00118006', // Order pk: 8799959875629
            'py_1JX3oWCJ1CohXfXfMiWOv8kt' : '00118006', // Order pk: 8799959875629
            'py_1JWwU6CJ1CohXfXftyex1jtr' : '00118006'] // Order pk: 8799959875629

    void run() {
        chargesToOrders.each{chargeId, orderCode ->
            handleCharge(chargeId, orderCode)
        }
    }

    void handleCharge(String chargeId, String orderCode) {
        log.info("Process charge {}.", chargeId)
        Optional<PaymentTransactionModel> transaction = paymentTransactionDao.getTransferTransaction(chargeId)
        if (transaction.isPresent()) {
            log.info("Transfer tx for charge {} found. Skip charge.", chargeId)
            return;
        }
        Charge expandedCharge = stripeCustomClient.retrieveExpandedCharge(chargeId)
        OrderModel order = cisOrderDao.findOrderForCode(orderCode).get()
        stripePaymentTransactionService.persistSuccessfulCaptureTransaction(order, expandedCharge, chargeId);
        log.info("Charge {} linked to Order {}.", chargeId, orderCode)
    }
}

new CreateTransactionsForCharges(
        cisOrderDao: cisOrderDao,
        stripeCustomClient: stripeCustomClient,
        paymentTransactionDao: paymentTransactionDao,
        stripePaymentTransactionService: stripePaymentTransactionService
).run()

// Charges for order number 00118006: 367,71 + 1767,15 + 296,31 + 289,17 = 2720,34
// Order total: 2286 + 434,34 = 2720,34
// Payout: po_1JXyC9LmRmiAqi9H6GMjaysh