package groovy_manual.release_1_02


import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.model.AppLicenseDraftModel
import com.sast.cis.core.model.CountriesAndPricesDraftModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import groovy.util.logging.Slf4j

@Slf4j
class MigratePriceFromCPDraftToLicenseDraft {

    FlexibleSearchService flexibleSearchService
    ModelService modelService

    boolean dryRun

    def migratePrices(int batchSize) {
        FlexibleSearchQuery query = new FlexibleSearchQuery(
                "SELECT { ${AppLicenseDraftModel.PK} } " +
                        "FROM {" +
                        "${AppLicenseDraftModel._TYPECODE} " +
                        "JOIN ${LicenseType._TYPECODE} ON {${AppLicenseDraftModel._TYPECODE}:${AppLicenseDraftModel.LICENSETYPE}} = {${LicenseType._TYPECODE}:pk} " +
                        "} " +
                        "WHERE {${LicenseType._TYPECODE}:code} IN ('${LicenseType.FULL}', '${LicenseType.SUBSCRIPTION}') " +
                        "AND {${AppLicenseDraftModel._TYPECODE}:${AppLicenseDraftModel.SPECIFIEDPRICE}} IS NULL " +
                        "ORDER BY {${AppLicenseDraftModel.CREATIONTIME}} DESC"
        )
        query.setCount(batchSize)
        query.setResultClassList(List.of(AppLicenseDraftModel.class))
        List<AppLicenseDraftModel> licenseDrafts = flexibleSearchService.search(query).getResult()
        log.info("Set prices for {} drafts", licenseDrafts.size())
        for (AppLicenseDraftModel draft : licenseDrafts) {
            Double price = getPriceForLicenseDraft(draft)
            if (price == null) {
                log.warn("Null price found for license draft {}, skip update.", draft.pk)
                continue
            }
            log.info("Set price {} to license draft {}", price, draft.pk)
            if (!dryRun) {
                draft.setSpecifiedPrice(price)
                modelService.save(draft)
            }
        }
    }

    private Double getPriceForLicenseDraft(AppLicenseDraftModel appLicenseDraft) {
        CountriesAndPricesDraftModel cpd = appLicenseDraft.countriesAndPricesDraft
        if (cpd == null) {
            log.info("License draft {} does not have a CPD", appLicenseDraft.pk)
            return null;
        }
        log.info("Get price from countries and prices draft {} to license draft {}", cpd.code, appLicenseDraft.pk)
        if (LicenseType.FULL == appLicenseDraft.licenseType) {
            return cpd.specifiedPrice
        } else if (LicenseType.SUBSCRIPTION == appLicenseDraft.licenseType) {
            return cpd.subscriptionPrice
        } else {
            throw new IllegalArgumentException("App License Draft ${appLicenseDraft.pk} is not of type FULL or SUBSCRIPTION")
        }
    }

    def run() {
        def batchSize = 10
        migratePrices(batchSize)
    }
}

boolean dryRun = true

new MigratePriceFromCPDraftToLicenseDraft(
        flexibleSearchService: flexibleSearchService,
        modelService: modelService,
        dryRun: dryRun
).run()
