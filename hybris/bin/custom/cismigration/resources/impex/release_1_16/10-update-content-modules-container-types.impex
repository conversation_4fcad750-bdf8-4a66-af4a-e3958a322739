$atCc = 040

INSERT_UPDATE ContentModule; code[unique = true]; containerType[default = 'ESI'];
                           ; CM_$atCcA          ;                               ;
                           ; CM_$atCcSDSDA      ;                               ;
                           ; CM_$atCcEBR        ;                               ;
                           ; CM_$atCcTSB        ;                               ;
                           ; CM_$atCcSIS        ;                               ;
                           ; CM_$atCcM          ;                               ;
                           ; CM_$atCcP          ;                               ;
                           ; CM_$atCcD          ;                               ;
                           ; CM_$atCcE          ;                               ;
                           ; CM_$atCcK3         ;                               ;
                           ; CM_$atCcK2         ;                               ;
                           ; CM_$atCcETruck     ;                               ;
                           ; CM_$atCcETOHW1     ;                               ;
                           ; CM_$atCcETOHW2     ;                               ;
                           ; CM_$atCcTrAD       ;                               ;
                           ; CM_$atCcCSFSA7     ;                               ;
                           ; CM_$atCcEW         ;                               ;


INSERT_UPDATE ContentModule; code[unique = true] ; containerType;
                           ; CM_$atCcCoRe        ; CORE         ;
                           ; CM_$atCcPKWTHL      ;              ;
                           ; CM_$atCcTHLCarTruck ;              ;
                           ; CM_$atCcCSFSA5      ;              ;
                           ; CM_$atCcCSBEAPCSW   ; BEA          ;
                           ; CM_$atCcCSBEA750SW  ; BEA          ;
                           ; CM_$atCcDCICRI      ; DCI          ;
                           ; CM_$atCcDCICRIN     ; DCI          ;
                           ; CM_$atCcTVPMCP      ; EPSTD        ;
