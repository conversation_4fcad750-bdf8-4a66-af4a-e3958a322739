UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$workflowManagerRole = workflowManagerRole
$appManagerGroup = appManagerGroup
$apkManagerGroup = apkManagerGroup
$exchangeRateRole = exchangeRateRole

INSERT_UPDATE Employee; uid[unique = true]; name          ; sessionLanguage(isocode); encodedPassword                                                                                                                                                        ; passwordEncoding[default = pbkdf2]; groups(uid)
                      ; mehdi.haitami     ; Mehdi <PERSON> ; en                      ; 1000:d9df7ebd10f5dd4c6e98a55893c82051:86a772555817a208cb4f6fadec26282f1223e69c926fd75e6500bb1040fb61fd6f499ee173a4aab0fc72e2f8d4b8ffa9689fd0d68b938b1c2fab7e0b1454b59f ;                                   ; admingroup,backofficeadmingroup,backofficeworkflowadmingroup,itemLockingGroup,customersupportagentrole,$appManagerGroup,$apkManagerGroup,$exchangeRateRole