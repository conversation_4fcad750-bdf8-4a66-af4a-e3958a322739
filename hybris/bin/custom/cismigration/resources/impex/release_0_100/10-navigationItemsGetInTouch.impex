UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

# Insert get in touch (Kontakt) header link
INSERT_UPDATE NavigationItem; uid[unique = true]; store(uid); itemCode   ; index; url                                                                 ; target   ; icon; text[lang = en]; text[lang = de]; type(code)[default = GLOBAL]; group(code)[default = HELP]; enabled[default = true];
                            ; aa_getInTouch     ; aastore   ; getInTouch ; 8    ; "https://www.boschaftermarket.com/de/de/esitronic-kontaktformular/" ; "_blank" ; ""  ; "Get In Touch" ; "Kontakt"
                            ; getInTouch        ; iotstore  ; getInTouch ; 8    ; "https://www.azena.com/getintouch"                                  ; "_blank" ; ""  ; "Get In Touch" ; "Kontakt"
