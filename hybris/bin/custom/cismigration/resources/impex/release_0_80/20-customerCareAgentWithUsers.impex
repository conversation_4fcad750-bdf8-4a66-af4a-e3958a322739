UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$businessDeveloperRole=businessDeveloperRole
$readonlygroup=readonlygroup
$customerCareAgent = customerCareAgent
$agentRoleName=Customer Care Agent

INSERT_UPDATE BackofficeRole;uid[unique=true];backOfficeLoginDisabled;authorities;name;locName[lang=en];locName[lang=de]
; $customerCareAgent ; false ; customer-care-authority ;$agentRoleName ;$agentRoleName;$agentRoleName

$START_USERRIGHTS
Type;UID;MemberOfGroups;Password;Target;read;change;create;delete;change_perm
BackofficeRole;$customerCareAgent; $readonlygroup,employeegroup;
;                  ;                ;          ; Catalog                     ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; CatalogVersion                     ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; App                     ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; ArticleApprovalStatus   ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; AppDraft                ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; ApkMedia                ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; AppLicense              ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; AppLicenseDraft         ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; ApkSignature            ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; AppVersion              ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; AppVersionDraft         ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; Country                 ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; Currency                ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; CountriesAndPricesDraft ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; IotCompany              ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; LicenseType             ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; Media                   ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; MediaContainer          ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; MediaContainerList      ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; MediaMetaData           ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; MediaMetaDataCollection ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; MediaFolder             ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; ProductContainer        ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; Permission              ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; PriceRow                ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; StoreContentDraft       ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; Order                   ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; OrderEntry              ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; OrderStatus             ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; User                    ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; Invoice                 ; +    ; -      ; -      ; -      ; -
;                  ;                ;          ; SelfBillingInvoice      ; +    ; -      ; -      ; -      ; -
$END_USERRIGHTS

INSERT_UPDATE PrincipalGroupRelation;source(uid)[unique=true];target(uid)[unique=true,default=$customerCareAgent]
;marc.shepard
;matthias.hahn
;bojan.radonjic

INSERT_UPDATE Employee;uid[unique = true];name;sessionLanguage(isocode);encodedPassword;passwordEncoding[default = pbkdf2]; groups(uid)
;andreas.hepp;Andreas Hepp;en;1000:6343726938383230647a306f:0e3c93e5191992092a67dee3931f354dea1e5535be70739abc1060d902a2e3a9cbadd8ba8158a08dd0dd8d26606135854ba8310a7fdc0f30bda84bfb9508b123;;$businessDeveloperRole,$customerCareAgent

REMOVE PrincipalGroupRelation;source(uid)[unique=true];target(uid)[unique=true,default=customerCareGroup]
;matthias.hahn
;bojan.radonjic
;marc.shepard
