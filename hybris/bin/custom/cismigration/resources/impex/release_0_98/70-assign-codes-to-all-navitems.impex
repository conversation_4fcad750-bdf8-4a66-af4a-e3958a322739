UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
UPDATE NavigationItem; pk[unique = true]; itemCode;
"#%
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.core.Registry;

import com.google.common.collect.ImmutableMap;

fqlQuery = ""SELECT { PK } FROM { NavigationItem } where { itemCode } is null "";
flexibleSearchQuery = new FlexibleSearchQuery(fqlQuery);
searchservice = Registry.getApplicationContext().getBean(""flexibleSearchService"");
navigationItems = searchservice.search(flexibleSearchQuery).getResult();
for(navigationItem : navigationItems) {
    impex.insertLine(ImmutableMap.of(
        1, String.valueOf(navigationItem.getPk()),
        2, navigationItem.getUid()
    ));
}
"