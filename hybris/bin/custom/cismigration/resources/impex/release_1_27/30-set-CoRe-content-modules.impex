$atCc = 040
INSERT_UPDATE ContentModule; code[unique = true]; name                     ; containerType[default = 'ESI'];
; CM_$atCcCoReESIPKG;CoRe for ESI 2.0 packages
# Subscription

INSERT_UPDATE RuntimeContentModule; contentModule(ContentModule.code)[unique = true]; contentModuleIds; runtime(Runtime.code)[unique = true,default=runtime_subs_unlimited];
                                  ; CM_$atCcCoReESIPKG                                       ; 1687P15130999