INSERT_UPDATE Eula; &eulaRef     ; customUrl[unique = true]                                            ; type(code)[unique = true, default = 'CUSTOM']
                  ; ESItronic_LT ; https://cdn.esitronic.de/eula/ESItronic/LT_EULA.html                ;
                  ; DCICRI_LT    ; https://cdn.esitronic.de/eula/DCI-CRI/LT_EN_EULA.htm                ;
                  ; DCICRIN_LT   ; https://cdn.esitronic.de/eula/DCI-CRIN/LT_EN_EULA.htm               ;
                  ; DCITEST_LT   ; https://cdn.esitronic.de/eula/Testdata/LT_Data.html                 ;
                  ; BEA_LT       ; https://cdn.esitronic.de/eula/BEA/Eula/Eula_lt.htm                  ;
                  ; BEA_ADAT_LT  ; https://cdn.esitronic.de/eula/BEA/EulaIncludingAutodata/Eula_lt.htm ;


INSERT_UPDATE CountryEula; &countryEulaRef; country(isocode)[unique = true]; eula(&eulaRef)[unique = true]
                         ; ESItronic_LT   ; LT                             ; ESItronic_LT
                         ; DCICRI_LT      ; LT                             ; DCICRI_LT
                         ; DCICRIN_LT     ; LT                             ; DCICRIN_LT
                         ; DCITEST_LT     ; LT                             ; DCITEST_LT
                         ; BEA_LT         ; LT                             ; BEA_LT
                         ; BEA_ADAT_LT    ; LT                             ; BEA_ADAT_LT


INSERT_UPDATE EulaContainer; code[unique = true]; countryEulas(&countryEulaRef)[mode = merge]
                           ; AA2_ESItronic      ; ESItronic_LT
                           ; AA2_BEA            ; BEA_LT
                           ; AA2_BEA_ADAT       ; BEA_ADAT_LT
                           ; AA2_DCICRI         ; DCICRI_LT
                           ; AA2_DCICRIN        ; DCICRIN_LT
                           ; AA2_DCITEST        ; DCITEST_LT
