INSERT_UPDATE Eula; &eulaRef   ; customUrl[unique = true]                              ; type(code)[unique = true, default = 'CUSTOM']
                  ; DCICRI_IS  ; https://cdn.esitronic.de/eula/DCI-CRI/IS_EN_EULA.htm  ;
                  ; DCICRIN_IS ; https://cdn.esitronic.de/eula/DCI-CRIN/IS_EN_EULA.htm ;
                  ; DCITEST_IS ; https://cdn.esitronic.de/eula/Testdata/IS_Data.html   ;


INSERT_UPDATE CountryEula; &countryEulaRef; country(isocode)[unique = true]; eula(&eulaRef)[unique = true]
                         ; DCICRI_IS      ; IS                             ; DCICRI_IS
                         ; DCICRIN_IS     ; IS                             ; DCICRIN_IS
                         ; DCITEST_IS     ; IS                             ; DCITEST_IS



INSERT_UPDATE EulaContainer; code[unique = true]; countryEulas(&countryEulaRef)[mode = merge]
                           ; AA2_DCICRI         ; DCICRI_IS
                           ; AA2_DCICRIN        ; DCICRIN_IS
                           ; AA2_DCITEST        ; DCITEST_IS

