INSERT_UPDATE Eula; &eulaRef     ; customUrl[unique = true]                                            ; type(code)[unique = true, default = 'CUSTOM']
                  ; ESItronic_LV ; https://cdn.esitronic.de/eula/ESItronic/LV_EULA.html                ;
                  ; DCICRI_LV    ; https://cdn.esitronic.de/eula/DCI-CRI/LV_EN_EULA.htm                ;
                  ; DCICRIN_LV   ; https://cdn.esitronic.de/eula/DCI-CRIN/LV_EN_EULA.htm               ;
                  ; DCITEST_LV   ; https://cdn.esitronic.de/eula/Testdata/LV_Data.html                 ;
                  ; BEA_LV       ; https://cdn.esitronic.de/eula/BEA/Eula/Eula_lv.htm                  ;
                  ; BEA_ADAT_LV  ; https://cdn.esitronic.de/eula/BEA/EulaIncludingAutodata/Eula_lv.htm ;


INSERT_UPDATE CountryEula; &countryEulaRef; country(isocode)[unique = true]; eula(&eulaRef)[unique = true]
                         ; ESItronic_LV   ; LV                             ; ESItronic_LV
                         ; DCICRI_LV      ; LV                             ; DCICRI_LV
                         ; DCICRIN_LV     ; LV                             ; DCICRIN_LV
                         ; DCITEST_LV     ; LV                             ; DCITEST_LV
                         ; BEA_LV         ; LV                             ; BEA_LV
                         ; BEA_ADAT_LV    ; LV                             ; BEA_ADAT_LV


INSERT_UPDATE EulaContainer; code[unique = true]; countryEulas(&countryEulaRef)[mode = merge]
                           ; AA2_ESItronic      ; ESItronic_LV
                           ; AA2_BEA            ; BEA_LV
                           ; AA2_BEA_ADAT       ; BEA_ADAT_LV
                           ; AA2_DCICRI         ; DCICRI_LV
                           ; AA2_DCICRIN        ; DCICRIN_LV
                           ; AA2_DCITEST        ; DCITEST_LV
