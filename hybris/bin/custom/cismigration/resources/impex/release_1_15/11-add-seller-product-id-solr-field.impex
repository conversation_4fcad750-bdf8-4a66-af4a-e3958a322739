$solrIndexedType = aaAppType
$solrSearchQueryTemplateName = DEFAULT

INSERT_UPDATE SolrIndexedProperty; solrIndexedType(identifier)[unique = true]; name[unique = true]; type(code); sortableType(code); currency[default = false]; localized[default = false]; multiValue[default = false]; useForSpellchecking[default = false]; useForAutocomplete[default = false]; fieldValueProvider       ; valueProviderParameter
; $solrIndexedType                   ; sellerProductIds   ; string    ;                   ;                          ;                           ;    true                    ;                                     ;                                    ; sellerProductIdValueResolver ;

INSERT_UPDATE SolrSearchQueryProperty; indexedProperty(name, solrIndexedType(identifier))[unique = true]; searchQueryTemplate(name, indexedType(identifier))[unique = true][default = $solrSearchQueryTemplateName:$solrIndexedType]; facet ; ftsPhraseQuery[default = false]; ftsPhraseQuerySlop[default = 0]; ftsPhraseQueryBoost; ftsQuery[default = false]; ftsQueryBoost; ftsFuzzyQuery[default = false]; ftsFuzzyQueryBoost; ftsWildcardQuery[default = false]; ftsWildcardQueryType(code)[default = POSTFIX]; ftsWildcardQueryBoost; includeInResponse[default = true];
; sellerProductIds:$solrIndexedType                           ;                                                                                                                           ; false  ; true                           ; 1                              ; 0                  ; true                     ; 100          ; false                         ;                   ; true                             ;                                              ; 10                   ;                                  ;
