$productCatalog=aaProductCatalog
$classificationCatalog=aaClassificationCatalog

$catalogVersion=catalogversion(catalog(id[default=$productCatalog]),version[default='Staged'])[unique=true,default=$productCatalog:Staged]
$classCatalogVersion=catalogversion(catalog(id[default='aaClassificationCatalog']),version[default='1.0'])[unique=true,default='aaClassificationCatalog:1.0']
$classSystemVersion=systemVersion(catalog(id[default='aaClassificationCatalog']),version[default='1.0'])[unique=true]
$class=classificationClass(ClassificationClass.code,$classCatalogVersion)[unique=true]
$supercategories=source(code, $classCatalogVersion)[unique=true]
$categories=target(code, $catalogVersion)[unique=true]
$attribute=classificationAttribute(code,$classSystemVersion)[unique=true]
$unit=unit(code,$classSystemVersion)

# Insert Classifications
INSERT_UPDATE ClassificationClass;$classCatalogVersion;code[unique=true];allowedPrincipals(uid)[default='customergroup']
;;100

# Insert Classification Attributes
INSERT_UPDATE ClassificationAttribute;$classSystemVersion;code[unique=true]
;;package,100
;;hardware-requirements,100
;;license-types,100
;;vehicle-types,100

# Links ClassificationClasses to Categories
INSERT_UPDATE CategoryCategoryRelation;$categories;$supercategories
 ;main;100

 INSERT_UPDATE ClassificationAttributeValue;code[unique=true];$classSystemVersion
  ;hw_kts_250
  ;hw_kts_560_590
  ;hw_kts_truck
  ;hw_dci_700
  ;hw_fsa_500
  ;hw_fsa_7xx

 INSERT_UPDATE ClassificationAttributeValue;code[unique=true];$classSystemVersion
  ;package_esitronic-2.0_online
  ;package_truck-and-off-highway
  ;package_repair-information
  ;package_technic-hotline
  ;package_hardware-specific

INSERT_UPDATE ClassificationAttributeValue;code[unique=true];$classSystemVersion
 ;licenseType_main
 ;licenseType_onetime
 ;licenseType_additional

 INSERT_UPDATE ClassificationAttributeValue;code[unique=true];$classSystemVersion
  ;vt_pkw
  ;vt_truck
  ;vt_agricultural_machines
  ;vt_construction_machine_engines

INSERT_UPDATE ClassAttributeAssignment;$class;$attribute;position;$unit;attributeType(code[default=string]);attributeValues(code,$classSystemVersion);multiValued[default=false];range[default=false];localized[default=true]
 ;100;hardware-requirements,100;1;<ignore>;enum;hw_kts_560_590,hw_kts_truck,hw_dci_700,hw_fsa_500,hw_fsa_7xx;true;false;false
 ;100;package,100;2;<ignore>;enum;package_esitronic-2.0_online,package_truck-and-off-highway,package_repair-information,package_technic-hotline,package_hardware-specific;;;false
 ;100;license-types,100;3;<ignore>;enum;licenseType_main,licenseType_onetime,licenseType_additional;true;false;false
 ;100;vehicle-types,100;4;<ignore>;enum;vt_pkw,vt_truck,vt_agricultural_machines,vt_construction_machine_engines;true;false;false
