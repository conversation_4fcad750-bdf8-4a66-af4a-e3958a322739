package groovy_manual.release_0_70

import com.sast.cis.core.enums.EulaType
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.EulaModel
import com.sast.cis.core.model.StoreContentDraftModel
import com.sast.cis.core.service.AppService
import de.hybris.platform.servicelayer.exceptions.ModelCreationException
import de.hybris.platform.servicelayer.exceptions.ModelSavingException
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import groovy.util.logging.Slf4j

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG

@Slf4j
class MigrateTermsOfUseToEula {

    private static final String DRAFT_QUERY = "select {pk} from {StoreContentDraft}"

    ModelService modelService
    FlexibleSearchService flexibleSearchService
    AppService appService

    def getAllApps() {
        appService.getAppsFromStagedCatalog(CIS_PRODUCT_CATALOG)
    }

    def getAllDrafts() {
        def draftQuery = new FlexibleSearchQuery(DRAFT_QUERY)
        return flexibleSearchService.search(draftQuery).result
    }

    def run() {
        migrateApps()
        migrateDrafts()
    }

    private void migrateApps() {
        def apps = getAllApps()
        for (AppModel app : apps) {
            try {
                def existingEula = app.getEula()
                if (existingEula != null) {
                    log.info('App {} already contains EULA', app.getCode())
                    compareEulaAndLog(existingEula, app.getTermsOfUseUrl())
                    continue
                }

                log.info('Migrating app {}', app.getCode())
                def eula = createEula(app.getTermsOfUseUrl())
                app.setEula(eula)
                modelService.saveAll(app, eula)
                log.info('Created EULA {} for app {}', eula, app.getCode())

            } catch (ModelSavingException | ModelCreationException e) {
                log.error('Could not migrate for app {}', app.getCode(), e)
            }
        }
    }

    private void migrateDrafts() {
        def drafts = getAllDrafts()
        for (StoreContentDraftModel draft : drafts) {
            try {
                def existingEula = draft.getEula()

                if (existingEula != null) {
                    log.info('Draft {} already contains EULA', draft.getCode())
                    compareEulaAndLog(existingEula, draft.getTermsOfUseUrl())
                    continue
                }

                log.info('Migrating draft {}', draft.getCode())
                if (draft.getTermsOfUseUrl() != null) {
                    def eula = createEula(draft.getTermsOfUseUrl())
                    draft.setEula(eula)
                    modelService.saveAll(draft, eula)
                    log.info('Created custom EULA {} for draft {}', eula, draft.getCode())
                } else {
                    EulaModel eula = modelService.create(EulaModel)
                    eula.setType(EulaType.STANDARD)
                    draft.setEula(eula)
                    modelService.saveAll(draft, eula)
                    log.info('Created standard EULA {} for draft {}', eula, draft.getCode())
                }
            } catch (ModelSavingException | ModelCreationException e) {
                log.error('Could not migrate for draft {}', draft.getCode(), e)
            }
        }
    }

    private void compareEulaAndLog(EulaModel existingEula, String url) {
        if (url == null || existingEula.getType() != EulaType.CUSTOM
                || existingEula.getCustomUrl() != url) {
            log.info('Custom url {} and EULA {} don\'t match', url, existingEula)
        }
    }

    private EulaModel createEula(String termsOfUseUrl) {
        EulaModel eula = modelService.create(EulaModel)
        eula.setCustomUrl(termsOfUseUrl)
        eula.setType(EulaType.CUSTOM)
        eula
    }

}

new MigrateTermsOfUseToEula(modelService: modelService,
        flexibleSearchService: flexibleSearchService,
        appService: appService
).run()
