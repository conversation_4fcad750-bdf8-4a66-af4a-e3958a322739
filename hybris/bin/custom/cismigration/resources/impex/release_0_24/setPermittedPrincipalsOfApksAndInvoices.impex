#% impex.enableCodeExecution(true);

UPDATE GenericItem[processor=de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor];pk[unique=true]

UPDATE ApkMedia;pk[unique=true];permittedPrincipals[default='']
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT apk.pk FROM appversion AS av JOIN catalogversions AS cv ON av.p_catalogversion=cv.pk JOIN medias AS apk ON av.p_apk=apk.pk JOIN products AS app ON av.p_app=app.pk JOIN usergroups AS company ON app.p_company=company.pk WHERE cv.p_version='Staged'"");"

UPDATE ApkMedia;pk[unique=true];permittedPrincipals(uid)
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT apk.pk, company.p_uid FROM appversion AS av JOIN catalogversions AS cv ON av.p_catalogversion=cv.pk JOIN medias AS apk ON av.p_apk=apk.pk JOIN products AS app ON av.p_app=app.pk JOIN usergroups AS company ON app.p_company=company.pk WHERE cv.p_version='Staged'"");"

UPDATE ApkMedia;pk[unique=true];permittedPrincipals[default='']
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT apk.pk FROM appversiondraft AS avd JOIN medias AS apk ON avd.p_apk=apk.pk JOIN productcontainer AS pc ON avd.ownerPkString=pc.pk JOIN usergroups AS company ON pc.p_company=company.pk"");"

UPDATE ApkMedia;pk[unique=true];permittedPrincipals(uid)
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT apk.pk, company.p_uid FROM appversiondraft AS avd JOIN medias AS apk ON avd.p_apk=apk.pk JOIN productcontainer AS pc ON avd.ownerPkString=pc.pk JOIN usergroups AS company ON pc.p_company=company.pk"");"
