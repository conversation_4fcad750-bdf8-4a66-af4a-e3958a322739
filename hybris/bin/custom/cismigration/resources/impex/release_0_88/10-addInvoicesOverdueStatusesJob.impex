INSERT_UPDATE ServicelayerJob; code[unique = true]     ; springId
                             ; invoiceStatusOverdueJob ; invoiceStatusOverdueJobPerformable

INSERT_UPDATE CronJob; code[unique = true]         ; job(code)               ; sessionUser(uid); sessionLanguage(isocode); filesCount; filesDaysOld; filesOperator(code); logToFile; nodeGroup
                     ; invoiceStatusOverdueCronJob ; invoiceStatusOverdueJob ; anonymous       ; en                      ; 60        ; 60          ; AND                ; TRUE     ; ncf