INSERT_UPDATE DynamicProcessDefinition;code[unique=true];active;content
;applicense-to-store-process;true;"
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='createVariantProductAtPjEco'
    name='applicense-to-store-process' processClass='com.sast.cis.approval.model.AppLicenseProcessModel' onError='error'>
    <action id='createVariantProductAtPjEco' bean='exportVariantProductAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='failed'/>
    </action>

    <end id='error' state='ERROR'>An error occurred</end>
    <end id='failed' state='FAILED'>Failed to complete</end>
    <end id='success' state='SUCCEEDED'>Successfully completed</end>
</process>"
