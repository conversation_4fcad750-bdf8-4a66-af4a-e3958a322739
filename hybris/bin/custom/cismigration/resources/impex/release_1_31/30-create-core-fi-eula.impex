INSERT_UPDATE Eula; &eulaRef; customUrl[unique = true]                       ; type(code)[unique = true, default = 'CUSTOM']
                  ; CoRe_FI ; https://cdn.esitronic.de/eula/CORE/Eula_FI.htm ;


INSERT_UPDATE CountryEula; &countryEulaRef; country(isocode)[unique = true]; eula(&eulaRef)[unique = true]
                         ; CoRe_FI        ; FI                             ; CoRe_FI


INSERT_UPDATE EulaContainer; code[unique = true]; countryEulas(&countryEulaRef)[mode = merge]
                           ; AA2_CoRe           ; CoRe_FI
