INSERT_UPDATE DynamicProcessDefinition; code[unique = true]; active[unique=true]; version[unique=true];content
; aa-order-process; true; 6  ; "
<process xmlns='http://www.hybris.de/xsd/processdefinition' start='checkNotMigrationOrder' name='aa-order-process'
        processClass='de.hybris.platform.orderprocessing.model.OrderProcessModel' onError='error'>

    <action id='checkNotMigrationOrder' bean='checkNotMigrationOrderAction'>
        <transition name='OK' to='setOrderStatusCreated'/>
        <transition name='NOK' to='failed'/>
    </action>

    <action id='setOrderStatusCreated' bean='orderStatusCreatedAction'>
        <transition name='OK' to='checkCompanyStatus'/>
    </action>

    <action id='checkCompanyStatus' bean='companyStatusAction'>
        <transition name='OK' to='orderRouterByLicenseType'/>
        <transition name='NOK' to='waitForCompanyApprovalEvent'/>
    </action>

    <wait id='waitForCompanyApprovalEvent' then='error'>
        <case event='company-approved-event'>
            <choice id='success' then='orderRouterByLicenseType'/>
            <choice id='fail' then='setOrderStatusCancelled'/>
        </case>
    </wait>

    <action id='orderRouterByLicenseType' bean='orderRouterAction'>
        <transition name='PAID' to='checkOrderActivationMode'/>
        <transition name='TRIAL' to='error'/>
        <transition name='MULTI' to='error'/>
        <transition name='NO_LICENSE' to='error'/>
    </action>

    <action id='checkOrderActivationMode' bean='checkOrderActivationModeAction'>
        <transition name='OK' to='exportOrder'/>
        <transition name='NOK' to='delayOrderStatus'/>
    </action>

    <action id='delayOrderStatus' bean='delayedOrderStatusAction'>
        <transition name='OK' to='publishOrderToDmp'/>
    </action>

    <action id='publishOrderToDmp' bean='aaSqsMessageAction'>
        <transition name='OK' to='waitForActivationEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForActivationEvent' then='error'>
        <case event='activate-license-event'>
            <choice id='success' then='setStartDateOfContracts'/>
            <choice id='cancel' then='setOrderStatusCancelled'/>
            <choice id='fail' then='error'/>
        </case>
    </wait>

    <action id='setOrderStatusCancelled' bean='orderStatusCancelledAction'>
        <transition name='OK' to='cancel'/>
    </action>

    <action id='setStartDateOfContracts' bean='startContractAction'>
        <transition name='OK' to='exportOrder'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='exportOrder' bean='brimExportOrderAction'>
        <transition name='OK' to='waitForOrderCompleteEvent'/>
        <transition name='NOK' to='error'/>
    </action>

    <wait id='waitForOrderCompleteEvent' then='error'>
        <case event='order-response-event'>
                <choice id='success' then='setOrderStatusCompleted'/>
                <choice id='fail' then='error'/>
        </case>
    </wait>

    <action id='setOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='publishOrderToSqs'/>
    </action>

    <action id='publishOrderToSqs' bean='aaSqsMessageAction'>
        <transition name='OK' to='sendOrderCompletedNotification'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='sendOrderCompletedNotification' bean='aaOrderSuccessMessageAction'>
        <transition name='OK' to='waitForInvoiceEvent'/>
        <transition name='NOK' to='error'/>
	</action>

	<wait id='waitForInvoiceEvent' then='error'>
	    <case event='invoice-received-event'>
	        <choice id='success' then='success'/>
	        <choice id='fail' then='error'/>
        </case>
	</wait>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Order not placed.</end>
    <end id='cancel' state='FAILED'>Order Canceled.</end>
    <end id='success' state='SUCCEEDED'>Order Fulfilled.</end>
</process>"
