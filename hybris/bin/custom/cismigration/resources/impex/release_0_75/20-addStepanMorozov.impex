UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$workflowManagerRole = workflowManagerRole
$appManagerGroup = appManagerGroup
$apkManagerGroup = apkManagerGroup
$exchangeRateRole = exchangeRateRole

INSERT_UPDATE Employee;uid[unique = true];name;sessionLanguage(isocode);encodedPassword;passwordEncoding[default = pbkdf2]; groups(uid)
;stepan.morozov;<PERSON><PERSON>;en;1000:c47a0cc4fd22ddde6d48c010eb9e35e0:90c499a445d88c28863e398c1baf9bda3cbbb5e52d82429237cc2164058a5a2ba0e6ff5d4676e75c2f3c790a8185baee725a0fbf6468cc0a7174e1284551132e;;admingroup,backofficeadmingroup,backofficeworkflowadmingroup,itemLockingGroup,customersupportagentrole,$appManagerGroup,$apkManagerGroup,$exchangeRateRole
