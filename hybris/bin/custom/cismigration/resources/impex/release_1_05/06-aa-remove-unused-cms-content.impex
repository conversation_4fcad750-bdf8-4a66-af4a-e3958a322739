$contentCatalog=aaContentCatalog
$contentCV=catalogVersion(CatalogVersion.catalog(Catalog.id[default=$contentCatalog]),CatalogVersion.version[default=Staged])[default=$contentCatalog:Staged]

# Error Page Template
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='ErrorPageTemplate'];
;HeaderLinks;;
;NavigationBar;;
;Footer;;

# Product Details Page Template
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='ProductDetailsPageTemplate'];validComponentTypes(code);compTypeGroup(code)
;HeaderLinks;;
;NavigationBar;;
;Footer;;

# Cart Page Template
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='CartPageTemplate'];
;HeaderLinks;;
;NavigationBar;;
;TopContent;;
;EmptyCartMiddleContent;;
;Footer;;;footer;;
;CartStickyFooter;;
;MiddleContentSlot;;
;BottomContentSlot;;

# Category Page Template
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='CategoryPageTemplate'];
;HeaderLinks;;;headerlinks
;NavigationBar;;;navigation
;ProductContainerGrid;;;wide
;Footer;;;footer

# Template used for all of the Account pages
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='AccountPageTemplate'];
;HeaderLinks;;;headerlinks
;NavigationBar;;;navigation
;Footer;;;footer

# Order Confirmation Page Template
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='OrderConfirmationPageTemplate'];
;HeaderLinks;;;headerlinks
;NavigationBar;;;navigation
;SideContent;;;narrow
;TopContent;;;wide
;Footer;;;footer

#TermsAndConditionsPageTemplate
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='TermsAndConditionsPageTemplate'];
;HeaderLinks;;;headerlinks
;NavigationBar;;;navigation
;Footer;;;footer
;MiddleContent;;;

#ExportInformationPageTemplate
REMOVE ContentSlotName;name[unique=true];template(uid,$contentCV)[unique=true][default='ExportInformationPageTemplate'];
;HeaderLinks;;;headerlinks
;Footer;;;footer

### CREATE CONTENT SLOTS ###
REMOVE ContentSlot;$contentCV[unique=true];uid[unique=true];
;;NavigationBarSlot;Navigation Bar;true
;;TopContentSlot;Top Content;true
;;SideContentSlot;Side Content;true
;;BottomContentSlot;Bottom Content;true
;;FooterSlot;Footer;true
;;HeaderLinksSlot;Header links;true
;;CartStickyFooterSlot;Sticky Footer;true
;;ProductGridSlot;Product Grid Content Slot;true
;;MiddleContentSlot;Middle Content Slot;true
;;EmptyCartMiddleContent;Empty CartMiddle Content Slot;true

### BIND CONTENT SLOTS TO PAGE TEMPLATES ###
REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='ProductDetailsPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-ProductDetails;;NavigationBarSlot
;;Footer-ProductDetails;;FooterSlot
;;HeaderLinks-ProductDetails;;HeaderLinksSlot

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='CartPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-CartPage;;NavigationBarSlot
;;Footer-CartPage;;NavigationBarSlot
;;CartStickyFooter-CartPage;;CartStickyFooterSlot;
;;HeaderLinks-CartPage;;HeaderLinksSlot;true
;;BottomContentSlot-CartPage;;BottomContentSlot;
;;MiddleContentSlot-CartPage;;MiddleContentSlot;
;;EmptyCartMiddleContent-CartPage;;EmptyCartMiddleContent;

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='CategoryPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-CategoryPage;;NavigationBarSlot;true
;;Footer-CategoryPage;;FooterSlot;true
;;HeaderLinks-CategoryPage;;HeaderLinksSlot;true
;;ProductGridSlot-CategoryPage;;ProductGridSlot;true

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='ErrorPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-ErrorPage;;NavigationBarSlot;true
;;Footer-ErrorPage;;FooterSlot;true
;;HeaderLinks-ErrorPage;;HeaderLinksSlot;true

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='MultiStepCheckoutSummaryPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-MultiStepCheckoutSummaryPage;;NavigationBarSlot;true
;;Footer-MultiStepCheckoutSummaryPage;;FooterSlot;true
;;HeaderLinks-MultiStepCheckoutSummaryPage;;HeaderLinksSlot;true

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='OrderConfirmationPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-OrderConfirmationPage;;NavigationBarSlot;true
;;Footer-OrderConfirmationPage;;FooterSlot;true
;;HeaderLinks-OrderConfirmationPage;;HeaderLinksSlot;true

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='AccountPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-AccountPage;;NavigationBarSlot;true
;;Footer-AccountPage;;FooterSlot;true
;;HeaderLinks-AccountPage;;HeaderLinksSlot;true

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='TermsAndConditionsPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;NavigationBar-TermsAndConditions;;NavigationBarSlot;true
;;Footer-TermsAndConditions;;FooterSlot;true
;;HeaderLinks-TermsAndConditions;;HeaderLinksSlot;true

REMOVE ContentSlotForTemplate;$contentCV[unique=true];uid[unique=true];pageTemplate(uid,$contentCV)[unique=true][default='ExportInformationPageTemplate'];contentSlot(uid,$contentCV)[unique=true]
;;HeaderLinks-ExportInformation;;HeaderLinksSlot;true
;;Footer-ExportInformation;;FooterSlot;true

REMOVE ContentPage;$contentCV[unique=true];uid[unique=true]
;;orderConfirmationPage
;;multiStepCheckoutSummaryPage
;;cartPage
;;payment-details
;;order
;;termsandconditions
;;exportInformationPage
;;orders
;;products
;;tools
;;companyProfile
;;helpAndResources

REMOVE CategoryPage;$contentCV[unique=true];uid[unique=true]
;;category

REMOVE ProductPage;$contentCV[unique=true];uid[unique=true]
;;productDetails

REMOVE PageTemplate;$contentCV[unique=true];uid[unique=true];
;;CartPageTemplate;
;;ProductDetailsPageTemplate
;;CategoryPageTemplate
;;AccountPageTemplate
;;OrderConfirmationPageTemplate
;;TermsAndConditionsPageTemplate
;;ExportInformationPageTemplate
;;StripePageTemplate
;;MultiStepCheckoutSummaryPageTemplate
