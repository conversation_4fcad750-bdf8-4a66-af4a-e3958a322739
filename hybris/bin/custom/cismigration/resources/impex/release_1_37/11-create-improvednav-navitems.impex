UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

$aaLmpUrl = $config-aa.dmp.url
$aaAccountUrl = $config-usermanagementportal.myprofile.url

INSERT_UPDATE NavigationItem; uid[unique = true]            ; itemCode                   ; index; url           ; icon        ; group(code); type(code); enabled; store(uid);
                            ; aa_marketplaceComponent       ; marketplaceComponent       ; 1    ; "/shop"       ; "$store"    ; COMPONENTS ; STORE     ; true   ; aastore
                            ; aa_licenseManagementComponent ; licenseManagementComponent ; 2    ; $aaLmpUrl     ; "$licenses" ; COMPONENTS ; STORE     ; true   ; aastore
                            ; aa_accountComponent           ; accountComponent           ; 3    ; $aaAccountUrl ; "$user"     ; COMPONENTS ; STORE     ; true   ; aastore
