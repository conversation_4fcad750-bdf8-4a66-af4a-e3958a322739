# Note: Header for updates of the process need to be as follows (cf. https://experts.hybris.com/questions/108948/ambiguousitemexception-updating-dynamic-process-de.html):
#UPDATE DynamicProcessDefinition[batchmode=true];code[unique=true];active[unique=true];content

UPDATE DynamicProcessDefinition[batchmode = true]; code[unique = true]; active[unique = true]; content
                                                 ; order-process      ; true                 ; "<process xmlns='http://www.hybris.de/xsd/processdefinition' start='setOrderStatusCreated' name='order-process'
        processClass='de.hybris.platform.orderprocessing.model.OrderProcessModel' onError='error'>
    <action id='setOrderStatusCreated' bean='orderStatusCreatedAction'>
        <transition name='OK' to='orderRouterByLicenseType'/>
    </action>

    <action id='orderRouterByLicenseType' bean='orderRouterAction'>
        <transition name='PAID' to='exportOrderToPjEco'/>
        <transition name='TRIAL' to='publishTrialOrderToSqs'/>
        <transition name='MULTI' to='error'/>
        <transition name='NO_LICENSE' to='error'/>
    </action>

    <action id='exportOrderToPjEco' bean='exportOrderAction'>
        <transition name='OK' to='waitForOrderComplete'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='waitForOrderComplete' bean='waitForOrderCompletedAction'>
        <transition name='OK' to='publishOrderToSqs'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='publishOrderToSqs' bean='sqsMessageAction'>
        <transition name='OK' to='setOrderStatusCompleted'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='sendOrderCompletedNotification'/>
    </action>

    <action id='sendOrderCompletedNotification' bean='orderSuccessMessageAction'>
        <transition name='OK' to='fetchInvoice'/>
        <transition name='NOK' to='error'/>
	</action>

	<action id='fetchInvoice' bean='fetchInvoiceAction'>
		<transition name='OK' to='sendInvoiceReadyNotification'/>
		<transition name='NOK' to='error'/>
	</action>

    <action id='sendInvoiceReadyNotification' bean='invoiceReadyMessageAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
	</action>

	<action id='publishTrialOrderToSqs' bean='sqsMessageAction'>
        <transition name='OK' to='setTrialOrderStatusCompleted'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setTrialOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='sendTrialOrderCompletedNotification'/>
    </action>

    <action id='sendTrialOrderCompletedNotification' bean='orderSuccessMessageAction'>
        <transition name='OK' to='success'/>
        <transition name='NOK' to='error'/>
	</action>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Order not placed.</end>
    <end id='success' state='SUCCEEDED'>Order Fulfilled.</end>
</process>"