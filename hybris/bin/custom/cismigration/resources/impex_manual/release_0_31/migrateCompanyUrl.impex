UPDATE GenericItem[processor=de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor];pk[unique=true]

UPDATE App;pk[unique=true];catalogVersion[unique=true];termsOfUseUrl;productWebsiteUrl;
"#%
    impex.initDatabase(""$config-db.url"", ""$config-db.username"", ""$config-db.password"", ""$config-db.driver"");
    impex.includeSQLData(""SELECT App.PK, App.p_catalogversion, Company.p_url, Company.p_url "" +
                         ""FROM products AS App "" +
                         ""JOIN usergroups AS Company ON App.p_company=Company.PK "" +
                         ""WHERE Company.p_url is not null;"");
"

UPDATE StoreContentDraft;pk[unique=true];termsOfUseUrl;productWebsiteUrl;
"#%
    impex.initDatabase(""$config-db.url"", ""$config-db.username"", ""$config-db.password"", ""$config-db.driver"");
    impex.includeSQLData(""SELECT Scd.PK, Company.p_url, Company.p_url "" +
                         ""FROM storecontentdraft AS Scd "" +
                         ""JOIN appdraft AS Ad ON Scd.PK=Ad.p_storecontentdraft "" +
                         ""JOIN productcontainer AS Pc ON Ad.pk=Pc.p_appdraft "" +
                         ""JOIN usergroups AS Company ON Pc.p_company=Company.PK "" +
                         ""WHERE Company.p_url is not null;"");
"