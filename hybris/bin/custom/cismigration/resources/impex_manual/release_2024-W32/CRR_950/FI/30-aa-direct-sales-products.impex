#% impex.setLocale( Locale.ENGLISH );

# numerical isocode for FI, used as prefix for product code
$fiCc = 358
$aaCc = 040
$aaPackageName = com.sast.aa.fi.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

#local
#$companyId = d13faef4-769e-43e3-94e7-fae2c2009f10
#DEV
#$companyId = 09014d91-7f1d-40b6-b4a2-646756334bcf
#DEMO
$companyId = a8d470e0-ee94-41fd-b233-3eb88f9ea7e4

$crr950DieselInjectorsRepairSoftDescription = CRR 950 -dieselsuuttimien korjausohjelmisto
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch
$crr950DieselInjectorsRepairSoftDescription_sv = CRR 950 Programvara för reparation av dieselinjektorer

$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)

$countryRestricted = true
# @formatter:off
$enabledIn = FI
# @formatter:on

INSERT_UPDATE App; code[unique = true]       ; packageName                    ; name[lang = fi]                          ; summary[lang = fi]                          ; description[lang = fi]                      ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$fiCc1687_CRR950      ; $aaPackageName1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;

# English translations
INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]                     ; $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en

# Swedish translations
INSERT_UPDATE App; code[unique = true]       ; name[lang = sv]                          ; summary[lang = sv]                             ; description[lang = sv]                     ; $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_sv ; $crr950DieselInjectorsRepairSoftDescription_sv

INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$fiCc1687_CRR950      ; AA2_$fiCc1687_CRR950


INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; enabledCountries(isocode); billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$fiCc1687P15137 ; AA2_$fiCc1687_CRR950      ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 100           ; $enabledIn
                        ; AA2_$fiCc1687P15139 ; AA2_$fiCc1687_CRR950      ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 200           ; $enabledIn

UPDATE AppLicense; code[unique = true] ; userGroups(uid); $catalogVersion[unique = true]
                 ; AA2_$fiCc1687P15137 ; IDW000, WD0001 ;
                 ; AA2_$fiCc1687P15139 ; IDW000, WD0001 ;

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$fiCc1687P15137                          ; 100  ;
                      ; AA2_$fiCc1687P15139                          ; 200  ;

INSERT_UPDATE App; code[unique = true]; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CRR950 ; CM_$aaCcCRR950

INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CRR950      ; cat_401


INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$fiCc1687_CRR950      ; AA2_ESItronic
