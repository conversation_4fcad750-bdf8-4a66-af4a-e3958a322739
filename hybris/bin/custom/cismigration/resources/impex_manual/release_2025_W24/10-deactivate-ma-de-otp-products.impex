#% impex.enableCodeExecution(true);
$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])

UPDATE AppLicense; code[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]; $catalogVersion[unique = true];
"#%
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.core.Registry;
import java.util.Map;

fqlQuery = ""SELECT { PK } FROM { AppLicense } WHERE { sellerProductId } IN ('1687P15142', '1687P15152', '1987P12051', '1987P12389', '1987P12412')"";
flexibleSearchQuery = new FlexibleSearchQuery(fqlQuery);
searchservice = Registry.getApplicationContext().getBean(""flexibleSearchService"");
appLicenses = searchservice.search(flexibleSearchQuery).getResult();
for(appLicense : appLicenses) {
        impex.insertLine(Map.of(1, String.valueOf(appLicense.getCode())));
}
"
