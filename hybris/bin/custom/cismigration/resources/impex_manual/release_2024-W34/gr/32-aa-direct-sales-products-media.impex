#% impex.setLocale( Locale.ENGLISH );

# numerical isocode for AT, used as prefix for product code
$grCc = 030

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$media = @media[translator = de.hybris.platform.impex.jalo.media.MediaDataTranslator]
$siteResource = jar:com.sast.cis.aainitialdata.setup.AaInitialDataSystemSetup&/aainitialdata/import/sampledata/productCatalogs/$productCatalog/images

INSERT_UPDATE Media; code[unique = true]                ; owner(App.code)           ; $media; mime[default = 'image/png']; $catalogVersion[unique = true]; folder(qualifier)[default = images]
                   ; pcaa2_$grCc1987_TRKOHW3_icon_01    ; AA2_$grCc1987_TRKOHW3     ; $siteResource/crane.png

INSERT_UPDATE MediaContainer; qualifier[unique = true]                    ; owner(App.code)           ; medias(code, $catalogVersion); $catalogVersion[unique = true]; conversionGroup(code)[default = 'Icon-Conversion']
                            ; pcaa2_$grCc1987_TRKOHW3_iconContainer_01    ; AA2_$grCc1987_TRKOHW3     ; pcaa2_$grCc1987_TRKOHW3_icon_01

INSERT_UPDATE App; code[unique = true]       ; icon(qualifier, $catalogVersion); $catalogVersion[unique = true]
                 ; AA2_$grCc1987_TRKOHW3     ; pcaa2_$grCc1987_TRKOHW3_iconContainer_01