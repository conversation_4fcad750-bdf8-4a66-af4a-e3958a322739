#% impex.setLocale( Locale.GERMAN );
# numerical isocode for AT, used as prefix for product code

$atCc = 040
$aaPackageName = com.sast.aa.at.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf
$countryRestricted = true

# Please use companyId for the environment you want to import to:
# live
# $companyId = c0cfa9dd-8d1d-4d36-9a0d-2eb09b8a0638
# demo
# $companyId = 6f66a508-e637-446e-8817-6d41a313500a
# dev
# $companyId = fc82b363-c3f7-4797-a97c-af37711ac1fd
# local
$companyId = c8c04cf7-b173-48c3-9127-5917910b4fe6

$knorrBremseDiagnosticDescription = ESI 2.0 Truck Knorr-Bremse Diagnostics Add-On
$knorrBremseDiagnosticDescription_en = ESI 2.0 Truck Knorr-Bremse Diagnostics Add-On
$knrBremseBrimName1L = ESI[tronic] Knorr-Bremse (3 years)
$knrBremseMultiBrimName3L = ESI[tronic] Knorr-Bremse (3 years) Multi
$knrBremseUnlimitedBrimName1L = ESI[tronic] Knorr-Bremse
$knrBremseUnlimitedBrimName3L = ESI[tronic] Knorr-Bremse Multi

INSERT_UPDATE App; code[unique = true]  ; packageName               ; name[lang = de]                           ; name[lang = en]                           ; summary[lang = de]                ; summary[lang = en]                   ; description[lang = de]            ; description[lang = en]; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$atCc1987_KNRBRE ; $aaPackageName1987_KNRBRE ; ESI Truck Knorr-Bremse Diagnostics Add-On ; ESI Truck Knorr-Bremse Diagnostics Add-On ; $knorrBremseDiagnosticDescription ; $knorrBremseDiagnosticDescription_en ; $knorrBremseDiagnosticDescription ; $knorrBremseDiagnosticDescription_en

INSERT_UPDATE ProductContainer; code[unique = true]   ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$atCc1987_KNRBRE ; AA2_$atCc1987_KNRBRE

INSERT_UPDATE App; code[unique = true]  ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$atCc1987_KNRBRE ; MAT_$atCcTrFZ,MAT_$atCcTrD,MAT_$atCcTrSp,MAT_$atCcTrWt,MAT_$atCcTrTD,MAT_$atCcTrETK

INSERT_UPDATE App; code[unique = true]  ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$atCc1987_KNRBRE ; cat_10102

INSERT_UPDATE App; code[unique = true]  ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$atCc1987_KNRBRE ; AA2_ESItronic

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct         ; sellerProductId; brimName[lang = en]           ; brimName[lang = de]           ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = 'AT']; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$atCc1987P12282 ; AA2_$atCc1987_KNRBRE ; 1987P12282     ; $knrBremseBrimName1L          ; $knrBremseBrimName1L          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1             ;
                        ; AA2_$atCc1987P12283 ; AA2_$atCc1987_KNRBRE ; 1987P12283     ; $knrBremseMultiBrimName3L     ; $knrBremseMultiBrimName3L     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1             ;
                        ; AA2_$atCc1987P12878 ; AA2_$atCc1987_KNRBRE ; 1987P12878     ; $knrBremseUnlimitedBrimName1L ; $knrBremseUnlimitedBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1             ;
                        ; AA2_$atCc1987P12290 ; AA2_$atCc1987_KNRBRE ; 1987P12290     ; $knrBremseUnlimitedBrimName3L ; $knrBremseUnlimitedBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1             ;

INSERT_UPDATE AppLicense; code[unique = true] ; userGroups(uid); $catalogVersion[unique = true];
                        ; AA2_$atCc1987P12282 ; IDW000         ;
                        ; AA2_$atCc1987P12283 ; IDW000         ;
                        ; AA2_$atCc1987P12878 ; IDW000         ;
                        ; AA2_$atCc1987P12290 ; IDW000         ;

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$atCc1987P12282                          ; 1
                      ; AA2_$atCc1987P12283                          ; 1
                      ; AA2_$atCc1987P12878                          ; 1
                      ; AA2_$atCc1987P12290                          ; 1

INSERT_UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; contentModules(code)[default = CM_$atCcSDSDA];
                        ; AA2_$atCc1987P12282 ;                               ; CM_$atCcKNRBRE
                        ; AA2_$atCc1987P12283 ;                               ; CM_$atCcKNRBRE
                        ; AA2_$atCc1987P12878 ;                               ; CM_$atCcKNRBRE
                        ; AA2_$atCc1987P12290 ;                               ; CM_$atCcKNRBRE

$classificationCatalog = aaClassificationCatalog
$clAttrModifiersVariants = system = '$classificationCatalog', version = '1.0', translator = de.hybris.platform.catalog.jalo.classification.impex.ClassificationAttributeTranslator;
# @formatter:off
$featureHR = @hardware-requirements,100[$clAttrModifiersVariants];
$featureVT = @vehicle-types,100[$clAttrModifiersVariants];
# @formatter:on


INSERT_UPDATE App; code[unique = true]  ; $featureHR; $catalogVersion;
                 ; AA2_$atCc1987_KNRBRE ; hw_kts_truck,hw_kts_900_truck

INSERT_UPDATE App; code[unique = true]  ; $featureVT; $catalogVersion;
                 ; AA2_$atCc1987_KNRBRE ; vt_construction_machine_engines


$media = @media[translator = de.hybris.platform.impex.jalo.media.MediaDataTranslator]
$siteResource = jar:com.sast.cis.aainitialdata.setup.AaInitialDataSystemSetup&/aainitialdata/import/sampledata/productCatalogs/$productCatalog/images

INSERT_UPDATE Media; code[unique = true]            ; owner(App.code)      ; $media; mime[default = 'image/png']; $catalogVersion[unique = true]; folder(qualifier)[default = images]
                   ; pcaa2_$atCc1987_KNRBRE_icon_01 ; AA2_$atCc1987_KNRBRE ; $siteResource/crane.png

INSERT_UPDATE MediaContainer; qualifier[unique = true]                ; owner(App.code)      ; medias(code, $catalogVersion); $catalogVersion[unique = true]; conversionGroup(code)[default = 'Icon-Conversion']
                            ; pcaa2_$atCc1987_KNRBRE_iconContainer_01 ; AA2_$atCc1987_KNRBRE ; pcaa2_$atCc1987_KNRBRE_icon_01

INSERT_UPDATE App; code[unique = true]  ; icon(qualifier, $catalogVersion); $catalogVersion[unique = true]
                 ; AA2_$atCc1987_KNRBRE ; pcaa2_$atCc1987_KNRBRE_iconContainer_01

INSERT_UPDATE Product; code[unique = true]  ; order; $catalogVersion[unique = true];
                     ; AA2_$atCc1987_KNRBRE ; 6    ;                               ;