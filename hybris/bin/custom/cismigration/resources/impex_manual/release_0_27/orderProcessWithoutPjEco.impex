UPDATE DynamicProcessDefinition[batchmode=true];code[unique=true];active[unique=true];content
;order-process;true;"<process xmlns='http://www.hybris.de/xsd/processdefinition' start='setOrderStatusCreated' name='order-process'
        processClass='de.hybris.platform.orderprocessing.model.OrderProcessModel'>
    <action id='setOrderStatusCreated' bean='orderStatusCreatedAction'>
        <transition name='OK' to='performCapture'/>
    </action>

    <action id='performCapture' bean='performCaptureAction'>
        <transition name='OK' to='setOrderStatusPaymentCaptured'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderStatusPaymentCaptured' bean='orderStatusPaymentCapturedAction'>
        <transition name='OK' to='publishOrderToSqs'/>
    </action>

    <action id='publishOrderToSqs' bean='sqsMessageAction'>
        <transition name='OK' to='setOrderStatusWaitForInvoice'/>
        <transition name='NOK' to='error'/>
    </action>

    <action id='setOrderStatusWaitForInvoice' bean='orderStatusWaitForInvoiceAction'>
        <transition name='OK' to='setOrderStatusCompleted'/>
    </action>

    <action id='setOrderStatusCompleted' bean='orderStatusCompletedAction'>
        <transition name='OK' to='success'/>
    </action>

    <end id='error' state='ERROR'>All went wrong.</end>
    <end id='failed' state='FAILED'>Order not placed.</end>
    <end id='success' state='SUCCEEDED'>Order placed.</end>
</process>"
