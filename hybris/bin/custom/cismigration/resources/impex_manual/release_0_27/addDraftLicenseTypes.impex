UPDATE CountriesAndPricesDraft[batchmode=true];itemtype(code)[unique=true];licenses(code)[default='FULL,EVALUATION']
#% import de.hybris.platform.core.Registry;
#% import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
#% import de.hybris.platform.servicelayer.search.SearchResult;
#% import de.hybris.platform.servicelayer.search.FlexibleSearchService;
#%
#% queryString = "SELECT * FROM {CountriesAndPricesDraft}";
#% query = new FlexibleSearchQuery(queryString);
#% result = Registry.getApplicationContext().getBean("flexibleSearchService").search(query);
#% beforeEach: if (result.getResult().isEmpty()) line.clear();
;CountriesAndPricesDraft
