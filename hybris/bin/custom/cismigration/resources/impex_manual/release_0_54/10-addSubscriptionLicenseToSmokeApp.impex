UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
##############################################################################
### DO NOT RUN ON LIVE                                                    ####
##############################################################################

#% if: !"live".equals("$config-spring.profiles.active");
$productCatalog = cisProductCatalog
$catalogVersion = catalogversion(catalog(id[default=$productCatalog]), version[default='Staged'])
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default='approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default=eu-vat-full]

"#%
    impex.warn(""Running impex for: $config-spring.profiles.active"");
    import com.sast.cis.initialdata.SampleData;
    import  de.hybris.platform.servicelayer.search.FlexibleSearchQuery;

    sampleData = SampleData.prepareDemoData(impex);

    flexibleSearchService = Registry.getApplicationContext().getBean(""flexibleSearchService"");
    query = ""SELECT {app.PK} FROM {App as app} where {app.packageName} = 'com.securityandsafetythings.examples.smokeapp'"";
    flexibleSearchQuery = new FlexibleSearchQuery(query);
    result = flexibleSearchService.search(flexibleSearchQuery).getResult().get(0);

    smokeAppCode = result.code;
"

INSERT_UPDATE AppLicense; code[unique = true]; $baseProduct; licenseType(code)[default = 'SUBSCRIPTION'];billingSystemStatus(code)[default=IN_SYNC];\
    unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default='DE']
"#%
    sampleData.insertImpexLine(new String[]{
        smokeAppCode + ""_subscription"",
        smokeAppCode,
        ""SUBSCRIPTION"",
        ""IN_SYNC"" });
"

INSERT_UPDATE PriceRow; productId[unique = true]; price; currency(isocode)[unique = true, default = USD];\
    unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true];startTime[dateformat=yyyy-MM-dd hh:mm:ssss, default='2019-10-01 12:12:2222']
"#%
sampleData.insertImpexLine(new String[]{ smokeAppCode + ""_subscription"", ""100"", ""EUR"" });
sampleData.insertImpexLine(new String[]{ smokeAppCode + ""_subscription"", ""100"", ""USD"" });
"

#% endif: