#% impex.enableCodeExecution(true);
UPDATE GenericItem[processor=de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor];pk[unique=true]
UPDATE App;pk[unique=true];logo(code)[default = '']
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#% impex.includeSQLData(""SELECT pk FROM products WHERE p_logo IS NOT null"");"

UPDATE StoreContentDraft;pk[unique=true];logo(code)[default='']
#% impex.initDatabase( "$config-db.url", "$config-db.username","$config-db.password","$config-db.driver");
"#%impex.includeSQLData(""SELECT pk FROM storecontentdraft WHERE p_logo IS NOT null"");"