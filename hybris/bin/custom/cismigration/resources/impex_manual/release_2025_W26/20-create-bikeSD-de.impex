#% impex.setLocale( Locale.GERMAN );

$deCc = 049
$atCc = 040
$aaPackageName = com.sast.aa.de.
$lang = de
$isoCode = 'DE'

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf
$countryRestricted = true

# please make sure, to use right DELEVELOPER COMPANY for Germany in the right environment.
# live
# $companyId = 18231ebc-ce34-4a34-b51e-a17806cb18ae
# demo
# $companyId = 774ae2d7-2ca2-42f7-b7bd-eba0128a666f
# dev
$companyId = c7cc0be2-8851-4947-b9b7-51a8bb1261fd

$bikeSDDescription = ESI[tronic] Bike SD
$bikeSDDescription_en = ESI[tronic] Bike SD


INSERT_UPDATE App; code[unique = true]  ; packageName               ; name[lang = de]                  ; name[lang = en]           ; summary[lang = de] ; summary[lang = en]    ; description[lang = de]; description[lang = en]; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$deCc1987_BIKESD ; $aaPackageName1987_bikeSD ; ESI[tronic] Bike SD ; ESI[tronic] Bike SD ; $bikeSDDescription ; $bikeSDDescription_en ; $bikeSDDescription    ; $bikeSDDescription_en ;                                      ;                 ;                                                              ;                             ;                                        ;                               ;          ;          ;                                              ;              ;               ;                  ;                                   ;

INSERT_UPDATE ProductContainer; code[unique = true]   ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId]
                              ; pcaa_$deCc1987_BIKESD ; AA2_$deCc1987_BIKESD      ;

INSERT_UPDATE App; code[unique = true]  ; masterEnabled [default = false]
                 ; AA2_$deCc1987_BIKESD ;

INSERT_UPDATE App; code[unique = true]  ; $supercategories; $catalogVersion[unique = true]
                 ; AA2_$deCc1987_BIKESD ; cat_10101       ;

INSERT_UPDATE App; code[unique = true]  ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$deCc1987_BIKESD ; AA2_ESItronic       ;

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct         ; sellerProductId; brimName[lang = en]                     ; brimName[lang = de]              ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = $isoCode]; availabilityStatus(code)[default = UNPUBLISHED]
                        ; AA2_$deCc1987P12097 ; AA2_$deCc1987_BIKESD ; 1987P12097     ; ESI[tronic] Bike SD (one-time purchase) ; ESI[tronic] Bike SD (Einmalkauf) ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 1             ;                                         ;                             ;                               ;          ;          ;                                              ;

INSERT_UPDATE AppLicense; code[unique = true] ; userGroups(uid); $catalogVersion[unique = true]
                        ; AA2_$deCc1987P12097 ; IDW000,BM0000  ;

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code)[unique = true, default = pieces]; minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$deCc1987P12097                          ; 1    ;                                                ;                                        ;                    ;                        ;                    ;

INSERT_UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; contentModules(code)
                        ; AA2_$deCc1987P12097 ;                               ; CM_$atCcBikeSD


$media = @media[translator = de.hybris.platform.impex.jalo.media.MediaDataTranslator]
$siteResource = jar:com.sast.cis.aainitialdata.setup.AaInitialDataSystemSetup&/aainitialdata/import/sampledata/productCatalogs/$productCatalog/images

INSERT_UPDATE Media; code[unique = true]            ; owner(App.code)      ; $media                ; mime[default = 'image/png']; $catalogVersion[unique = true]; folder(qualifier)[default = images]
                   ; pcaa2_$deCc1987_BIKESD_icon_01 ; AA2_$deCc1987_BIKESD ; $siteResource/car.png ;                            ;                               ;

INSERT_UPDATE MediaContainer; qualifier[unique = true]                ; owner(App.code)      ; medias(code, $catalogVersion)  ; $catalogVersion[unique = true]; conversionGroup(code)[default = 'Icon-Conversion']
                            ; pcaa2_$deCc1987_BIKESD_iconContainer_01 ; AA2_$deCc1987_BIKESD ; pcaa2_$deCc1987_BIKESD_icon_01 ;                               ;

INSERT_UPDATE App; code[unique = true]  ; icon(qualifier, $catalogVersion)        ; $catalogVersion[unique = true]
                 ; AA2_$deCc1987_BIKESD ; pcaa2_$deCc1987_BIKESD_iconContainer_01 ;

