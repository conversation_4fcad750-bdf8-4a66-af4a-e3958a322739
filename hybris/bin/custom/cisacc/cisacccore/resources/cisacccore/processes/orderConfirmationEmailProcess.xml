<?xml version="1.0" encoding="utf-8"?>
<!--
 [y] hybris Platform

 Copyright (c) 2018 SAP SE or an SAP affiliate company.  All rights reserved.

 This software is the confidential and proprietary information of SAP
 ("Confidential Information"). You shall not disclose such Confidential
 Information and shall use it only in accordance with the terms of the
 license agreement you entered into with SAP.
-->
<process xmlns="http://www.hybris.de/xsd/processdefinition" start="generateOrderConfirmationEmail" name="orderConfirmationEmailProcess"
		processClass="de.hybris.platform.orderprocessing.model.OrderProcessModel" onError="error">

	<action id="generateOrderConfirmationEmail" bean="generateOrderConfirmationEmail">
		<transition name="OK" to="sendEmail"/>
		<transition name="NOK" to="error"/>
	</action>

	<action id="sendEmail" bean="sendEmail">
		<transition name="OK" to="removeSentEmail"/>
		<transition name="NOK" to="failed"/>
	</action>

	<action id="removeSentEmail" bean="removeSentEmail">
		<transition name="OK" to="success"/>
		<transition name="NOK" to="error"/>
	</action>

	<end id="error" state="ERROR">Something went wrong.</end>
	<end id="failed" state="FAILED">Could not send order confirmation email.</end>
	<end id="success" state="SUCCEEDED">Sent order confirmation email.</end>

</process>