package com.sast.cis.payment.pgw.service.selleraccount;

import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.selleraccount.PspSellerAccountProvider;
import com.sast.cis.core.service.order.OrderSellerProvider;
import com.sast.cis.payment.pgw.model.PgwSellerAccountModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.sast.cis.core.enums.PaymentProvider.PGW;

@Service
@Slf4j
public class PgwSellerAccountProvider extends PspSellerAccountProvider<PgwSellerAccountModel> {

    public PgwSellerAccountProvider(final OrderSellerProvider orderSellerProvider) {
        super(orderSellerProvider);
    }

    protected PaymentProvider getPaymentProvider() {
        return PGW;
    }

    protected Class<PgwSellerAccountModel> getSellerAccountType() {
        return PgwSellerAccountModel.class;
    }
}
