package com.sast.cis.payment.pgw.service

import com.sast.cis.core.data.PaymentInfoData
import com.sast.cis.core.enums.LicenseType
import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.enums.PspSellerAccountStatus
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.paymentintegration.data.*
import com.sast.cis.core.service.license.ProductSellerProvider
import com.sast.cis.payment.pgw.model.PgwSellerAccountModel
import com.sast.cis.payment.pgw.service.selleraccount.PgwSellerAccountProvider
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.IntegratorBuilder
import generated.com.sast.cis.payment.pgw.model.PgwSellerAccountBuilder
import org.junit.Test

@UnitTest
class PgwPaymentServiceUnitSpec extends JUnitPlatformSpecification {
    private PgwPaymentService pgwPaymentService
    private ProductSellerProvider productSellerProvider = Mock()
    private PgwPaymentMethodDispatch pgwPaymentMethodDispatch = Mock()
    private PgwSellerAccountProvider pgwSellerAccountProvider = Mock()

    private AppLicenseModel appLicense = Mock()
    private IoTCompanyModel sellerCompany = Mock()
    private IoTCompanyModel buyerCompany = Mock()
    private PgwSellerAccountModel pgwSellerAccount = Mock()
    private CartModel cart = Mock()
    private CountryModel sellerCountry = Mock()
    private CountryModel buyerCountry = Mock()
    private PaymentTransactionEntryModel mockTxEntry = Mock()
    private PaymentTransactionModel mockTx = Mock()


    def setup() {
        pgwPaymentService = new PgwPaymentService(productSellerProvider, pgwPaymentMethodDispatch, pgwSellerAccountProvider)

        PgwSellerAccountBuilder.generate()
                .withStatus(PspSellerAccountStatus.ONBOARDING)
                .withCompany(sellerCompany)
                .withPaymentProvider(PaymentProvider.PGW)
                .buildMockInstance()

        pgwSellerAccount.getStatus() >> PspSellerAccountStatus.ONBOARDING
        pgwSellerAccount.getCompany() >> sellerCompany
        pgwSellerAccount.getPaymentProvider() >> PaymentProvider.PGW

        sellerCompany.getCountry() >> sellerCountry
        buyerCompany.getCountry() >> buyerCountry

        sellerCountry.getSupportedPaymentProviders() >> [PaymentProvider.ZERO, PaymentProvider.PGW]
        buyerCountry.getSupportedPaymentProviders() >> [PaymentProvider.ZERO, PaymentProvider.PGW]

        pgwSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.of(pgwSellerAccount)
        pgwPaymentMethodDispatch.supportsPaymentMethod(PaymentMethodType.SEPA_DIRECTDEBIT) >> true

        productSellerProvider.getSellerOrThrow(appLicense) >> sellerCompany
        appLicense.getLicenseType() >> LicenseType.FULL
    }

    @Test
    void 'prepareCheckout delegates to paymentMethodDispatch'() {
        given:
        def givenAuthParameter = authParameter
        def paymentMethod = PaymentMethodType.SEPA_DIRECTDEBIT
        def expectedCheckoutInfo = new CheckoutInfo()

        when:
        def actualCheckoutInfo = pgwPaymentService.prepareCheckout(givenAuthParameter, paymentMethod)

        then:
        1 * pgwPaymentMethodDispatch.prepareCheckout(givenAuthParameter, paymentMethod) >> expectedCheckoutInfo
        actualCheckoutInfo == expectedCheckoutInfo
    }

    @Test
    void 'prepareCheckout throws for null auth parameter'() {
        when:
        pgwPaymentService.prepareCheckout(null, PaymentMethodType.SEPA_DIRECTDEBIT)

        then:
        0 * pgwPaymentMethodDispatch._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'prepareCheckout throws for null payment method'() {
        when:
        pgwPaymentService.prepareCheckout(authParameter, null)

        then:
        0 * pgwPaymentMethodDispatch._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'confirmSelectedPaymentInfo delegates to paymentMethodDispatch'() {
        given:
        def givenAuth = authParameter

        when:
        pgwPaymentService.confirmSelectedPaymentInfo(givenAuth)

        then:
        1 * pgwPaymentMethodDispatch.confirmSelectedPaymentInfo(givenAuth)
    }

    @Test
    void 'confirmSelectedPaymentInfo throws for null auth parameter'() {
        when:
        pgwPaymentService.confirmSelectedPaymentInfo(null)

        then:
        0 * pgwPaymentMethodDispatch._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'authorize delegates to paymentMethodDispatch'() {
        given:
        def givenAuth = authParameter

        when:
        def actualTx = pgwPaymentService.authorize(givenAuth)

        then:
        1 * pgwPaymentMethodDispatch.authorize(authParameter) >> mockTxEntry
        actualTx == mockTxEntry
    }

    @Test
    void 'authorize throws for null auth parameter'() {
        when:
        pgwPaymentService.authorize(null)

        then:
        0 * pgwPaymentMethodDispatch._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'getAuthorizationResult delegates to paymentMethodDispatch'() {
        given:
        def expectedResult = new AuthorizationResult()

        when:
        def actualResult = pgwPaymentService.getAuthorizationResult(mockTxEntry)

        then:
        1 * pgwPaymentMethodDispatch.getAuthorizationResult(mockTxEntry) >> expectedResult
        actualResult == expectedResult
    }

    @Test
    void 'getAuthorizationResult throws for given null transaction'() {
        when:
        pgwPaymentService.getAuthorizationResult(null)

        then:
        0 * pgwPaymentMethodDispatch._
        thrown(IllegalArgumentException)
    }

    @Test
    void 'capture throws UnsupportedOperationException'() {
        when:
        pgwPaymentService.capture(mockTx)

        then:
        thrown(UnsupportedOperationException)
    }

    @Test
    void 'refund throws UnsupportedOperationException'() {
        when:
        pgwPaymentService.refund(mockTx)

        then:
        thrown(UnsupportedOperationException)
    }

    @Test
    void 'notifyRedirectResult delegates to paymentMethodDispatch'() {
        given:
        def givenRedirectResult = PaymentRedirectResult.builder()
                .status(PaymentRedirectStatus.SUCCESS)
                .abstractOrder(cart)
                .build()

        when:
        pgwPaymentService.notifyRedirectResult(givenRedirectResult)

        then:
        1 * pgwPaymentMethodDispatch.notifyRedirectResult(givenRedirectResult)
    }

    @Test
    void 'notifyRedirectResult throws if given redirectResult is null'() {
        when:
        pgwPaymentService.notifyRedirectResult(null)

        then:
        0 * pgwPaymentMethodDispatch._
        thrown(IllegalArgumentException)
    }

    @Test
    def 'PGW seller account is not active, returns false for supportsPaymentMethod'() {
        when:
        def actualResult = pgwPaymentService.supportsSale(PaymentMethodType.SEPA_DIRECTDEBIT, sellerCompany)

        then:
        pgwSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.empty()
        !actualResult
    }

    @Test
    def 'PGW as payment provider in the given SellerCompany country supported but unknown payment method, returns false for supportsPaymentMethod'() {
        when:
        def actualResult = pgwPaymentService.supportsSale(PaymentMethodType.CREDIT_CARD, sellerCompany)

        then:
        !actualResult
    }

    @Test
    def 'PGW as payment provider in the given SellerCompany and BuyerCompany country supported, returns true for supportsPaymentMethod'() {
        when:
        def actualResult = pgwPaymentService.supportsPurchase(PaymentMethodType.SEPA_DIRECTDEBIT, buyerCompany, Set.of(appLicense))

        then:
        actualResult
    }

    @Test
    def 'supportsPurchase returns false for PGW as payment provider in the given SellerCompany but payment provider unsupported in buyer country'() {
        when:
        def actualResult = pgwPaymentService.supportsPurchase(PaymentMethodType.SEPA_DIRECTDEBIT, buyerCompany, Set.of(appLicense))

        then:
        buyerCountry.getSupportedPaymentProviders() >> Set.of()
        !actualResult
    }

    @Test
    def 'supportsPurchase returns false for PGW as payment provider if seller has no valid payout account'() {
        when:
        def actualResult = pgwPaymentService.supportsPurchase(PaymentMethodType.SEPA_DIRECTDEBIT, buyerCompany, Set.of(appLicense))

        then:
        pgwSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.empty()
        !actualResult
    }

    @Test
    def 'PGW as payment provider in the given BuyerCompany country not supported, returns false for supportsPaymentMethod'() {
        when:
        def actualResult = pgwPaymentService.supportsPurchase(PaymentMethodType.SEPA_DIRECTDEBIT, buyerCompany, Set.of(appLicense))

        then:
        buyerCountry.getSupportedPaymentProviders() >> [PaymentProvider.ZERO, PaymentProvider.DPG]
        !actualResult
    }

    @Test
    void 'canSetupPayment returns false'() {
        when:
        def actualResult = pgwPaymentService.canSetupPayment(sellerCompany)

        then:
        !actualResult
    }

    @Test
    def 'isPayoutAccountValidated returns true if given company has an active PGW seller account'() {
        when:
        def actualResult = pgwPaymentService.isPayoutAccountValidated(sellerCompany)

        then:
        pgwSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.of(pgwSellerAccount)
        actualResult
    }

    @Test
    def 'isPayoutAccountValidated returns true if given company does not have an active PGW seller account'() {
        when:
        def actualResult = pgwPaymentService.isPayoutAccountValidated(sellerCompany)

        then:
        pgwSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany) >> Optional.empty()
        !actualResult
    }

    @Test
    def 'returns payment provider PGW'() {
        when:
        def actualProvider = pgwPaymentService.getPaymentProvider()

        then:
        actualProvider == PaymentProvider.PGW
    }

    @Test
    def 'createSellerAccount throws UnsupportedOperationException'() {
        when:
        pgwPaymentService.createSellerAccount(sellerCompany)

        then:
        thrown(UnsupportedOperationException)
    }

    @Test
    def 'createPaymentInfo delegates to paymentMethodDispatch'() {
        given:
        def paymentInfoData = new PaymentInfoData()
        def integrator = IntegratorBuilder.generate().buildInstance()

        when:
        pgwPaymentService.createPaymentInfo(integrator, paymentInfoData)

        then:
        1 * pgwPaymentMethodDispatch.createPaymentInfo(integrator, paymentInfoData)
    }

    private AuthorizationParameter getAuthParameter() {
        AuthorizationParameter.builder()
                .abstractOrder(cart)
                .plannedAmount(BigDecimal.TEN)
                .build()
    }
}
