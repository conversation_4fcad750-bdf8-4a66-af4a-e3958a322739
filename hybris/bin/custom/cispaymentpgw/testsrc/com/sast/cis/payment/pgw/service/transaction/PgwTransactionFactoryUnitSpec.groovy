package com.sast.cis.payment.pgw.service.transaction

import com.sast.cis.core.enums.PaymentProvider
import com.sast.cis.core.service.AbstractOrderHashService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CurrencyModel
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.core.model.order.payment.PaymentInfoModel
import de.hybris.platform.payment.dto.TransactionStatus
import de.hybris.platform.payment.dto.TransactionStatusDetails
import de.hybris.platform.payment.enums.PaymentTransactionType
import de.hybris.platform.payment.model.PaymentTransactionEntryModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.time.TimeService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.de.hybris.platform.payment.model.PaymentTransactionBuilder
import generated.de.hybris.platform.payment.model.PaymentTransactionEntryBuilder
import org.junit.Test

@UnitTest
class PgwTransactionFactoryUnitSpec extends JUnitPlatformSpecification {
    private static final String MERCHANT_ID = 'merchant12345'
    private static final Date NOW = new Date()
    private static final String ORDER_HASH = 'orderHash9435213'

    private ModelService modelService = Mock()
    private TimeService timeService = Mock()
    private AbstractOrderHashService abstractOrderHashService = Mock()

    private PgwTransactionFactory pgwTransactionFactory

    private AbstractOrderModel mockOrder = Mock()
    private PaymentInfoModel mockInfo = Mock()
    private CurrencyModel mockCurrency = Mock()

    private PaymentTransactionModel newTx = PaymentTransactionBuilder.generate().buildInstance()
    private PaymentTransactionEntryModel newTxEntry = PaymentTransactionEntryBuilder.generate().buildInstance()
    private PaymentTransactionModel existingTxMock = Mock()

    def setup() {
        pgwTransactionFactory = new PgwTransactionFactory(modelService, timeService, abstractOrderHashService)
        mockOrder.getPaymentInfo() >> mockInfo
        mockOrder.getCurrency() >> mockCurrency
        mockInfo.getPgwMerchantId() >> MERCHANT_ID
        mockInfo.getPaymentProvider() >> PaymentProvider.PGW
        existingTxMock.getCurrency() >> mockCurrency
        existingTxMock.getOrder() >> mockOrder


        modelService.create(PaymentTransactionModel.class) >> newTx
        modelService.create(PaymentTransactionEntryModel.class) >> newTxEntry
        timeService.getCurrentTime() >> NOW
        abstractOrderHashService.calculateHash(mockOrder) >> ORDER_HASH
    }

    @Test
    void 'createTransaction persists a new transaction according to given data'() {
        given:
        def givenType = PaymentTransactionType.AUTHORIZATION
        def givenAmount = BigDecimal.TEN

        when:
        def actualTransaction = pgwTransactionFactory.createTransaction(givenType, mockOrder, givenAmount)

        then:
        verifyAll(actualTransaction) {
            code.startsWith('PgwTx')
            type == givenType
            paymentProvider == PaymentProvider.PGW.code
            currency == mockCurrency
            plannedAmount == givenAmount
            order == mockOrder
            info == mockInfo
            pgwMerchantId == MERCHANT_ID
        }
        actualTransaction == newTx
        1 * modelService.save(newTx)
        1 * modelService.refresh(mockOrder)
    }

    @Test
    void 'createTransaction throws if given transaction type is null'() {
        when:
        pgwTransactionFactory.createTransaction(null, mockOrder, BigDecimal.TEN)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createTransaction throws if given order is null'() {
        when:
        pgwTransactionFactory.createTransaction(PaymentTransactionType.CHECKOUT, null, BigDecimal.TEN)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createTransaction throws if given amount is null'() {
        when:
        pgwTransactionFactory.createTransaction(PaymentTransactionType.CHECKOUT, mockOrder, null)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createTransaction throws if given order has no payment info'() {
        when:
        pgwTransactionFactory.createTransaction(PaymentTransactionType.CHECKOUT, mockOrder, BigDecimal.ZERO)

        then:
        mockOrder.getPaymentInfo() >> null
        thrown(IllegalStateException)
        0 * modelService._
    }

    @Test
    void 'createTransaction throws if given order payment info has no merchant id'() {
        when:
        pgwTransactionFactory.createTransaction(PaymentTransactionType.CHECKOUT, mockOrder, BigDecimal.ZERO)

        then:
        mockInfo.getPgwMerchantId() >> ''
        thrown(IllegalStateException)
        0 * modelService._
    }

    @Test
    void 'createTransaction throws if given order payment info has wrong payment provider'() {
        when:
        pgwTransactionFactory.createTransaction(PaymentTransactionType.CHECKOUT, mockOrder, BigDecimal.ZERO)

        then:
        mockInfo.getPaymentProvider() >> PaymentProvider.ZERO
        thrown(IllegalStateException)
        0 * modelService._
    }

    @Test
    void 'createEntry creates a new entry with given data for given transaction'() {
        when:
        def actualEntry = pgwTransactionFactory.createEntry(existingTxMock, PaymentTransactionType.CHECKOUT,
                TransactionStatus.PENDING)

        then:
        verifyAll(actualEntry) {
            code.startsWith('PgwTxEntry')
            paymentTransaction == existingTxMock
            time == NOW
            transactionStatus == TransactionStatus.PENDING.name()
            type == PaymentTransactionType.CHECKOUT
            currency == mockCurrency
            cartHash == ORDER_HASH
        }
        actualEntry == newTxEntry
        1 * modelService.save(newTxEntry)
        1 * modelService.refresh(existingTxMock)
    }

    @Test
    void 'createEntry throws if given transaction has no order'() {
        when:
        pgwTransactionFactory.createEntry(existingTxMock, PaymentTransactionType.AUTHORIZATION, TransactionStatus.PENDING)

        then:
        existingTxMock.getOrder() >> null
        thrown(IllegalStateException)
    }

    @Test
    void 'createEntry throws if given transaction is null'() {
        when:
        pgwTransactionFactory.createEntry(null, PaymentTransactionType.AUTHORIZATION, TransactionStatus.PENDING)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createEntry throws if given transaction type is null'() {
        when:
        pgwTransactionFactory.createEntry(existingTxMock, null, TransactionStatus.PENDING)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'createEntry throws if given transaction status is null'() {
        when:
        pgwTransactionFactory.createEntry(existingTxMock, PaymentTransactionType.AUTHORIZATION, null)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'setAccepted sets the given transaction entry to status ACCEPTED with details SUCCESFULL - yes that typo belongs there'() {
        when:
        def actualEntry = pgwTransactionFactory.setAccepted(newTxEntry)

        then:
        actualEntry == newTxEntry
        1 * modelService.save(newTxEntry)
        verifyAll(actualEntry) {
            transactionStatus == TransactionStatus.ACCEPTED.name()
            transactionStatusDetails == TransactionStatusDetails.SUCCESFULL.name()
        }
    }

    @Test
    void 'setAccepted throws if given entry is null'() {
        when:
        pgwTransactionFactory.setAccepted(null)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'setError sets the given transaction entry to status ERROR with given details'() {
        when:
        def actualEntry = pgwTransactionFactory.setError(newTxEntry, TransactionStatusDetails.BANK_DECLINE)

        then:
        actualEntry == newTxEntry
        1 * modelService.save(newTxEntry)
        verifyAll(actualEntry) {
            transactionStatus == TransactionStatus.ERROR.name()
            transactionStatusDetails == TransactionStatusDetails.BANK_DECLINE.name()
        }
    }

    @Test
    void 'setError throws if given entry is null'() {
        when:
        pgwTransactionFactory.setError(null, TransactionStatusDetails.AMOUNTS_MUST_MATCH)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }

    @Test
    void 'setError throws if given status details is null'() {
        when:
        pgwTransactionFactory.setError(newTxEntry, null)

        then:
        thrown(IllegalArgumentException)
        0 * modelService._
    }
}