package com.sast.cis.companyprofile.validator;

import com.sast.cis.companyprofile.data.SaveHeaderData;
import com.sast.cis.companyprofile.exception.CompanyProfileErrorMessage;
import com.sast.cis.companyprofile.model.CompanySizeModel;
import com.sast.cis.companyprofile.service.CompanySizeService;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import generated.com.sast.cis.companyprofile.model.CompanySizeBuilder;
import org.apache.commons.configuration.Configuration;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import java.util.Calendar;
import java.util.Set;

import static com.sast.cis.companyprofile.constants.ErrorCode.*;
import static com.sast.cis.companyprofile.constants.InputField.*;
import static com.sast.cis.companyprofile.constants.InputField.HEADER_TAGLINE;
import static com.sast.cis.companyprofile.exception.CompanyProfileErrorMessage.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(DataProviderRunner.class)
public class SaveHeaderDataValidatorUnitTest {

    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private ConfigurationService configurationService;
    @Mock
    private Configuration configuration;
    @Mock
    private CompanySizeService companySizeService;

    @InjectMocks
    private SaveHeaderDataValidator saveHeaderDataValidator;

    @Before
    public void setup() {
        when(configurationService.getConfiguration()).thenReturn(configuration);
        when(configuration.getInt(anyString(), anyInt())).thenReturn(20);
        CompanySizeModel companySizeModel = CompanySizeBuilder.generate().buildMockInstance();
        when(companySizeService.findByCode("MINI")).thenReturn(companySizeModel);
    }

    @DataProvider
    public static Object[][] headerInputExpectedValidationError() {
        return new Object[][] {
            { new SaveHeaderData().withCompanySizeCode("UNKNOWN"), Set.of(createErrorMessage(
                COMPANY_PROFILE_COMPANY_SIZE_UNKNOWN, HEADER_COMPANY_SIZE_CODE))
            },
            { new SaveHeaderData().withCompanySizeCode("UNKNOWN").withCompanyWebsite("test"), Set.of(createErrorMessage(
                COMPANY_PROFILE_COMPANY_SIZE_UNKNOWN, HEADER_COMPANY_SIZE_CODE),
                createErrorMessage(
                COMPANY_PROFILE_COMPANY_WEBSITE_URL_INVALID, HEADER_COMPANY_WEBSITE))
            },
            { new SaveHeaderData().withCompanySizeCode("UNKNOWN").withCompanyWebsite("test").withTagLine("This is bigger than 10 chars"), Set.of(
                createErrorMessage(COMPANY_PROFILE_COMPANY_SIZE_UNKNOWN, HEADER_COMPANY_SIZE_CODE),
                createErrorMessage(COMPANY_PROFILE_COMPANY_WEBSITE_URL_INVALID, HEADER_COMPANY_WEBSITE),
                createErrorMessage(COMPANY_PROFILE_TAGLINE_TOO_LONG, HEADER_TAGLINE))
            },
            { new SaveHeaderData()
                .withLinkedInProfileUrl("wongUrlLongerThanTenChars")
                .withCompanyWebsite("test")
                .withFoundedIn(Calendar.getInstance().get(Calendar.YEAR)+1)
                .withTagLine("This is bigger than 10 chars"), Set.of(
                createErrorMessage(COMPANY_PROFILE_LINKEDIN_PROFILE_URL_INVALID, HEADER_LINKEDIN_PROFILE_URL),
                createErrorMessage(COMPANY_PROFILE_LINKEDIN_PROFILE_URL_TOO_LONG, HEADER_LINKEDIN_PROFILE_URL),
                createErrorMessage(COMPANY_PROFILE_COMPANY_WEBSITE_URL_INVALID, HEADER_COMPANY_WEBSITE),
                createErrorMessage(COMPANY_PROFILE_COMPANY_FOUNDED_INVALID_RANGE, HEADER_FOUNDED_IN),
                createErrorMessage(COMPANY_PROFILE_TAGLINE_TOO_LONG, HEADER_TAGLINE))
            },
            { new SaveHeaderData()
                .withFoundedIn(1799)
                .withTagLine("This is bigger than 10 chars"), Set.of(
                createErrorMessage(COMPANY_PROFILE_COMPANY_FOUNDED_INVALID_RANGE, HEADER_FOUNDED_IN),
                createErrorMessage(COMPANY_PROFILE_TAGLINE_TOO_LONG, HEADER_TAGLINE))
            },
            { new SaveHeaderData().withCompanySizeCode("MINI").withFoundedIn(2019).withTagLine("tagline").withCompanyWebsite("wwww.test.com")
                .withLinkedInProfileUrl("linkedin.com/profile")
                .withShowCompanySize(true)
                .withShowFounded(true), Set.of()
            }

        };
    }

    @Test
    @UseDataProvider("headerInputExpectedValidationError")
    public void validate_invalidInput_givesExpectedErrorMessages(SaveHeaderData saveHeaderData,Set<CompanyProfileErrorMessage> expectedErrorMessages){
        Set<CompanyProfileErrorMessage> actualValidationErrors = saveHeaderDataValidator.validate(saveHeaderData);
        assertThat(actualValidationErrors).usingFieldByFieldElementComparator().containsExactlyInAnyOrder(expectedErrorMessages.toArray(CompanyProfileErrorMessage[]::new));
    }

}
