package com.sast.cis.companyprofile.constants;

public enum InputField {
    ABOUT_DESCRIPTION("aboutData.description"),
    CONTACT_SUPPORT_EMAIL("contactData.supportEmail"),
    CONTACT_SUPPORT_PAGE_URL("contactData.supportPageUrl"),
    CONTACT_SUPPORT_PHONE("contactData.supportPhone"),
    CONTACT_SALES_EMAIL("contactData.salesEmail"),
    CONTACT_SALES_PHONE("contactData.salesPhone"),
    HEADER_LOGO("headerData.logo"),
    HEADER_TAGLINE("headerData.tagLine"),
    HEADER_COMPANY_SIZE_CODE("headerData.companySizeCode"),
    HEADER_FOUNDED_IN("headerData.foundedIn"),
    HEADER_COMPANY_WEBSITE("headerData.companyWebsite"),
    HEADER_LINKEDIN_PROFILE_URL("headerData.linkedInProfileUrl");

    private final String value;

    InputField(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
