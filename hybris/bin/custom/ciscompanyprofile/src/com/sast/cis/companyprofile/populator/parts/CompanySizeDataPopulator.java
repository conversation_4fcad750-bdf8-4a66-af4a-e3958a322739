package com.sast.cis.companyprofile.populator.parts;

import com.sast.cis.companyprofile.data.CompanySizeData;
import com.sast.cis.companyprofile.model.CompanySizeModel;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import org.springframework.stereotype.Component;

@Component
public class CompanySizeDataPopulator implements Populator<CompanySizeModel, CompanySizeData> {
    @Override
    public void populate(CompanySizeModel companySize, CompanySizeData companySizeData) throws ConversionException {
        companySizeData.setCode(companySize.getCode());
        companySizeData.setOrder(companySize.getOrder());
        companySizeData.setValue(companySize.getValue());
    }
}
