package com.sast.cis.companyprofile.populator;

import com.sast.cis.companyprofile.data.SaveHeaderData;
import com.sast.cis.companyprofile.model.CompanyProfileModel;
import com.sast.cis.companyprofile.model.CompanySizeModel;
import com.sast.cis.companyprofile.service.CompanySizeService;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@AllArgsConstructor
public class SaveHeaderDataCompanyProfileReversePopulator implements Populator<SaveHeaderData, CompanyProfileModel> {

    private final CompanySizeService companySizeService;

    @Override
    public void populate(SaveHeaderData saveHeaderData, CompanyProfileModel companyProfile) throws ConversionException {
        companyProfile.setTagLine(saveHeaderData.getTagLine());
        companyProfile.setLinkedInProfile(saveHeaderData.getLinkedInProfileUrl());
        companyProfile.setCompanyWebsite(saveHeaderData.getCompanyWebsite());
        companyProfile.setFoundedIn(saveHeaderData.getFoundedIn());
        companyProfile.setShowFounded(saveHeaderData.isShowFounded());
        companyProfile.setShowCompanySize(saveHeaderData.isShowCompanySize());
        if (isNotBlank(saveHeaderData.getCompanySizeCode())) {
            CompanySizeModel companySize = companySizeService.findByCode(saveHeaderData.getCompanySizeCode());
            companyProfile.setCompanySize(companySize);
        } else {
            companyProfile.setCompanySize(null);
        }
    }
}
