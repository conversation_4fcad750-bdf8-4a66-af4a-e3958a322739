package com.sast.cis.productapi.v1.controller;

import com.sast.cis.productapi.v1.facade.ProductApiContainerFacade;
import com.sast.cis.productapi.v1.model.ErrorsDto;
import com.sast.cis.productapi.v1.model.ProductContainerDetailsDto;
import com.sast.cis.productapi.v1.model.ProductContainerDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

import java.util.List;

@RestController
@RequestMapping("/v1/containers")
@Api(tags = "Product Container")
@ApiResponses(value = {
    @ApiResponse(code = 400, message = "Bad request", response = ErrorsDto.class),
    @ApiResponse(code = 429, message = "Too many requests"),
    @ApiResponse(code = 500, message = "Server error", response = ErrorsDto.class)
})
@RequiredArgsConstructor
public class ProductContainerController {
    private final ProductApiContainerFacade productApiContainerFacade;

    @GetMapping
    @ApiOperation(value = "Get product containers", notes = "Returns summary information for all accessible product containers")
    public List<ProductContainerDto> getProductContainers() {
        return productApiContainerFacade.getProductContainers();
    }

    @GetMapping("/{containerId}")
    @ApiOperation(value = "Get product container details", notes = "Returns detailed information the given product container")
    public ProductContainerDetailsDto getContainerDetails(@NotNull @PathVariable String containerId) {
        return productApiContainerFacade.getProductContainerData(containerId);
    }
}
