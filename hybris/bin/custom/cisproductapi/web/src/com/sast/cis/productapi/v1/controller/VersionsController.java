package com.sast.cis.productapi.v1.controller;

import com.sast.cis.core.data.AppVersionMetaData;
import com.sast.cis.core.exceptions.web.NotFoundException;
import com.sast.cis.core.facade.AppVersionMetaDataFacade;
import com.sast.cis.productapi.v1.converter.VersionDtoToFacadeConverter;
import com.sast.cis.productapi.v1.converter.VersionMetaDataToDtoConverter;
import com.sast.cis.productapi.v1.model.ErrorsDto;
import com.sast.cis.productapi.v1.model.VersionDto;
import com.sast.cis.productapi.v1.model.VersionUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("/v1/containers/{containerId}/versions/{versionId}")
@Api(tags = "Versions Management")
@ApiResponses(value = {
    @ApiResponse(code = 400, message = "Bad request", response = ErrorsDto.class),
    @ApiResponse(code = 429, message = "Too many requests"),
    @ApiResponse(code = 500, message = "Server error", response = ErrorsDto.class)
})
@RequiredArgsConstructor
public class VersionsController {
    private final AppVersionMetaDataFacade appVersionMetaDataFacade;
    private final VersionMetaDataToDtoConverter versionMetaDataToDtoConverter;
    private final VersionDtoToFacadeConverter versionDtoToFacadeConverter;

    @GetMapping
    @ApiOperation(value = "Get application version", notes = "Returns the given app version metadata")
    public VersionDto getVersion(@NotNull @PathVariable String containerId, @NotNull @PathVariable String versionId) {
        AppVersionMetaData appVersionMetaData = appVersionMetaDataFacade.getAppVersionMetaData(containerId, versionId)
            .orElseThrow(() -> new NotFoundException("product container or version is not found"));
        return versionMetaDataToDtoConverter.convert(appVersionMetaData);
    }

    @PutMapping
    @ApiOperation(value = "Update application version data", notes = "Updates app version metadata")
    public VersionDto updateVersion(@NotNull @PathVariable String containerId, @NotNull @PathVariable String versionId,
        @NotNull @Valid @RequestBody VersionUpdateDto versionUpdate) {
        AppVersionMetaData appVersionMetaData = versionDtoToFacadeConverter.convert(versionUpdate);
        AppVersionMetaData savedAppVersionData = appVersionMetaDataFacade.updateChangeLog(appVersionMetaData, containerId, versionId);
        return versionMetaDataToDtoConverter.convert(savedAppVersionData);
    }
}
