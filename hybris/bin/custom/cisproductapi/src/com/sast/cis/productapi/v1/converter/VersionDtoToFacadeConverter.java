package com.sast.cis.productapi.v1.converter;

import com.sast.cis.core.data.AppVersionMetaData;
import com.sast.cis.productapi.converter.ProductApiConverter;
import com.sast.cis.productapi.v1.model.VersionUpdateDto;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@Component
public class VersionDtoToFacadeConverter implements ProductApiConverter<VersionUpdateDto, AppVersionMetaData> {
    @Override
    public AppVersionMetaData convert(@Nonnull VersionUpdateDto versionDto) {
        AppVersionMetaData appVersionMetaData = new AppVersionMetaData();
        Map<String, String> isocodeChangelogMap = new HashMap<>();
        isocodeChangelogMap.put(Locale.ENGLISH.getLanguage(), versionDto.getChangelog().getEn());
        appVersionMetaData.setChangelogByIsocode(isocodeChangelogMap);
        return appVersionMetaData;
    }
}
