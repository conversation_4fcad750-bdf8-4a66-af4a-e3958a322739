package com.sast.cis.productapi.v1.converter;

import com.sast.cis.core.data.AppVersionOverviewData;
import com.sast.cis.productapi.converter.ProductApiConverter;
import com.sast.cis.productapi.v1.model.ManifestMetadataDto;
import com.sast.cis.productapi.v1.model.VersionDraftDto;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@RequiredArgsConstructor
public class VersionDraftToDtoConverter implements ProductApiConverter<AppVersionOverviewData, VersionDraftDto> {

    @Resource
    private final ApprovalStateToDtoConverter approvalStateToDtoConverter;

    public VersionDraftDto convert(@NonNull AppVersionOverviewData appVersionOverviewData) {
        VersionDraftDto versionDraftDto = new VersionDraftDto();
        versionDraftDto.setState(approvalStateToDtoConverter.convert(appVersionOverviewData.getApprovalStatus()));
        versionDraftDto.setManifestMetadata(populateManifestMetadata(appVersionOverviewData));
        return versionDraftDto;
    }

    private ManifestMetadataDto populateManifestMetadata(AppVersionOverviewData appVersionOverviewData) {
        ManifestMetadataDto manifestMetadataDto = new ManifestMetadataDto();
        manifestMetadataDto.setVersionName(appVersionOverviewData.getVersionName());
        manifestMetadataDto.setPackageName(appVersionOverviewData.getPackageName());
        return manifestMetadataDto;
    }

}
