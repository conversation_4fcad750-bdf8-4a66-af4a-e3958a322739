package com.sast.cis.productapi.v1.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

import java.util.Set;

@Data
public class ManifestMetadataDto {
    @NotNull
    @ApiModelProperty(required = true)
    private String packageName;

    @NotNull
    @ApiModelProperty(required = true)
    private String versionName;

    @NotNull
    @ApiModelProperty(required = true)
    private Integer versionCode;
    private Set<String> permissions;
    private Set<String> requiredCapabilities;

    @NotNull
    @ApiModelProperty(required = true)
    private Integer sdkAddonVersion;

    @NotNull
    @ApiModelProperty(required = true)
    private Integer minAndroidApiVersion;
    private Integer maxAndroidApiVersion;
}
