# Specifies the location of the spring context file putted automatically to the global platform application context.
cisproductapi.application-context=cisproductapi-spring.xml

cisproductapi.oauth.scope=basic
cisproductapi.license.url=
cisproductapi.terms.of.service.url=
cisproductapi.licence=
cisproductapi.documentation.desc=Interface for the management of products
cisproductapi.documentation.title=Azena Developer Console Product API
cisproductapi.buildversion=20211209-draft
website.productapi.https=https://localhost:9002


#enables static documentation generation by wsStaticDoc ant task
cisproductapi.documentation.static.generate=true

cisproductapi.oauth2.resource.issuer-uri=${sso.keycloak.configuration.auth-server-url}/realms/sast
cisproductapi.oauth2.resource.clientId=iotstore-devcon
cisproductapi.oauth2.resource.client.secret=${sso.keycloak.configuration.devcon.credentials.secret}
cisproductapi.oauth2.resource.token-info-uri=${cisproductapi.oauth2.resource.issuer-uri}/protocol/openid-connect/token/introspect
cisproductapi.oauth2.resource.user-info-uri=${cisproductapi.oauth2.resource.issuer-uri}/protocol/openid-connect/userinfo
cisproductapi.oauth2.resource.token-uri=${cisproductapi.oauth2.resource.issuer-uri}/protocol/openid-connect/token

# cors properties
corsfilter.cisdevconapi.allowedOrigins=*
corsfilter.cisdevconapi.allowedMethods=GET POST PUT OPTIONS DELETE
corsfilter.cisdevconapi.allowedHeaders=origin content-type accept authorization


