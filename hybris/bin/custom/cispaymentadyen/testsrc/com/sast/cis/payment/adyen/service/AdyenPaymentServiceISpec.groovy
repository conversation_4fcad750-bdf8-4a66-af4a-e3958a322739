package com.sast.cis.payment.adyen.service

import com.sast.cis.core.model.*
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter
import com.sast.cis.payment.adyen.model.AdyenSellerAccountModel
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.core.model.order.CartModel
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import generated.com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoBuilder
import generated.com.sast.cis.payment.adyen.model.AdyenSellerAccountBuilder
import org.junit.Test

import javax.annotation.Resource

import static com.sast.cis.core.constants.BaseStoreEnum.AA
import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.BillingSystemStatus.IN_SYNC
import static com.sast.cis.core.enums.PaymentMethodType.SEPA_DIRECTDEBIT
import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static com.sast.cis.core.enums.PspSellerAccountStatus.ACTIVE
import static com.sast.cis.core.util.Base58UUIDCodeGenerator.generateCode
import static com.sast.cis.test.utils.TestDataConstants.*
import static org.assertj.core.util.DateUtil.now

@IntegrationTest
class AdyenPaymentServiceISpec extends ServicelayerTransactionalSpockSpecification {

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Resource
    private AdyenPaymentService adyenPaymentService

    @Resource
    private ModelService modelService

    private IntegratorModel buyer
    private DeveloperModel seller

    private AppLicenseModel license
    private AdyenSellerAccountModel adyenSellerAccount

    private SepaMandatePaymentInfoModel savedMatchingInfo
    private SepaMandatePaymentInfoModel savedNonMatchingInfo

    private final String matchingMerchantId = 'merchant-id'
    private final String otherMerchantId = 'other-merchant-id'

    def setup() {
        buyer = sampleDataCreator.getIntegratorByInternalUserId(AA_AUSTRIA1_COMPANY_INTEGRATOR_UID)
        seller = sampleDataCreator.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID)

        def app = sampleDataCreator.createApp("AA2_app", "com.AdyenPaymentServiceISpec", seller, AA_PRODUCT_CATALOG, ONLINE)
        license = sampleDataCreator.createFullAppLicense(app)

        savedMatchingInfo = createPaymentInfo(buyer, matchingMerchantId)
        savedNonMatchingInfo = createPaymentInfo(buyer, otherMerchantId)
        buyer.setDefaultPaymentInfo(savedMatchingInfo)

        def sellerCompany = seller.getCompany()
        adyenSellerAccount = createSellerAccount(sellerCompany)
        sellerCompany.setPspSellerAccounts(List.of(adyenSellerAccount))

        modelService.saveAll(buyer, sellerCompany)
    }

    @Test
    void 'test prepare checkout'() {
        given:
        def authorizationParameter = createAuthorizationParameter()
        def sessionCart = authorizationParameter.abstractOrder() as CartModel
        sampleDataCreator.createCartEntry(sessionCart, license)

        when:
        def checkout = adyenPaymentService.prepareCheckout(authorizationParameter, SEPA_DIRECTDEBIT)

        then:
        verifyAll(checkout) {
            storedPaymentInfos == [savedMatchingInfo] as Set
            defaultPaymentInfo == savedMatchingInfo
            paymentProvider == ADYEN
            cartPaymentInfo == savedMatchingInfo
        }
    }

    private AuthorizationParameter createAuthorizationParameter() {
        def cart = sampleDataCreator.createCart(buyer, AA)
        cart.setPaymentInfo(savedMatchingInfo)
        return AuthorizationParameter.builder()
                .abstractOrder(cart)
                .plannedAmount(BigDecimal.valueOf(100.00))
                .build()
    }

    private AdyenSellerAccountModel createSellerAccount(IoTCompanyModel sellerCompany) {
        AdyenSellerAccountModel sellerAccount = AdyenSellerAccountBuilder.generate()
                .withCompany(sellerCompany)
                .withPaymentProvider(ADYEN)
                .withBillingSystemStatus(IN_SYNC)
                .withStatus(ACTIVE)
                .withAccountId(matchingMerchantId)
                .buildIntegrationInstance()
        modelService.save(sellerAccount)
        return sellerAccount
    }

    private TokenizedSepaDirectDebitPaymentInfoModel createPaymentInfo(IntegratorModel integrator, String merchantId) {
        def newInfo = TokenizedSepaDirectDebitPaymentInfoBuilder.generate()
                .withCode(generateCode("paymentInfo"))
                .withPaymentProvider(ADYEN)
                .withPgwMerchantId(merchantId)
                .withUser(integrator)
                .withAccountHolderName(generateCode('accountHolder'))
                .withIBAN(generateCode('IBAN'))
                .withShopperReference(generateCode('shopperReference'))
                .withRecurringReference(generateCode('recurringReference'))
                .withMandateReference(generateCode('mandateReference'))
                .withDateOfSignature(now())
                .withSaved(true)
                .withDuplicate(false)
                .buildIntegrationInstance()
        modelService.save(newInfo)
        return newInfo
    }

}
