package com.sast.cis.payment.adyen.service.directdebit

import com.sast.cis.core.model.IntegratorModel
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.core.paymentintegration.paymentinfo.IntegratorPaymentInfoService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

import static com.sast.cis.core.enums.PaymentProvider.ADYEN

@UnitTest
class AdyenTokenizedSepaDirectDebitPaymentInfoServiceUnitSpec extends JUnitPlatformSpecification {

    private IntegratorPaymentInfoService integratorPaymentInfoService = Mock()

    private AdyenTokenizedSepaDirectDebitPaymentInfoService paymentInfoService

    private TokenizedSepaDirectDebitPaymentInfoModel firstStoredInfo = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel secondStoredInfo = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel paymentInfoWithOtherMerchant = Mock()

    private IntegratorModel integrator = Mock()
    private AbstractOrderModel cart = Mock()

    private final String merchantId = 'merchant-id'

    def setup() {
        paymentInfoService = new AdyenTokenizedSepaDirectDebitPaymentInfoService(integratorPaymentInfoService)

        firstStoredInfo.getUser() >> integrator
        firstStoredInfo.getPgwMerchantId() >> merchantId

        secondStoredInfo.getUser() >> integrator
        secondStoredInfo.getPgwMerchantId() >> merchantId

        paymentInfoWithOtherMerchant.getPgwMerchantId() >> "other-merchant-id"
        paymentInfoWithOtherMerchant.getUser() >> integrator
    }

    @Test
    void 'getPaymentProvider returns ADYEN'() {
        when:
        def actualPaymentProvider = paymentInfoService.getPaymentProvider()

        then:
        actualPaymentProvider == ADYEN
    }

    @Test
    void 'getPaymentInfos retrieves infos for the given integrator'() {
        when:
        def actualPaymentInfos = paymentInfoService.getPaymentInfos(integrator, merchantId)

        then:
        1 * integratorPaymentInfoService.getPaymentInfos(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> ([firstStoredInfo, secondStoredInfo] as Set)
        actualPaymentInfos == [firstStoredInfo, secondStoredInfo] as Set
    }

    @Test
    void 'given payment info with other merchant when getPaymentInfos then exclude from result'() {
        when:
        def actualPaymentInfos = paymentInfoService.getPaymentInfos(integrator, merchantId)

        then:
        1 * integratorPaymentInfoService.getPaymentInfos(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> ([firstStoredInfo, paymentInfoWithOtherMerchant] as Set)
        actualPaymentInfos == [firstStoredInfo] as Set
    }

    @Test
    void 'getDefaultPaymentInfo retrieves matching default payment info'() {
        when:
        def actualPaymentInfo = paymentInfoService.getDefaultPaymentInfo(integrator, merchantId)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(secondStoredInfo)
        actualPaymentInfo == Optional.of(secondStoredInfo)
    }

    @Test
    void 'given user has no default payment info when getDefaultPaymentInfo then return empty'() {
        when:
        def actualPaymentInfo = paymentInfoService.getDefaultPaymentInfo(integrator, merchantId)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.empty()
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'given user has no default payment info with matching merchant id when getDefaultPaymentInfo then return empty'() {
        when:
        def actualPaymentInfo = paymentInfoService.getDefaultPaymentInfo(integrator, merchantId)

        then:
        1 * integratorPaymentInfoService.getDefaultPaymentInfo(integrator, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(paymentInfoWithOtherMerchant)
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'getCartPaymentInfo retrieves matching order payment info'() {
        when:
        def actualPaymentInfo = paymentInfoService.getCartPaymentInfo(cart, merchantId)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(cart, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(firstStoredInfo)
        actualPaymentInfo == Optional.of(firstStoredInfo)
    }

    @Test
    void 'given user has no default payment info when getCartPaymentInfo then return empty'() {
        when:
        def actualPaymentInfo = paymentInfoService.getCartPaymentInfo(cart, merchantId)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(cart, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.empty()
        actualPaymentInfo == Optional.empty()
    }

    @Test
    void 'given user has no default payment info with matching merchant id when getDefaultPaymentInfo then return empty'() {
        when:
        def actualPaymentInfo = paymentInfoService.getCartPaymentInfo(cart, merchantId)

        then:
        1 * integratorPaymentInfoService.getCartPaymentInfo(cart, ADYEN, TokenizedSepaDirectDebitPaymentInfoModel.class) >> Optional.of(paymentInfoWithOtherMerchant)
        actualPaymentInfo == Optional.empty()
    }
}
