package com.sast.cis.payment.adyen.service.selleraccount


import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.model.PspSellerAccountModel
import com.sast.cis.core.service.order.OrderSellerProvider
import com.sast.cis.payment.adyen.model.AdyenSellerAccountModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.AbstractOrderModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.junit.Test

import static com.sast.cis.core.enums.BillingSystemStatus.IN_SYNC
import static com.sast.cis.core.enums.BillingSystemStatus.NEW
import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static com.sast.cis.core.enums.PaymentProvider.BOSCH_TRANSFER
import static com.sast.cis.core.enums.PspSellerAccountStatus.ACTIVE
import static com.sast.cis.core.enums.PspSellerAccountStatus.ONBOARDING

@UnitTest
class AdyenPspSellerAccountProviderUnitSpec extends JUnitPlatformSpecification {

    private OrderSellerProvider orderSellerProvider = Mock()

    private AdyenSellerAccountProvider adyenSellerAccountProvider

    private IoTCompanyModel sellerCompany = Mock()
    private AbstractOrderModel order = Mock()

    private AdyenSellerAccountModel activeAdyenSellerAccount = Mock()
    private AdyenSellerAccountModel inactiveAdyenSellerAccount = Mock()
    private AdyenSellerAccountModel unsyncedAdyenSellerAccount = Mock()
    private PspSellerAccountModel boschTransferSellerAccount = Mock()
    private PspSellerAccountModel adyenSellerAccountWithWrongType = Mock()


    def setup() {
        adyenSellerAccountProvider = new AdyenSellerAccountProvider(orderSellerProvider)

        activeAdyenSellerAccount.getPaymentProvider() >> ADYEN
        activeAdyenSellerAccount.getBillingSystemStatus() >> IN_SYNC
        activeAdyenSellerAccount.getStatus() >> ACTIVE

        inactiveAdyenSellerAccount.getPaymentProvider() >> ADYEN
        inactiveAdyenSellerAccount.getBillingSystemStatus() >> IN_SYNC
        inactiveAdyenSellerAccount.getStatus() >> ONBOARDING

        unsyncedAdyenSellerAccount.getPaymentProvider() >> ADYEN
        unsyncedAdyenSellerAccount.getBillingSystemStatus() >> NEW
        unsyncedAdyenSellerAccount.getStatus() >> ACTIVE

        boschTransferSellerAccount.getPaymentProvider() >> BOSCH_TRANSFER
        boschTransferSellerAccount.getBillingSystemStatus() >> IN_SYNC
        boschTransferSellerAccount.getStatus() >> ACTIVE

        adyenSellerAccountWithWrongType.getPaymentProvider() >> ADYEN
        adyenSellerAccountWithWrongType.getBillingSystemStatus() >> IN_SYNC
        adyenSellerAccountWithWrongType.getStatus() >> ACTIVE

        sellerCompany.getPspSellerAccounts() >> [activeAdyenSellerAccount,
                                                 inactiveAdyenSellerAccount,
                                                 unsyncedAdyenSellerAccount,
                                                 boschTransferSellerAccount,
                                                 adyenSellerAccountWithWrongType]

        orderSellerProvider.getSellerForOrderOrThrow(order) >> sellerCompany
    }

    @Test
    void 'getActiveSellerAccountForCompany returns only the active seller account'() {
        when:
        def actualResult = adyenSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany)

        then:
        actualResult == Optional.of(activeAdyenSellerAccount)
    }

    @Test
    void 'getSellerAccountForOrder returns only the active seller account'() {
        when:
        def actualResult = adyenSellerAccountProvider.getSellerAccountForOrder(order)

        then:
        actualResult == Optional.of(activeAdyenSellerAccount)
    }

    @Test
    void 'getActiveSellerAccountForCompany returns empty if there is no active Adyen account'() {
        when:
        def actualResult = adyenSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany)

        then:
        sellerCompany.getPspSellerAccounts() >> [boschTransferSellerAccount, inactiveAdyenSellerAccount, unsyncedAdyenSellerAccount]
        actualResult == Optional.empty()
    }

    @Test
    void 'getSellerAccountForOrder returns empty if there is no active Adyen account'() {
        when:
        def actualResult = adyenSellerAccountProvider.getSellerAccountForOrder(order)

        then:
        sellerCompany.getPspSellerAccounts() >> [boschTransferSellerAccount, inactiveAdyenSellerAccount, unsyncedAdyenSellerAccount]
        actualResult == Optional.empty()
    }

    @Test
    void 'getActiveSellerAccountForCompany throws IllegalStateException if there are multiple active Adyen accounts'() {
        given:
        def anotherActiveAccount = Mock(AdyenSellerAccountModel)
        anotherActiveAccount.getPaymentProvider() >> ADYEN
        anotherActiveAccount.getBillingSystemStatus() >> IN_SYNC
        anotherActiveAccount.getStatus() >> ACTIVE

        when:
        adyenSellerAccountProvider.getActiveSellerAccountForCompany(sellerCompany)

        then:
        sellerCompany.getPspSellerAccounts() >> [activeAdyenSellerAccount, anotherActiveAccount]
        thrown(IllegalStateException)
    }

    @Test
    void 'getActiveSellerAccountForOrder throws IllegalStateException if there are multiple active Adyen accounts'() {
        given:
        def anotherActiveAccount = Mock(AdyenSellerAccountModel)
        anotherActiveAccount.getPaymentProvider() >> ADYEN
        anotherActiveAccount.getBillingSystemStatus() >> IN_SYNC
        anotherActiveAccount.getStatus() >> ACTIVE

        when:
        adyenSellerAccountProvider.getSellerAccountForOrder(order)

        then:
        sellerCompany.getPspSellerAccounts() >> [activeAdyenSellerAccount, anotherActiveAccount]
        thrown(IllegalStateException)
    }
}
