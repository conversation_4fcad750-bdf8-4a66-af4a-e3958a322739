package com.sast.cis.payment.adyen.service.directdebit;

import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter;
import com.sast.cis.core.paymentintegration.data.CheckoutInfo;
import com.sast.cis.payment.adyen.model.AdyenSellerAccountModel;
import com.sast.cis.payment.adyen.service.AdyenPaymentMethodStrategy;
import com.sast.cis.payment.adyen.service.selleraccount.AdyenSellerAccountProvider;
import de.hybris.platform.core.model.order.AbstractOrderModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.sast.cis.core.enums.PaymentMethodType.SEPA_DIRECTDEBIT;
import static com.sast.cis.core.enums.PaymentProvider.ADYEN;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdyenSepaDirectDebitMethodStrategy implements AdyenPaymentMethodStrategy {
    private final AdyenTokenizedSepaDirectDebitPaymentInfoService paymentInfoService;
    private final AdyenSellerAccountProvider adyenSellerAccountProvider;

    @Override
    public PaymentMethodType getPaymentMethod() {
        return SEPA_DIRECTDEBIT;
    }

    @Override
    public CheckoutInfo prepareCheckout(@NonNull final AuthorizationParameter authorizationParameter) {
        final AbstractOrderModel abstractOrder = authorizationParameter.abstractOrder();
        final AdyenSellerAccountModel adyenSellerAccount = adyenSellerAccountProvider.getSellerAccountForOrderOrThrow(abstractOrder);
        final IntegratorModel integrator = (IntegratorModel) abstractOrder.getUser();
        final String accountId = adyenSellerAccount.getAccountId();

        final CheckoutInfo checkoutInfo = new CheckoutInfo();
        checkoutInfo.setPaymentMethod(getPaymentMethod());
        checkoutInfo.setPaymentProvider(ADYEN);
        checkoutInfo.setUserCreatable(true);
        checkoutInfo.setSavableForReuse(true);

        checkoutInfo.setStoredPaymentInfos(paymentInfoService.getPaymentInfos(integrator, accountId));
        paymentInfoService.getDefaultPaymentInfo(integrator, accountId).ifPresent(checkoutInfo::setDefaultPaymentInfo);
        paymentInfoService.getCartPaymentInfo(abstractOrder, accountId).ifPresent(checkoutInfo::setCartPaymentInfo);

        return checkoutInfo;
    }
}
