package com.sast.cis.payment.adyen.service.selleraccount;

import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.selleraccount.PspSellerAccountProvider;
import com.sast.cis.core.service.order.OrderSellerProvider;
import com.sast.cis.payment.adyen.model.AdyenSellerAccountModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.sast.cis.core.enums.PaymentProvider.ADYEN;

@Service
@Slf4j
public class AdyenSellerAccountProvider extends PspSellerAccountProvider<AdyenSellerAccountModel> {

    public AdyenSellerAccountProvider(final OrderSellerProvider orderSellerProvider) {
        super(orderSellerProvider);
    }

    protected PaymentProvider getPaymentProvider() {
        return ADYEN;
    }

    protected Class<AdyenSellerAccountModel> getSellerAccountType() {
        return AdyenSellerAccountModel.class;
    }
}
