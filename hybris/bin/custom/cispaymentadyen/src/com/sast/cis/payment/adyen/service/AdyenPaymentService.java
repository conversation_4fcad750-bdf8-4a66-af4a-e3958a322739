package com.sast.cis.payment.adyen.service;

import com.sast.cis.core.data.PaymentInfoData;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.paymentintegration.PaymentService;
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter;
import com.sast.cis.core.paymentintegration.data.AuthorizationResult;
import com.sast.cis.core.paymentintegration.data.CheckoutInfo;
import com.sast.cis.core.service.license.ProductSellerProvider;
import com.sast.cis.payment.adyen.service.selleraccount.AdyenSellerAccountProvider;
import de.hybris.platform.core.model.order.payment.PaymentInfoModel;
import de.hybris.platform.payment.model.PaymentTransactionEntryModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;

import static com.sast.cis.core.enums.PaymentProvider.ADYEN;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdyenPaymentService implements PaymentService {

    private final AdyenPaymentMethodDispatch adyenPaymentMethodDispatch;

    @Override
    public PaymentProvider getPaymentProvider() {
        return ADYEN;
    }

    @Override
    public CheckoutInfo prepareCheckout(
        @NonNull final AuthorizationParameter authorizationParameter,
        @NonNull final PaymentMethodType paymentMethod) {

        return adyenPaymentMethodDispatch.prepareCheckout(authorizationParameter, paymentMethod);
    }

    @Override
    public void confirmSelectedPaymentInfo(@NonNull final AuthorizationParameter authorizationParameter) {

    }

    @Override
    public PaymentTransactionEntryModel authorize(@NonNull final AuthorizationParameter authorizationParameter) {
        return null;
    }

    @Override
    public AuthorizationResult getAuthorizationResult(@NonNull final PaymentTransactionEntryModel paymentTransactionEntry) {
        return null;
    }

    @Override
    public void capture(final PaymentTransactionModel paymentTransaction) {

    }

    @Override
    public void refund(final PaymentTransactionModel paymentTransaction) {

    }

    @Override
    public boolean supportsPurchase(
        @NonNull final PaymentMethodType paymentMethod,
        @NonNull final IoTCompanyModel buyerCompany,
        @NonNull final Set<AppLicenseModel> licenses) {
        return false;
    }

    @Override
    public boolean supportsSale(@NonNull final PaymentMethodType paymentMethod, @NonNull final IoTCompanyModel sellerCompany) {
        return false;
    }

    @Override
    public boolean canSetupPayment(final IoTCompanyModel sellerCompany) {
        return false;
    }

    @Override
    public boolean isPayoutAccountValidated(final IoTCompanyModel sellerCompany) {
        return false;
    }

    @Override
    public void createSellerAccount(final IoTCompanyModel sellerCompany) {

    }

    @Override
    public PaymentInfoModel createPaymentInfo(final IntegratorModel integrator, final PaymentInfoData paymentInfoData) {
        return null;
    }
}
