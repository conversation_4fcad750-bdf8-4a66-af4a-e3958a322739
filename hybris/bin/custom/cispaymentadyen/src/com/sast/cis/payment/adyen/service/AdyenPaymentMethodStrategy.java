package com.sast.cis.payment.adyen.service;

import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter;
import com.sast.cis.core.paymentintegration.data.CheckoutInfo;
import lombok.NonNull;

public interface AdyenPaymentMethodStrategy {

    PaymentMethodType getPaymentMethod();

    CheckoutInfo prepareCheckout(@NonNull final AuthorizationParameter authorizationParameter);
}
