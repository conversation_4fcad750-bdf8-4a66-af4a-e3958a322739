package com.sast.cis.payment.adyen.client.tokenization;

public record TokenizationResult(String storedPaymentMethodId,
                                 String pspReference,
                                 String resultCode) {

    public static TokenizationResult of(String storedPaymentMethodId, String pspReference, String resultCode) {
        return new TokenizationResult(storedPaymentMethodId, pspReference, resultCode);
    }
}
