package com.sast.cis.payment.adyen.service;

import com.google.common.collect.ImmutableMap;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.paymentintegration.data.AuthorizationParameter;
import com.sast.cis.core.paymentintegration.data.CheckoutInfo;
import com.sast.cis.core.paymentintegration.exception.PaymentMethodNotSupportedByProviderException;
import lombok.NonNull;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

import static com.sast.cis.core.enums.PaymentProvider.ADYEN;
import static java.util.Optional.ofNullable;

@Service
public class AdyenPaymentMethodDispatch {

    private final Map<PaymentMethodType, AdyenPaymentMethodStrategy> paymentMethodStrategies;

    public AdyenPaymentMethodDispatch(@NonNull final Set<AdyenPaymentMethodStrategy> strategies) {
        this.paymentMethodStrategies = buildStrategyMap(strategies);
    }

    public CheckoutInfo prepareCheckout(
        @NonNull final AuthorizationParameter authorizationParameter,
        @NonNull final PaymentMethodType paymentMethod) {

        return getStrategyForPaymentMethod(paymentMethod).prepareCheckout(authorizationParameter);
    }

    private AdyenPaymentMethodStrategy getStrategyForPaymentMethod(final PaymentMethodType paymentMethodType) {
        return ofNullable(paymentMethodStrategies.get(paymentMethodType))
            .orElseThrow(() -> PaymentMethodNotSupportedByProviderException.forMethodAndProvider(paymentMethodType, ADYEN));
    }

    private Map<PaymentMethodType, AdyenPaymentMethodStrategy> buildStrategyMap(Set<AdyenPaymentMethodStrategy> strategies) {
        var strategyMapBuilder = ImmutableMap.<PaymentMethodType, AdyenPaymentMethodStrategy>builder();
        for (AdyenPaymentMethodStrategy strategy : strategies) {
            strategyMapBuilder.put(strategy.getPaymentMethod(), strategy);
        }
        return strategyMapBuilder.build();
    }
}
