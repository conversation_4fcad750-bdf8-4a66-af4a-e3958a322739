package com.sast.cis.payment.adyen.service.directdebit;

import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.paymentintegration.paymentinfo.IntegratorPaymentInfoService;
import com.sast.cis.core.paymentintegration.paymentinfo.TokenizedSepaDirectDebitPaymentInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.sast.cis.core.enums.PaymentProvider.ADYEN;

@Service
@Slf4j
public class AdyenTokenizedSepaDirectDebitPaymentInfoService extends TokenizedSepaDirectDebitPaymentInfoService {

    public AdyenTokenizedSepaDirectDebitPaymentInfoService(final IntegratorPaymentInfoService integratorPaymentInfoService) {
        super(integratorPaymentInfoService);
    }

    @Override
    public PaymentProvider getPaymentProvider() {
        return ADYEN;
    }
}
