package com.sast.cis.payment.adyen.client.proxyclientapi;

import com.sast.adyengateway.dto.TokenizationRequestDto;
import com.sast.adyengateway.dto.TokenizationResponseDto;

import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

/**
 * IMPORTANT: Interface duplicated due to JAX-RS Namespace Compatibility issue.
 * The Store uses javax.ws.rs.* (Java EE 8), where the Adyen Gateway client library uses jakarta.ws.rs.* (Jakarta EE 9+).
 * When consuming libraries or dependencies built with jakarta.ws.rs.* (Jakarta EE 9+), Jersey and other JAX-RS implementations will NOT recognize resource/interface annotations across the namespace boundary.
 * This can cause runtime errors such as UnsupportedOperationException("Not a resource method.").
 */
@Path("/payment/tokenization")
@Produces({ "application/json" })
@Consumes({ "application/json" })
public interface TokenizationApi {
    @POST
    @Path("/")
    TokenizationResponseDto tokenize(@Valid TokenizationRequestDto var1);
}

