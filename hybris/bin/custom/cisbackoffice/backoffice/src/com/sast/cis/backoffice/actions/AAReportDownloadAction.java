package com.sast.cis.backoffice.actions;

import com.hybris.cockpitng.actions.ActionContext;
import com.hybris.cockpitng.actions.ActionResult;
import com.hybris.cockpitng.actions.CockpitAction;
import com.sast.cis.aa.core.reports.service.AaSubscriptionReportExportService;
import lombok.SneakyThrows;
import org.zkoss.zul.Filedownload;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public class AAReportDownloadAction implements CockpitAction<Map<?, ?>, String> {

    @Resource
    private AaSubscriptionReportExportService aaSubscriptionReportExportService;

    private static final String FILENAME_FORMAT = "AAReport_%s_%s.xlsx";
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    @SneakyThrows
    @Override
    public ActionResult<String> perform(ActionContext<Map<?, ?>> actionContext) {
        String fileName = String
                .format(FILENAME_FORMAT,
                        new SimpleDateFormat(DATE_FORMAT).format(new Date()),
                        Math.abs(ThreadLocalRandom.current().nextInt()));
        ByteArrayOutputStream excelBytes = aaSubscriptionReportExportService.createExcelReport();
        Filedownload.save(excelBytes.toByteArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        return new ActionResult<>(ActionResult.SUCCESS, "Download Successful");
    }

    @Override
    public boolean needsConfirmation(ActionContext<Map<?, ?>> ctx) {
        return true;
    }

    @Override
    public String getConfirmationMessage(ActionContext<Map<?, ?>> context) {
        return "Do you really want to download AA Report ?";
    }

}
