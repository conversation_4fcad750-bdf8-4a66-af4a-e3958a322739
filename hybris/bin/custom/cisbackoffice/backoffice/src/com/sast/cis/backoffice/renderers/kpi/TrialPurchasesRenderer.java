package com.sast.cis.backoffice.renderers.kpi;

import com.hybris.cockpitng.core.config.impl.jaxb.listview.ListColumn;
import com.hybris.cockpitng.dataaccess.facades.type.DataType;
import com.hybris.cockpitng.engine.WidgetInstanceManager;
import com.hybris.cockpitng.widgets.common.AbstractWidgetComponentRenderer;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppLicenseService;
import org.zkoss.zul.Label;
import org.zkoss.zul.Listcell;

import java.util.Optional;

public class TrialPurchasesRenderer  extends AbstractWidgetComponentRenderer<Listcell, ListColumn, AppModel> {
    private final AppLicenseService appLicenseService;

    public TrialPurchasesRenderer(AppLicenseService appLicenseService) {
        this.appLicenseService = appLicenseService;
    }

    @Override
    public void render(Listcell parent, ListColumn configuration, AppModel appModel, DataType dataType,
        WidgetInstanceManager widgetInstanceManager) {
        Optional<AppLicenseModel> evaluationLicense = appLicenseService.getEvaluationLicense(appModel);
        Label acquisitionsLabel = new Label();
        evaluationLicense.ifPresent(license -> {
            int acquisitions = license.getAcquisitionCount() != null ? license.getAcquisitionCount() : 0;
            acquisitionsLabel.setValue(acquisitions+"");
        });
        parent.appendChild(acquisitionsLabel);
        fireComponentRendered(acquisitionsLabel, parent, configuration, appModel);
        fireComponentRendered(parent, configuration, appModel);
    }
}
