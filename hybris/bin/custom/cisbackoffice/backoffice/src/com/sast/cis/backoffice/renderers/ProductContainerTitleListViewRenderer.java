package com.sast.cis.backoffice.renderers;

import com.hybris.cockpitng.core.config.impl.jaxb.listview.ListColumn;
import com.hybris.cockpitng.dataaccess.facades.type.DataType;
import com.hybris.cockpitng.engine.WidgetInstanceManager;
import com.hybris.cockpitng.widgets.collectionbrowser.mold.impl.common.AbstractMoldStrategy;
import com.hybris.cockpitng.widgets.common.AbstractWidgetComponentRenderer;
import com.sast.cis.core.model.AppDraftModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.ProductContainerModel;
import org.springframework.stereotype.Component;
import org.zkoss.zul.Label;
import org.zkoss.zul.Listcell;

import static com.hybris.cockpitng.util.UITools.modifySClass;

@Component
public class ProductContainerTitleListViewRenderer extends AbstractWidgetComponentRenderer<Listcell, ListColumn, Object> {

    private static final String CELL_LABEL_CSS_CLASS = "yw-listview-cell-label";

    @Override
    public void render(Listcell parent, ListColumn configuration, Object data, DataType dataType,
        WidgetInstanceManager widgetInstanceManager) {

        Label label = extractAppName(data);

        setupLabel(parent, label);

        fireComponentRendered(label, parent, configuration, data);
        fireComponentRendered(parent, configuration, data);
    }

    private void setupLabel(Listcell parent, Label label) {
        modifySClass(label, CELL_LABEL_CSS_CLASS, true);
        label.setAttribute(AbstractMoldStrategy.ATTRIBUTE_HYPERLINK_CANDIDATE, true);
        parent.appendChild(label);
    }

    private Label extractAppName(Object data) {
        if (!(data instanceof ProductContainerModel)) {
            return new Label();
        }
        ProductContainerModel productContainer = (ProductContainerModel) data;

        AppModel app = productContainer.getApp();
        if (app != null) {
            return new Label(app.getName());
        }

        AppDraftModel appDraft = productContainer.getAppDraft();
        if (appDraft != null && appDraft.getStoreContentDraft() != null) {
            return new Label(appDraft.getStoreContentDraft().getName());
        }

        return new Label();
    }

}
