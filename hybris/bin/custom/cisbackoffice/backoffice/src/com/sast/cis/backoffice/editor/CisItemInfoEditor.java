package com.sast.cis.backoffice.editor;

import com.hybris.cockpitng.editors.CockpitEditorRenderer;
import com.hybris.cockpitng.editors.EditorContext;
import com.hybris.cockpitng.editors.EditorListener;
import de.hybris.platform.core.model.ItemModel;
import org.zkoss.zk.ui.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class CisItemInfoEditor extends ItemInfoEditor implements CockpitEditorRenderer<ItemModel> {

    public static final String CISATTRIBUTE = "cisattribute";

    public CisItemInfoEditor() {
        super();
    }

    @Override
    public void render(Component parent, EditorContext<ItemModel> editorContext, EditorListener<ItemModel> editorListener) {
        try {
            List<ItemModel> cisItems = new ArrayList<>();
            if (editorContext.getInitialValue() != null) {
                cisItems.add(editorContext.getInitialValue());
            }
            for (String paramKey : editorContext.getParameterKeys()) {
                Object attrName = editorContext.getParameter(paramKey);
                if (attrName instanceof String && paramKey.startsWith(CISATTRIBUTE)) {
                    renderValue((String) attrName, cisItems, parent);
                }
            }
        } catch (Exception e) {
            LOG.error("Error rendering editor for element '{}': {}", editorContext.getEditorLabel(), e.getMessage(), e);
        }
    }

    private void renderValue(String paramValue, List<ItemModel> cisItems, Component parent) {
        Locale currentLocale = i18nService.getCurrentLocale();
        if (currentLocale == null) {
            currentLocale = Locale.getDefault();
        }
        List<CisItemInfo> itemInfos = cisItemInfoService.buildItemInfo(ItemModel.class, cisItems, paramValue, currentLocale);
        if (itemInfos != null) {
            itemInfos.forEach(itemI -> buildView(itemI, parent, false));
        }
    }
}
