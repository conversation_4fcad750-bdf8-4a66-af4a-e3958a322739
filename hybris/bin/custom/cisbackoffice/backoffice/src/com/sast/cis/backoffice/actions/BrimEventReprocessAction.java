package com.sast.cis.backoffice.actions;

import com.hybris.cockpitng.actions.ActionContext;
import com.hybris.cockpitng.actions.ActionResult;
import com.hybris.cockpitng.actions.CockpitAction;
import com.sast.cis.brim.model.BrimEventModel;
import com.sast.cis.core.billingintegration.backoffice.BillingEventProcessingService;
import org.zkoss.zul.Messagebox;

import javax.annotation.Resource;

public class BrimEventReprocessAction implements CockpitAction<BrimEventModel, String> {
    @Resource
    private BillingEventProcessingService billingEventProcessingService;

    @Override
    public ActionResult<String> perform(ActionContext<BrimEventModel> actionContext) {
        final BrimEventModel brimEvent = actionContext.getData();
        try {
            billingEventProcessingService.process(brimEvent);
            final String successMessage = String.format("Manual reprocess of BRIM event %s successful", brimEvent.getCode());
            Messagebox.show(successMessage);
            return new ActionResult<>(ActionResult.SUCCESS, successMessage);
        } catch (Exception e) {
            final String errorMessage = String.format("Manual reprocess of BRIM event %s failed: %s",
                    brimEvent.getCode(), e.getMessage());
            Messagebox.show(errorMessage);
            return new ActionResult<>(ActionResult.ERROR, errorMessage);
        }
    }

    @Override
    public boolean canPerform(ActionContext<BrimEventModel> actionContext) {
        return CockpitAction.super.canPerform(actionContext);
    }

    @Override
    public boolean needsConfirmation(ActionContext<BrimEventModel> actionContext) {
        return true;
    }

    @Override
    public String getConfirmationMessage(ActionContext<BrimEventModel> actionContext) {
        final BrimEventModel brimEvent = actionContext.getData();
        return String.format("Do you want to reprocess BRIM event %s?", brimEvent.getCode());
    }
}
