package com.sast.cis.backoffice.editor;

import com.sast.cis.backoffice.editor.attribute.CisItemAttribute;
import com.sast.cis.backoffice.editor.service.CisItemInfoService;
import com.sast.cis.backoffice.editor.service.DefaultCisItemInfoService;
import de.hybris.platform.core.Registry;
import de.hybris.platform.servicelayer.i18n.I18NService;
import de.hybris.platform.servicelayer.type.TypeService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zkoss.zk.ui.Component;
import org.zkoss.zul.*;

public abstract class ItemInfoEditor {

    protected static final Logger LOG = LoggerFactory.getLogger(ItemInfoEditor.class);

    protected static final String STYLE_HBOX = "padding-bottom:1em;";
    protected static final String WIDTH_LISTBOX = "400px";
    protected static final String STYLE_LISTCELL_HEADER = "background-color:#E8EAEC;";
    protected static final int SPAN_LISTCELL_HEADER = 2;

    protected I18NService i18nService;
    protected TypeService typeService;
    protected CisItemInfoService cisItemInfoService;

    public ItemInfoEditor() {
        this.cisItemInfoService = Registry.getApplicationContext().getBean("cisItemInfoService", DefaultCisItemInfoService.class);
        this.typeService = Registry.getApplicationContext().getBean("typeService", TypeService.class);
        this.i18nService = Registry.getApplicationContext().getBean("i18nService", I18NService.class);
    }

    protected void buildView(CisItemInfo cisItemInfo, Component parent, boolean isChild) {
        Div hbox = new Div();
        hbox.setParent(parent);
        hbox.setStyle(STYLE_HBOX);
        parent.appendChild(hbox);

        Listbox listbox = new Listbox();
        listbox.setWidth(WIDTH_LISTBOX);
        listbox.setMultiple(Boolean.TRUE);
        listbox.setParent(hbox);
        hbox.appendChild(listbox);

        if (isChild) {
            Listitem listitemHeader = new Listitem();
            Listcell listcellHeader = new Listcell();
            listcellHeader.setStyle(STYLE_LISTCELL_HEADER);
            listcellHeader.setSpan(SPAN_LISTCELL_HEADER);
            Label lab = new Label(cisItemInfo.getCode());
            listcellHeader.appendChild(lab);
            listcellHeader.setParent(listitemHeader);
            listitemHeader.appendChild(listcellHeader);
            listitemHeader.setParent(listbox);
            listbox.appendChild(listitemHeader);
        }
        buildViewForAttributes(cisItemInfo, listbox);
    }

    protected void buildViewForAttributes(CisItemInfo cisItemInfo, Listbox listbox) {
        for (CisItemAttribute attribute : cisItemInfo.getAttributes()) {
            Listitem listitem = new Listitem();

            Listcell listcellLabel = new Listcell();
            Label headLabel = new Label(attribute.getLabel());
            listcellLabel.appendChild(headLabel);
            listcellLabel.setParent(listitem);
            listitem.appendChild(listcellLabel);

            Listcell listcellValue = new Listcell();
            if (CollectionUtils.isNotEmpty(attribute.getValues())) {
                if (attribute.getValues().size() == 1) {
                    String value = attribute.getValues().get(0);
                    Textbox textbox = new Textbox(value);
                    textbox.setDisabled(true);
                    listcellValue.appendChild(textbox);
                } else {
                    for (String value : attribute.getValues()) {
                        Textbox textbox = new Textbox(value);
                        textbox.setDisabled(true);
                        Div div = new Div();
                        div.appendChild(textbox);
                        div.setParent(listcellValue);
                        listcellValue.appendChild(div);
                    }
                }
            } else {
                Label label = new Label("n/a");
                listcellValue.appendChild(label);
            }
            listcellValue.setParent(listitem);
            listitem.appendChild(listcellValue);

            listitem.setParent(listbox);
            listbox.appendChild(listitem);
        }
    }
}
