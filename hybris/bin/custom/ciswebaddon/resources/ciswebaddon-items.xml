<?xml version="1.0" encoding="ISO-8859-1"?>

<items 	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
			xsi:noNamespaceSchemaLocation="items.xsd">
	<!-- NOTE: typecodes for this extension should start at 21100 -->

	<itemtypes>
		<itemtype code="CMSImageLinkComponent" extends="CMSLinkComponent"
				  jaloclass="com.sast.cis.web.jalo.CMSImageLinkComponent"
				  autocreate="true" generate="true">
			<description>A CMS Link Component with an image</description>
			<attributes>
				<attribute qualifier="image" type="Media">
					<persistence type="property"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="CMSLinkComponent" autocreate="false" generate="false">
			<attributes>
				<attribute qualifier="urlProperty" type="java.lang.String">
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="icon" type="java.lang.String">
					<persistence type="property"/>
				</attribute>
				<attribute qualifier="resolvedUrl" type="java.lang.String">
					<modifiers read="true" write="false" search="true"/>
					<persistence type="dynamic" attributeHandler="cmsLinkComponentResolvedUrlHandler"/>
				</attribute>
			</attributes>
		</itemtype>

		<itemtype code="BreadcrumbComponent" generate="false" autocreate="false">
			<attributes>
				<attribute qualifier="homepage" type="ContentPage">
					<persistence type="property"/>
				</attribute>
			</attributes>
		</itemtype>
	</itemtypes>
</items>
