package com.sast.cis.web.handler;

import com.sast.cis.web.util.CmsLinkComponentUrlResolver;
import de.hybris.platform.category.model.CategoryModel;
import de.hybris.platform.cms2.model.contents.components.CMSLinkComponentModel;
import de.hybris.platform.commercefacades.product.data.CategoryData;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.servicelayer.model.attribute.DynamicAttributeHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CmsLinkComponentResolvedUrlHandler implements DynamicAttributeHandler<String, CMSLinkComponentModel> {

    @Resource
    private CmsLinkComponentUrlResolver cmsLinkComponentUrlResolver;

    @Resource
    private Converter<ProductModel, ProductData> productUrlConverter;

    @Resource
    private Converter<CategoryModel, CategoryData> categoryUrlConverter;

    @Override
    public String get(CMSLinkComponentModel component) {
        return cmsLinkComponentUrlResolver.resolveUrl(component, productUrlConverter, categoryUrlConverter);
    }

    @Override
    public void set(CMSLinkComponentModel model, String s) {
        throw new IllegalArgumentException("ProductContainer's 'approved' attribute is read-only.");
    }
}
